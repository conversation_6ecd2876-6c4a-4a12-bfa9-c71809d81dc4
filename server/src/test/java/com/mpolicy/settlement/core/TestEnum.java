package com.mpolicy.settlement.core;

import lombok.Getter;

@Getter
public enum TestEnum {

        FIRST_YR_COMM("首续年佣金","FIRST_YR_COMM"),

        SECOND_YR_COMM("首续年佣金二档","SECOND_YR_COMM"),

        THIRD_YR_COMM("首续年佣金三档","THIRD_YR_COMM"),

        FIRST_YR_LOW_COMM("首续年佣金一档","FIRST_YR_LOW_COMM"),

        DEFERRED_REWARD("递延奖励","DEFERRED_REWARD"),

        MONTHLY_SALES_BONUS("月度销售奖金","MONTHLY_SALES_BONUS"),

        BUSINESS_PROMOTION_REWARD("业务推动奖励","BUSINESS_PROMOTION_REWARD"),

        BUSINESS_PROMOTION_REWARD_2("业务推动奖励-二档","BUSINESS_PROMOTION_REWARD_2"),

        BUSINESS_PROMOTION_REWARD_3("业务推动奖励-三档","BUSINESS_PROMOTION_REWARD_3"),

        BUSINESS_PROMOTION_REWARD_4("业务推动奖励-四档","BUSINESS_PROMOTION_REWARD_4"),

        BUSINESS_PROMOTION_REWARD_1("业务推动奖励-一档","BUSINESS_PROMOTION_REWARD_1"),

        COMPANY_QUALITY_COOP_BONUS("公司品质合作奖金","COMPANY_QUALITY_COOP_BONUS"),

        COMPANY_QUALITY_COOP_BONUS_1("公司品质合作奖金-一档","COMPANY_QUALITY_COOP_BONUS_1"),

        COMPANY_QUALITY_COOP_BONUS_2("公司品质合作奖金-二档","COMPANY_QUALITY_COOP_BONUS_2"),

        COMPANY_QUALITY_COOP_BONUS_3("公司品质合作奖金-三档","COMPANY_QUALITY_COOP_BONUS_3"),

        COMPANY_QUALITY_COOP_BONUS_4("公司品质合作奖金-四档","COMPANY_QUALITY_COOP_BONUS_4"),

        CONTINUATION_SERVICE_ALLOWANCE("续年度服务津贴","CONTINUATION_SERVICE_ALLOWANCE"),

        CONTINUATION_SERVICE_COMM("续年度服务佣金","CONTINUATION_SERVICE_COMM"),

        RETENTION_BONUS("继续率奖金","RETENTION_BONUS"),

        RETENTION_BONUS_1("继续率奖金-一档","RETENTION_BONUS_1"),

        RETENTION_BONUS_2("继续率奖金-二档","RETENTION_BONUS_2"),

        RETENTION_BONUS_3("继续率奖金-三档","RETENTION_BONUS_3"),

        RETENTION_BONUS_4("继续率奖金-四档","RETENTION_BONUS_4"),

        RETENTION_BONUS_5("继续率奖金-五档","RETENTION_BONUS_5"),

        RETENTION_BONUS_6("继续率奖金-六档","RETENTION_BONUS_6"),

        RETENTION_BONUS_7("继续率奖金-七档","RETENTION_BONUS_7"),

        RETENTION_BONUS_8("继续率奖金-八档","RETENTION_BONUS_8"),

        COOPERATION_BONUS("合作奖金","COOPERATION_BONUS"),

        INCENTIVE_COST("激励费用","INCENTIVE_COST"),

        RETENTION_PERF_COMM("继续率绩效佣金","RETENTION_PERF_COMM"),

        RETENTION_BENEFIT_COMM("继续率效益佣金","RETENTION_BENEFIT_COMM"),

        COMPETITION_AWARD("竞赛奖","COMPETITION_AWARD"),

        ANNUAL_BONUS("年度奖金","ANNUAL_BONUS"),

        TRAINING_ALLOWANCE("培训津贴","TRAINING_ALLOWANCE"),

        FIRST_QUARTER_COMM("首年季度佣金","FIRST_QUARTER_COMM"),

        FIRST_YEAR_RETENTION_RATE("首年自留佣金率","FIRST_YEAR_RETENTION_RATE"),

        FIRST_YEAR_MGMT_COMM_RATE("首年综合管理佣金率","FIRST_YEAR_MGMT_COMM_RATE"),

        SPECIAL_COOP_AGREEMENT_RETENTION_RATE_BONUS("特别合作协议持续率奖金","SPECIAL_COOP_AGREEMENT_RETENTION_RATE_BONUS"),

        SPECIAL_BONUS("特别奖金","SPECIAL_BONUS"),

        SPECIAL_PROC_FEE("特别手续费","SPECIAL_PROC_FEE"),

        SALES_BONUS("销售奖金","SALES_BONUS"),

        RENEWAL_PROMOTION_BONUS("续期推动奖金","RENEWAL_PROMOTION_BONUS"),

        BUSINESS_DEV_COST("业务推动发展费用","BUSINESS_DEV_COST"),

        OPERATION_MGMT_COMM("营管佣金","OPERATION_MGMT_COMM"),

        MONTHLY_BONUS("月度奖金","MONTHLY_BONUS"),

        INCREASED_PROC_FEE("增提手续费","INCREASED_PROC_FEE"),

        ADDITIONAL_BONUS("追加奖金","ADDITIONAL_BONUS"),

        TOTAL_PROC_FEE("总对总手续费","TOTAL_PROC_FEE"),

        TECHNICAL_SERVICE_FEE("技术服务费","TECHNICAL_SERVICE_FEE"),

        CONSULTING_SERVICE_FEE("咨询服务费","CONSULTING_SERVICE_FEE"),

        PROMOTION_SERVICE_FEE("宣传服务费","PROMOTION_SERVICE_FEE"),

        EXTENSION_SERVICE_FEE("推广服务费","EXTENSION_SERVICE_FEE"),

        MODERN_SERVICE_FEE("现代服务费","MODERN_SERVICE_FEE"),

        OTHER("其他","OTHER"),

        NULL("未配置","NULL");
        private final String name;

        private final String code;

        TestEnum(String name, String code) {
            this.name = name;
            this.code = code;
        }


        public static void main(String[] args) {
                StringBuffer sql = new StringBuffer("INSERT INTO `settlement_subject` (`subject_name`, `subject_code`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ");
                for (TestEnum testEnum : TestEnum.values()) {
                        sql.append("(").append("'").append(testEnum.name).append("',").append("'").append(testEnum.code).append("'").append(", 'liushuchen', '2022-11-29 11:05:53', 'liushuchen', '2022-12-11 17:57:33')),");
                }
                System.out.println(sql.toString());
        }

    }