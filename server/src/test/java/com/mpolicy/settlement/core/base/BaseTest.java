package com.mpolicy.settlement.core.base;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.mpolicy.settlement.core.SettlementCoreCenterApplicationTests;
import com.mpolicy.settlement.core.modules.reconcile.dto.rule.ReconcileCompanyRuleInfo;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcileInfoService;
import com.mpolicy.settlement.core.service.PcDicCompanyService;
import com.mpolicy.settlement.core.service.SysDocumentService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.stream.Collectors;

/**
 * 测试
 *
 * <AUTHOR>
 * @date 2022-04-28 16:59
 */
@Slf4j
public class BaseTest extends SettlementCoreCenterApplicationTests {

    @Autowired
    private SysDocumentService sysDocumentService;

    @Autowired
    private PcDicCompanyService pcDicCompanyService;

    @Autowired
    private SettlementReconcileInfoService settlementReconcileInfoService;

    @Test
    public void test() {
        // 查询小鲸库
        pcDicCompanyService.queryOne();

        Integer count = settlementReconcileInfoService.lambdaQuery()
                .eq(SettlementReconcileInfoEntity::getCompanyCode, "JG20230505200")
                .eq(SettlementReconcileInfoEntity::getPolicyMonth, DateUtil.month(new Date()) + 1)
                .eq(SettlementReconcileInfoEntity::getSubjectRuleCodes, "R20230619155247791268")
                .eq(SettlementReconcileInfoEntity::getCreateReconcileDay, "每月2日")
                .count();

        log.info("");
    }
}
