package com.mpolicy.settlement.core.base;
import java.util.ArrayList;
import java.util.List;

public class IncomeDistribution {
    public static void main(String[] args) {
        List<Person> people = new ArrayList<>();
        people.add(new Person("杨九", -311));
        people.add(new Person("周十", 100));
        people.add(new Person("吴十一", -90));
        people.add(new Person("郑十二", 300));

        double totalAmount = 100;

        distributeIncome(people, totalAmount);

        // 打印分摊后的结果
        for (Person person : people) {
            System.out.println(person);
        }
    }

    static class Person {
        String name;
        double income;
        double additionalIncome;

        public Person(String name, double income) {
            this.name = name;
            this.income = income;
        }

        @Override
        public String toString() {
            return "Person{" +
                    "name='" + name + '\'' +
                    ", income=" + income +
                    ", additionalIncome=" + additionalIncome +
                    '}';
        }
    }

    static void distributeIncome(List<Person> people, double totalAmount) {
        // 计算总收益
        double totalIncome = 0;
        for (Person person : people) {
            totalIncome += person.income;
        }

        // 计算每个人分摊的金额并赋值到新的key中
        for (Person person : people) {
            double ratio = person.income / totalIncome;
            double additionalIncome = ratio * totalAmount;
            person.additionalIncome = additionalIncome;
        }
    }
}

