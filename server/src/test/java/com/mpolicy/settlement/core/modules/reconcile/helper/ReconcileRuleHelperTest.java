package com.mpolicy.settlement.core.modules.reconcile.helper;

import com.mpolicy.settlement.core.SettlementCoreCenterApplicationTests;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ReconcileRuleHelperTest extends SettlementCoreCenterApplicationTests {


    /*@Test
    public void queryReconcileCompanyInfo() {
        int day = 26;
        List<ReconcileCompanyInfo> resultList = ReconcileRuleHelper.queryReconcileCompanyInfoList(day);

        log.info("时间={}生成对账单列表={}", day, JSONUtil.toJsonStr(resultList));

        List<ReconcileCompanySubjectDetail> mergerList = ReconcileRuleHelper.
                queryReconcileCompanySubjectDetailList("SR20230526154202669773",
                        CollUtil.newArrayList("R20230526160902360599", "R20230526161144466297"));
        log.info("合并后的数据:{}", JSONUtil.toJsonStr(mergerList));
    }

    @Test
    public void queryReconcileCompanySubjectDetailList() {
        List<ReconcileCompanySubjectDetail> mergerList = ReconcileRuleHelper.
                queryReconcileCompanySubjectDetailList("SR20230526154202669773",
                        CollUtil.newArrayList("R20230526160902360599", "R20230526161144466297"));
        log.info("合并后的数据:{}", JSONUtil.toJsonStr(mergerList));
    }*/
}
