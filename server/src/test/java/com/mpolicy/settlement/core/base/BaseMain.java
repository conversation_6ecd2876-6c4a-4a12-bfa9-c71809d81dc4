package com.mpolicy.settlement.core.base;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileMonthEnum;
import com.mpolicy.settlement.core.modules.autocost.utils.AutoCostUtils;
import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 测试
 *
 * <AUTHOR>
 * @date 2022-04-28 16:59
 */
@Slf4j
public class BaseMain {

    public static void main(String[] args) throws ParseException {
        log.info("{}",Integer.parseInt(DateUtil.format(new Date(), "yyyyMM")));
        log.info("构建时间信息  = {} ",AutoCostUtils.builderDateTime(ReconcileMonthEnum.matchSearchCode("ONE_MONTH_AGO"),11));

        List<String> lista = Arrays.asList("GDLS","LNJC","LNYX","NMCH","SCSS","NMTT","SDYG","SCST","HNBB","HBLR","SDXJ","SCXS","HBWQ","HBMC","HBZB","HBLT","HBBD","SCGH","NMJY","HNML","JXXG","SDGT","YWTZ","HNDK","ZHGL","HNBS","","HNYL","HNRC","YNMD","YNAN","GSGG","HNXX","TEST","HNZL","HBZL","HBQL","HBCB","HBWD","HBGB","NMLT","NMHG","GSYZ","LNTA","FJXP","YNFN","HBLL","HBWY","LNLZ","HBCW","NMYK","NMHL","NMQS","GSLX","GSJY","HBFN","SXYC","HBYX","SCYC","HBZJ","HB5Q","GSQA","SXCZ","HNSM","HNHJ","HBHS","CQQJ","BDL","HNLG","HBLY","HNSJ","HNYG","HBJX","GSWS","GDLN","JXND","HBNQ","HNCL","YNGN","HNSQ","HNPJ","HNDX","NMWT","HNYS","HNNN","SXWX","SXYS","LNXB","NMSW","NMTP","HZBQ","SXTG","NMSZ","NMET","SXHS","SXLC","SXZQ","GLSD","SCSF","HBQX","LNQY","LNKZ","SCPZ","SCMZ","SXLS","SCLJ","SXJX","LNKP","NMHJ","NMDM","LNDG","LNBP","LNXS","LNLY","LNXC","LNHS","LNKY","LNLH","LNXY","LNDT","NMSD","NMGY","NMCZ","LNSZ","SXXD","SCZJ","NMWQ","SXXY","SXFY","GSYD","HBLH","HBLP","LNFK","HNDZ","HNTH","HBBX","HBDM","SDGX","HBGP","HBCA","HBGZ","HBJL","HBLC","HBNJ","HBWX","HBHW","HBTH","HBXT","GSXX","GSHZ","QHLD","GSTZ","GSKL","GSLZ","HBLN","FJFA","HBST","HBJZ","GSGL","GSMX","GSZX","QHHZ","HNXC","HBPX","HBPQ","XTYPQ","HBWC","HBKC","HBCC","HBFX","HBRX","HBSP","HBCM","HBHX","HBYS","HBQY","HBLS","HBXC","HBRY","SCYJ","HBCL","QHDT","HBDX","SXPY","LNCY","HBGY","HBBY","HBBL","SXYP","HBSL","HBCZ","HNWH","HNLL","HNJS","HBTL","BJF","HNWC","CXCY","SCLS","SXDX","HBQA","LNCT","SXSY","SXZZ","YNLY","HBJC","HBGT","GSAD","SXCL","SCYD","YNNJ","NMNE","NMMZ","HNTC","NMLH","HBNF","SXWT","HNYZ","HZAB","NMXT","NMHE","124","HNLD","YNBC","HNWW","CSSYBFZ","LNFX","CNHN","NMHD","NMXQ","HNWN","SXQX","GDYS","HNDF","SXHZ","GDQX","GDXF","GDFG","HNCY","GDLZ","HBKB","NMHW","HNNY","HNLX","HNHS","HBNH","NMHS","HBBT","SCJY","NMLX","HBDG","HBXH","LNZW","HNZP","HBWA","HBFR","SCYL","SCJG","SCLL","HBFC","SCCS","NMLC","HNZB","HNHR","HBSY","HNLS","HNQH","GDWY","GDLC","GDQJ","LNSQ","LNYL","LNXM","NMLQ","NMBZ","NMDK","JLLJ","YNCN","SXCY","HNCJ","YNCX","GSHN","HNTT","JXRJ","SCSH","HNXL","NMYB","LNFS","GDYD","GDNX","GDRY","GSYJ","HNAY","LNGZ","SDLS","SXGP","HNXY","JXGX","NMWK","NMTY","NMAY","NMAZ","NMTZ","HNAR","SXXX","CNGS","HNYX");
        List<String> listb = Arrays.asList("HNZP","HNSQ","NMHS","HNCJ","BJF","HZBQ","HBZL","HBZB","HBXH","HBYX","HBWC","HBWQ","HBQL","HBPQ","HBLP","HBLL","HBLH","HBKC","HBGY","HBFN","HBCL","HBCC","SXZQ","SXYS","SXYC","HBXT","HBTH","SXWX","HBHW","HBWX","SXTG","SXSY","HBRX","SXQX","HBQX","SXPY","HBPX","HBNQ","HBNH","HBLR","HBSL","SXLS","HBTL","HBLC","HBJL","SXJX","HBJZ","SXHS","HBST","HBGZ","HBGP","HBFX","HBDM","HBCZ","HBCA","HBBX","HBYS","HBWY","HB5Q","HBCW","HBWD","HBSP","HBRY","HBQY","HBMC","HBLY","HBLS","HBZJ","HBHX","HBFC","HBBY","NMLT","NMWT","NMWQ","NMTY","NMTP","NMSW","HBSY","NMSD","NMQS","NMLC","NMHD","NMHJ","NMGY","GLSD","NMDK","NMDM","NMCZ","NMCH","NMHL","LNZW","LNYX","LNXY","LNXC","LNXM","LNXB","LNSZ","LNQY","LNLY","LNKP","LNKY","LNKZ","LNJC","LNHS","LNFX","LNFK","LNDG","LNDT","LNCY","LNCT","LNBP","SDYG","SDGX","SDGT","HNYL","HNYX","HNYZ","HNYS","JXXG","FJXP","HNSM","HNRC","HNPJ","JXND","GDLS","GDLN","HNJS","FJFA","HNDK","HNZL","HNCL","HNAR","GSZX","GSYD","GSWS","GSTZ","GSQA","GSLX","GSKL","QHHZ","GSHZ","GSGL","GSGG","QHDT","GSAD","NMAZ","SCZJ","SCYC","SCYJ","SCSF","SCST","CQQJ","SCMZ","SCLJ","SCJY","SCGH","YNNJ","YNLY","YNGN","YNFN","YNCX","YNCN","YNBC","HNBS","NMWK","HBLT","HNDZ","HNTH","HBNF","HBLN","HNXC","HNXX","HZAB","HBCB","HBDX","JLLJ","HBNJ","NMSZ","SXYP","HBGT","SCSH","YNAN","HNLS","NMNE","SDXJ","SXFY","SXWT","SXCZ","SXHZ","SXDX","SXCY","SXZZ","HBBL","HBJX","HBCM","HNLL","SXLC","SXXX","SXXD","HBJC","LNLZ","LNLH","LNXS","HBGB","QHLD","GSJY","GSYZ","HNQH","SDLS","HNWC","NMLX","HBQA","HBFR","SCLS","LNFS","HBDG","NMHW","NMET","NMXT","YNMD","HNXY","HNHJ","HBXC","NMBZ","HBBD","NMHE","NMMZ","NMJY","NMYK","NMTZ","NMAY","NMXQ","HBHS","HBKB","HBWA","CXCY","HNWH","HNLD","HNTC","HNWW","HNWN","HNDF","GDYS","GDLZ","GDFG","GDYD","GDQX","HNNY","HNYG","HNHS","HNLX","HNBB","HBBT","HNML","SCPZ","NMLH","NMLQ","NMTT","GSMX","GSXX","SCYL","HNNN","SCJG","SCLL","SCCS","HNZB","SCSS","SCXS","GSLZ","LNYL","LNTA","LNSQ","LNGZ","JXRJ","HNTT","HNCY","HNHR","HNSJ","GDQJ","GDXF","GDWY","GDLC","NMHG","HNLG","NMYB","JXGX","GSHN","HNDX","HNXL","GDNX","SCYD","GDRY","GSYJ","SXCL","SXXY","HNAY","SXGP","YWTZ");

        log.info("差额  = {} ",CollUtil.subtractToList(lista,listb));

        String monthSnapshot = "202312";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Date date = null;
        try {
            date = sdf.parse(monthSnapshot);
        } catch (ParseException e) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("格式无法进行转换日期", monthSnapshot)));
        }
        // 将日期对象格式化为输出格式的字符串
        String year = DateUtil.format(DateUtil.offsetMonth(date, -1), "yyyy");
        String month = DateUtil.format(DateUtil.offsetMonth(date, -1), "MM");
        String result = DateUtil.format(DateUtil.offsetDay(new Date(), -1), "yyyyMMdd");
        // 将结果转换为整数类型
        log.info("year  = {} ",year);
        log.info("month  = {} ",month);
        log.info("day  = {} ",result);

        log.info("出生日期 1988-10-18 -> 生效日期 2023-10-16 年龄  = {} ",DateUtil.age(DateUtil.parse("1988-10-18", "yyyy-MM-dd"),DateUtil.parse("2023-10-16", "yyyy-MM-dd")));
        log.info("出生日期 1988-10-18 -> 生效日期 2023-10-17 年龄  = {} ",DateUtil.age(DateUtil.parse("1988-10-18", "yyyy-MM-dd"),DateUtil.parse("2023-10-17", "yyyy-MM-dd")));
        log.info("出生日期 1988-10-18 -> 生效日期 2023-10-18 年龄  = {} ",DateUtil.age(DateUtil.parse("1988-10-18", "yyyy-MM-dd"),DateUtil.parse("2023-10-18", "yyyy-MM-dd")));
        log.info("出生日期 1988-10-18 -> 生效日期 2023-10-19 年龄  = {} ",DateUtil.age(DateUtil.parse("1988-10-18", "yyyy-MM-dd"),DateUtil.parse("2023-10-19", "yyyy-MM-dd")));
    }


    /**
     * 获取快照周期年份
     *
     * @param monthSnapshot 快照周期
     * @return 快照年份
     * <AUTHOR>
     * @since 2023/11/18 11:09
     */
    private static Integer getSnapshotYear(String monthSnapshot) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Date date = null;
        try {
            date = sdf.parse(monthSnapshot);
        } catch (ParseException e) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("格式无法进行转换日期", monthSnapshot)));
        }
        return DateUtil.year(date);
    }

    /**
     * 获取快照周期月份
     *
     * @param monthSnapshot 快照周期
     * @return 快照月份
     * <AUTHOR>
     * @since 2023/11/18 11:09
     */
    private static Integer getSnapshotMonth(String monthSnapshot) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Date date = null;
        try {
            date = sdf.parse(monthSnapshot);
        } catch (ParseException e) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("格式无法进行转换日期", monthSnapshot)));
        }
        return DateUtil.month(date) - 1;
    }

    private static DateTime builderDateTime(Integer timeEndDay, DateTime dateTime) {
        // 获取日期的月最后日
        DateTime dayOfMonth = DateUtil.endOfMonth(dateTime);
        if(timeEndDay > dayOfMonth.dayOfMonth()){
            return dayOfMonth;
        }else{
            return DateUtil.date(dateTime).setField(DateField.DAY_OF_MONTH, timeEndDay);
        }
    }
}
