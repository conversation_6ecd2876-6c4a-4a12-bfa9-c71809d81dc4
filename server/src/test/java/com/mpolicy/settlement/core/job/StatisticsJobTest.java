package com.mpolicy.settlement.core.job;

import com.mpolicy.settlement.core.SettlementCoreCenterApplicationTests;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileDiffTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileConfirmEntity;
import com.mpolicy.settlement.core.modules.reconcile.job.StatisticsJob;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcileConfirmService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class StatisticsJobTest extends SettlementCoreCenterApplicationTests {

    @Autowired
    public StatisticsJob statisticsJob;
    @Autowired
    public SettlementReconcileConfirmService settlementReconcileConfirmService;

    @Test
    public void settlementIncome() {
        statisticsJob.settlementIncome();
    }

    @Test
    public void test() {
        int id = 1000;
        String reconcileCode = "1000";
        List<SettlementReconcileConfirmEntity> reconcileConfirmList = settlementReconcileConfirmService.lambdaQuery()
                .gt(SettlementReconcileConfirmEntity::getId, id)
                .eq(SettlementReconcileConfirmEntity::getReconcileCode, reconcileCode)
                .eq(SettlementReconcileConfirmEntity::getDiffStatus, 0)
                .eq(SettlementReconcileConfirmEntity::getBillEnable, 1)
                .eq(SettlementReconcileConfirmEntity::getDiffType, ReconcileDiffTypeEnum.AMOUNT_ACCURACY.getCode())
                .orderByAsc(SettlementReconcileConfirmEntity::getId)
                .last("LIMIT 1000")
                .list();
    }

}
