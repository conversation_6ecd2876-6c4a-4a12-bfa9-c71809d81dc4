package com.mpolicy.settlement.core.modules.reconcile.helper;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileMonthEnum;
import lombok.extern.slf4j.Slf4j;

/**
 * test
 *
 * <AUTHOR>
 * @since 2023-05-30 21:37
 */
@Slf4j
public class XiaomaTest {
    public static void main(String[] args) {

        Integer underwriteTimeEndDay = 31;
        DateTime underwriteTime = DateUtil.date()
                    .setField(DateField.DAY_OF_MONTH, underwriteTimeEndDay)
                    .setField(DateField.HOUR_OF_DAY, 23)
                    .setField(DateField.MINUTE, 59)
                    .setField(DateField.SECOND, 59)
                    .offset(DateField.MONTH, -ReconcileMonthEnum.matchSearchCode("THIS_MONTH").getMonth());


        log.info("underwriteTime={}", underwriteTime);
    }
}