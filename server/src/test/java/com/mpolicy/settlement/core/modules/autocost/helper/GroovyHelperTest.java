package com.mpolicy.settlement.core.modules.autocost.helper;

import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.common.redis.key.KeyPrefix;
import com.mpolicy.settlement.core.SettlementCoreCenterApplicationTests;
import groovy.lang.Binding;
import groovy.lang.Script;
import org.junit.Before;
import org.junit.Test;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

public class GroovyHelperTest  extends SettlementCoreCenterApplicationTests {

    private GroovyHelper groovyHelperUnderTest;


}
