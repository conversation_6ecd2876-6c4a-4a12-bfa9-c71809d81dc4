package com.mpolicy.settlement.core.modules.autocost.service;

import com.alibaba.fastjson.JSON;
import com.mpolicy.open.common.cfpamf.hr.EmployeeByBranchAndPostingCodeRequest;
import com.mpolicy.open.common.cfpamf.hr.EmployeeResp;
import com.mpolicy.open.common.hr.vo.EmployeeLdomVO;
import com.mpolicy.settlement.core.SettlementCoreCenterApplicationTests;
import com.mpolicy.settlement.core.modules.autocost.dto.employee.EmployeeInfo;
import com.mpolicy.settlement.core.modules.autocost.enums.EmployeeRoleEnum;
import com.mpolicy.settlement.core.modules.autocost.utils.AutoCostUtils;
import com.mpolicy.settlement.core.service.common.OpenApiBaseService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class EmployeeServiceTest extends SettlementCoreCenterApplicationTests {

    @Autowired
    private EmployeeService employeeService;

    String monthSnapshot = AutoCostUtils.costSettlementMonthSnapshot();
    String employeeNo = "ZHNX13643";
    List<String> employeeNos = Arrays.asList("ZHNX39522", "ZHNX39488","ZHNX39498","ZHNX39517","ZHNX39550");
    EmployeeRoleEnum employeeRole = EmployeeRoleEnum.PCO;
    EmployeeRoleEnum employeeRoleSupervisor = EmployeeRoleEnum.SUPERVISOR;
    List<EmployeeRoleEnum> employeeRoleList = Arrays.asList(EmployeeRoleEnum.DIRECTOR, EmployeeRoleEnum.DEPUTY_DIRECTOR);

    String regionCode = "CNDB";

    String orgCode = "LNSQ";

    @Autowired
    private OpenApiBaseService openApiBaseService;


    @Test
    public void queryEmployeeInfo() {
        EmployeeInfo employeeInfo = employeeService.queryEmployeeInfo(employeeNo);
        log.info("员工信息={}", JSON.toJSONString(employeeInfo));
    }

    @Test
    public void queryEmployeeInfoSnapshot() {
        EmployeeInfo employeeInfo = employeeService.queryEmployeeInfoSnapshot(employeeNo,monthSnapshot);
        log.info("员工信息={}", JSON.toJSONString(employeeInfo));
    }

    @Test
    public void queryEmployeeAllInfo() {
        List<EmployeeInfo> employeeInfos = employeeService.queryEmployeeAllInfo();
        log.info("返回员工数量={}", employeeInfos.size());
    }

    @Test
    public void queryEmployeeAllSnapshot() {
        List<EmployeeInfo> employeeInfos = employeeService.queryEmployeeAllSnapshot(monthSnapshot);
        log.info("返回员工数量={}", employeeInfos.size());
    }

    @Test
    public void listEmployeeByEmployeeCodes() {
        List<EmployeeInfo> employeeInfos = employeeService.listEmployeeByEmployeeCodes(employeeNos);
        log.info("返回员工数量={}", employeeInfos.size());
    }

    @Test
    public void mapEmployeeByEmployeeCodes() {
        Map<String, EmployeeInfo> employeeInfoMap = employeeService.mapEmployeeByEmployeeCodes(employeeNos);
        log.info("员工信息={}", JSON.toJSONString(employeeInfoMap));
    }

    @Test
    public void listEmployeeByEmployeeCodesSnapshot() {
        List<EmployeeInfo> employeeInfos = employeeService.listEmployeeByEmployeeCodesSnapshot(employeeNos,monthSnapshot);
        log.info("返回员工数量={}", employeeInfos.size());
    }

    @Test
    public void mapEmployeeByEmployeeCodesSnapshot() {
        Map<String, EmployeeInfo> employeeInfoMap = employeeService.mapEmployeeByEmployeeCodesSnapshot(employeeNos,monthSnapshot);
        log.info("员工信息={}", JSON.toJSONString(employeeInfoMap));
    }

    @Test
    public void listEmployeeByEmployeeRoleSupervisor() {
        List<EmployeeInfo> result = employeeService.listEmployeeByEmployeeRole(employeeRoleSupervisor,true);
        log.info("返回督导员工数量={}", result.size());
        log.info("员工信息={}", JSON.toJSONString(result));
    }

    @Test
    public void mapEmployeeByEmployeeRole() {
        Map<String, EmployeeInfo> employeeInfoMap = employeeService.mapEmployeeByEmployeeRole(employeeRole,true);
        log.info("接口2 员工信息={}", JSON.toJSONString(employeeInfoMap));
    }

    @Test
    public void listEmployeeByEmployeeRolePco() {
        List<EmployeeInfo> result = employeeService.listEmployeeByEmployeeRole(employeeRole,true);
        log.info("返回PCO员工数量={}", result.size());
        log.info("员工信息={}", JSON.toJSONString(result));
    }

    @Test
    public void listEmployeeByEmployeeRoleSnapshot() {
        List<EmployeeInfo> employeeInfos = employeeService.listEmployeeByEmployeeRoleSnapshot(employeeRoleSupervisor,true,monthSnapshot);
        log.info("返回员工数量={}", employeeInfos.size());
    }

    @Test
    public void mapEmployeeByEmployeeRoleSnapshot() {
        Map<String, EmployeeInfo> employeeInfoMap = employeeService.mapEmployeeByEmployeeRoleSnapshot(employeeRole,true,monthSnapshot);
        log.info("接口2 员工信息={}", JSON.toJSONString(employeeInfoMap));
    }

    @Test
    public void testListEmployeeByEmployeeRole() {
        List<EmployeeInfo> employeeInfos = employeeService.listEmployeeByEmployeeRole(employeeRoleList,true);
        log.info("返回员工机构编码集合={}", employeeInfos.stream().map(EmployeeInfo::getOrgCode).distinct().collect(Collectors.toList()));
        log.info("返回员工数量={}", employeeInfos.size());
    }

    @Test
    public void testMapEmployeeByEmployeeRole() {
        Map<String, EmployeeInfo> employeeInfoMap = employeeService.mapEmployeeByEmployeeRole(employeeRoleList,true);
        log.info("map 信息={}", JSON.toJSONString(employeeInfoMap));
    }

    @Test
    public void testListEmployeeByEmployeeRoleSnapshot() {
        List<EmployeeInfo> employeeInfos = employeeService.listEmployeeByEmployeeRoleSnapshot(employeeRoleList,true,monthSnapshot);
        log.info("返回员工数量={}", employeeInfos.size());
    }

    @Test
    public void testMapEmployeeByEmployeeRoleSnapshot() {
        Map<String, EmployeeInfo> employeeInfoMap = employeeService.mapEmployeeByEmployeeRoleSnapshot(employeeRoleList,true,monthSnapshot);
        log.info("map 信息={}", JSON.toJSONString(employeeInfoMap));
    }


    @Test
    public void listEmployeeByRegion() {
        List<EmployeeInfo> employeeInfos = employeeService.listEmployeeByRegion(regionCode);
        log.info("返回员工数量={}", employeeInfos.size());
    }

    @Test
    public void listEmployeeByRegionSnapshot() {
        List<EmployeeInfo> employeeInfos = employeeService.listEmployeeByRegionSnapshot(regionCode,monthSnapshot);
        log.info("返回员工数量={}", employeeInfos.size());
    }

    @Test
    public void mapEmployeeByRegion() {
        Map<String, EmployeeInfo> employeeInfoMap = employeeService.mapEmployeeByRegion(regionCode);
        log.info("map 信息={}", JSON.toJSONString(employeeInfoMap));
    }

    @Test
    public void mapEmployeeByRegionSnapshot() {
        Map<String, EmployeeInfo> employeeInfoMap = employeeService.mapEmployeeByRegionSnapshot(regionCode,monthSnapshot);
        log.info("map 信息={}", JSON.toJSONString(employeeInfoMap));
    }

    @Test
    public void listEmployeeByOrg() {
        List<EmployeeInfo> employeeInfos = employeeService.listEmployeeByOrg(orgCode);
        log.info("返回员工数量={}", employeeInfos.size());
    }

    @Test
    public void listEmployeeByOrgSnapshot() {
        List<EmployeeInfo> employeeInfos = employeeService.listEmployeeByOrgSnapshot(orgCode,monthSnapshot);
        log.info("返回员工数量={}", employeeInfos.size());
    }

    @Test
    public void mapEmployeeByOrg() {
        Map<String, EmployeeInfo> employeeInfoMap = employeeService.mapEmployeeByOrg("HNZP");
        log.info("map 信息={}", JSON.toJSONString(employeeInfoMap));
    }

    @Test
    public void mapEmployeeByOrgSnapshot() {
        Map<String, EmployeeInfo> employeeInfoMap = employeeService.mapEmployeeByOrgSnapshot(orgCode,monthSnapshot);
        log.info("map 信息={}", JSON.toJSONString(employeeInfoMap));
    }


    @Test
    public void listEmployeeBySupervisor() {
        List<EmployeeInfo> employeeInfoMap = employeeService.listEmployeeBySupervisor(orgCode);
        log.info("map 信息={}", JSON.toJSONString(employeeInfoMap));
    }

    @Test
    public void listEmployeeBySupervisorSnapshot() {
        List<EmployeeInfo> employeeInfoMap = employeeService.listEmployeeBySupervisorSnapshot("ZHNX11486",monthSnapshot);
        log.info("map 信息={}", JSON.toJSONString(employeeInfoMap));
    }

    @Test
    public void queryByPostingAndBranchCode() {
        EmployeeByBranchAndPostingCodeRequest request = new EmployeeByBranchAndPostingCodeRequest();
        request.setBranchCode("HNZP");
        request.setPostingCode(1083);
        List<EmployeeLdomVO> employeeRes = openApiBaseService.queryByPostingAndBranchCode(request, true);
        log.info("queryByPostingAndBranchCode 响应信息={}", JSON.toJSONString(employeeRes));
    }

    @Test
    public void queryByPostingAndBranchCodePCO() {
        EmployeeByBranchAndPostingCodeRequest request = new EmployeeByBranchAndPostingCodeRequest();
        request.setBranchCode("NMHS");
        request.setPostingCode(2357);
        request.setYear(2023);
        request.setMonth(10);
        List< EmployeeLdomVO > employeeRes = openApiBaseService.queryByPostingAndBranchCode(request, true);
        log.info("queryByPostingAndBranchCode PCO响应信息={}", JSON.toJSONString(employeeRes));
    }

}