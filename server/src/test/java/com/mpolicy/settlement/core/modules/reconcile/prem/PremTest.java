package com.mpolicy.settlement.core.modules.reconcile.prem;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.mpolicy.settlement.core.SettlementCoreCenterApplicationTests;
import com.mpolicy.settlement.core.common.reconcile.enums.PremEventEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.PolicyInsuredPeriodTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.factory.PremEventFactory;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.PremEventHandler;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyProductPremInput;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyProductPremResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.List;

@Slf4j
public class PremTest extends SettlementCoreCenterApplicationTests {

    @Test
    public void queryPolicyProductPrem() {
        //获取费率处理器,
        PolicyProductPremInput input = new PolicyProductPremInput();
        input.setPolicyNo("12312312312");
        input.setInsuranceType(0);//0:新单 1:续投
        input.setOrgCode("OR20211214182320KHkeI9");
        input.setApprovedTime(DateUtil.parseDateTime("2023-04-29 00:00:00"));
        input.setInsuredPeriodType(PolicyInsuredPeriodTypeEnum.YEAR.getCode());
        input.setInsuredPeriod(1);
       // input.setPaymentPeriodType(PolicyPaymentPeriodTypeEnum.YEAR.getCode());
        input.setPaymentPeriod(2);
       // input.setPeriodType(PolicyPaymentTypeEnum.NJ.getCode());
        input.setRenewalYear(1);
        input.setRenewalPeriod(1);
        input.setProductCode("ZLRSTBHM21102801");
        input.setSalesType(0);
        PremEventHandler premEventHandler = PremEventFactory.getInvoke(PremEventEnum.PROTOCOL_PREM.getCode());
        List<PolicyProductPremResult> policyProductPremResults = premEventHandler.queryPolicyProductPrem(input);
        log.info("费率信息:{}", JSONUtil.toJsonStr(policyProductPremResults));


    }

}
