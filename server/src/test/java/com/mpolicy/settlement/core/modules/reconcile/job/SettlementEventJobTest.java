package com.mpolicy.settlement.core.modules.reconcile.job;

import cn.hutool.core.util.StrUtil;
import com.mpolicy.settlement.core.SettlementCoreCenterApplicationTests;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.event.factory.SettlementEventFactory;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.SettlementEventHandler;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementEventJobService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 保险公司对账服务相关定时任务
 *
 * <AUTHOR>
 * @since 2023/05/20
 */
@Component
@Slf4j
public class SettlementEventJobTest extends SettlementCoreCenterApplicationTests {

    @Autowired
    private SettlementEventJobService settlementEventJobService;

    /**
     * 结算事件定时任务处理
     *
     * <AUTHOR>
     * @since 2023/05/21
     */
    @Test
    public void settlementEventJob() {
        SettlementEventJobEntity eventJob = settlementEventJobService
                .lambdaQuery().eq(SettlementEventJobEntity::getPushEventCode, "PEC20241021173534dPufv3")
                .one();
        SettlementEventHandler settlementEventHandler =
                SettlementEventFactory.getSettlementEventHandler(eventJob.getEventType());
        if (settlementEventHandler != null) {
            log.info("执行获取需要处理的事件任务,事件类型={}", eventJob.getEventType());
            // 事件执行
            //settlementEventHandler.handle(eventJob);
        } else {
            log.warn(StrUtil.format("事件类型类型不支持，事件类型={}", eventJob.getEventType()));
        }
    }
}