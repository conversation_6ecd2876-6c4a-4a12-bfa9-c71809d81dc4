package com.mpolicy.settlement.core.modules.reconcile.service;

import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementInnerCompanyLicenseInfoDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementInnerCompanyLicenseInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.impl.SettlementInnerCompanyLicenseInfoServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

/**
 * 工商信息服务测试类
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@ExtendWith(MockitoExtension.class)
public class SettlementInnerCompanyLicenseInfoServiceTest {

    @Mock
    private SettlementInnerCompanyLicenseInfoDao settlementInnerCompanyLicenseInfoDao;

    @InjectMocks
    private SettlementInnerCompanyLicenseInfoServiceImpl settlementInnerCompanyLicenseInfoService;

    private List<SettlementInnerCompanyLicenseInfoEntity> testData;

    @BeforeEach
    public void setUp() {
        SettlementInnerCompanyLicenseInfoEntity entity1 = SettlementInnerCompanyLicenseInfoEntity.builder()
                .businessLicenseStatus(1)
                .businessNature(1)
                .licenseName("湖南莺歌资产经营有限公司")
                .licenseType(1)
                .managementType(1)
                .modifyDate(new Date())
                .socialCreditCode("4444444")
                .subjectType(2)
                .deleted(0)
                .createUser("system")
                .updateUser("system")
                .revision(1)
                .build();

        SettlementInnerCompanyLicenseInfoEntity entity2 = SettlementInnerCompanyLicenseInfoEntity.builder()
                .businessLicenseStatus(1)
                .businessNature(1)
                .licenseName("测试0813-5月29号测试改名")
                .licenseType(1)
                .managementType(1)
                .modifyDate(new Date())
                .socialCreditCode("1000")
                .subjectType(2)
                .deleted(0)
                .createUser("system")
                .updateUser("system")
                .revision(1)
                .build();

        testData = Arrays.asList(entity1, entity2);
    }

    @Test
    public void testBatchInsertOrUpdate() {
        // Mock返回值
        when(settlementInnerCompanyLicenseInfoDao.batchInsertOrUpdate(anyList())).thenReturn(2);

        // 执行测试
        settlementInnerCompanyLicenseInfoService.batchInsertOrUpdate(testData);

        // 验证调用
        verify(settlementInnerCompanyLicenseInfoDao, times(1)).batchInsertOrUpdate(testData);
    }

    @Test
    public void testBatchInsertOrUpdateWithEmptyList() {
        // 执行测试
        settlementInnerCompanyLicenseInfoService.batchInsertOrUpdate(null);
        settlementInnerCompanyLicenseInfoService.batchInsertOrUpdate(Arrays.asList());

        // 验证没有调用数据库操作
        verify(settlementInnerCompanyLicenseInfoDao, never()).batchInsertOrUpdate(anyList());
    }

    @Test
    public void testGetBySocialCreditCodeFromCache() {
        // 由于缓存是在实际运行时初始化的，这里主要测试方法不会抛异常
        String socialCreditCode = "4444444";

        // 执行测试（缓存为空时会调用数据库查询）
        SettlementInnerCompanyLicenseInfoEntity result =
                settlementInnerCompanyLicenseInfoService.getBySocialCreditCodeFromCache(socialCreditCode);

        // 验证方法执行完成
        assertNull(result); // 由于是Mock环境，返回null是正常的
    }

    @Test
    public void testGetBySocialCreditCodeFromCacheWithNotExist() {
        String socialCreditCode = "NOT_EXIST_CODE";

        // 调用不存在的编码，应该直接从全量缓存返回null
        SettlementInnerCompanyLicenseInfoEntity result1 =
                settlementInnerCompanyLicenseInfoService.getBySocialCreditCodeFromCache(socialCreditCode);
        assertNull(result1);

        // 第二次调用，同样直接从缓存返回null
        SettlementInnerCompanyLicenseInfoEntity result2 =
                settlementInnerCompanyLicenseInfoService.getBySocialCreditCodeFromCache(socialCreditCode);
        assertNull(result2);
    }

    @Test
    public void testGetCacheStatistics() {
        String statistics = settlementInnerCompanyLicenseInfoService.getCacheStatistics();
        assertNotNull(statistics);
        assertTrue(statistics.contains("工商信息全量缓存统计"));
    }

    @Test
    public void testGetAllFromCache() {
        List<SettlementInnerCompanyLicenseInfoEntity> allFromCache =
                settlementInnerCompanyLicenseInfoService.getAllFromCache();
        assertNotNull(allFromCache);
        // 由于是Mock环境，返回空列表是正常的
        assertTrue(allFromCache.isEmpty());
    }
}
