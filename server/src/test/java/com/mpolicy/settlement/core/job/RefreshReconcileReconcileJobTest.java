package com.mpolicy.settlement.core.job;

import com.mpolicy.settlement.core.SettlementCoreCenterApplicationTests;
import com.mpolicy.settlement.core.modules.reconcile.job.RefreshReconcileReconcileJob;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class RefreshReconcileReconcileJobTest  extends SettlementCoreCenterApplicationTests {


    @Autowired
    private RefreshReconcileReconcileJob refreshReconcileReconcileJob;

    @Test
    public void handleSettlementInfo() {
        refreshReconcileReconcileJob.handleSettlementInfo();
    }
}
