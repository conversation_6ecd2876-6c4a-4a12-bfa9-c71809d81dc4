package com.mpolicy.settlement.core.job;

import com.mpolicy.settlement.core.SettlementCoreCenterApplicationTests;
import com.mpolicy.settlement.core.modules.reconcile.job.SettlementEventJob;
import com.mpolicy.settlement.core.modules.reconcile.job.SettlementReconcilePolicyFileJob;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class SettlementEventJobTest extends SettlementCoreCenterApplicationTests {

    @Autowired
    private SettlementEventJob settlementEventJob;
    @Autowired
    private SettlementReconcilePolicyFileJob settlementReconcilePolicyFileJob;

    @Test
    public void correctSettlementEventJob() {
        settlementEventJob.correctSettlementEventJob();
    }

    @Test
    public void updateReconcilePolicyFileJob() {
        settlementReconcilePolicyFileJob.updateReconcilePolicyFileJob();
    }

    @Test
    public void updateSettlementEventJob() {
        settlementEventJob.updateSettlementEventJob();
    }

}
