package com.mpolicy.settlement.core.modules.protocol.helper;

import com.mpolicy.settlement.core.SettlementCoreCenterApplicationTests;
import com.mpolicy.settlement.core.common.protocol.ProductOrgListOut;
import com.mpolicy.settlement.core.common.protocol.ProductOrgListVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.List;

@Slf4j
public class ProtocolBaseHelperTest extends SettlementCoreCenterApplicationTests {


    @Test
    public void findProductOrgList() {
        ProductOrgListVo product = new ProductOrgListVo();
        product.setProductCode("XTRYYHWY22030801");
        product.setEffectiveDate("2023-03-01 00:00:00");
        product.setReconcileType(0);
        List<ProductOrgListOut> productOrgList = ProtocolBaseHelper.findProductOrgList(product);
        log.info("productOrgList:{}", productOrgList);
    }

    @Test
    public void findMainProductInfoListByPolicyNos() {
//        List<String> policyNos = CollUtil.newArrayList("10199006600592101880");
//        Map<String, MainProductInfoVo> nos = ReconcileBaseHelper.findMainProductInfoListByPolicyNos(policyNos);
//        System.out.println(nos);
    }
}
