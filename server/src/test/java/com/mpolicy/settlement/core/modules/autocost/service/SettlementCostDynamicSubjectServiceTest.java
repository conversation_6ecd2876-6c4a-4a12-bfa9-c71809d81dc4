package com.mpolicy.settlement.core.modules.autocost.service;

import com.mpolicy.settlement.core.SettlementCoreCenterApplicationTests;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostSubjectDataDynamicApplyEntity;
import com.mpolicy.settlement.core.modules.autocost.utils.AutoCostUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class SettlementCostDynamicSubjectServiceTest extends SettlementCoreCenterApplicationTests {

    @Autowired
    private SettlementCostDynamicSubjectService settlementCostDynamicSubjectService;

    @Autowired
    private SettlementCostSubjectDataDynamicApplyService settlementCostSubjectDataDynamicApplyService;

    @Test
    public void saveDynamicSubject() {
        SettlementCostSubjectDataDynamicApplyEntity bean = new SettlementCostSubjectDataDynamicApplyEntity();
        bean.setApplyCode("AP202301010");
        bean.setCostSettlementCycle(AutoCostUtils.costSettlementMonthSnapshot());
        bean.setFileCode("oss20231127215024t2rj1m");
        bean.setFileName("结佣项导入浮动科目模板-v1.0.xlsx");
        bean.setFilePath("other/settlement/20231127/e6472d300ab5404386985b4d36a124fa/结佣项导入浮动科目模板-v1.0.xlsx");
        bean.setDomainPath("https://oss-xjxhserver.xiaowhale.com/other/settlement/20231127/e6472d300ab5404386985b4d36a124fa/结佣项导入浮动科目模板-v1.0.xlsx");
        bean.setApplyStatus(0);
        bean.setCreateUser("小马哥");
        settlementCostSubjectDataDynamicApplyService.save(bean);
    }

    @Test
    public void saveDynamicSubject2() {
        SettlementCostSubjectDataDynamicApplyEntity bean = new SettlementCostSubjectDataDynamicApplyEntity();
        bean.setApplyCode("AP202301011");
        bean.setCostSettlementCycle(AutoCostUtils.costSettlementMonthSnapshot());
        bean.setFileCode("oss202311281908381PIFoz");
        bean.setFileName("结佣项导入浮动科目模板-v1.1.xlsx");
        bean.setFilePath("other/portrait/20231128/83424a96b73641069efb101c7fcff0a2/结佣项导入浮动科目模板-v1.1.xlsx");
        bean.setDomainPath("https://oss-xjxhserver.xiaowhale.com/other/portrait/20231128/83424a96b73641069efb101c7fcff0a2/结佣项导入浮动科目模板-v1.1.xlsx");
        bean.setApplyStatus(0);
        bean.setCreateUser("小马哥");
        settlementCostSubjectDataDynamicApplyService.save(bean);
    }

    @Test
    public void saveDynamicSubject3() {
        SettlementCostSubjectDataDynamicApplyEntity bean = new SettlementCostSubjectDataDynamicApplyEntity();
        bean.setApplyCode("AP202301013");
        bean.setCostSettlementCycle(AutoCostUtils.costSettlementMonthSnapshot());
        bean.setFileCode("oss20231128193723FI210t");
        bean.setFileName("结佣项导入浮动科目模板-v1.1.xlsx");
        bean.setFilePath("other/portrait/20231128/73414198e6464854b5cacdc31e942fc0/结佣项导入浮动科目模板-v1.1.xlsx");
        bean.setDomainPath("https://oss-xjxhserver.xiaowhale.com/other/portrait/20231128/73414198e6464854b5cacdc31e942fc0/结佣项导入浮动科目模板-v1.1.xlsx");
        bean.setApplyStatus(0);
        bean.setCreateUser("小马哥");
        settlementCostSubjectDataDynamicApplyService.save(bean);
    }

    @Test
    public void saveDynamicSubject4() {
        SettlementCostSubjectDataDynamicApplyEntity bean = new SettlementCostSubjectDataDynamicApplyEntity();
        bean.setApplyCode("AP202301014");
        bean.setCostSettlementCycle(AutoCostUtils.costSettlementMonthSnapshot());
        bean.setFileCode("oss202312012217290HH9dN");
        bean.setFileName("结佣项导入浮动科目模板-v1.1.xlsx");
        bean.setFilePath("other/portrait/20231201/63c5fd20bdb64723987b5fe37b951e7e/结佣项导入浮动科目模板-v1.1.xlsx");
        bean.setDomainPath("https://oss-xjxhserver.xiaowhale.com/other/portrait/20231201/63c5fd20bdb64723987b5fe37b951e7e/结佣项导入浮动科目模板-v1.1.xlsx");
        bean.setApplyStatus(0);
        bean.setCreateUser("小马哥");
        settlementCostSubjectDataDynamicApplyService.save(bean);
    }

    @Test
    public void loadDynamicSubject() {
        //settlementCostDynamicSubjectService.loadDynamicSubject("AP202301014", "小马哥");
    }
}