package com.mpolicy.settlement.core.cost.data;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.mpolicy.common.utils.thread.ThreadUtils;
import com.mpolicy.settlement.core.SettlementCoreCenterApplicationTests;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.autocost.dto.CostProgrammeRecordInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.base.SettlementInstitutionInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.data.*;
import com.mpolicy.settlement.core.modules.autocost.entity.*;
import com.mpolicy.settlement.core.modules.autocost.enums.SettlementPeriodEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.SubjectCycleEnum;
import com.mpolicy.settlement.core.modules.autocost.factory.SubjectDataFactory;
import com.mpolicy.settlement.core.modules.autocost.handler.SubjectDataBuilderHandler;
import com.mpolicy.settlement.core.modules.autocost.handler.SubjectDataQueryHandler;
import com.mpolicy.settlement.core.modules.autocost.helper.SettlementInstitutionHelper;
import com.mpolicy.settlement.core.modules.autocost.project.data.rule.PolicyCommDataRuleEnum;
import com.mpolicy.settlement.core.modules.autocost.service.*;
import com.mpolicy.settlement.core.modules.autocost.utils.AutoCostUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 科目数据测试
 *
 * <AUTHOR>
 * @since 2023-11-01 16:57
 */
@Slf4j
public class SubjectDataTest extends SettlementCoreCenterApplicationTests {

    @Autowired
    private SettlementCostProgrammeInfoService settlementCostProgrammeInfoService;

    @Autowired
    private SettlementCostProgrammeTaskService settlementCostProgrammeTaskService;

    @Autowired
    private SettlementCostProgrammeSubjectService settlementCostProgrammeSubjectService;

    @Autowired
    private SettlementCostSubjectInfoService settlementCostSubjectInfoService;

    @Autowired
    private SettlementCostSubjectDataRuleConfigService settlementCostSubjectDataRuleConfigService;


    @Autowired
    private SubjectDataService subjectDataService;

    @Autowired
    private CostProgrammeService costProgrammeService;

    @Test
    public void costProgrammeInfoInit() {
        // 1 初始化方案
        SettlementCostProgrammeInfoEntity programmeInfo = new SettlementCostProgrammeInfoEntity();
        String programmeCode = AutoCostUtils.costSettlementMonthSnapshot();
        programmeInfo.setProgrammeCode(programmeCode);
        programmeInfo.setProgrammeName("农保基本管理方案1.0");
        programmeInfo.setProgrammeType("ZHNX");
        programmeInfo.setProgrammeStatus(1);
        programmeInfo.setProgrammeDesc("农保基本管理方案1.0");
        programmeInfo.setCreateUser("TEST");
        programmeInfo.setCreateTime(new Date());
        settlementCostProgrammeInfoService.save(programmeInfo);
        // 2 设置方案的执行
        SettlementCostProgrammeTaskEntity task = new SettlementCostProgrammeTaskEntity();
        task.setProgrammeCode(programmeCode);
        task.setProgrammeTaskDesc("农保基本管理方案1.0每天执行一次");
        task.setCreateUser("TEST");
        task.setSettlementPeriod(SettlementPeriodEnum.AUTO.getCode());
        task.setCronExpression("0 30,0 0/1 * * ?");
        task.setCreateTime(new Date());
        settlementCostProgrammeTaskService.save(task);
        // 3 设置方案与科目关系表
        SettlementCostProgrammeSubjectEntity programmeSubject = new SettlementCostProgrammeSubjectEntity();
        programmeSubject.setProgrammeCode(programmeCode);
        programmeSubject.setSubjectCode(CostSubjectEnum.FIRST_BASIC_COMM.getCode());
        programmeSubject.setBindDesc("单元测试绑定");
        programmeSubject.setCreateUser("TEST");
        programmeSubject.setCreateTime(new Date());
        settlementCostProgrammeSubjectService.save(programmeSubject);

        programmeSubject = new SettlementCostProgrammeSubjectEntity();
        programmeSubject.setProgrammeCode(programmeCode);
        programmeSubject.setSubjectCode(CostSubjectEnum.PCO_ALLOWANCE.getCode());
        programmeSubject.setBindDesc("单元测试绑定");
        programmeSubject.setCreateUser("TEST");
        programmeSubject.setCreateTime(new Date());
        settlementCostProgrammeSubjectService.save(programmeSubject);
    }

    @Test
    public void costSubjectInfoInit() {
        // 1 初始化科目1 的规则文件
        SettlementCostSubjectInfoEntity bean = new SettlementCostSubjectInfoEntity();
        bean.setSubjectCode(CostSubjectEnum.PCO_ALLOWANCE.getCode());
        bean.setSubjectName(CostSubjectEnum.PCO_ALLOWANCE.getName());
        bean.setSubjectType("subjectType");
        bean.setDependentSubjectCodeList(null);
        bean.setSubjectObject("PCO");
        bean.setSubjectStatus(1);
        // 设置前置任务
        bean.setDependentSubjectCodeList(CostSubjectEnum.FIRST_BASIC_COMM.getCode());
        bean.setSubjectCycle(SubjectCycleEnum.MONTH.getCode());
        bean.setSubjectCalcWay(SettlementPeriodEnum.AUTO.getCode());
        bean.setSubjectDesc("单元测试科目2信息");
        settlementCostSubjectInfoService.save(bean);
    }

    @Test
    public void costSubjectRuleInit() {
        // 1 初始化科目1 的规则文件
        SettlementCostSubjectDataRuleConfigEntity bean = new SettlementCostSubjectDataRuleConfigEntity();
        bean.setSubjectCode(CostSubjectEnum.SHORT_PROMOTION.getCode());

        bean.setCreateTime(new Date());
        bean.setCreateUser("TEST");

        bean.setRuleKey(PolicyCommDataRuleEnum.LONG_SHORT_FLAG.getCode());
        bean.setRuleType("int");
        bean.setRuleValue("1");
        bean.setRuleDesc("长短线标识 0否 1是");
        settlementCostSubjectDataRuleConfigService.save(bean);

        bean.setRuleKey(PolicyCommDataRuleEnum.DISTRIBUTION.getCode());
        bean.setRuleType("int");
        bean.setRuleValue("1");
        bean.setRuleDesc("分销标识 0否 1是");
        settlementCostSubjectDataRuleConfigService.save(bean);

        bean.setRuleKey(PolicyCommDataRuleEnum.RURAL_PROXY_FLAG.getCode());
        bean.setRuleType("int");
        bean.setRuleValue("1");
        bean.setRuleDesc("整村推进标识 0否 1是");

        bean.setRuleKey(PolicyCommDataRuleEnum.NEW_UNDERWRITE_END_TIME.getCode());
        bean.setRuleType("json");
        bean.setRuleValue("{\"type\":\"ONE_MONTH_AGO\",\"day\":10}");
        bean.setRuleDesc("新契约-承保截止时间 type为类型(上个月、上上个月等)，day为天数");
        settlementCostSubjectDataRuleConfigService.save(bean);

        bean.setRuleKey(PolicyCommDataRuleEnum.PRESERVATION_EFFECTIVE_END_TIME.getCode());
        bean.setRuleType("json");
        bean.setRuleValue("{\"type\":\"ONE_MONTH_AGO\",\"day\":10}");
        bean.setRuleDesc("保全生效时间 type为类型(上个月、上上个月等)，day为天数");
        settlementCostSubjectDataRuleConfigService.save(bean);
    }

    @Test
    public void factoryTest() {
        // 1 获取科目数据【构建器】
        SubjectDataBuilderHandler builderDataHandler = SubjectDataFactory.getSubjectBuilderDataHandler(CostSubjectEnum.FIRST_BASIC_COMM);
        CostSubjectDataRecord builderResult = builderDataHandler.handle("123", true);
        log.info("科目数据构建完成: {}", builderResult);

        // 2 获取科目数据【查询器】
        SubjectDataQueryHandler<SubjectDataRequest<String>, List<SubjectDataPolicyComm>> queryHandler = SubjectDataFactory.getSubjectDataQueryHandler(CostSubjectEnum.FIRST_BASIC_COMM);
        SubjectDataRequest<String> request = new SubjectDataRequest<>();
        SubjectDataResponse<List<SubjectDataPolicyComm>> result = queryHandler.getSubjectData(request);
        log.info("科目数据：{}", result);
    }

    @Test
    public void subjectBuilderData() {
        // 获取科目数据【构建器】
        SubjectDataBuilderHandler builderDataHandler = SubjectDataFactory.getSubjectBuilderDataHandler(CostSubjectEnum.FIRST_BASIC_COMM);
        CostSubjectDataRecord builderResult = builderDataHandler.handle(AutoCostUtils.costSettlementMonthSnapshot(), true);
        ThreadUtils.sleep(2000);
        log.info("科目数据构建完成: {}", JSON.toJSONString(builderResult));
    }

    @Test
    public void subjectQueryData() {
        // 获取科目数据【A】- 基础佣金 + 员工 + 指标
        SubjectDataQueryHandler<SubjectDataRequest<String>, List<SubjectDataPolicyComm>> queryHandlerA = SubjectDataFactory.getSubjectDataQueryHandler(CostSubjectEnum.SHORT_PROMOTION);

        // 获取科目数据【B】- 员工 + 指标
        SubjectDataQueryHandler<SubjectDataRequest<String>, List<SubjectDataEmployee>> queryHandlerB = SubjectDataFactory.getSubjectDataQueryHandler(CostSubjectEnum.PCO_ALLOWANCE);

        // 获取科目数据【C】- 补发数据
        SubjectDataQueryHandler<SubjectDataRequest<String>, List<SubjectDataReissue>> queryHandlerC = SubjectDataFactory.getSubjectDataQueryHandler(CostSubjectEnum.LONG_REISSUE_PROMOTION);

        SubjectDataRequest<String> request = new SubjectDataRequest<>();
        // 设置查询周期
        request.setCostSettlementCycle("ZHNX20231106");
        SubjectDataResponse<List<SubjectDataPolicyComm>> result = queryHandlerA.getSubjectData(request);
        log.info("科目数据：{}", JSON.toJSONString(result));
    }

    @Test
    public void subjectQueryDynamicData3() {
        // 获取科目数据【A】- 基础佣金 + 员工 + 指标
        SubjectDataQueryHandler<SubjectDataRequest<String>, SubjectDataDynamic> queryDynamicHandler = SubjectDataFactory.getSubjectDataQueryHandler(CostSubjectEnum.AGRICULTURAL_MACHINERY_PERFORMANCE);
        SubjectDataRequest<String> request = new SubjectDataRequest<>();
        // 设置查询周期
        request.setCostSettlementCycle("202312");
        // 代发区域营销费
        SubjectDataQueryHandler<SubjectDataRequest<String>, SubjectDataDynamic> queryDynamicHandler2 = SubjectDataFactory.getSubjectDataQueryHandler(CostSubjectEnum.ISSUING_REGIONAL_MARKETING_FEE);
        SubjectDataResponse<SubjectDataDynamic> result2 = queryDynamicHandler2.getSubjectData(request);
        log.info("区域代发科目数据：{}", JSON.toJSONString(result2));

    }

    @Test
    public void subjectQueryDynamicData() {
        // 获取科目数据【A】- 基础佣金 + 员工 + 指标
        SubjectDataQueryHandler<SubjectDataRequest<String>, SubjectDataDynamic> queryDynamicHandler = SubjectDataFactory.getSubjectDataQueryHandler(CostSubjectEnum.AGRICULTURAL_MACHINERY_PERFORMANCE);
        SubjectDataRequest<String> request = new SubjectDataRequest<>();
        // 设置查询周期
        request.setCostSettlementCycle("202312");
        SubjectDataResponse<SubjectDataDynamic> result = queryDynamicHandler.getSubjectData(request);
        log.info("农机险科目数据：{}", JSON.toJSONString(result));

        // 代发区域营销费
        SubjectDataQueryHandler<SubjectDataRequest<String>, SubjectDataDynamic> queryDynamicHandler2 = SubjectDataFactory.getSubjectDataQueryHandler(CostSubjectEnum.ISSUING_REGIONAL_MARKETING_FEE);
        SubjectDataResponse<SubjectDataDynamic> result2 = queryDynamicHandler2.getSubjectData(request);
        log.info("区域代发科目数据：{}", JSON.toJSONString(result2));

        // 长险复效补发
        SubjectDataQueryHandler<SubjectDataRequest<String>, SubjectDataDynamic> queryDynamicHandler3 = SubjectDataFactory.getSubjectDataQueryHandler(CostSubjectEnum.LONG_RESTATEMENT_REISSUE_COMM);
        SubjectDataResponse<SubjectDataDynamic> result3 = queryDynamicHandler3.getSubjectData(request);
        log.info("长险复效补发：{}", JSON.toJSONString(result3));

        // 获取科目数据【A】- 基础佣金 + 员工 + 指标
        SubjectDataQueryHandler<SubjectDataRequest<String>, List<SubjectDataDynamic>> queryDynamicHandlerManage = SubjectDataFactory.getSubjectDataQueryHandler(CostSubjectEnum.DYNAMIC_SUBJECT);
        SubjectDataResponse<List<SubjectDataDynamic>> manageResult = queryDynamicHandlerManage.getSubjectData(request);
        log.info("动态科目数据：{}", JSON.toJSONString(manageResult));
    }

    @Test
    public void subjectQueryDynamicDataCheck() {
        SubjectDataRequest<String> request = new SubjectDataRequest<>();
        // 设置查询周期
        request.setCostSettlementCycle("202312");
        // 获取科目数据【A】- 基础佣金 + 员工 + 指标
        SubjectDataQueryHandler<SubjectDataRequest<String>, List<SubjectDataDynamic>> queryDynamicHandlerManage = SubjectDataFactory.getSubjectDataQueryHandler(CostSubjectEnum.DYNAMIC_SUBJECT);
        SubjectDataResponse<List<SubjectDataDynamic>> manageResult = queryDynamicHandlerManage.getSubjectData(request);
        log.info("动态科目数据：{}", JSON.toJSONString(manageResult));
    }

    @Test
    public void subjectQueryDynamicData2() {
        // 获取科目数据【A】- 基础佣金 + 员工 + 指标
        SubjectDataQueryHandler<SubjectDataRequest<String>, List<SubjectDataDynamic>> queryDynamicHandlerManage = SubjectDataFactory.getSubjectDataQueryHandler(CostSubjectEnum.DYNAMIC_SUBJECT);
        SubjectDataRequest<String> manageRequest = new SubjectDataRequest<>();
        // 设置查询周期
        manageRequest.setCostSettlementCycle("202312");
        SubjectDataResponse<List<SubjectDataDynamic>> manageResult = queryDynamicHandlerManage.getSubjectData(manageRequest);
        log.info("动态科目数据：{}", JSON.toJSONString(manageResult));
    }

    @Test
    public void costProgrammeRun() {
        // 1 获取当前生效的科目
        SettlementCostProgrammeInfoEntity programmeInfo = settlementCostProgrammeInfoService.lambdaQuery().eq(SettlementCostProgrammeInfoEntity::getProgrammeCode, "**********").one();
        log.info("执行方案信息，方案信息={}", JSON.toJSONString(programmeInfo));

        // 2 获取方案的科目集合信息
        List<SettlementCostProgrammeSubjectEntity> costProgrammeSubject = settlementCostProgrammeSubjectService.lambdaQuery()
                .eq(SettlementCostProgrammeSubjectEntity::getProgrammeCode, programmeInfo.getProgrammeCode())
                .orderByDesc(SettlementCostProgrammeSubjectEntity::getOrderNumber)
                .list();
        String costSettlementCycle = AutoCostUtils.costSettlementMonthSnapshot();
        // 3 执行方案科目数据构建处理
        costProgrammeSubject.forEach(x -> {
            SubjectDataBuilderHandler builderDataHandler = SubjectDataFactory.getSubjectBuilderDataHandler(CostSubjectEnum.matchSearchCode(x.getSubjectCode()));
            CostSubjectDataRecord builderResult = builderDataHandler.handle(costSettlementCycle, true);
            if (builderResult.getSubjectDataStatus() == 1) {
                log.info("科目数据构建处理成功，科目={}，准备执行运算", CostSubjectEnum.matchSearchCode(x.getSubjectCode()).getName());
                // TODO: 2023/11/6 执行方案科目计算 zhangjian
            } else {
                log.info("科目数据构建处理失败，科目={} 错误摘要={}", CostSubjectEnum.matchSearchCode(x.getSubjectCode()).getName(), builderResult.getSubjectDataDesc());
            }
        });
    }

    @Test
    public void settlementInstitutionHelper() {
        List<SettlementInstitutionInfo> settlementInstitutionInfos = SettlementInstitutionHelper.listSettlementInstitution();
        log.info("结算机构信息：{}", JSON.toJSONString(settlementInstitutionInfos));
    }

    @Test
    public void costProgrammeJob() {
        // 1 获取生效中的方案集合
        List<SettlementCostProgrammeInfoEntity> costProgrammeList = settlementCostProgrammeInfoService.lambdaQuery().eq(SettlementCostProgrammeInfoEntity::getProgrammeStatus, 1).list();

        log.info("当前生效的方案集合：{}", JSON.toJSONString(costProgrammeList));
        // 2 处理方案
        costProgrammeList.forEach(x -> {
            // 2-1 获取方案配置的科目
            List<SettlementCostProgrammeSubjectEntity> costProgrammeSubject = settlementCostProgrammeSubjectService.lambdaQuery()
                    .eq(SettlementCostProgrammeSubjectEntity::getProgrammeCode, x.getProgrammeCode())
                    .orderByAsc(SettlementCostProgrammeSubjectEntity::getOrderNumber)
                    .list();
            // 2-2 生成方案结算周期
            String costSettlementCycle = AutoCostUtils.costSettlementMonthSnapshot();
            // 2-3 执行方案科目数据构建处理
            costProgrammeSubject.forEach(c -> {
                CostSubjectEnum costSubject = CostSubjectEnum.matchSearchCode(c.getSubjectCode());
                SubjectDataBuilderHandler builderDataHandler = SubjectDataFactory.getSubjectBuilderDataHandler(costSubject);
                CostSubjectDataRecord builderResult = builderDataHandler.handle(costSettlementCycle, true);
                if (builderResult.getSubjectDataStatus() == 1) {
                    log.info("科目数据构建处理成功，科目={}，准备执行运算", costSubject.getName());
                    // TODO: 2023/11/6 执行方案科目计算 zhangjian
                } else {
                    log.info("科目数据构建处理失败，科目={} 错误摘要={}", costSubject.getName(), builderResult.getSubjectDataDesc());
                }
            });

            // 2-4 设置方案记录情况
            CostProgrammeRecordInfo recordInfo = new CostProgrammeRecordInfo();
            // 方案周期 + 方案编码
            recordInfo.setCostSettlementCycle(costSettlementCycle);
            recordInfo.setProgrammeCode(x.getProgrammeCode());

            /************科目方案数据处理状态 start***************/
            // 获取方案配置的客户编码集合
            List<String> costProgrammeSubjectCodeList = costProgrammeSubject.stream().map(SettlementCostProgrammeSubjectEntity::getSubjectCode).collect(Collectors.toList());
            // 2-5 获取方案数据范围生成状况
            List<CostSubjectDataRecord> costSubjectDataRecords = subjectDataService.querySubjectDataRecord(costSettlementCycle);
            // 2-5-1 【所有科目记录】 + 【成功科目记录】+ 【失败科目记录】
            List<String> allSubjectDataRecord = costSubjectDataRecords.stream().map(CostSubjectDataRecord::getSubjectCode).distinct().collect(Collectors.toList());
            List<String> successSubject = costSubjectDataRecords.stream().filter(f -> f.getSubjectDataStatus() == 1).map(CostSubjectDataRecord::getSubjectCode).distinct().collect(Collectors.toList());
            List<String> errorSubject = costSubjectDataRecords.stream().filter(f -> f.getSubjectDataStatus() != 1).map(CostSubjectDataRecord::getSubjectCode).distinct().collect(Collectors.toList());
            // 2-5-2 判断是否存在未完成的抽数的科目
            Collection<String> subtract = CollUtil.subtract(costProgrammeSubjectCodeList, allSubjectDataRecord);
            if (subtract.isEmpty()) {
                // 如果【成功科目记录】 ==  所需的科目数据
                if (CollUtil.subtract(costProgrammeSubjectCodeList, successSubject).isEmpty()) {
                    recordInfo.setSubjectDataStatus(1);
                    recordInfo.setSubjectDataDesc(StrUtil.format("所有科目数据采集完成：科目信息={}", costSubjectDataRecords.stream().map(CostSubjectDataRecord::getSubjectCode).distinct().collect(Collectors.toList())));
                } else {
                    recordInfo.setSubjectDataStatus(0);
                    recordInfo.setSubjectDataDesc(StrUtil.format("存在未完成科目数据采集：未完成科目信息={}", JSON.toJSONString(CollUtil.subtract(errorSubject, successSubject))));
                }
            } else {
                recordInfo.setSubjectDataStatus(0);
                recordInfo.setSubjectDataDesc(StrUtil.format("存在客户数据采集差异：差异科目信息={}", JSON.toJSONString(subtract)));
            }
            /************科目方案数据处理状态 end***************/

            // TODO: 2023/11/7 根据方案周期 + 方案编码获取运算的状态 zhangjian
            // 2-6 保存或修改方案记录信息
            costProgrammeService.saveOrUpdateSettlementCostProgrammeRecord(recordInfo);
        });
    }
}