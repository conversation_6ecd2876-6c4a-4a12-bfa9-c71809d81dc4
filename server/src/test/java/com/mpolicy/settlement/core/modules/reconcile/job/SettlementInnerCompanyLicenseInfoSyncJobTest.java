package com.mpolicy.settlement.core.modules.reconcile.job;

import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementInnerCompanyLicenseInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementInnerCompanyLicenseInfoService;
import com.mpolicy.settlement.core.thirdpart.dto.omsbiz.LicenseInfoDto;
import com.mpolicy.settlement.core.thirdpart.dto.omsbiz.LicenseSyncPageData;
import com.mpolicy.settlement.core.thirdpart.dto.omsbiz.LicenseSyncRequest;
import com.mpolicy.settlement.core.thirdpart.dto.omsbiz.LicenseSyncResponse;
import com.mpolicy.settlement.core.thirdpart.service.OmsBizClient;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

/**
 * 工商信息同步定时任务测试类
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@ExtendWith(MockitoExtension.class)
public class SettlementInnerCompanyLicenseInfoSyncJobTest {

    @Mock
    private OmsBizClient omsBizClient;

    @Mock
    private SettlementInnerCompanyLicenseInfoService settlementInnerCompanyLicenseInfoService;

    @InjectMocks
    private SettlementInnerCompanyLicenseInfoSyncJob syncJob;

    @Test
    public void testSyncLicenseInfoJob() {
        // 准备测试数据
        LicenseInfoDto licenseInfo1 = LicenseInfoDto.builder()
                .id(934)
                .businessLicenseStatus(1)
                .businessNature(1)
                .licenseName("湖南莺歌资产经营有限公司")
                .licenseType(1)
                .managementType(1)
                .modifyDate(1753265525000L)
                .socialCreditCode("4444444")
                .subjectType(2)
                .build();

        LicenseInfoDto licenseInfo2 = LicenseInfoDto.builder()
                .id(917)
                .businessLicenseStatus(1)
                .businessNature(1)
                .licenseName("测试0813-5月29号测试改名")
                .licenseType(1)
                .managementType(1)
                .modifyDate(1748504558000L)
                .socialCreditCode("1000")
                .subjectType(2)
                .build();

        List<LicenseInfoDto> licenseInfoList = Arrays.asList(licenseInfo1, licenseInfo2);

        LicenseSyncPageData pageData = LicenseSyncPageData.builder()
                .list(licenseInfoList)
                .hasNextPage(false)
                .pageNum(1)
                .pageSize(2)
                .total(2)
                .build();

        LicenseSyncResponse response = LicenseSyncResponse.builder()
                .code("0")
                .data(pageData)
                .success(true)
                .message("OK")
                .build();

        // Mock外部服务调用
        when(omsBizClient.syncLicenseInfo(any(LicenseSyncRequest.class))).thenReturn(response);

        // 执行测试
        syncJob.syncLicenseInfoJob();

        // 验证调用
        verify(omsBizClient, times(1)).syncLicenseInfo(any(LicenseSyncRequest.class));
        verify(settlementInnerCompanyLicenseInfoService, times(1)).saveOrUpdateBatch(anyList());
    }
}
