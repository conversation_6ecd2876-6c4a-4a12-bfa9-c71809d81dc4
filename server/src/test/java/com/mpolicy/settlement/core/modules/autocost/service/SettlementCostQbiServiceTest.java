package com.mpolicy.settlement.core.modules.autocost.service;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.mpolicy.open.common.cfpamf.hr.OrganizationRequest;
import com.mpolicy.open.common.cfpamf.hr.OrganizationResp;
import com.mpolicy.open.common.hr.vo.OrganizationLdomVO;
import com.mpolicy.settlement.core.SettlementCoreCenterApplicationTests;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataEmployee;
import com.mpolicy.settlement.core.modules.autocost.dto.employee.EmployeeInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.qbi.EmployeeQbiInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.qbi.OrgQbiInfo;
import com.mpolicy.settlement.core.modules.autocost.entity.*;
import com.mpolicy.settlement.core.modules.autocost.enums.EmployeeRoleEnum;
import com.mpolicy.settlement.core.modules.autocost.utils.AutoCostUtils;
import com.mpolicy.settlement.core.service.common.OpenApiBaseService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Slf4j
public class SettlementCostQbiServiceTest extends SettlementCoreCenterApplicationTests {

    @Autowired
    private OpenApiBaseService openApiBaseService;

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private SettlementCostQbiService settlementCostQbiService;

    @Autowired
    private SettlementCostQbiEmployeeService settlementCostQbiEmployeeService;

    @Autowired
    private SettlementCostQbiEmployeeProductService settlementCostQbiEmployeeProductService;

    @Autowired
    private SettlementCostQbiSupervisorService settlementCostQbiSupervisorService;

    @Autowired
    private SettlementCostQbiSupervisorProductService settlementCostQbiSupervisorProductService;

    @Autowired
    private SettlementCostQbiOrgService settlementCostQbiOrgService;

    @Autowired
    private SettlementCostQbiOrgProductService settlementCostQbiOrgProductService;

    @Autowired
    private SettlementCostSubjectDataEmployeeService settlementCostSubjectDataEmployeeService;

    String monthSnapshot = AutoCostUtils.costSettlementMonthSnapshot();
    String employeeNo = "ZHNX39522";
    List<String> employeeNos = Arrays.asList("ZHNX39522", "ZHNX39488", "ZHNX39498", "ZHNX39517", "ZHNX39550");

    @Test
    public void queryEmployeeQbi() {
        EmployeeQbiInfo employeeQbiInfo = settlementCostQbiService.queryEmployeeQbi(employeeNo, monthSnapshot);
        Assert.assertNotNull(employeeQbiInfo);
    }

    @Test
    public void queryOrgQbi() {
        SubjectDataEmployee bean = new SubjectDataEmployee();
        bean.setCostSettlementCycle("ceshi");
        bean.setSubjectCode("ceshi");
        EmployeeInfo employee = new EmployeeInfo();
        employee.setEmployeeCode("123");
        employee.setEmployeeId(123);
        employee.setEmployeeName("测试");
        employee.setOrgCode("CNGS");
        employee.setOrgName("CNGS");
        employee.setServiceType(1);
        employee.setPostingStatus(1);
        BeanUtils.copyProperties(employee, bean);
        Map<String, OrgQbiInfo> stringOrgQbiInfoMap = settlementCostQbiService.mapOrgQbi(Arrays.asList("CNGS"), "20231130");
        log.info(JSON.toJSONString(stringOrgQbiInfoMap));
        BeanUtils.copyProperties(stringOrgQbiInfoMap.get("CNGS"), bean);
        log.info(JSON.toJSONString(bean));

        settlementCostSubjectDataEmployeeService.saveList(Arrays.asList(bean));
    }

    @Test
    public void initEmployeeQbi() {
        employeeService.queryEmployeeAllInfo().parallelStream().forEach(employeeInfo ->{
            SettlementCostQbiEmployeeEntity bean = new SettlementCostQbiEmployeeEntity();
            bean.setEmployeeCode(employeeInfo.getEmployeeCode());
            bean.setEmployeeName(employeeInfo.getEmployeeName());
            bean.setRenewalRate(RandomUtil.randomBigDecimal(new BigDecimal("10"),new BigDecimal("100")));
            bean.setLongPromotion(RandomUtil.randomBigDecimal(new BigDecimal("10"),new BigDecimal("100")));
            bean.setLongSettlementPremium(RandomUtil.randomBigDecimal(new BigDecimal("10"),new BigDecimal("100")));
            bean.setShortPromotion(RandomUtil.randomBigDecimal(new BigDecimal("10"),new BigDecimal("100")));
            bean.setShortSettlementPremium(RandomUtil.randomBigDecimal(new BigDecimal("10"),new BigDecimal("100")));
            bean.setPcoLevel("S");
            bean.setRegisterUserCount(RandomUtil.randomInt(1,1000));
            bean.setPt(Integer.parseInt(AutoCostUtils.costSettlementMonthSnapshot()));
            settlementCostQbiEmployeeService.save(bean);

            SettlementCostQbiEmployeeProductEntity product = new SettlementCostQbiEmployeeProductEntity();
            product.setEmployeeCode(employeeInfo.getEmployeeCode());
            product.setEmployeeName(employeeInfo.getEmployeeName());
            product.setOrgCode(employeeInfo.getOrgCode());
            product.setOrgName(employeeInfo.getOrgName());
            product.setLongPromotion(RandomUtil.randomBigDecimal(new BigDecimal("10"),new BigDecimal("100")));
            product.setLongSettlementPremium(RandomUtil.randomBigDecimal(new BigDecimal("10"),new BigDecimal("100")));
            product.setShortPromotion(RandomUtil.randomBigDecimal(new BigDecimal("10"),new BigDecimal("100")));
            product.setShortSettlementPremium(RandomUtil.randomBigDecimal(new BigDecimal("10"),new BigDecimal("100")));
            bean.setPt(Integer.parseInt(AutoCostUtils.costSettlementMonthSnapshot()));

            product.setSettlementOrgCode("XJXH");
            product.setSettlementOrgName("小鲸向海");
            product.setProductCode("HGRSQJDS23010301");
            product.setProductName("华贵大麦旗舰版定期寿险（互联网专属）（必选责任）");
            settlementCostQbiEmployeeProductService.save(product);

            product.setProductCode("TBJKLYB23031501");
            product.setProductName("太平洋健康个人长期医疗保险（费率可调）（互联网）（必选责任）（蓝医保）");
            settlementCostQbiEmployeeProductService.save(product);

            product.setProductCode("ZAZXZATY22011901");
            product.setProductName("众安在线众安团体意外险");
            settlementCostQbiEmployeeProductService.save(product);


            product.setSettlementOrgCode("XZ");
            product.setSettlementOrgName("乡助");
            product.setProductCode("TBJKLYB23031504");
            product.setProductName("太平洋健康个人长期医疗保险（费率可调）（互联网）（含可选责任）（蓝医保）");
            settlementCostQbiEmployeeProductService.save(product);
        });
    }

    @Test
    public void initSupervisorEmployeeQbi() {
        for (EmployeeInfo employeeInfo : employeeService.listEmployeeByEmployeeRole(EmployeeRoleEnum.SUPERVISOR, true)) {
            SettlementCostQbiSupervisorEntity bean = new SettlementCostQbiSupervisorEntity();
            bean.setEmployeeCode(employeeInfo.getEmployeeCode());
            bean.setEmployeeName(employeeInfo.getEmployeeName());
            bean.setRenewalRate(RandomUtil.randomBigDecimal(new BigDecimal("210"),new BigDecimal("5100")));
            bean.setLongPromotion(RandomUtil.randomBigDecimal(new BigDecimal("210"),new BigDecimal("5100")));
            bean.setLongSettlementPremium(RandomUtil.randomBigDecimal(new BigDecimal("210"),new BigDecimal("5100")));
            bean.setShortPromotion(RandomUtil.randomBigDecimal(new BigDecimal("210"),new BigDecimal("5100")));
            bean.setShortSettlementPremium(RandomUtil.randomBigDecimal(new BigDecimal("210"),new BigDecimal("5100")));
            bean.setPcoLevel("S");
            bean.setRegisterUserCount(RandomUtil.randomInt(1,1000));
            bean.setPt(Integer.parseInt(AutoCostUtils.costSettlementMonthSnapshot()) - 1);
            settlementCostQbiSupervisorService.save(bean);

            SettlementCostQbiSupervisorProductEntity product = new SettlementCostQbiSupervisorProductEntity();
            product.setEmployeeCode(employeeInfo.getEmployeeCode());
            product.setEmployeeName(employeeInfo.getEmployeeName());
            product.setOrgCode(employeeInfo.getOrgCode());
            product.setOrgName(employeeInfo.getOrgName());
            product.setLongPromotion(RandomUtil.randomBigDecimal(new BigDecimal("210"),new BigDecimal("2100")));
            product.setLongSettlementPremium(RandomUtil.randomBigDecimal(new BigDecimal("210"),new BigDecimal("2100")));
            product.setShortPromotion(RandomUtil.randomBigDecimal(new BigDecimal("210"),new BigDecimal("2100")));
            product.setShortSettlementPremium(RandomUtil.randomBigDecimal(new BigDecimal("100"),new BigDecimal("1000")));
            bean.setPt(Integer.parseInt(AutoCostUtils.costSettlementMonthSnapshot()) - 1);

            product.setSettlementOrgCode("XJXH");
            product.setSettlementOrgName("小鲸向海");
            product.setProductCode("HGRSQJDS23010301");
            product.setProductName("华贵大麦旗舰版定期寿险（互联网专属）（必选责任）");
            settlementCostQbiSupervisorProductService.save(product);

            product.setProductCode("TBJKLYB23031501");
            product.setProductName("太平洋健康个人长期医疗保险（费率可调）（互联网）（必选责任）（蓝医保）");
            settlementCostQbiSupervisorProductService.save(product);

            product.setProductCode("ZAZXZATY22011901");
            product.setProductName("众安在线众安团体意外险");
            settlementCostQbiSupervisorProductService.save(product);


            product.setSettlementOrgCode("XZ");
            product.setSettlementOrgName("乡助");
            product.setProductCode("TBJKLYB23031504");
            product.setProductName("太平洋健康个人长期医疗保险（费率可调）（互联网）（含可选责任）（蓝医保）");
            settlementCostQbiSupervisorProductService.save(product);
        }
    }

    @Test
    public void initOrgEmployeeQbi() {
        // 1 获取所有的机构分支信息
        OrganizationRequest orgRequest = new OrganizationRequest();
        Integer page = 1;
        // 2 业务处理
        while (true) {
            orgRequest.setPage(page);
            orgRequest.setSize(200);
            orgRequest.setYear(null);
            orgRequest.setMonth(null);
            // 2-1 分页获取分支信息
            List<OrganizationLdomVO> orgList = openApiBaseService.queryBranchInfo(orgRequest, true);
            if (orgList.isEmpty()) {
                break;
            }
            // 2-2 循环分支获取分支编码 + 角色编码获取员工信息
            orgList.forEach(x -> {
                SettlementCostQbiOrgEntity bean = new SettlementCostQbiOrgEntity();
                bean.setOrgCode(x.getBranchCode());
                bean.setOrgName(x.getBranchName());
                bean.setRenewalRate(RandomUtil.randomBigDecimal(new BigDecimal("10"),new BigDecimal("100")));
                bean.setLongPromotion(RandomUtil.randomBigDecimal(new BigDecimal("810"),new BigDecimal("8100")));
                bean.setLongSettlementPremium(RandomUtil.randomBigDecimal(new BigDecimal("810"),new BigDecimal("8100")));
                bean.setShortPromotion(RandomUtil.randomBigDecimal(new BigDecimal("810"),new BigDecimal("8100")));
                bean.setShortSettlementPremium(RandomUtil.randomBigDecimal(new BigDecimal("810"),new BigDecimal("8100")));
                bean.setPcoLevel("S");
                bean.setRegisterUserCount(RandomUtil.randomInt(1,1000));
                bean.setAgriculturalPromotion(RandomUtil.randomBigDecimal(new BigDecimal("110"),new BigDecimal("8100")));
                bean.setAgriculturalSettlementPremium(RandomUtil.randomBigDecimal(new BigDecimal("210"),new BigDecimal("8100")));
                bean.setPt(Integer.parseInt(AutoCostUtils.costSettlementMonthSnapshot()) - 1);
                settlementCostQbiOrgService.save(bean);


                SettlementCostQbiOrgProductEntity product = new SettlementCostQbiOrgProductEntity();
                product.setOrgCode(x.getBranchCode());
                product.setOrgName(x.getBranchName());
                product.setLongPromotion(RandomUtil.randomBigDecimal(new BigDecimal("110"),new BigDecimal("6100")));
                product.setLongSettlementPremium(RandomUtil.randomBigDecimal(new BigDecimal("110"),new BigDecimal("1100")));
                product.setShortPromotion(RandomUtil.randomBigDecimal(new BigDecimal("110"),new BigDecimal("8100")));
                product.setShortSettlementPremium(RandomUtil.randomBigDecimal(new BigDecimal("110"),new BigDecimal("6100")));
                product.setAgriculturalPromotion(RandomUtil.randomBigDecimal(new BigDecimal("110"),new BigDecimal("21100")));
                product.setAgriculturalSettlementPremium(RandomUtil.randomBigDecimal(new BigDecimal("110"),new BigDecimal("12100")));
                bean.setPt(Integer.parseInt(AutoCostUtils.costSettlementMonthSnapshot()) - 1);

                product.setSettlementOrgCode("XJXH");
                product.setSettlementOrgName("小鲸向海");
                product.setProductCode("HGRSQJDS23010301");
                product.setProductName("华贵大麦旗舰版定期寿险（互联网专属）（必选责任）");
                settlementCostQbiOrgProductService.save(product);

                product.setProductCode("TBJKLYB23031501");
                product.setProductName("太平洋健康个人长期医疗保险（费率可调）（互联网）（必选责任）（蓝医保）");
                settlementCostQbiOrgProductService.save(product);

                product.setProductCode("ZAZXZATY22011901");
                product.setProductName("众安在线众安团体意外险");
                settlementCostQbiOrgProductService.save(product);


                product.setSettlementOrgCode("XZ");
                product.setSettlementOrgName("乡助");
                product.setProductCode("TBJKLYB23031504");
                product.setProductName("太平洋健康个人长期医疗保险（费率可调）（互联网）（含可选责任）（蓝医保）");
                settlementCostQbiOrgProductService.save(product);

            });
            // 2 根据分支获取岗位信息
            page++;
        }
    }

    @Test
    public void initOrgEmployeeQbiList() {
        // 1 获取所有的机构分支信息
        OrganizationRequest orgRequest = new OrganizationRequest();
        Integer page = 1;
        // 2 业务处理
        while (true) {
            orgRequest.setPage(page);
            orgRequest.setSize(200);
            orgRequest.setYear(null);
            orgRequest.setMonth(null);
            // 2-1 分页获取分支信息
            List<OrganizationLdomVO> orgList = openApiBaseService.queryBranchInfo(orgRequest, true);
            if (orgList.isEmpty()) {
                break;
            }
            log.info("机构信息={}", JSON.toJSONString(orgList));
            // 2 根据分支获取岗位信息
            page++;
        }
    }
}