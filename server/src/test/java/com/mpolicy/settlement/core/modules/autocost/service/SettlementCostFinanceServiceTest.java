package com.mpolicy.settlement.core.modules.autocost.service;

import com.mpolicy.settlement.core.SettlementCoreCenterApplicationTests;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class SettlementCostFinanceServiceTest extends SettlementCoreCenterApplicationTests {


    @Autowired
    private SettlementCostFinanceService settlementCostFinanceService;


    @Test
    public void loadFinanceQuarter() {
        // 创建参数
        String fileCode = "oss20231216132755H4fEM1";
        String userName = "孙海军";

        // 调用方法
        settlementCostFinanceService.loadFinanceQuarter(fileCode, userName);
    }
}