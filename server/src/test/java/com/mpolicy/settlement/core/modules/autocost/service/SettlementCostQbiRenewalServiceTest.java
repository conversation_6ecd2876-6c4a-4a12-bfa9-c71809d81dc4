package com.mpolicy.settlement.core.modules.autocost.service;

import com.mpolicy.settlement.core.SettlementCoreCenterApplicationTests;
import com.mpolicy.settlement.core.modules.autocost.dto.qbi.EmployeeQbiRenewalRate;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class SettlementCostQbiRenewalServiceTest extends SettlementCoreCenterApplicationTests {


    @Autowired
    private SettlementCostQbiService settlementCostQbiService;

    @Test
    public void queryEmployeeQbiRenewalRate() {
        final EmployeeQbiRenewalRate zhnx202020 = settlementCostQbiService.queryEmployeeQbiRenewalRate("ZHNX202020", "2023-07", "202312");
    }

    @Test
    public void listEmployeeQbiRenewalRate() {
        settlementCostQbiService.listEmployeeQbiRenewalRate("ZHNX202020", "202312");
    }

    @Test
    public void querySupervisorQbiRenewalRate() {
        settlementCostQbiService.querySupervisorQbiRenewalRate("ZHNX202020", "2023-07", "202312");
    }

    @Test
    public void listSupervisorQbiRenewalRate() {
        settlementCostQbiService.listSupervisorQbiRenewalRate("ZHNX202020", "202312");
    }

    @Test
    public void queryOrgQbiRenewalRate() {
        settlementCostQbiService.queryOrgQbiRenewalRate("ZHNX202020", "2023-07", "202312");
    }

    @Test
    public void listOrgQbiRenewalRate() {
        settlementCostQbiService.listOrgQbiRenewalRate("ZHNX202020", "202312");
    }
}