package com.mpolicy.settlement.core.service.common;

import com.mpolicy.settlement.core.SettlementCoreCenterApplicationTests;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class SettlementPolicyBaseClientTest extends SettlementCoreCenterApplicationTests {

    @Autowired
    private SettlementPolicyBaseClient settlementPolicyBaseClient;

//    @Test
//    public void getSettlementPolicyByPolicyCode() {
//        SettlementPolicyInfo settlementPolicyInfo = settlementPolicyBaseClient.getSettlementPolicyByPolicyCode("987654321", true);
//        log.info("settlementPolicyInfo:{}", JSON.toJSONString(settlementPolicyInfo));
//    }
//
//    @Test
//    public void listSettlementPolicy() {
//        List<String> policyCodeList = Arrays.asList("8026900057253158", "8026900058597778", "888050001627601", "8026900064551358", "888050001631593", "888050001629282", "8026900056337658", "8026900055503068", "8026900056438188", "888050001647578", "8026900054928578", "8026900055198188", "888050001630348", "8026900056639908", "8026900056330378", "888050001626430", "8026900056335348", "888050001627324", "8026900056636318", "8026900056552568", "1613869714112188", "W87210003017566", "W87210003082469", "1613868886469988");
//        List<SettlementPolicyInfo> settlementPolicyInfos = settlementPolicyBaseClient.listSettlementPolicy(policyCodeList, true);
//        log.info("settlementPolicyList:{}", JSON.toJSONString(settlementPolicyInfos));
//    }
//
//    @Test
//    public void mapSettlementPolicy() {
//        List<String> policyCodeList = Arrays.asList("8026900057253158", "8026900058597778", "888050001627601", "8026900064551358", "888050001631593", "888050001629282", "8026900056337658", "8026900055503068", "8026900056438188", "888050001647578", "8026900054928578", "8026900055198188", "888050001630348", "8026900056639908", "8026900056330378", "888050001626430", "8026900056335348", "888050001627324", "8026900056636318", "8026900056552568", "1613869714112188", "W87210003017566", "W87210003082469", "1613868886469988");
//        Map<String, SettlementPolicyInfo> settlementPolicyInfos = settlementPolicyBaseClient.mapSettlementPolicy(policyCodeList, true);
//        log.info("settlementPolicyMap:{}", JSON.toJSONString(settlementPolicyInfos));
//    }
}