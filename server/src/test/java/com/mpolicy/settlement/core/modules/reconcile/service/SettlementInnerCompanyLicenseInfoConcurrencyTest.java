package com.mpolicy.settlement.core.modules.reconcile.service;

import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementInnerCompanyLicenseInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.impl.SettlementInnerCompanyLicenseInfoServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 工商信息服务并发安全性测试
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@SpringBootTest
@ActiveProfiles("test")
public class SettlementInnerCompanyLicenseInfoConcurrencyTest {

    @Resource
    private SettlementInnerCompanyLicenseInfoService settlementInnerCompanyLicenseInfoService;

    /**
     * 测试并发查询缓存的安全性
     */
    @Test
    public void testConcurrentCacheQuery() throws InterruptedException {
        int threadCount = 50;
        int queryCount = 100;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);

        // 创建多个线程并发查询缓存
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < queryCount; j++) {
                        String socialCreditCode = "TEST_CODE_" + (threadIndex * queryCount + j);
                        
                        // 并发查询缓存
                        SettlementInnerCompanyLicenseInfoEntity result = 
                                settlementInnerCompanyLicenseInfoService.getBySocialCreditCodeFromCache(socialCreditCode);
                        
                        // 对于不存在的编码，应该返回null
                        assertNull(result);
                        successCount.incrementAndGet();
                    }
                } catch (Exception e) {
                    errorCount.incrementAndGet();
                    e.printStackTrace();
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        latch.await(30, TimeUnit.SECONDS);
        executor.shutdown();

        // 验证结果
        assertEquals(threadCount * queryCount, successCount.get());
        assertEquals(0, errorCount.get());
        
        System.out.println("并发查询测试完成 - 成功次数: " + successCount.get() + ", 错误次数: " + errorCount.get());
    }

    /**
     * 测试并发刷新缓存的安全性
     */
    @Test
    public void testConcurrentCacheRefresh() throws InterruptedException {
        int threadCount = 10;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);

        // 创建多个线程并发刷新缓存
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    // 并发刷新缓存
                    settlementInnerCompanyLicenseInfoService.refreshCache();
                    successCount.incrementAndGet();
                } catch (Exception e) {
                    errorCount.incrementAndGet();
                    e.printStackTrace();
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        latch.await(30, TimeUnit.SECONDS);
        executor.shutdown();

        // 验证结果
        assertEquals(threadCount, successCount.get());
        assertEquals(0, errorCount.get());
        
        System.out.println("并发刷新测试完成 - 成功次数: " + successCount.get() + ", 错误次数: " + errorCount.get());
    }

    /**
     * 测试读写并发的安全性
     */
    @Test
    public void testConcurrentReadWrite() throws InterruptedException {
        int readerCount = 30;
        int writerCount = 5;
        int totalThreads = readerCount + writerCount;
        ExecutorService executor = Executors.newFixedThreadPool(totalThreads);
        CountDownLatch latch = new CountDownLatch(totalThreads);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);

        // 创建读线程
        for (int i = 0; i < readerCount; i++) {
            final int threadIndex = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < 50; j++) {
                        String socialCreditCode = "READ_TEST_" + threadIndex + "_" + j;
                        settlementInnerCompanyLicenseInfoService.getBySocialCreditCodeFromCache(socialCreditCode);
                        
                        // 获取所有缓存数据
                        List<SettlementInnerCompanyLicenseInfoEntity> allData = 
                                settlementInnerCompanyLicenseInfoService.getAllFromCache();
                        assertNotNull(allData);
                        
                        Thread.sleep(1); // 模拟处理时间
                    }
                    successCount.incrementAndGet();
                } catch (Exception e) {
                    errorCount.incrementAndGet();
                    e.printStackTrace();
                } finally {
                    latch.countDown();
                }
            });
        }

        // 创建写线程
        for (int i = 0; i < writerCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < 10; j++) {
                        settlementInnerCompanyLicenseInfoService.refreshCache();
                        Thread.sleep(10); // 模拟处理时间
                    }
                    successCount.incrementAndGet();
                } catch (Exception e) {
                    errorCount.incrementAndGet();
                    e.printStackTrace();
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        latch.await(60, TimeUnit.SECONDS);
        executor.shutdown();

        // 验证结果
        assertEquals(totalThreads, successCount.get());
        assertEquals(0, errorCount.get());
        
        System.out.println("读写并发测试完成 - 成功次数: " + successCount.get() + ", 错误次数: " + errorCount.get());
    }
}
