package com.mpolicy.settlement.core.modules.reconcile.helper;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mpolicy.common.utils.other.BigDecimalUtils;
import com.mpolicy.settlement.core.SettlementCoreCenterApplicationTests;
import com.mpolicy.settlement.core.common.reconcile.BatchIsCompletedReconcileRecord;
import com.mpolicy.settlement.core.common.reconcile.diff.DiffBacklogInput;
import com.mpolicy.settlement.core.common.reconcile.enums.PremEventEnum;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileDiffWhyEnum;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileStatusEnum;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileSubjectOnlineEnum;
import com.mpolicy.settlement.core.modules.reconcile.entity.*;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementGenerateTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.factory.PremEventFactory;
import com.mpolicy.settlement.core.modules.reconcile.event.factory.SettlementEventFactory;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.PremEventHandler;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.SettlementEventHandler;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyProductPremInput;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyProductPremResult;
import com.mpolicy.settlement.core.modules.reconcile.mq.service.ReconcilePolicyMessageService;
import com.mpolicy.settlement.core.modules.reconcile.service.*;
import com.mpolicy.settlement.core.modules.reconcile.vo.RefreshPolicyPremiumVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ReconcileTest extends SettlementCoreCenterApplicationTests {
    private static List<String> RECONCILE_SUBJECT_LIST = CollUtil.newArrayList(
            ReconcileSubjectOnlineEnum.FIRST_YR_COMM.getCode(),
            ReconcileSubjectOnlineEnum.CONSULTING_SERVICE_FEE.getCode(),
            ReconcileSubjectOnlineEnum.PROMOTION_SERVICE_FEE.getCode(),
            ReconcileSubjectOnlineEnum.TECHNICAL_SERVICE_FEE.getCode());
    private static final List<String> SPLIT_INSURANCE_PRODUCT_CODE_LIST = CollUtil.newArrayList("150", "172", "173", "180", "190", "573", "923");

    @Autowired
    private SettlementReconcileService settlementReconcileService;

    @Autowired
    private SettlementReconcileConfirmService settlementReconcileConfirmService;

    @Autowired
    private SettlementPolicyInfoService settlementPolicyInfoService;

    @Autowired
    private ReconcileHelpService reconcileHelpService;

    @Autowired
    private ReconcilePolicyMessageService reconcilePolicyMessageService;

    @Autowired
    private SettlementEventJobService settlementEventJobService;


    @Autowired
    private SettlementReconcilePolicyService settlementReconcilePolicyService;

    @Autowired
    private SettlementReconcileCompanyService settlementReconcileCompanyService;

    @Test
    public void queryReconcileCompanyInfo() {
        String policyCode = "test-73656243562156cf";
        JSONObject data = new JSONObject();
        data.put("pushEventCode", "test-73656243562156cf");
        data.put("contractCode", "ct20230523141607E06zKo");
        reconcilePolicyMessageService.saveEventInfo(policyCode, SettlementEventTypeEnum.PERSONAL_NEW_POLICY, data,
                false);

        policyCode = "test-74656243geghv";
        data = new JSONObject();
        data.put("pushEventCode", "test-74656243geghv");
        data.put("contractCode", "ct20230523141153EhyGEA");
        reconcilePolicyMessageService.saveEventInfo(policyCode, SettlementEventTypeEnum.PERSONAL_NEW_POLICY, data,false);
    }

    @Test
    public void queryReconcileCompanyInfoGroup() {
        String policyCode = "00002023052302";
        JSONObject data = new JSONObject();
        data.put("pushEventCode", "00002023052302");
        data.put("contractCode", "ct2023052314152572rG5t");
        reconcilePolicyMessageService.saveEventInfo(policyCode, SettlementEventTypeEnum.GROUP_NEW_POLICY, data,false);

        policyCode = "00002023052303";
        data = new JSONObject();
        data.put("pushEventCode", "00002023052303");
        data.put("contractCode", "ct20230523143035E3QANd");
        reconcilePolicyMessageService.saveEventInfo(policyCode, SettlementEventTypeEnum.GROUP_NEW_POLICY, data,false);
    }

    @Test
    public void getPolicyInfo() {
       /* EpContractInfoVo policyInfo = policyCenterBaseClient.getPolicyInfo("ct202305221610564ef64P");
        log.info("保单详情查询完成");
        log.info("保单详情={}", JSON.toJSONString(policyInfo));*/
    }

    @Test
    public void settlementEventHandler() {
        List<SettlementEventJobEntity> list = settlementEventJobService.lambdaQuery().eq(SettlementEventJobEntity::getEventStatus, 0).list();
        log.info("待处理事件数量={}", list.size());
        // 2 获取事件工厂服务
        for (SettlementEventJobEntity eventJob : list) {
            SettlementEventHandler settlementEventHandler = SettlementEventFactory.getSettlementEventHandler(eventJob.getEventType());
            // 事件执行
            log.info("执行事件处理，事件报文={}", JSON.toJSONString(eventJob));
            //settlementEventHandler.handle(eventJob);
        }
    }

    @Test
    public void settlementPolicyFinish() {
        settlementPolicyInfoService.settlementPolicyFinish("aaa", "张三","");
    }

    @Test
    public void analysisTemplate() {
        //ReconcileTemplateHandler invokeStrategy = ReconcileTemplateFactory.getInvokeStrategy(ReconcileTemplateEnum.LIFE_INSURANCE_HORIZONTAL.getCode());
        //invokeStrategy.readFile("");
    }

    @Test
    public void standardSurrender() {
//        List<SettlementEventJobEntity> list = settlementEventJobService.lambdaQuery().eq(SettlementEventJobEntity::getEventStatus, 0).eq(SettlementEventJobEntity::getId,19).list();
        List<SettlementEventJobEntity> list = settlementEventJobService.lambdaQuery().eq(SettlementEventJobEntity::getEventStatus, 0).list();
        log.info("待处理事件数量={}", list.size());
        // 2 获取事件工厂服务
        for (SettlementEventJobEntity eventJob : list) {
            SettlementEventHandler settlementEventHandler = SettlementEventFactory.getSettlementEventHandler(eventJob.getEventType());
            // 事件执行
            log.info("执行事件处理，事件报文={}", JSON.toJSONString(eventJob));
           // settlementEventHandler.handle(eventJob);
        }
    }

    /**
     * 开始对账
     */
    @Test
    public void startReconcile() {
        settlementReconcileService.startReconcile("CR17096318375060989749773", "xiaoma", "1");
    }

    @Test
    public void diffBacklog() {
        DiffBacklogInput diffBacklog = new DiffBacklogInput();
        diffBacklog.setReconcileCode("CR20230530180537431996");
        diffBacklog.setBillCodeList(CollUtil.newArrayList("BC20230601003524961222"));
        diffBacklog.setDiffWhy(ReconcileDiffWhyEnum.MONTH_DIFF.getCode());
        diffBacklog.setAssignAcceptUser(1);
        diffBacklog.setAcceptUserCode("xiaoma");
        diffBacklog.setAcceptUserName("小马");
        diffBacklog.setDiffDesc("单元测试做差异处理");
        diffBacklog.setOpeUserName("xiaoma-test");
        diffBacklog.setOpeType(1);
        settlementReconcileService.diffBacklog(diffBacklog);
    }

    @Test
    public void finishReconcile() {
        settlementReconcileService.finishReconcile("CR20230530180537431996", "xiaoma", "2");
    }

    @Test
    public void authCreateReconcile() {
        List<SettlementPolicyInfoEntity> list = settlementPolicyInfoService.lambdaQuery().eq(SettlementPolicyInfoEntity::getReconcileExecuteStatus, 0).list();
        log.info("未配置协议的保单明细纪录条数={}", list.size());
        if (!list.isEmpty()) {
            // 批量写入
            if (list.size() > 600) {
                List<List<SettlementPolicyInfoEntity>> partition = ListUtils.partition(list, 600);
                partition.forEach(x -> {
                    x.forEach(p -> {
                        // 是否可以对账
                        try {
                            PolicyProductPremInput policyProductPremInput = builderGroupPolicyPrem(p);
                            PremEventHandler premEventHandler = PremEventFactory.getInvoke(p.getBusinessSignType() == 1 ? PremEventEnum.PROTOCOL_PREM.getCode() : PremEventEnum.CONTRACT_PREM.getCode());
                            PolicyProductPremResult productPremInfo = premEventHandler.queryPolicyProductPrem(policyProductPremInput).get(0);
                            p.setReconcileExecuteStatus(1);
                            p.setReconcileExecuteDesc("可执行对账");
                            p.setProtocolCode(productPremInfo.getBusinessCode());
                            // 计算费率及小鲸结算金额
                            p.setSettlementRate(String.valueOf(productPremInfo.getYearRate()));
                            p.setSettlementAmount(BigDecimalUtils.mul(p.getPremium().doubleValue(), productPremInfo.getYearRate().doubleValue()));
                        } catch (Exception e) {
                            log.warn("获取产品险种费率表信息异常，异常信息={}", e.getMessage());
                        }
                    });
                    settlementPolicyInfoService.updateBatchById(x);
                });
            } else {
                list.forEach(p -> {
                    // 是否可以对账
                    try {
                        PolicyProductPremInput policyProductPremInput = builderGroupPolicyPrem(p);
                        PremEventHandler premEventHandler = PremEventFactory.getInvoke(p.getBusinessSignType() == 1 ? PremEventEnum.PROTOCOL_PREM.getCode() : PremEventEnum.CONTRACT_PREM.getCode());
                        PolicyProductPremResult productPremInfo = premEventHandler.queryPolicyProductPrem(policyProductPremInput).get(0);
                        p.setReconcileExecuteStatus(1);
                        p.setReconcileExecuteDesc("可执行对账");
                        p.setProtocolCode(productPremInfo.getBusinessCode());
                        // 计算费率及小鲸结算金额
                        p.setSettlementRate(String.valueOf(productPremInfo.getYearRate()));
                        p.setSettlementAmount(BigDecimalUtils.mul(p.getPremium().doubleValue(), productPremInfo.getYearRate().doubleValue()));
                    } catch (Exception e) {
                        log.warn("获取产品险种费率表信息异常，异常信息={}", e.getMessage());
                    }
                });
                settlementPolicyInfoService.updateBatchById(list);
            }
        }
    }

    private PolicyProductPremInput builderGroupPolicyPrem(SettlementPolicyInfoEntity product) {
        PolicyProductPremInput input = new PolicyProductPremInput();
        input.setInsuranceType(0);//0:新单 1:续投
        if (StringUtils.equals(product.getSettlementEventCode(), SettlementEventTypeEnum.RENEWAL_POLICY.getEventCode())) {
            input.setInsuranceType(1);//0:新单 1:续投
        }
        // 代理人机构编码
        input.setOrgCode(product.getOrgCode());
        // 险种信息
        input.setApprovedTime(product.getApprovedTime());
        input.setInsuredPeriodType(product.getInsuredPeriodType());
        input.setInsuredPeriod(product.getInsuredPeriod());
        input.setPaymentPeriodType(product.getPaymentPeriodType());
        input.setPaymentPeriod(product.getPaymentPeriod());
        input.setPeriodType(product.getPeriodType());
        input.setRenewalYear(1);
        input.setRenewalPeriod(1);
        input.setProductCode(product.getProductCode());
        // 投保人信息不为空
        input.setApplicantGender(product.getApplicantGender());
        input.setApplicantBirthday(product.getApplicantBirthday());
        return input;
    }


    @Test
    public void read() {

        List<String> list = FileUtil.readUtf8Lines("/Users/<USER>/Desktop/db.json");
        JSONUtil.parseArray(list.get(0)).forEach(action -> {
            cn.hutool.json.JSONObject object = JSONUtil.parseObj(action);
            SettlementReconcileConfirmEntity bean = object.toBean(SettlementReconcileConfirmEntity.class);

            try {
                settlementReconcileConfirmService.save(bean);
            } catch (Exception e) {
                System.out.println("报错" + object.toStringPretty());
            }
        });

    }


    @Test
    public void buildSettlementReconcileConfirm() {
        String reconcileCode = "CR17041254002094055776933";
        List<SettlementReconcilePolicyEntity> reconcilePolicy = settlementReconcilePolicyService.lambdaQuery()
                .eq(SettlementReconcilePolicyEntity::getReconcileCode, reconcileCode)
                .list();
        List<SettlementReconcileCompanyEntity> reconcileCompany = settlementReconcileCompanyService.lambdaQuery()
                .eq(SettlementReconcileCompanyEntity::getReconcileCode, reconcileCode)
                .list();
        // 申明返回对象集合
        List<SettlementReconcileConfirmEntity> result = new ArrayList<>();
        // 开始构建线上对账单 合并批单数据....
        Map<String, SettlementReconcilePolicyEntity> reconcilePolicyMap = reconcilePolicy.stream()
                .filter(f -> f.getReconcileGenerateType() == SettlementGenerateTypeEnum.BUSINESS_EVENTS.getCode())
                .collect(Collectors.toMap(k -> getKey(k.getPolicyNo(), k.getReconcileSubjectCode(), k.getProtocolProductCode(), k.getProductCode()), v -> v,
                        (v1, v2) -> {
                            SettlementReconcilePolicyEntity settlementReconcilePolicy = BeanUtil.copyProperties(v1, SettlementReconcilePolicyEntity.class);
                            settlementReconcilePolicy.setPremium(v1.getPremium().add(v2.getPremium()));
                            settlementReconcilePolicy.setSettlementAmount(v1.getSettlementAmount().add(v2.getSettlementAmount()));
                            settlementReconcilePolicy.setProductPremiumTotal(v1.getProductPremiumTotal().add(v2.getProductPremiumTotal()));
                            if (StrUtil.isNotBlank(v1.getEndorsementNo()) && StrUtil.isNotBlank(v2.getEndorsementNo())) {
                                settlementReconcilePolicy.setEndorsementNo(v1.getEndorsementNo() + "," + v2.getEndorsementNo());
                            } else if (StrUtil.isNotBlank(v1.getEndorsementNo())) {
                                settlementReconcilePolicy.setEndorsementNo(v1.getEndorsementNo());
                            } else if (StrUtil.isNotBlank(v2.getEndorsementNo())) {
                                settlementReconcilePolicy.setEndorsementNo(v2.getEndorsementNo());
                            }
                            return settlementReconcilePolicy;
                        }));
        Map<String, SettlementReconcileCompanyEntity> reconcileCompanyMap = reconcileCompany.stream()
                .filter(c -> RECONCILE_SUBJECT_LIST.contains(c.getSettlementSubjectCode()))
                .collect(Collectors.toMap(k -> getKey(k.getPolicyNo(), k.getSettlementSubjectCode(), k.getProtocolProductCode(), k.getProductCode()), v -> v,
                        (v1, v2) -> {
                            SettlementReconcileCompanyEntity settlementReconcileCompany = BeanUtil.copyProperties(v1, SettlementReconcileCompanyEntity.class);
                            settlementReconcileCompany.setRealityPremium(v1.getRealityPremium().add(v2.getRealityPremium()));
                            settlementReconcileCompany.setCompanyAmount(v1.getCompanyAmount().add(v2.getCompanyAmount()));
                            if (StrUtil.isNotBlank(v1.getEndorsementNo()) && StrUtil.isNotBlank(v2.getEndorsementNo())) {
                                settlementReconcileCompany.setEndorsementNo(v1.getEndorsementNo() + "," + v2.getEndorsementNo());
                            } else if (StrUtil.isNotBlank(v1.getEndorsementNo())) {
                                settlementReconcileCompany.setEndorsementNo(v1.getEndorsementNo());
                            } else if (StrUtil.isNotBlank(v2.getEndorsementNo())) {
                                settlementReconcileCompany.setEndorsementNo(v2.getEndorsementNo());
                            }

                            return settlementReconcileCompany;
                        }));
        Set<String> keySet = new HashSet<>(reconcilePolicyMap.keySet());
        keySet.addAll(reconcileCompanyMap.keySet());
        System.out.println(JSONUtil.toJsonStr(keySet));
    }

    private static String getKey(String policyNo, String settlementSubjectCode, String protocolProductCode, String productCode) {
        // 这些是按照保单+险种维度
        if (SPLIT_INSURANCE_PRODUCT_CODE_LIST.contains(protocolProductCode)) {
            return DigestUtil.md5Hex(StrUtil.format("保单号=[{}]_科目编码=[{}]_险种编码=[{}]", policyNo, settlementSubjectCode, StrUtil.nullToDefault(productCode, "")));
        }
        return DigestUtil.md5Hex(StrUtil.format("保单号=[{}]_科目编码=[{}]", policyNo, settlementSubjectCode));
    }


    @Test
    public void batchRefreshRate() {
        settlementPolicyInfoService.batchRefreshRate("CR17040278678712650731273", "2");
    }

    @Test
    public void refreshPolicyPremium() {
        List<RefreshPolicyPremiumVo> policyList = new ArrayList<>();
        RefreshPolicyPremiumVo refreshPolicyPremium = new RefreshPolicyPremiumVo();
        refreshPolicyPremium.setPolicyNo("86110020220019932286");
        refreshPolicyPremium.setPremium(new BigDecimal("200.00"));
        policyList.add(refreshPolicyPremium);
        refreshPolicyPremium = new RefreshPolicyPremiumVo();
        refreshPolicyPremium.setPolicyNo("86110020220019895286");
        refreshPolicyPremium.setPremium(new BigDecimal("100.00"));
        policyList.add(refreshPolicyPremium);
        refreshPolicyPremium = new RefreshPolicyPremiumVo();
        refreshPolicyPremium.setPolicyNo("86110020220019887386");
        refreshPolicyPremium.setPremium(new BigDecimal("40.00"));
        policyList.add(refreshPolicyPremium);
        refreshPolicyPremium = new RefreshPolicyPremiumVo();
        refreshPolicyPremium.setPolicyNo("86110020220019953786");
        refreshPolicyPremium.setPremium(new BigDecimal("70.00"));
        policyList.add(refreshPolicyPremium);
        reconcileHelpService.refreshPolicyPremium(policyList);
    }


    @Test
    public void isCompletedReconcileRecord() {
        boolean res = Optional.ofNullable(settlementPolicyInfoService.lambdaQuery()
                .eq(SettlementPolicyInfoEntity::getPolicyNo, "45464")
                .eq(SettlementPolicyInfoEntity::getReconcileStatus, ReconcileStatusEnum.RECONCILE_FINISH.getStatusCode())
                .count()).orElse(0) > 0;
        log.info("45464={}",res);

        res = Optional.ofNullable(settlementPolicyInfoService.lambdaQuery()
                .eq(SettlementPolicyInfoEntity::getPolicyNo, "86110020220019887386")
                .eq(SettlementPolicyInfoEntity::getReconcileStatus, ReconcileStatusEnum.RECONCILE_FINISH.getStatusCode())
                .count()).orElse(0) > 0;
        log.info("86110020220019887386={}",res);
    }
    @Test
    public void batchIsCompletedReconcileRecord() {
        List<BatchIsCompletedReconcileRecord> batchIsCompletedReconcileRecords = settlementPolicyInfoService.batchIsCompletedReconcileRecord(CollUtil.newArrayList("45464", "86110020220019887386"));
        log.info(JSONUtil.toJsonStr(batchIsCompletedReconcileRecords));
    }

}
