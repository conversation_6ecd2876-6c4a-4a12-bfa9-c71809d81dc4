package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.mpolicy.agent.common.model.agent.AgentInfoBasicsOut;
import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.common.redis.key.KeyPrefix;
import com.mpolicy.open.common.common.ChannelInfoDetail;
import com.mpolicy.open.common.referrer.vo.ReferrerInfoVo;
import com.mpolicy.policy.common.ep.policy.*;
import com.mpolicy.policy.common.ep.policy.preserve.EpPreservationV2Vo;
import com.mpolicy.policy.common.ep.policy.preserve.EpPreserveProductVo;
import com.mpolicy.product.common.base.ProductBase;
import com.mpolicy.settlement.core.common.reconcile.BatchIsCompletedReconcileRecord;
import com.mpolicy.settlement.core.common.reconcile.BatchIsCompletedReconcileRecordPreservation;
import com.mpolicy.settlement.core.common.reconcile.RefreshSettlementPolicyInfoCommissionVo;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.service.*;
import com.mpolicy.settlement.core.service.common.AgentBaseService;
import com.mpolicy.settlement.core.service.common.OpenApiBaseService;
import com.mpolicy.settlement.core.service.common.PolicyCenterBaseClient;
import com.mpolicy.settlement.core.service.common.ProductBaseService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SettlementPolicyInfoServiceImplTest {

    @Mock
    private SettlementReconcileSubjectService mockSettlementReconcileSubjectService;
    @Mock
    private PolicyCenterBaseClient mockPolicyCenterBaseClient;
    @Mock
    private ProductBaseService mockProductBaseService;
    @Mock
    private OpenApiBaseService mockOpenApiBaseService;
    @Mock
    private AgentBaseService mockAgentBaseService;
    @Mock
    private SettlementReconcileInfoService mockSettlementReconcileInfoService;
    @Mock
    private SettlementReconcileConfirmService mockSettlementReconcileConfirmService;
    @Mock
    private SettlementReconcilePolicyService mockSettlementReconcilePolicyService;
    @Mock
    private SettlementReconcileCompanyService mockSettlementReconcileCompanyService;
    @Mock
    private IRedisService mockRedisService;

    @InjectMocks private SettlementPolicyInfoServiceImpl settlementPolicyInfoServiceImplUnderTest;

    @Test
    public void testMain() {
        // Setup
        // Run the test
        SettlementPolicyInfoServiceImpl.main(new String[]{"args"});

        // Verify the results
    }

    @Test
    public void testQuerySettlementPolicyInfoList() {
        // Setup
        final SettlementReconcileInfoEntity reconcileInfo = SettlementReconcileInfoEntity.builder()
                .reconcileCode("reconcileCode")
                .reconcileType(0)
                .reconcileMonth("reconcileMonth")
                .reconcileCompanyCode("reconcileCompanyCode")
                .reconcileStatus(0)
                .build();
        final List<SettlementPolicyInfoEntity> expectedResult = Arrays.asList(SettlementPolicyInfoEntity.builder()
                                                                                      .id(0)
                                                                                      .settlementCode("settlementCode")
                                                                                      .premCode("premCode")
                                                                                      .reconcileType(0)
                                                                                      .settlementDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .settlementTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .settlementGenerateType(0)
                                                                                      .settlementEventCode("settlementEventCode")
                                                                                      .settlementSubjectCode("settlementSubjectCode")
                                                                                      .settlementSubjectName("settlementSubjectName")
                                                                                      .businessType(0)
                                                                                      .contractCode("contractCode")
                                                                                      .policyNo("policyNo")
                                                                                      .policyProductType("policyProductType")
                                                                                      .hesitateSurrender(0)
                                                                                      .policySource("policySource")
                                                                                      .salesType(0)
                                                                                      .selfPreservation(0)
                                                                                      .customerCode("customerCode")
                                                                                      .applicantTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .approvedTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .payableTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .realityTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .revisitStatus(0)
                                                                                      .revisitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .receiptStatus(0)
                                                                                      .receiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .applicantName("applicantName")
                                                                                      .applicantMobile("applicantMobile")
                                                                                      .applicantIdCard("applicantIdCard")
                                                                                      .applicantGender(0)
                                                                                      .applicantBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .applicantAge(0)
                                                                                      .insuredPolicyAge(0)
                                                                                      .insuredName("insuredName")
                                                                                      .insuredMobile("insuredMobile")
                                                                                      .insuredIdCard("insuredIdCard")
                                                                                      .insuredGender(0)
                                                                                      .insuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .productCode("productCode")
                                                                                      .productName("productName")
                                                                                      .protocolCode("protocolCode")
                                                                                      .protocolProductCode("insuranceProductCode")
                                                                                      .protocolProductName("protocolProductName")
                                                                                      .planCode("planCode")
                                                                                      .planName("planName")
                                                                                      .productGroup("productGroup")
                                                                                      .level2Code("level2Code")
                                                                                      .level3Code("level3Code")
                                                                                      .productType("periodType")
                                                                                      .mainInsurance(0)
                                                                                      .additionalRisksType(0)
                                                                                      .effectiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .endDate("endDate")
                                                                                      .renewalYear(2020)
                                                                                      .renewalPeriod(0)
                                                                                      .coverage(new BigDecimal("0.00"))
                                                                                      .coverageUnit(0)
                                                                                      .coverageUnitName("coverageUnitName")
                                                                                      .insuredPeriodType("insuredPeriodType")
                                                                                      .insuredPeriod(0)
                                                                                      .periodType("periodType")
                                                                                      .paymentPeriodType("paymentPeriodType")
                                                                                      .paymentPeriod(0)
                                                                                      .drawAge("drawAge")
                                                                                      .premium(new BigDecimal("0.00"))
                                                                                      .productPremiumTotal(new BigDecimal("0.00"))
                                                                                      .discountPremium(new BigDecimal("0.00"))
                                                                                      .copies(0)
                                                                                      .build());
        when(mockSettlementReconcileSubjectService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Run the test
        final List<SettlementPolicyInfoEntity> result = settlementPolicyInfoServiceImplUnderTest.querySettlementPolicyInfoList(reconcileInfo);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSettlementPolicyFinish() {
        // Setup
        // Run the test
        settlementPolicyInfoServiceImplUnderTest.settlementPolicyFinish("reconcileCode", "userName", "settlementMonth");

        // Verify the results
    }

    @Test
    public void testSaveList() {
        // Setup
        final List<SettlementPolicyInfoEntity> listData = Arrays.asList(SettlementPolicyInfoEntity.builder()
                                                                                .id(0)
                                                                                .settlementCode("settlementCode")
                                                                                .premCode("premCode")
                                                                                .reconcileType(0)
                                                                                .settlementDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                .settlementTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                .settlementGenerateType(0)
                                                                                .settlementEventCode("settlementEventCode")
                                                                                .settlementSubjectCode("settlementSubjectCode")
                                                                                .settlementSubjectName("settlementSubjectName")
                                                                                .businessType(0)
                                                                                .contractCode("contractCode")
                                                                                .policyNo("policyNo")
                                                                                .policyProductType("policyProductType")
                                                                                .hesitateSurrender(0)
                                                                                .policySource("policySource")
                                                                                .salesType(0)
                                                                                .selfPreservation(0)
                                                                                .customerCode("customerCode")
                                                                                .applicantTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                .approvedTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                .payableTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                .realityTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                .revisitStatus(0)
                                                                                .revisitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                .receiptStatus(0)
                                                                                .receiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                .applicantName("applicantName")
                                                                                .applicantMobile("applicantMobile")
                                                                                .applicantIdCard("applicantIdCard")
                                                                                .applicantGender(0)
                                                                                .applicantBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                .applicantAge(0)
                                                                                .insuredPolicyAge(0)
                                                                                .insuredName("insuredName")
                                                                                .insuredMobile("insuredMobile")
                                                                                .insuredIdCard("insuredIdCard")
                                                                                .insuredGender(0)
                                                                                .insuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                .productCode("productCode")
                                                                                .productName("productName")
                                                                                .protocolCode("protocolCode")
                                                                                .protocolProductCode("insuranceProductCode")
                                                                                .protocolProductName("protocolProductName")
                                                                                .planCode("planCode")
                                                                                .planName("planName")
                                                                                .productGroup("productGroup")
                                                                                .level2Code("level2Code")
                                                                                .level3Code("level3Code")
                                                                                .productType("periodType")
                                                                                .mainInsurance(0)
                                                                                .additionalRisksType(0)
                                                                                .effectiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                .endDate("endDate")
                                                                                .renewalYear(2020)
                                                                                .renewalPeriod(0)
                                                                                .coverage(new BigDecimal("0.00"))
                                                                                .coverageUnit(0)
                                                                                .coverageUnitName("coverageUnitName")
                                                                                .insuredPeriodType("insuredPeriodType")
                                                                                .insuredPeriod(0)
                                                                                .periodType("periodType")
                                                                                .paymentPeriodType("paymentPeriodType")
                                                                                .paymentPeriod(0)
                                                                                .drawAge("drawAge")
                                                                                .premium(new BigDecimal("0.00"))
                                                                                .productPremiumTotal(new BigDecimal("0.00"))
                                                                                .discountPremium(new BigDecimal("0.00"))
                                                                                .copies(0)
                                                                                .build());

        // Run the test
        settlementPolicyInfoServiceImplUnderTest.saveList(listData);

        // Verify the results
    }

    @Test
    public void testRefreshSettlementPolicyInfoCommission() {
        // Setup
        final RefreshSettlementPolicyInfoCommissionVo params = new RefreshSettlementPolicyInfoCommissionVo();
        params.setIds(Arrays.asList(0));
        params.setEventSourceCodeList(Arrays.asList("value"));

        // Run the test
        settlementPolicyInfoServiceImplUnderTest.refreshSettlementPolicyInfoCommission(params);

        // Verify the results
    }

    @Test
    public void testBatchRefreshRate() {
        // Setup
        when(mockSettlementReconcileInfoService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));
        when(mockSettlementReconcileConfirmService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));
        when(mockSettlementReconcileConfirmService.lambdaUpdate()).thenReturn(new LambdaUpdateChainWrapper<>(null));
        when(mockSettlementReconcilePolicyService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));
        when(mockSettlementReconcileCompanyService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Run the test
        settlementPolicyInfoServiceImplUnderTest.batchRefreshRate("reconcileCode", "d3469db2-6378-499a-a034-92f70a999fc2");

        // Verify the results
        verify(mockRedisService).set(any(KeyPrefix.class), eq("d3469db2-6378-499a-a034-92f70a999fc2"), eq("2"));
    }

    @Test
    public void testHandleEndorsementNoChange() {
        // Setup
        final List<SettlementPolicyInfoEntity> settlementPolicyInfoList = Arrays.asList(SettlementPolicyInfoEntity.builder()
                                                                                                .id(0)
                                                                                                .settlementCode("settlementCode")
                                                                                                .premCode("premCode")
                                                                                                .reconcileType(0)
                                                                                                .settlementDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .settlementTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .settlementGenerateType(0)
                                                                                                .settlementEventCode("settlementEventCode")
                                                                                                .settlementSubjectCode("settlementSubjectCode")
                                                                                                .settlementSubjectName("settlementSubjectName")
                                                                                                .businessType(0)
                                                                                                .contractCode("contractCode")
                                                                                                .policyNo("policyNo")
                                                                                                .policyProductType("policyProductType")
                                                                                                .hesitateSurrender(0)
                                                                                                .policySource("policySource")
                                                                                                .salesType(0)
                                                                                                .selfPreservation(0)
                                                                                                .customerCode("customerCode")
                                                                                                .applicantTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .approvedTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .payableTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .realityTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .revisitStatus(0)
                                                                                                .revisitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .receiptStatus(0)
                                                                                                .receiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .applicantName("applicantName")
                                                                                                .applicantMobile("applicantMobile")
                                                                                                .applicantIdCard("applicantIdCard")
                                                                                                .applicantGender(0)
                                                                                                .applicantBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .applicantAge(0)
                                                                                                .insuredPolicyAge(0)
                                                                                                .insuredName("insuredName")
                                                                                                .insuredMobile("insuredMobile")
                                                                                                .insuredIdCard("insuredIdCard")
                                                                                                .insuredGender(0)
                                                                                                .insuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .productCode("productCode")
                                                                                                .productName("productName")
                                                                                                .protocolCode("protocolCode")
                                                                                                .protocolProductCode("insuranceProductCode")
                                                                                                .protocolProductName("protocolProductName")
                                                                                                .planCode("planCode")
                                                                                                .planName("planName")
                                                                                                .productGroup("productGroup")
                                                                                                .level2Code("level2Code")
                                                                                                .level3Code("level3Code")
                                                                                                .productType("periodType")
                                                                                                .mainInsurance(0)
                                                                                                .additionalRisksType(0)
                                                                                                .effectiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .endDate("endDate")
                                                                                                .renewalYear(2020)
                                                                                                .renewalPeriod(0)
                                                                                                .coverage(new BigDecimal("0.00"))
                                                                                                .coverageUnit(0)
                                                                                                .coverageUnitName("coverageUnitName")
                                                                                                .insuredPeriodType("insuredPeriodType")
                                                                                                .insuredPeriod(0)
                                                                                                .periodType("periodType")
                                                                                                .paymentPeriodType("paymentPeriodType")
                                                                                                .paymentPeriod(0)
                                                                                                .drawAge("drawAge")
                                                                                                .premium(new BigDecimal("0.00"))
                                                                                                .productPremiumTotal(new BigDecimal("0.00"))
                                                                                                .discountPremium(new BigDecimal("0.00"))
                                                                                                .copies(0)
                                                                                                .build());

        // Run the test
        settlementPolicyInfoServiceImplUnderTest.handleEndorsementNoChange(settlementPolicyInfoList, "pushEventCode", "newEndorsementNo");

        // Verify the results
    }

    @Test
    public void testHandlePolicyNoChange() {
        // Setup
        final List<SettlementPolicyInfoEntity> settlementPolicyInfoList = Arrays.asList(SettlementPolicyInfoEntity.builder()
                                                                                                .id(0)
                                                                                                .settlementCode("settlementCode")
                                                                                                .premCode("premCode")
                                                                                                .reconcileType(0)
                                                                                                .settlementDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .settlementTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .settlementGenerateType(0)
                                                                                                .settlementEventCode("settlementEventCode")
                                                                                                .settlementSubjectCode("settlementSubjectCode")
                                                                                                .settlementSubjectName("settlementSubjectName")
                                                                                                .businessType(0)
                                                                                                .contractCode("contractCode")
                                                                                                .policyNo("policyNo")
                                                                                                .policyProductType("policyProductType")
                                                                                                .hesitateSurrender(0)
                                                                                                .policySource("policySource")
                                                                                                .salesType(0)
                                                                                                .selfPreservation(0)
                                                                                                .customerCode("customerCode")
                                                                                                .applicantTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .approvedTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .payableTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .realityTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .revisitStatus(0)
                                                                                                .revisitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .receiptStatus(0)
                                                                                                .receiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .applicantName("applicantName")
                                                                                                .applicantMobile("applicantMobile")
                                                                                                .applicantIdCard("applicantIdCard")
                                                                                                .applicantGender(0)
                                                                                                .applicantBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .applicantAge(0)
                                                                                                .insuredPolicyAge(0)
                                                                                                .insuredName("insuredName")
                                                                                                .insuredMobile("insuredMobile")
                                                                                                .insuredIdCard("insuredIdCard")
                                                                                                .insuredGender(0)
                                                                                                .insuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .productCode("productCode")
                                                                                                .productName("productName")
                                                                                                .protocolCode("protocolCode")
                                                                                                .protocolProductCode("insuranceProductCode")
                                                                                                .protocolProductName("protocolProductName")
                                                                                                .planCode("planCode")
                                                                                                .planName("planName")
                                                                                                .productGroup("productGroup")
                                                                                                .level2Code("level2Code")
                                                                                                .level3Code("level3Code")
                                                                                                .productType("periodType")
                                                                                                .mainInsurance(0)
                                                                                                .additionalRisksType(0)
                                                                                                .effectiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .endDate("endDate")
                                                                                                .renewalYear(2020)
                                                                                                .renewalPeriod(0)
                                                                                                .coverage(new BigDecimal("0.00"))
                                                                                                .coverageUnit(0)
                                                                                                .coverageUnitName("coverageUnitName")
                                                                                                .insuredPeriodType("insuredPeriodType")
                                                                                                .insuredPeriod(0)
                                                                                                .periodType("periodType")
                                                                                                .paymentPeriodType("paymentPeriodType")
                                                                                                .paymentPeriod(0)
                                                                                                .drawAge("drawAge")
                                                                                                .premium(new BigDecimal("0.00"))
                                                                                                .productPremiumTotal(new BigDecimal("0.00"))
                                                                                                .discountPremium(new BigDecimal("0.00"))
                                                                                                .copies(0)
                                                                                                .build());

        // Run the test
        settlementPolicyInfoServiceImplUnderTest.handlePolicyNoChange(settlementPolicyInfoList, "pushEventCode", "policyNo");

        // Verify the results
    }

    @Test
    public void testCreateSettlementPolicyInfo() {
        // Setup
        final EpContractInfoVo policyInfo = new EpContractInfoVo();
        policyInfo.setContractCode("contractCode");
        policyInfo.setPolicyProductType("policyProductType");
        final EpContractBaseInfoVo contractBaseInfo = new EpContractBaseInfoVo();
        contractBaseInfo.setPolicyNo("policyNo");
        contractBaseInfo.setMainProductCode("productCode");
        contractBaseInfo.setSelfPreservation(0);
        contractBaseInfo.setAgentCode("mainAgentCode");
        contractBaseInfo.setOrgCode("orgCode");
        policyInfo.setContractBaseInfo(contractBaseInfo);
        final EpContractExtendInfoVo contractExtendInfo = new EpContractExtendInfoVo();
        contractExtendInfo.setIsNeedRevisit(0);
        contractExtendInfo.setRevisitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        contractExtendInfo.setEnforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        contractExtendInfo.setIsNeedReceipt(0);
        contractExtendInfo.setReceiptSignTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        policyInfo.setContractExtendInfo(contractExtendInfo);
        final EpAgentInfoVo epAgentInfoVo = new EpAgentInfoVo();
        epAgentInfoVo.setAgentCode("mainAgentCode");
        epAgentInfoVo.setOrgCode("orgCode");
        epAgentInfoVo.setMainFlag(0);
        policyInfo.setAgentInfoList(Arrays.asList(epAgentInfoVo));
        final EpPolicyChannelInfoVo channelInfo = new EpPolicyChannelInfoVo();
        channelInfo.setChannelCode("channelCode");
        channelInfo.setMiniAppCustomerCode("customerCode");
        policyInfo.setChannelInfo(channelInfo);
        final EpApplicantInfoVo applicantInfo = new EpApplicantInfoVo();
        applicantInfo.setApplicantName("applicantName");
        applicantInfo.setApplicantIdCard("applicantIdCard");
        applicantInfo.setApplicantBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        applicantInfo.setApplicantGender(0);
        applicantInfo.setApplicantMobile("applicantMobile");
        applicantInfo.setApplicantAge(0);
        policyInfo.setApplicantInfo(applicantInfo);
        final EpProductInfoVo epProductInfoVo = new EpProductInfoVo();
        epProductInfoVo.setProductCode("productCode");
        epProductInfoVo.setProductName("productName");
        epProductInfoVo.setAdditionalRisksType(0);
        epProductInfoVo.setPremium(new BigDecimal("0.00"));
        epProductInfoVo.setCoverage(new BigDecimal("0.00"));
        epProductInfoVo.setCoverageUnit(0);
        epProductInfoVo.setCoverageUnitName("coverageUnitName");
        epProductInfoVo.setInsuredPeriodType("insuredPeriodType");
        epProductInfoVo.setInsuredPeriod(0);
        epProductInfoVo.setPeriodType("periodType");
        epProductInfoVo.setPaymentPeriodType("paymentPeriodType");
        epProductInfoVo.setPaymentPeriod(0);
        epProductInfoVo.setAnnDrawAge("drawAge");
        epProductInfoVo.setCopies(0);
        epProductInfoVo.setMainInsurance(0);
        epProductInfoVo.setPlanCode("planCode");
        epProductInfoVo.setPlanName("planName");
        policyInfo.setProductInfoList(Arrays.asList(epProductInfoVo));
        final EpInsuredInfoVo epInsuredInfoVo = new EpInsuredInfoVo();
        epInsuredInfoVo.setInsuredName("insuredName");
        epInsuredInfoVo.setInsuredGender(0);
        epInsuredInfoVo.setInsuredMobile("insuredMobile");
        epInsuredInfoVo.setInsuredIdCard("insuredIdCard");
        epInsuredInfoVo.setInsuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        epInsuredInfoVo.setInsuredPolicyAge(0);
        final EpPersonalProductInfoVo epPersonalProductInfoVo = new EpPersonalProductInfoVo();
        epPersonalProductInfoVo.setProductCode("productCode");
        epPersonalProductInfoVo.setProductName("productName");
        epPersonalProductInfoVo.setAdditionalRisksType(0);
        epPersonalProductInfoVo.setPremium(new BigDecimal("0.00"));
        epPersonalProductInfoVo.setCoverage(new BigDecimal("0.00"));
        epPersonalProductInfoVo.setCoverageUnit(0);
        epPersonalProductInfoVo.setCoverageUnitName("coverageUnitName");
        epPersonalProductInfoVo.setInsuredPeriodType("insuredPeriodType");
        epPersonalProductInfoVo.setInsuredPeriod(0);
        epPersonalProductInfoVo.setPeriodType("periodType");
        epPersonalProductInfoVo.setPaymentPeriodType("paymentPeriodType");
        epPersonalProductInfoVo.setPaymentPeriod(0);
        epPersonalProductInfoVo.setAnnDrawAge("drawAge");
        epPersonalProductInfoVo.setCopies(0);
        epPersonalProductInfoVo.setMainInsurance(0);
        epPersonalProductInfoVo.setPlanCode("planCode");
        epInsuredInfoVo.setProductInfoList(Arrays.asList(epPersonalProductInfoVo));
        policyInfo.setInsuredInfoList(Arrays.asList(epInsuredInfoVo));

        final EpPreservationV2Vo preservationDetail = new EpPreservationV2Vo();
        preservationDetail.setEndorsementNo("newEndorsementNo");
        preservationDetail.setPreservationType("preservationType");
        preservationDetail.setPreservationProject("preservationProject");
        preservationDetail.setPreservationWhy("preservationWhy");
        preservationDetail.setPreservationEffectTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        preservationDetail.setSurrenderCash(new BigDecimal("0.00"));
        preservationDetail.setRenewalTermPeriod(0);
        final EpPreserveProductVo epPreserveProductVo = new EpPreserveProductVo();
        epPreserveProductVo.setProductCode("productCode");
        epPreserveProductVo.setSurrenderCash(new BigDecimal("0.00"));
        preservationDetail.setTerminationProductList(Arrays.asList(epPreserveProductVo));

        final List<SettlementPolicyInfoEntity> expectedResult = Arrays.asList(SettlementPolicyInfoEntity.builder()
                                                                                      .id(0)
                                                                                      .settlementCode("settlementCode")
                                                                                      .premCode("premCode")
                                                                                      .reconcileType(0)
                                                                                      .settlementDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .settlementTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .settlementGenerateType(0)
                                                                                      .settlementEventCode("settlementEventCode")
                                                                                      .settlementSubjectCode("settlementSubjectCode")
                                                                                      .settlementSubjectName("settlementSubjectName")
                                                                                      .businessType(0)
                                                                                      .contractCode("contractCode")
                                                                                      .policyNo("policyNo")
                                                                                      .policyProductType("policyProductType")
                                                                                      .hesitateSurrender(0)
                                                                                      .policySource("policySource")
                                                                                      .salesType(0)
                                                                                      .selfPreservation(0)
                                                                                      .customerCode("customerCode")
                                                                                      .applicantTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .approvedTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .payableTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .realityTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .revisitStatus(0)
                                                                                      .revisitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .receiptStatus(0)
                                                                                      .receiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .applicantName("applicantName")
                                                                                      .applicantMobile("applicantMobile")
                                                                                      .applicantIdCard("applicantIdCard")
                                                                                      .applicantGender(0)
                                                                                      .applicantBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .applicantAge(0)
                                                                                      .insuredPolicyAge(0)
                                                                                      .insuredName("insuredName")
                                                                                      .insuredMobile("insuredMobile")
                                                                                      .insuredIdCard("insuredIdCard")
                                                                                      .insuredGender(0)
                                                                                      .insuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .productCode("productCode")
                                                                                      .productName("productName")
                                                                                      .protocolCode("protocolCode")
                                                                                      .protocolProductCode("insuranceProductCode")
                                                                                      .protocolProductName("protocolProductName")
                                                                                      .planCode("planCode")
                                                                                      .planName("planName")
                                                                                      .productGroup("productGroup")
                                                                                      .level2Code("level2Code")
                                                                                      .level3Code("level3Code")
                                                                                      .productType("periodType")
                                                                                      .mainInsurance(0)
                                                                                      .additionalRisksType(0)
                                                                                      .effectiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .endDate("endDate")
                                                                                      .renewalYear(2020)
                                                                                      .renewalPeriod(0)
                                                                                      .coverage(new BigDecimal("0.00"))
                                                                                      .coverageUnit(0)
                                                                                      .coverageUnitName("coverageUnitName")
                                                                                      .insuredPeriodType("insuredPeriodType")
                                                                                      .insuredPeriod(0)
                                                                                      .periodType("periodType")
                                                                                      .paymentPeriodType("paymentPeriodType")
                                                                                      .paymentPeriod(0)
                                                                                      .drawAge("drawAge")
                                                                                      .premium(new BigDecimal("0.00"))
                                                                                      .productPremiumTotal(new BigDecimal("0.00"))
                                                                                      .discountPremium(new BigDecimal("0.00"))
                                                                                      .copies(0)
                                                                                      .build());

        // Configure ProductBaseService.getProductBaseList(...).
        final ProductBase productBase = new ProductBase();
        productBase.setProductCode("productCode");
        productBase.setProductName("productName");
        productBase.setMainProductFlag(0);
        productBase.setProductType("periodType");
        productBase.setAdditionalRisksType(0);
        productBase.setLongShortFlag(0);
        productBase.setProductGroup("productGroup");
        productBase.setLevel2Code("level2Code");
        productBase.setLevel3Code("level3Code");
        final List<ProductBase> productBases = Arrays.asList(productBase);
        when(mockProductBaseService.getProductBaseList(Arrays.asList("value"))).thenReturn(productBases);

        // Configure AgentBaseService.getAgentInfoByAgentCode(...).
        final AgentInfoBasicsOut agentInfoBasicsOut = new AgentInfoBasicsOut();
        agentInfoBasicsOut.setId(0);
        agentInfoBasicsOut.setAgentName("referrerName");
        agentInfoBasicsOut.setOrgCode("channelBranchCode");
        agentInfoBasicsOut.setOrgName("channelBranchName");
        agentInfoBasicsOut.setBusinessCode("referrerWno");
        when(mockAgentBaseService.getAgentInfoByAgentCode("referrerCode", false)).thenReturn(agentInfoBasicsOut);

        // Configure OpenApiBaseService.getReferrerInfo(...).
        final ReferrerInfoVo referrerInfoVo = new ReferrerInfoVo();
        referrerInfoVo.setReferrerCode("referrerCode");
        referrerInfoVo.setReferrerWno("referrerWno");
        referrerInfoVo.setReferrerName("referrerName");
        referrerInfoVo.setReferrerOgrCode("referrerOgrCode");
        referrerInfoVo.setReferrerOgrName("referrerOgrName");
        when(mockOpenApiBaseService.getReferrerInfo("referrerCode", false)).thenReturn(referrerInfoVo);

        // Configure OpenApiBaseService.getChannelInfo(...).
        final ChannelInfoDetail channelInfoDetail = new ChannelInfoDetail();
        channelInfoDetail.setChannelCode("channelCode");
        channelInfoDetail.setChannelName("channelName");
        channelInfoDetail.setUniformSocialCreditCode("uniformSocialCreditCode");
        channelInfoDetail.setName("name");
        channelInfoDetail.setIdCard("idCard");
        when(mockOpenApiBaseService.getChannelInfo("channelCode", false)).thenReturn(channelInfoDetail);

        // Configure ProductBaseService.getProductInfo(...).
        final ProductBase productBase1 = new ProductBase();
        productBase1.setProductCode("productCode");
        productBase1.setProductName("productName");
        productBase1.setMainProductFlag(0);
        productBase1.setProductType("periodType");
        productBase1.setAdditionalRisksType(0);
        productBase1.setLongShortFlag(0);
        productBase1.setProductGroup("productGroup");
        productBase1.setLevel2Code("level2Code");
        productBase1.setLevel3Code("level3Code");
        when(mockProductBaseService.getProductInfo("productCode")).thenReturn(productBase1);

        // Run the test
        final List<SettlementPolicyInfoEntity> result = settlementPolicyInfoServiceImplUnderTest.createSettlementPolicyInfo(SettlementEventTypeEnum.PERSONAL_NEW_POLICY,
                                                                                                                            policyInfo,
                                                                                                                            preservationDetail,
                                                                                                                            0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateSettlementPolicyInfo_ProductBaseServiceGetProductBaseListReturnsNoItems() {
        // Setup
        final EpContractInfoVo policyInfo = new EpContractInfoVo();
        policyInfo.setContractCode("contractCode");
        policyInfo.setPolicyProductType("policyProductType");
        final EpContractBaseInfoVo contractBaseInfo = new EpContractBaseInfoVo();
        contractBaseInfo.setPolicyNo("policyNo");
        contractBaseInfo.setMainProductCode("productCode");
        contractBaseInfo.setSelfPreservation(0);
        contractBaseInfo.setAgentCode("mainAgentCode");
        contractBaseInfo.setOrgCode("orgCode");
        policyInfo.setContractBaseInfo(contractBaseInfo);
        final EpContractExtendInfoVo contractExtendInfo = new EpContractExtendInfoVo();
        contractExtendInfo.setIsNeedRevisit(0);
        contractExtendInfo.setRevisitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        contractExtendInfo.setEnforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        contractExtendInfo.setIsNeedReceipt(0);
        contractExtendInfo.setReceiptSignTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        policyInfo.setContractExtendInfo(contractExtendInfo);
        final EpAgentInfoVo epAgentInfoVo = new EpAgentInfoVo();
        epAgentInfoVo.setAgentCode("mainAgentCode");
        epAgentInfoVo.setOrgCode("orgCode");
        epAgentInfoVo.setMainFlag(0);
        policyInfo.setAgentInfoList(Arrays.asList(epAgentInfoVo));
        final EpPolicyChannelInfoVo channelInfo = new EpPolicyChannelInfoVo();
        channelInfo.setChannelCode("channelCode");
        channelInfo.setMiniAppCustomerCode("customerCode");
        policyInfo.setChannelInfo(channelInfo);
        final EpApplicantInfoVo applicantInfo = new EpApplicantInfoVo();
        applicantInfo.setApplicantName("applicantName");
        applicantInfo.setApplicantIdCard("applicantIdCard");
        applicantInfo.setApplicantBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        applicantInfo.setApplicantGender(0);
        applicantInfo.setApplicantMobile("applicantMobile");
        applicantInfo.setApplicantAge(0);
        policyInfo.setApplicantInfo(applicantInfo);
        final EpProductInfoVo epProductInfoVo = new EpProductInfoVo();
        epProductInfoVo.setProductCode("productCode");
        epProductInfoVo.setProductName("productName");
        epProductInfoVo.setAdditionalRisksType(0);
        epProductInfoVo.setPremium(new BigDecimal("0.00"));
        epProductInfoVo.setCoverage(new BigDecimal("0.00"));
        epProductInfoVo.setCoverageUnit(0);
        epProductInfoVo.setCoverageUnitName("coverageUnitName");
        epProductInfoVo.setInsuredPeriodType("insuredPeriodType");
        epProductInfoVo.setInsuredPeriod(0);
        epProductInfoVo.setPeriodType("periodType");
        epProductInfoVo.setPaymentPeriodType("paymentPeriodType");
        epProductInfoVo.setPaymentPeriod(0);
        epProductInfoVo.setAnnDrawAge("drawAge");
        epProductInfoVo.setCopies(0);
        epProductInfoVo.setMainInsurance(0);
        epProductInfoVo.setPlanCode("planCode");
        epProductInfoVo.setPlanName("planName");
        policyInfo.setProductInfoList(Arrays.asList(epProductInfoVo));
        final EpInsuredInfoVo epInsuredInfoVo = new EpInsuredInfoVo();
        epInsuredInfoVo.setInsuredName("insuredName");
        epInsuredInfoVo.setInsuredGender(0);
        epInsuredInfoVo.setInsuredMobile("insuredMobile");
        epInsuredInfoVo.setInsuredIdCard("insuredIdCard");
        epInsuredInfoVo.setInsuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        epInsuredInfoVo.setInsuredPolicyAge(0);
        final EpPersonalProductInfoVo epPersonalProductInfoVo = new EpPersonalProductInfoVo();
        epPersonalProductInfoVo.setProductCode("productCode");
        epPersonalProductInfoVo.setProductName("productName");
        epPersonalProductInfoVo.setAdditionalRisksType(0);
        epPersonalProductInfoVo.setPremium(new BigDecimal("0.00"));
        epPersonalProductInfoVo.setCoverage(new BigDecimal("0.00"));
        epPersonalProductInfoVo.setCoverageUnit(0);
        epPersonalProductInfoVo.setCoverageUnitName("coverageUnitName");
        epPersonalProductInfoVo.setInsuredPeriodType("insuredPeriodType");
        epPersonalProductInfoVo.setInsuredPeriod(0);
        epPersonalProductInfoVo.setPeriodType("periodType");
        epPersonalProductInfoVo.setPaymentPeriodType("paymentPeriodType");
        epPersonalProductInfoVo.setPaymentPeriod(0);
        epPersonalProductInfoVo.setAnnDrawAge("drawAge");
        epPersonalProductInfoVo.setCopies(0);
        epPersonalProductInfoVo.setMainInsurance(0);
        epPersonalProductInfoVo.setPlanCode("planCode");
        epInsuredInfoVo.setProductInfoList(Arrays.asList(epPersonalProductInfoVo));
        policyInfo.setInsuredInfoList(Arrays.asList(epInsuredInfoVo));

        final EpPreservationV2Vo preservationDetail = new EpPreservationV2Vo();
        preservationDetail.setEndorsementNo("newEndorsementNo");
        preservationDetail.setPreservationType("preservationType");
        preservationDetail.setPreservationProject("preservationProject");
        preservationDetail.setPreservationWhy("preservationWhy");
        preservationDetail.setPreservationEffectTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        preservationDetail.setSurrenderCash(new BigDecimal("0.00"));
        preservationDetail.setRenewalTermPeriod(0);
        final EpPreserveProductVo epPreserveProductVo = new EpPreserveProductVo();
        epPreserveProductVo.setProductCode("productCode");
        epPreserveProductVo.setSurrenderCash(new BigDecimal("0.00"));
        preservationDetail.setTerminationProductList(Arrays.asList(epPreserveProductVo));

        when(mockProductBaseService.getProductBaseList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure AgentBaseService.getAgentInfoByAgentCode(...).
        final AgentInfoBasicsOut agentInfoBasicsOut = new AgentInfoBasicsOut();
        agentInfoBasicsOut.setId(0);
        agentInfoBasicsOut.setAgentName("referrerName");
        agentInfoBasicsOut.setOrgCode("channelBranchCode");
        agentInfoBasicsOut.setOrgName("channelBranchName");
        agentInfoBasicsOut.setBusinessCode("referrerWno");
        when(mockAgentBaseService.getAgentInfoByAgentCode("referrerCode", false)).thenReturn(agentInfoBasicsOut);

        // Configure OpenApiBaseService.getReferrerInfo(...).
        final ReferrerInfoVo referrerInfoVo = new ReferrerInfoVo();
        referrerInfoVo.setReferrerCode("referrerCode");
        referrerInfoVo.setReferrerWno("referrerWno");
        referrerInfoVo.setReferrerName("referrerName");
        referrerInfoVo.setReferrerOgrCode("referrerOgrCode");
        referrerInfoVo.setReferrerOgrName("referrerOgrName");
        when(mockOpenApiBaseService.getReferrerInfo("referrerCode", false)).thenReturn(referrerInfoVo);

        // Configure OpenApiBaseService.getChannelInfo(...).
        final ChannelInfoDetail channelInfoDetail = new ChannelInfoDetail();
        channelInfoDetail.setChannelCode("channelCode");
        channelInfoDetail.setChannelName("channelName");
        channelInfoDetail.setUniformSocialCreditCode("uniformSocialCreditCode");
        channelInfoDetail.setName("name");
        channelInfoDetail.setIdCard("idCard");
        when(mockOpenApiBaseService.getChannelInfo("channelCode", false)).thenReturn(channelInfoDetail);

        // Configure ProductBaseService.getProductInfo(...).
        final ProductBase productBase = new ProductBase();
        productBase.setProductCode("productCode");
        productBase.setProductName("productName");
        productBase.setMainProductFlag(0);
        productBase.setProductType("periodType");
        productBase.setAdditionalRisksType(0);
        productBase.setLongShortFlag(0);
        productBase.setProductGroup("productGroup");
        productBase.setLevel2Code("level2Code");
        productBase.setLevel3Code("level3Code");
        when(mockProductBaseService.getProductInfo("productCode")).thenReturn(productBase);

        // Run the test
        final List<SettlementPolicyInfoEntity> result = settlementPolicyInfoServiceImplUnderTest.createSettlementPolicyInfo(SettlementEventTypeEnum.PERSONAL_NEW_POLICY,
                                                                                                                            policyInfo,
                                                                                                                            preservationDetail,
                                                                                                                            0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testCreateSettlementPolicyInfo_AgentBaseServiceReturnsNull() {
        // Setup
        final EpContractInfoVo policyInfo = new EpContractInfoVo();
        policyInfo.setContractCode("contractCode");
        policyInfo.setPolicyProductType("policyProductType");
        final EpContractBaseInfoVo contractBaseInfo = new EpContractBaseInfoVo();
        contractBaseInfo.setPolicyNo("policyNo");
        contractBaseInfo.setMainProductCode("productCode");
        contractBaseInfo.setSelfPreservation(0);
        contractBaseInfo.setAgentCode("mainAgentCode");
        contractBaseInfo.setOrgCode("orgCode");
        policyInfo.setContractBaseInfo(contractBaseInfo);
        final EpContractExtendInfoVo contractExtendInfo = new EpContractExtendInfoVo();
        contractExtendInfo.setIsNeedRevisit(0);
        contractExtendInfo.setRevisitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        contractExtendInfo.setEnforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        contractExtendInfo.setIsNeedReceipt(0);
        contractExtendInfo.setReceiptSignTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        policyInfo.setContractExtendInfo(contractExtendInfo);
        final EpAgentInfoVo epAgentInfoVo = new EpAgentInfoVo();
        epAgentInfoVo.setAgentCode("mainAgentCode");
        epAgentInfoVo.setOrgCode("orgCode");
        epAgentInfoVo.setMainFlag(0);
        policyInfo.setAgentInfoList(Arrays.asList(epAgentInfoVo));
        final EpPolicyChannelInfoVo channelInfo = new EpPolicyChannelInfoVo();
        channelInfo.setChannelCode("channelCode");
        channelInfo.setMiniAppCustomerCode("customerCode");
        policyInfo.setChannelInfo(channelInfo);
        final EpApplicantInfoVo applicantInfo = new EpApplicantInfoVo();
        applicantInfo.setApplicantName("applicantName");
        applicantInfo.setApplicantIdCard("applicantIdCard");
        applicantInfo.setApplicantBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        applicantInfo.setApplicantGender(0);
        applicantInfo.setApplicantMobile("applicantMobile");
        applicantInfo.setApplicantAge(0);
        policyInfo.setApplicantInfo(applicantInfo);
        final EpProductInfoVo epProductInfoVo = new EpProductInfoVo();
        epProductInfoVo.setProductCode("productCode");
        epProductInfoVo.setProductName("productName");
        epProductInfoVo.setAdditionalRisksType(0);
        epProductInfoVo.setPremium(new BigDecimal("0.00"));
        epProductInfoVo.setCoverage(new BigDecimal("0.00"));
        epProductInfoVo.setCoverageUnit(0);
        epProductInfoVo.setCoverageUnitName("coverageUnitName");
        epProductInfoVo.setInsuredPeriodType("insuredPeriodType");
        epProductInfoVo.setInsuredPeriod(0);
        epProductInfoVo.setPeriodType("periodType");
        epProductInfoVo.setPaymentPeriodType("paymentPeriodType");
        epProductInfoVo.setPaymentPeriod(0);
        epProductInfoVo.setAnnDrawAge("drawAge");
        epProductInfoVo.setCopies(0);
        epProductInfoVo.setMainInsurance(0);
        epProductInfoVo.setPlanCode("planCode");
        epProductInfoVo.setPlanName("planName");
        policyInfo.setProductInfoList(Arrays.asList(epProductInfoVo));
        final EpInsuredInfoVo epInsuredInfoVo = new EpInsuredInfoVo();
        epInsuredInfoVo.setInsuredName("insuredName");
        epInsuredInfoVo.setInsuredGender(0);
        epInsuredInfoVo.setInsuredMobile("insuredMobile");
        epInsuredInfoVo.setInsuredIdCard("insuredIdCard");
        epInsuredInfoVo.setInsuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        epInsuredInfoVo.setInsuredPolicyAge(0);
        final EpPersonalProductInfoVo epPersonalProductInfoVo = new EpPersonalProductInfoVo();
        epPersonalProductInfoVo.setProductCode("productCode");
        epPersonalProductInfoVo.setProductName("productName");
        epPersonalProductInfoVo.setAdditionalRisksType(0);
        epPersonalProductInfoVo.setPremium(new BigDecimal("0.00"));
        epPersonalProductInfoVo.setCoverage(new BigDecimal("0.00"));
        epPersonalProductInfoVo.setCoverageUnit(0);
        epPersonalProductInfoVo.setCoverageUnitName("coverageUnitName");
        epPersonalProductInfoVo.setInsuredPeriodType("insuredPeriodType");
        epPersonalProductInfoVo.setInsuredPeriod(0);
        epPersonalProductInfoVo.setPeriodType("periodType");
        epPersonalProductInfoVo.setPaymentPeriodType("paymentPeriodType");
        epPersonalProductInfoVo.setPaymentPeriod(0);
        epPersonalProductInfoVo.setAnnDrawAge("drawAge");
        epPersonalProductInfoVo.setCopies(0);
        epPersonalProductInfoVo.setMainInsurance(0);
        epPersonalProductInfoVo.setPlanCode("planCode");
        epInsuredInfoVo.setProductInfoList(Arrays.asList(epPersonalProductInfoVo));
        policyInfo.setInsuredInfoList(Arrays.asList(epInsuredInfoVo));

        final EpPreservationV2Vo preservationDetail = new EpPreservationV2Vo();
        preservationDetail.setEndorsementNo("newEndorsementNo");
        preservationDetail.setPreservationType("preservationType");
        preservationDetail.setPreservationProject("preservationProject");
        preservationDetail.setPreservationWhy("preservationWhy");
        preservationDetail.setPreservationEffectTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        preservationDetail.setSurrenderCash(new BigDecimal("0.00"));
        preservationDetail.setRenewalTermPeriod(0);
        final EpPreserveProductVo epPreserveProductVo = new EpPreserveProductVo();
        epPreserveProductVo.setProductCode("productCode");
        epPreserveProductVo.setSurrenderCash(new BigDecimal("0.00"));
        preservationDetail.setTerminationProductList(Arrays.asList(epPreserveProductVo));

        final List<SettlementPolicyInfoEntity> expectedResult = Arrays.asList(SettlementPolicyInfoEntity.builder()
                                                                                      .id(0)
                                                                                      .settlementCode("settlementCode")
                                                                                      .premCode("premCode")
                                                                                      .reconcileType(0)
                                                                                      .settlementDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .settlementTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .settlementGenerateType(0)
                                                                                      .settlementEventCode("settlementEventCode")
                                                                                      .settlementSubjectCode("settlementSubjectCode")
                                                                                      .settlementSubjectName("settlementSubjectName")
                                                                                      .businessType(0)
                                                                                      .contractCode("contractCode")
                                                                                      .policyNo("policyNo")
                                                                                      .policyProductType("policyProductType")
                                                                                      .hesitateSurrender(0)
                                                                                      .policySource("policySource")
                                                                                      .salesType(0)
                                                                                      .selfPreservation(0)
                                                                                      .customerCode("customerCode")
                                                                                      .applicantTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .approvedTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .payableTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .realityTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .revisitStatus(0)
                                                                                      .revisitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .receiptStatus(0)
                                                                                      .receiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .applicantName("applicantName")
                                                                                      .applicantMobile("applicantMobile")
                                                                                      .applicantIdCard("applicantIdCard")
                                                                                      .applicantGender(0)
                                                                                      .applicantBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .applicantAge(0)
                                                                                      .insuredPolicyAge(0)
                                                                                      .insuredName("insuredName")
                                                                                      .insuredMobile("insuredMobile")
                                                                                      .insuredIdCard("insuredIdCard")
                                                                                      .insuredGender(0)
                                                                                      .insuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .productCode("productCode")
                                                                                      .productName("productName")
                                                                                      .protocolCode("protocolCode")
                                                                                      .protocolProductCode("insuranceProductCode")
                                                                                      .protocolProductName("protocolProductName")
                                                                                      .planCode("planCode")
                                                                                      .planName("planName")
                                                                                      .productGroup("productGroup")
                                                                                      .level2Code("level2Code")
                                                                                      .level3Code("level3Code")
                                                                                      .productType("periodType")
                                                                                      .mainInsurance(0)
                                                                                      .additionalRisksType(0)
                                                                                      .effectiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .endDate("endDate")
                                                                                      .renewalYear(2020)
                                                                                      .renewalPeriod(0)
                                                                                      .coverage(new BigDecimal("0.00"))
                                                                                      .coverageUnit(0)
                                                                                      .coverageUnitName("coverageUnitName")
                                                                                      .insuredPeriodType("insuredPeriodType")
                                                                                      .insuredPeriod(0)
                                                                                      .periodType("periodType")
                                                                                      .paymentPeriodType("paymentPeriodType")
                                                                                      .paymentPeriod(0)
                                                                                      .drawAge("drawAge")
                                                                                      .premium(new BigDecimal("0.00"))
                                                                                      .productPremiumTotal(new BigDecimal("0.00"))
                                                                                      .discountPremium(new BigDecimal("0.00"))
                                                                                      .copies(0)
                                                                                      .build());

        // Configure ProductBaseService.getProductBaseList(...).
        final ProductBase productBase = new ProductBase();
        productBase.setProductCode("productCode");
        productBase.setProductName("productName");
        productBase.setMainProductFlag(0);
        productBase.setProductType("periodType");
        productBase.setAdditionalRisksType(0);
        productBase.setLongShortFlag(0);
        productBase.setProductGroup("productGroup");
        productBase.setLevel2Code("level2Code");
        productBase.setLevel3Code("level3Code");
        final List<ProductBase> productBases = Arrays.asList(productBase);
        when(mockProductBaseService.getProductBaseList(Arrays.asList("value"))).thenReturn(productBases);

        when(mockAgentBaseService.getAgentInfoByAgentCode("referrerCode", false)).thenReturn(null);

        // Configure OpenApiBaseService.getChannelInfo(...).
        final ChannelInfoDetail channelInfoDetail = new ChannelInfoDetail();
        channelInfoDetail.setChannelCode("channelCode");
        channelInfoDetail.setChannelName("channelName");
        channelInfoDetail.setUniformSocialCreditCode("uniformSocialCreditCode");
        channelInfoDetail.setName("name");
        channelInfoDetail.setIdCard("idCard");
        when(mockOpenApiBaseService.getChannelInfo("channelCode", false)).thenReturn(channelInfoDetail);

        // Configure ProductBaseService.getProductInfo(...).
        final ProductBase productBase1 = new ProductBase();
        productBase1.setProductCode("productCode");
        productBase1.setProductName("productName");
        productBase1.setMainProductFlag(0);
        productBase1.setProductType("periodType");
        productBase1.setAdditionalRisksType(0);
        productBase1.setLongShortFlag(0);
        productBase1.setProductGroup("productGroup");
        productBase1.setLevel2Code("level2Code");
        productBase1.setLevel3Code("level3Code");
        when(mockProductBaseService.getProductInfo("productCode")).thenReturn(productBase1);

        // Run the test
        final List<SettlementPolicyInfoEntity> result = settlementPolicyInfoServiceImplUnderTest.createSettlementPolicyInfo(SettlementEventTypeEnum.PERSONAL_NEW_POLICY,
                                                                                                                            policyInfo,
                                                                                                                            preservationDetail,
                                                                                                                            0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateSettlementPolicyInfo_OpenApiBaseServiceGetReferrerInfoReturnsNull() {
        // Setup
        final EpContractInfoVo policyInfo = new EpContractInfoVo();
        policyInfo.setContractCode("contractCode");
        policyInfo.setPolicyProductType("policyProductType");
        final EpContractBaseInfoVo contractBaseInfo = new EpContractBaseInfoVo();
        contractBaseInfo.setPolicyNo("policyNo");
        contractBaseInfo.setMainProductCode("productCode");
        contractBaseInfo.setSelfPreservation(0);
        contractBaseInfo.setAgentCode("mainAgentCode");
        contractBaseInfo.setOrgCode("orgCode");
        policyInfo.setContractBaseInfo(contractBaseInfo);
        final EpContractExtendInfoVo contractExtendInfo = new EpContractExtendInfoVo();
        contractExtendInfo.setIsNeedRevisit(0);
        contractExtendInfo.setRevisitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        contractExtendInfo.setEnforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        contractExtendInfo.setIsNeedReceipt(0);
        contractExtendInfo.setReceiptSignTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        policyInfo.setContractExtendInfo(contractExtendInfo);
        final EpAgentInfoVo epAgentInfoVo = new EpAgentInfoVo();
        epAgentInfoVo.setAgentCode("mainAgentCode");
        epAgentInfoVo.setOrgCode("orgCode");
        epAgentInfoVo.setMainFlag(0);
        policyInfo.setAgentInfoList(Arrays.asList(epAgentInfoVo));
        final EpPolicyChannelInfoVo channelInfo = new EpPolicyChannelInfoVo();
        channelInfo.setChannelCode("channelCode");
        channelInfo.setMiniAppCustomerCode("customerCode");
        policyInfo.setChannelInfo(channelInfo);
        final EpApplicantInfoVo applicantInfo = new EpApplicantInfoVo();
        applicantInfo.setApplicantName("applicantName");
        applicantInfo.setApplicantIdCard("applicantIdCard");
        applicantInfo.setApplicantBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        applicantInfo.setApplicantGender(0);
        applicantInfo.setApplicantMobile("applicantMobile");
        applicantInfo.setApplicantAge(0);
        policyInfo.setApplicantInfo(applicantInfo);
        final EpProductInfoVo epProductInfoVo = new EpProductInfoVo();
        epProductInfoVo.setProductCode("productCode");
        epProductInfoVo.setProductName("productName");
        epProductInfoVo.setAdditionalRisksType(0);
        epProductInfoVo.setPremium(new BigDecimal("0.00"));
        epProductInfoVo.setCoverage(new BigDecimal("0.00"));
        epProductInfoVo.setCoverageUnit(0);
        epProductInfoVo.setCoverageUnitName("coverageUnitName");
        epProductInfoVo.setInsuredPeriodType("insuredPeriodType");
        epProductInfoVo.setInsuredPeriod(0);
        epProductInfoVo.setPeriodType("periodType");
        epProductInfoVo.setPaymentPeriodType("paymentPeriodType");
        epProductInfoVo.setPaymentPeriod(0);
        epProductInfoVo.setAnnDrawAge("drawAge");
        epProductInfoVo.setCopies(0);
        epProductInfoVo.setMainInsurance(0);
        epProductInfoVo.setPlanCode("planCode");
        epProductInfoVo.setPlanName("planName");
        policyInfo.setProductInfoList(Arrays.asList(epProductInfoVo));
        final EpInsuredInfoVo epInsuredInfoVo = new EpInsuredInfoVo();
        epInsuredInfoVo.setInsuredName("insuredName");
        epInsuredInfoVo.setInsuredGender(0);
        epInsuredInfoVo.setInsuredMobile("insuredMobile");
        epInsuredInfoVo.setInsuredIdCard("insuredIdCard");
        epInsuredInfoVo.setInsuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        epInsuredInfoVo.setInsuredPolicyAge(0);
        final EpPersonalProductInfoVo epPersonalProductInfoVo = new EpPersonalProductInfoVo();
        epPersonalProductInfoVo.setProductCode("productCode");
        epPersonalProductInfoVo.setProductName("productName");
        epPersonalProductInfoVo.setAdditionalRisksType(0);
        epPersonalProductInfoVo.setPremium(new BigDecimal("0.00"));
        epPersonalProductInfoVo.setCoverage(new BigDecimal("0.00"));
        epPersonalProductInfoVo.setCoverageUnit(0);
        epPersonalProductInfoVo.setCoverageUnitName("coverageUnitName");
        epPersonalProductInfoVo.setInsuredPeriodType("insuredPeriodType");
        epPersonalProductInfoVo.setInsuredPeriod(0);
        epPersonalProductInfoVo.setPeriodType("periodType");
        epPersonalProductInfoVo.setPaymentPeriodType("paymentPeriodType");
        epPersonalProductInfoVo.setPaymentPeriod(0);
        epPersonalProductInfoVo.setAnnDrawAge("drawAge");
        epPersonalProductInfoVo.setCopies(0);
        epPersonalProductInfoVo.setMainInsurance(0);
        epPersonalProductInfoVo.setPlanCode("planCode");
        epInsuredInfoVo.setProductInfoList(Arrays.asList(epPersonalProductInfoVo));
        policyInfo.setInsuredInfoList(Arrays.asList(epInsuredInfoVo));

        final EpPreservationV2Vo preservationDetail = new EpPreservationV2Vo();
        preservationDetail.setEndorsementNo("newEndorsementNo");
        preservationDetail.setPreservationType("preservationType");
        preservationDetail.setPreservationProject("preservationProject");
        preservationDetail.setPreservationWhy("preservationWhy");
        preservationDetail.setPreservationEffectTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        preservationDetail.setSurrenderCash(new BigDecimal("0.00"));
        preservationDetail.setRenewalTermPeriod(0);
        final EpPreserveProductVo epPreserveProductVo = new EpPreserveProductVo();
        epPreserveProductVo.setProductCode("productCode");
        epPreserveProductVo.setSurrenderCash(new BigDecimal("0.00"));
        preservationDetail.setTerminationProductList(Arrays.asList(epPreserveProductVo));

        final List<SettlementPolicyInfoEntity> expectedResult = Arrays.asList(SettlementPolicyInfoEntity.builder()
                                                                                      .id(0)
                                                                                      .settlementCode("settlementCode")
                                                                                      .premCode("premCode")
                                                                                      .reconcileType(0)
                                                                                      .settlementDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .settlementTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .settlementGenerateType(0)
                                                                                      .settlementEventCode("settlementEventCode")
                                                                                      .settlementSubjectCode("settlementSubjectCode")
                                                                                      .settlementSubjectName("settlementSubjectName")
                                                                                      .businessType(0)
                                                                                      .contractCode("contractCode")
                                                                                      .policyNo("policyNo")
                                                                                      .policyProductType("policyProductType")
                                                                                      .hesitateSurrender(0)
                                                                                      .policySource("policySource")
                                                                                      .salesType(0)
                                                                                      .selfPreservation(0)
                                                                                      .customerCode("customerCode")
                                                                                      .applicantTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .approvedTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .payableTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .realityTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .revisitStatus(0)
                                                                                      .revisitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .receiptStatus(0)
                                                                                      .receiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .applicantName("applicantName")
                                                                                      .applicantMobile("applicantMobile")
                                                                                      .applicantIdCard("applicantIdCard")
                                                                                      .applicantGender(0)
                                                                                      .applicantBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .applicantAge(0)
                                                                                      .insuredPolicyAge(0)
                                                                                      .insuredName("insuredName")
                                                                                      .insuredMobile("insuredMobile")
                                                                                      .insuredIdCard("insuredIdCard")
                                                                                      .insuredGender(0)
                                                                                      .insuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .productCode("productCode")
                                                                                      .productName("productName")
                                                                                      .protocolCode("protocolCode")
                                                                                      .protocolProductCode("insuranceProductCode")
                                                                                      .protocolProductName("protocolProductName")
                                                                                      .planCode("planCode")
                                                                                      .planName("planName")
                                                                                      .productGroup("productGroup")
                                                                                      .level2Code("level2Code")
                                                                                      .level3Code("level3Code")
                                                                                      .productType("periodType")
                                                                                      .mainInsurance(0)
                                                                                      .additionalRisksType(0)
                                                                                      .effectiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .endDate("endDate")
                                                                                      .renewalYear(2020)
                                                                                      .renewalPeriod(0)
                                                                                      .coverage(new BigDecimal("0.00"))
                                                                                      .coverageUnit(0)
                                                                                      .coverageUnitName("coverageUnitName")
                                                                                      .insuredPeriodType("insuredPeriodType")
                                                                                      .insuredPeriod(0)
                                                                                      .periodType("periodType")
                                                                                      .paymentPeriodType("paymentPeriodType")
                                                                                      .paymentPeriod(0)
                                                                                      .drawAge("drawAge")
                                                                                      .premium(new BigDecimal("0.00"))
                                                                                      .productPremiumTotal(new BigDecimal("0.00"))
                                                                                      .discountPremium(new BigDecimal("0.00"))
                                                                                      .copies(0)
                                                                                      .build());

        // Configure ProductBaseService.getProductBaseList(...).
        final ProductBase productBase = new ProductBase();
        productBase.setProductCode("productCode");
        productBase.setProductName("productName");
        productBase.setMainProductFlag(0);
        productBase.setProductType("periodType");
        productBase.setAdditionalRisksType(0);
        productBase.setLongShortFlag(0);
        productBase.setProductGroup("productGroup");
        productBase.setLevel2Code("level2Code");
        productBase.setLevel3Code("level3Code");
        final List<ProductBase> productBases = Arrays.asList(productBase);
        when(mockProductBaseService.getProductBaseList(Arrays.asList("value"))).thenReturn(productBases);

        when(mockOpenApiBaseService.getReferrerInfo("referrerCode", false)).thenReturn(null);

        // Configure OpenApiBaseService.getChannelInfo(...).
        final ChannelInfoDetail channelInfoDetail = new ChannelInfoDetail();
        channelInfoDetail.setChannelCode("channelCode");
        channelInfoDetail.setChannelName("channelName");
        channelInfoDetail.setUniformSocialCreditCode("uniformSocialCreditCode");
        channelInfoDetail.setName("name");
        channelInfoDetail.setIdCard("idCard");
        when(mockOpenApiBaseService.getChannelInfo("channelCode", false)).thenReturn(channelInfoDetail);

        // Configure ProductBaseService.getProductInfo(...).
        final ProductBase productBase1 = new ProductBase();
        productBase1.setProductCode("productCode");
        productBase1.setProductName("productName");
        productBase1.setMainProductFlag(0);
        productBase1.setProductType("periodType");
        productBase1.setAdditionalRisksType(0);
        productBase1.setLongShortFlag(0);
        productBase1.setProductGroup("productGroup");
        productBase1.setLevel2Code("level2Code");
        productBase1.setLevel3Code("level3Code");
        when(mockProductBaseService.getProductInfo("productCode")).thenReturn(productBase1);

        // Run the test
        final List<SettlementPolicyInfoEntity> result = settlementPolicyInfoServiceImplUnderTest.createSettlementPolicyInfo(SettlementEventTypeEnum.PERSONAL_NEW_POLICY,
                                                                                                                            policyInfo,
                                                                                                                            preservationDetail,
                                                                                                                            0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateSettlementPolicyInfo_OpenApiBaseServiceGetChannelInfoReturnsNull() {
        // Setup
        final EpContractInfoVo policyInfo = new EpContractInfoVo();
        policyInfo.setContractCode("contractCode");
        policyInfo.setPolicyProductType("policyProductType");
        final EpContractBaseInfoVo contractBaseInfo = new EpContractBaseInfoVo();
        contractBaseInfo.setPolicyNo("policyNo");
        contractBaseInfo.setMainProductCode("productCode");
        contractBaseInfo.setSelfPreservation(0);
        contractBaseInfo.setAgentCode("mainAgentCode");
        contractBaseInfo.setOrgCode("orgCode");
        policyInfo.setContractBaseInfo(contractBaseInfo);
        final EpContractExtendInfoVo contractExtendInfo = new EpContractExtendInfoVo();
        contractExtendInfo.setIsNeedRevisit(0);
        contractExtendInfo.setRevisitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        contractExtendInfo.setEnforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        contractExtendInfo.setIsNeedReceipt(0);
        contractExtendInfo.setReceiptSignTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        policyInfo.setContractExtendInfo(contractExtendInfo);
        final EpAgentInfoVo epAgentInfoVo = new EpAgentInfoVo();
        epAgentInfoVo.setAgentCode("mainAgentCode");
        epAgentInfoVo.setOrgCode("orgCode");
        epAgentInfoVo.setMainFlag(0);
        policyInfo.setAgentInfoList(Arrays.asList(epAgentInfoVo));
        final EpPolicyChannelInfoVo channelInfo = new EpPolicyChannelInfoVo();
        channelInfo.setChannelCode("channelCode");
        channelInfo.setMiniAppCustomerCode("customerCode");
        policyInfo.setChannelInfo(channelInfo);
        final EpApplicantInfoVo applicantInfo = new EpApplicantInfoVo();
        applicantInfo.setApplicantName("applicantName");
        applicantInfo.setApplicantIdCard("applicantIdCard");
        applicantInfo.setApplicantBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        applicantInfo.setApplicantGender(0);
        applicantInfo.setApplicantMobile("applicantMobile");
        applicantInfo.setApplicantAge(0);
        policyInfo.setApplicantInfo(applicantInfo);
        final EpProductInfoVo epProductInfoVo = new EpProductInfoVo();
        epProductInfoVo.setProductCode("productCode");
        epProductInfoVo.setProductName("productName");
        epProductInfoVo.setAdditionalRisksType(0);
        epProductInfoVo.setPremium(new BigDecimal("0.00"));
        epProductInfoVo.setCoverage(new BigDecimal("0.00"));
        epProductInfoVo.setCoverageUnit(0);
        epProductInfoVo.setCoverageUnitName("coverageUnitName");
        epProductInfoVo.setInsuredPeriodType("insuredPeriodType");
        epProductInfoVo.setInsuredPeriod(0);
        epProductInfoVo.setPeriodType("periodType");
        epProductInfoVo.setPaymentPeriodType("paymentPeriodType");
        epProductInfoVo.setPaymentPeriod(0);
        epProductInfoVo.setAnnDrawAge("drawAge");
        epProductInfoVo.setCopies(0);
        epProductInfoVo.setMainInsurance(0);
        epProductInfoVo.setPlanCode("planCode");
        epProductInfoVo.setPlanName("planName");
        policyInfo.setProductInfoList(Arrays.asList(epProductInfoVo));
        final EpInsuredInfoVo epInsuredInfoVo = new EpInsuredInfoVo();
        epInsuredInfoVo.setInsuredName("insuredName");
        epInsuredInfoVo.setInsuredGender(0);
        epInsuredInfoVo.setInsuredMobile("insuredMobile");
        epInsuredInfoVo.setInsuredIdCard("insuredIdCard");
        epInsuredInfoVo.setInsuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        epInsuredInfoVo.setInsuredPolicyAge(0);
        final EpPersonalProductInfoVo epPersonalProductInfoVo = new EpPersonalProductInfoVo();
        epPersonalProductInfoVo.setProductCode("productCode");
        epPersonalProductInfoVo.setProductName("productName");
        epPersonalProductInfoVo.setAdditionalRisksType(0);
        epPersonalProductInfoVo.setPremium(new BigDecimal("0.00"));
        epPersonalProductInfoVo.setCoverage(new BigDecimal("0.00"));
        epPersonalProductInfoVo.setCoverageUnit(0);
        epPersonalProductInfoVo.setCoverageUnitName("coverageUnitName");
        epPersonalProductInfoVo.setInsuredPeriodType("insuredPeriodType");
        epPersonalProductInfoVo.setInsuredPeriod(0);
        epPersonalProductInfoVo.setPeriodType("periodType");
        epPersonalProductInfoVo.setPaymentPeriodType("paymentPeriodType");
        epPersonalProductInfoVo.setPaymentPeriod(0);
        epPersonalProductInfoVo.setAnnDrawAge("drawAge");
        epPersonalProductInfoVo.setCopies(0);
        epPersonalProductInfoVo.setMainInsurance(0);
        epPersonalProductInfoVo.setPlanCode("planCode");
        epInsuredInfoVo.setProductInfoList(Arrays.asList(epPersonalProductInfoVo));
        policyInfo.setInsuredInfoList(Arrays.asList(epInsuredInfoVo));

        final EpPreservationV2Vo preservationDetail = new EpPreservationV2Vo();
        preservationDetail.setEndorsementNo("newEndorsementNo");
        preservationDetail.setPreservationType("preservationType");
        preservationDetail.setPreservationProject("preservationProject");
        preservationDetail.setPreservationWhy("preservationWhy");
        preservationDetail.setPreservationEffectTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        preservationDetail.setSurrenderCash(new BigDecimal("0.00"));
        preservationDetail.setRenewalTermPeriod(0);
        final EpPreserveProductVo epPreserveProductVo = new EpPreserveProductVo();
        epPreserveProductVo.setProductCode("productCode");
        epPreserveProductVo.setSurrenderCash(new BigDecimal("0.00"));
        preservationDetail.setTerminationProductList(Arrays.asList(epPreserveProductVo));

        final List<SettlementPolicyInfoEntity> expectedResult = Arrays.asList(SettlementPolicyInfoEntity.builder()
                                                                                      .id(0)
                                                                                      .settlementCode("settlementCode")
                                                                                      .premCode("premCode")
                                                                                      .reconcileType(0)
                                                                                      .settlementDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .settlementTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .settlementGenerateType(0)
                                                                                      .settlementEventCode("settlementEventCode")
                                                                                      .settlementSubjectCode("settlementSubjectCode")
                                                                                      .settlementSubjectName("settlementSubjectName")
                                                                                      .businessType(0)
                                                                                      .contractCode("contractCode")
                                                                                      .policyNo("policyNo")
                                                                                      .policyProductType("policyProductType")
                                                                                      .hesitateSurrender(0)
                                                                                      .policySource("policySource")
                                                                                      .salesType(0)
                                                                                      .selfPreservation(0)
                                                                                      .customerCode("customerCode")
                                                                                      .applicantTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .approvedTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .payableTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .realityTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .revisitStatus(0)
                                                                                      .revisitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .receiptStatus(0)
                                                                                      .receiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .applicantName("applicantName")
                                                                                      .applicantMobile("applicantMobile")
                                                                                      .applicantIdCard("applicantIdCard")
                                                                                      .applicantGender(0)
                                                                                      .applicantBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .applicantAge(0)
                                                                                      .insuredPolicyAge(0)
                                                                                      .insuredName("insuredName")
                                                                                      .insuredMobile("insuredMobile")
                                                                                      .insuredIdCard("insuredIdCard")
                                                                                      .insuredGender(0)
                                                                                      .insuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .productCode("productCode")
                                                                                      .productName("productName")
                                                                                      .protocolCode("protocolCode")
                                                                                      .protocolProductCode("insuranceProductCode")
                                                                                      .protocolProductName("protocolProductName")
                                                                                      .planCode("planCode")
                                                                                      .planName("planName")
                                                                                      .productGroup("productGroup")
                                                                                      .level2Code("level2Code")
                                                                                      .level3Code("level3Code")
                                                                                      .productType("periodType")
                                                                                      .mainInsurance(0)
                                                                                      .additionalRisksType(0)
                                                                                      .effectiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                      .endDate("endDate")
                                                                                      .renewalYear(2020)
                                                                                      .renewalPeriod(0)
                                                                                      .coverage(new BigDecimal("0.00"))
                                                                                      .coverageUnit(0)
                                                                                      .coverageUnitName("coverageUnitName")
                                                                                      .insuredPeriodType("insuredPeriodType")
                                                                                      .insuredPeriod(0)
                                                                                      .periodType("periodType")
                                                                                      .paymentPeriodType("paymentPeriodType")
                                                                                      .paymentPeriod(0)
                                                                                      .drawAge("drawAge")
                                                                                      .premium(new BigDecimal("0.00"))
                                                                                      .productPremiumTotal(new BigDecimal("0.00"))
                                                                                      .discountPremium(new BigDecimal("0.00"))
                                                                                      .copies(0)
                                                                                      .build());

        // Configure ProductBaseService.getProductBaseList(...).
        final ProductBase productBase = new ProductBase();
        productBase.setProductCode("productCode");
        productBase.setProductName("productName");
        productBase.setMainProductFlag(0);
        productBase.setProductType("periodType");
        productBase.setAdditionalRisksType(0);
        productBase.setLongShortFlag(0);
        productBase.setProductGroup("productGroup");
        productBase.setLevel2Code("level2Code");
        productBase.setLevel3Code("level3Code");
        final List<ProductBase> productBases = Arrays.asList(productBase);
        when(mockProductBaseService.getProductBaseList(Arrays.asList("value"))).thenReturn(productBases);

        // Configure AgentBaseService.getAgentInfoByAgentCode(...).
        final AgentInfoBasicsOut agentInfoBasicsOut = new AgentInfoBasicsOut();
        agentInfoBasicsOut.setId(0);
        agentInfoBasicsOut.setAgentName("referrerName");
        agentInfoBasicsOut.setOrgCode("channelBranchCode");
        agentInfoBasicsOut.setOrgName("channelBranchName");
        agentInfoBasicsOut.setBusinessCode("referrerWno");
        when(mockAgentBaseService.getAgentInfoByAgentCode("referrerCode", false)).thenReturn(agentInfoBasicsOut);

        when(mockOpenApiBaseService.getChannelInfo("channelCode", false)).thenReturn(null);

        // Configure ProductBaseService.getProductInfo(...).
        final ProductBase productBase1 = new ProductBase();
        productBase1.setProductCode("productCode");
        productBase1.setProductName("productName");
        productBase1.setMainProductFlag(0);
        productBase1.setProductType("periodType");
        productBase1.setAdditionalRisksType(0);
        productBase1.setLongShortFlag(0);
        productBase1.setProductGroup("productGroup");
        productBase1.setLevel2Code("level2Code");
        productBase1.setLevel3Code("level3Code");
        when(mockProductBaseService.getProductInfo("productCode")).thenReturn(productBase1);

        // Run the test
        final List<SettlementPolicyInfoEntity> result = settlementPolicyInfoServiceImplUnderTest.createSettlementPolicyInfo(SettlementEventTypeEnum.PERSONAL_NEW_POLICY,
                                                                                                                            policyInfo,
                                                                                                                            preservationDetail,
                                                                                                                            0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testRectification() {
        // Setup
        final List<SettlementPolicyInfoEntity> settlementPolicyInfoList = Arrays.asList(SettlementPolicyInfoEntity.builder()
                                                                                                .id(0)
                                                                                                .settlementCode("settlementCode")
                                                                                                .premCode("premCode")
                                                                                                .reconcileType(0)
                                                                                                .settlementDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .settlementTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .settlementGenerateType(0)
                                                                                                .settlementEventCode("settlementEventCode")
                                                                                                .settlementSubjectCode("settlementSubjectCode")
                                                                                                .settlementSubjectName("settlementSubjectName")
                                                                                                .businessType(0)
                                                                                                .contractCode("contractCode")
                                                                                                .policyNo("policyNo")
                                                                                                .policyProductType("policyProductType")
                                                                                                .hesitateSurrender(0)
                                                                                                .policySource("policySource")
                                                                                                .salesType(0)
                                                                                                .selfPreservation(0)
                                                                                                .customerCode("customerCode")
                                                                                                .applicantTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .approvedTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .payableTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .realityTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .revisitStatus(0)
                                                                                                .revisitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .receiptStatus(0)
                                                                                                .receiptTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .applicantName("applicantName")
                                                                                                .applicantMobile("applicantMobile")
                                                                                                .applicantIdCard("applicantIdCard")
                                                                                                .applicantGender(0)
                                                                                                .applicantBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .applicantAge(0)
                                                                                                .insuredPolicyAge(0)
                                                                                                .insuredName("insuredName")
                                                                                                .insuredMobile("insuredMobile")
                                                                                                .insuredIdCard("insuredIdCard")
                                                                                                .insuredGender(0)
                                                                                                .insuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .productCode("productCode")
                                                                                                .productName("productName")
                                                                                                .protocolCode("protocolCode")
                                                                                                .protocolProductCode("insuranceProductCode")
                                                                                                .protocolProductName("protocolProductName")
                                                                                                .planCode("planCode")
                                                                                                .planName("planName")
                                                                                                .productGroup("productGroup")
                                                                                                .level2Code("level2Code")
                                                                                                .level3Code("level3Code")
                                                                                                .productType("periodType")
                                                                                                .mainInsurance(0)
                                                                                                .additionalRisksType(0)
                                                                                                .effectiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                                                                                                .endDate("endDate")
                                                                                                .renewalYear(2020)
                                                                                                .renewalPeriod(0)
                                                                                                .coverage(new BigDecimal("0.00"))
                                                                                                .coverageUnit(0)
                                                                                                .coverageUnitName("coverageUnitName")
                                                                                                .insuredPeriodType("insuredPeriodType")
                                                                                                .insuredPeriod(0)
                                                                                                .periodType("periodType")
                                                                                                .paymentPeriodType("paymentPeriodType")
                                                                                                .paymentPeriod(0)
                                                                                                .drawAge("drawAge")
                                                                                                .premium(new BigDecimal("0.00"))
                                                                                                .productPremiumTotal(new BigDecimal("0.00"))
                                                                                                .discountPremium(new BigDecimal("0.00"))
                                                                                                .copies(0)
                                                                                                .build());

        // Run the test
        settlementPolicyInfoServiceImplUnderTest.rectification(settlementPolicyInfoList);

        // Verify the results
    }

    @Test
    public void testBatchIsCompletedReconcileRecord() {
        // Setup
        final BatchIsCompletedReconcileRecord batchIsCompletedReconcileRecord = new BatchIsCompletedReconcileRecord();
        batchIsCompletedReconcileRecord.setPolicyNo("policyNo");
        batchIsCompletedReconcileRecord.setIsCompleted(false);
        final List<BatchIsCompletedReconcileRecord> expectedResult = Arrays.asList(batchIsCompletedReconcileRecord);

        // Run the test
        final List<BatchIsCompletedReconcileRecord> result = settlementPolicyInfoServiceImplUnderTest.batchIsCompletedReconcileRecord(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testBatchIsCompletedReconcileRecordPreservation() {
        // Setup
        final BatchIsCompletedReconcileRecordPreservation batchIsCompletedReconcileRecordPreservation = new BatchIsCompletedReconcileRecordPreservation();
        batchIsCompletedReconcileRecordPreservation.setPreservationCode("preservationCode");
        batchIsCompletedReconcileRecordPreservation.setPolicyNo("policyNo");
        batchIsCompletedReconcileRecordPreservation.setIsCompleted(false);
        final List<BatchIsCompletedReconcileRecordPreservation> expectedResult = Arrays.asList(batchIsCompletedReconcileRecordPreservation);

        // Run the test
        final List<BatchIsCompletedReconcileRecordPreservation> result = settlementPolicyInfoServiceImplUnderTest.batchIsCompletedReconcileRecordPreservation(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
