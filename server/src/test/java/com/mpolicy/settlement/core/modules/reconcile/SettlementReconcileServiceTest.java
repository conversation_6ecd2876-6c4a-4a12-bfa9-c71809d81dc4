package com.mpolicy.settlement.core.modules.reconcile;

import com.mpolicy.settlement.core.SettlementCoreCenterApplicationTests;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcileInfoService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class SettlementReconcileServiceTest extends SettlementCoreCenterApplicationTests {

    @Autowired
    private SettlementReconcileInfoService settlementReconcileInfoService;


    @Test
    public void generateDetail() {
        SettlementReconcileInfoEntity one = settlementReconcileInfoService.lambdaQuery()
                .eq(SettlementReconcileInfoEntity::getReconcileCode, "CR17040257904974300611046")
                .one();
        settlementReconcileInfoService.generateDetail(one);
    }
}
