package com.mpolicy.settlement.core.modules.commission.helper;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.mpolicy.settlement.core.SettlementCoreCenterApplicationTests;
import com.mpolicy.settlement.core.common.reconcile.enums.PremEventEnum;
import com.mpolicy.settlement.core.modules.commission.dto.CommissionBasicPremInfoInput;
import com.mpolicy.settlement.core.modules.reconcile.event.factory.PremEventFactory;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.PremEventHandler;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyProductPremInput;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyProductPremResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.List;

@Slf4j
public class CommissionBasicHelperTest extends SettlementCoreCenterApplicationTests {

    @Test
    public void queryCommissionBasicPrem() {
        //073026094984158
        CommissionBasicPremInfoInput input = new CommissionBasicPremInfoInput();
        input.setCompanyCode("TBAL000000");
        input.setChannelCode("zhnx");
        input.setProductCode("TBJKLYB23031502");
        input.setPolicyNo("LYB230830135");
        input.setMainProductCode("TBJKLYB23031501");
        input.setOrgCode("OR20211214182320KHkeI9");
        input.setInsuredPeriodType("INSURED_PERIOD_TYPE:3");
        input.setInsuredPeriod(70);
        input.setPeriodType("PERIOD_TYPE:1");
        input.setPaymentPeriodType("PAYMENT_PERIOD_TYPE:1");
        input.setPaymentPeriod(1);
        input.setInsuranceType(0);
        input.setSettlementSubjectName("首续年佣金");
        input.setApplicantGender(0);
        input.setApprovedTime(DateUtil.beginOfDay(DateUtil.parseDate("2023-04-10 00:00:00")));
        input.setApplicantBirthday(DateUtil.beginOfDay(DateUtil.parseDate("1993-04-11 00:00:00")));
        input.setSalesType(1);
        input.setSelfPreservation(0);
        String jsonStr = "{\"companyCode\":\"HGRS000000\",\"salesType\":0,\"renewalPeriod\":1,\"applicantBirthday\":315504000000,\"mainProductCode\":\"HGRSQJDS23010301\",\"renewalYear\":1,\"insuredPeriodType\":\"INSURED_PERIOD_TYPE:0\",\"policyNo\":\"DM2023102405\",\"insuranceType\":0,\"insuredPeriod\":10,\"productPlan\":\"\",\"settlementSubjectName\":\"首续年佣金\",\"applicantGender\":1,\"productCode\":\"HGRSQJDS23010304\",\"periodType\":\"PERIOD_TYPE:1\",\"approvedTime\":1664812800000,\"paymentPeriod\":10,\"paymentPeriodType\":\"PAYMENT_PERIOD_TYPE:1\",\"selfPreservation\":0,\"channelCode\":\"zhnx\"}";
        PolicyProductPremInput input1 = JSONUtil.toBean(jsonStr, PolicyProductPremInput.class);
        PremEventHandler premEventHandler = PremEventFactory.getInvoke(PremEventEnum.COMMISSION_PREM.getCode());
        List<PolicyProductPremResult> commissionBasicPremInfoList = premEventHandler.queryPolicyProductPrem(input1);
        log.info("费率表:{}", JSONUtil.toJsonStr(commissionBasicPremInfoList));
    }
}
