package com.mpolicy.settlement.core.helper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.mpolicy.open.common.referrer.vo.ReferrerInfoVo;
import com.mpolicy.settlement.core.SettlementCoreCenterApplicationTests;
import com.mpolicy.settlement.core.common.protocol.CheckOrgListIsMateInput;
import com.mpolicy.settlement.core.common.protocol.CheckOrgListIsMateOut;
import com.mpolicy.settlement.core.modules.autocost.dto.cache.OrganizationCache;
import com.mpolicy.settlement.core.modules.autocost.helper.SettlementEmployeeHelper;
import com.mpolicy.settlement.core.modules.protocol.dto.ProtocolInsuranceProductInfoOut;
import com.mpolicy.settlement.core.modules.protocol.helper.ProtocolBaseHelper;
import com.mpolicy.settlement.core.service.common.OpenApiBaseService;
import com.mpolicy.settlement.core.service.common.PolicyCenterBaseClient;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 帮助服务helper
 *
 * <AUTHOR>
 * @since 2023-09-16 22:50
 */
@Slf4j
public class ReconcileHelpTest extends SettlementCoreCenterApplicationTests {

    @Autowired
    private OpenApiBaseService openApiBaseService;

    @Autowired
    private PolicyCenterBaseClient policyCenterBaseClient;

    @Test
    public void getReferrerInfo() {
        List<OrganizationCache> organizationList =
                SettlementEmployeeHelper.listOrganizationByRegion(Collections.singletonList("CNHN"));

        log.info("info:{}", JSON.toJSONString(organizationList));
        ReferrerInfoVo referrerInfo = openApiBaseService.getReferrerInfo("R202309011953354rxCLr", false);
        log.info("referrerInfo:{}", JSON.toJSONString(referrerInfo));
    }

    @Test
    public void getPolicyInfoByPolicyCode() {
        policyCenterBaseClient.getPolicyInfoByPolicyCode("", false);

    }

    @Test
    public void queryProtocolInsuranceProductInfoByProductCode() {
        ProtocolInsuranceProductInfoOut protocolInsuranceProductInfoOut =
                ProtocolBaseHelper.queryProtocolInsuranceProductInfoByProductCode("TKZXJQX22071405", 0);
        log.info("协议产品={}", JSONUtil.toJsonStr(protocolInsuranceProductInfoOut));
        protocolInsuranceProductInfoOut = ProtocolBaseHelper.queryProtocolInsuranceProductInfoByProductCode(
                "TKZXJQX22071405", 1);
        log.info("合约产品={}", JSONUtil.toJsonStr(protocolInsuranceProductInfoOut));

        List<ProtocolInsuranceProductInfoOut> res =
                ProtocolBaseHelper.queryProtocolInsuranceProductList(CollUtil.newArrayList("TKZXJQX22071405"), 0);
        log.info("协议产品={}", JSONUtil.toJsonStr(res));
        res = ProtocolBaseHelper.queryProtocolInsuranceProductList(CollUtil.newArrayList("TKZXJQX22071405"), 1);
        log.info("合约产品={}", JSONUtil.toJsonStr(res));

    }

    @Test
    public void checkOrgListIsMate() {
        List<CheckOrgListIsMateInput> inputList = new ArrayList<>();
        CheckOrgListIsMateInput input1 =new CheckOrgListIsMateInput();
        input1.setOrgCode("OR20210222103029tIBLey");
        input1.setEffectiveDate("2021-04-17 14:30:50");
        input1.setBusinessCode("ZATX240417001");
        input1.setProductCode("ZAZXTY22011901");
        inputList.add(input1);
        List<CheckOrgListIsMateOut> checkOrgListIsMateOuts = ProtocolBaseHelper.checkOrgListIsMate(inputList);
        log.info("checkOrgListIsMateOuts={}",JSONUtil.toJsonStr(checkOrgListIsMateOuts));
    }

}
