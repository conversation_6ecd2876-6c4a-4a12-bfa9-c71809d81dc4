package com.mpolicy.settlement.core.modules.autocost.service;

import com.mpolicy.settlement.core.modules.autocost.service.impl.HrGrantPersonSyncServiceImpl;
import com.mpolicy.settlement.core.modules.autocost.utils.HrGrantPersonTimeUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Hr发放人员同步服务测试
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@ExtendWith(MockitoExtension.class)
class HrGrantPersonSyncServiceTest {

    @Mock
    private HrGrantPersonSyncService hrGrantPersonSyncService;

    @InjectMocks
    private HrGrantPersonSyncServiceImpl hrGrantPersonSyncServiceImpl;

    private static final DateTimeFormatter CYCLE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMM");

    @Test
    void testSettlementCycleFormat() {
        // 测试结算周期格式
        String validCycle = "202506";
        assertDoesNotThrow(() -> {
            YearMonth yearMonth = YearMonth.parse(validCycle, CYCLE_FORMATTER);
            assertEquals(2025, yearMonth.getYear());
            assertEquals(6, yearMonth.getMonthValue());
        });

        // 测试无效格式
        String invalidCycle = "2025-06-30";
        assertThrows(Exception.class, () -> {
            YearMonth.parse(invalidCycle, CYCLE_FORMATTER);
        });
    }

    @Test
    void testTimeRangeCalculation() {
        // 测试时间范围计算 - 以202506为例，查询上月（202505）的数据
        String settlementCycle = "202506";

        // 验证时间计算逻辑 - 查询上月数据
        LocalDate firstDay = HrGrantPersonTimeUtils.getPrevMonthFirstDay(settlementCycle);
        LocalDate lastDay = HrGrantPersonTimeUtils.getPrevMonthLastDay(settlementCycle);

        assertEquals("2025-05-01", firstDay.toString());  // 上月第一天
        assertEquals("2025-05-31", lastDay.toString());   // 上月最后一天
    }

    @Test
    void testTimeWithHourMinuteSecond() {
        // 测试带时分秒的时间处理
        String settlementCycle = "202506";

        Date startTime = HrGrantPersonTimeUtils.getPrevMonthStartTime(settlementCycle);
        Date endTime = HrGrantPersonTimeUtils.getPrevMonthEndTime(settlementCycle);
        Date syncTime = HrGrantPersonTimeUtils.getPrevMonthSyncTime(settlementCycle);

        // 验证时间格式
        assertNotNull(startTime);
        assertNotNull(endTime);
        assertNotNull(syncTime);

        // 验证时间范围描述
        String description = HrGrantPersonTimeUtils.formatTimeRangeDescription(settlementCycle);
        assertTrue(description.contains("202506"));
        assertTrue(description.contains("2025-05-01 00:00:00"));
        assertTrue(description.contains("2025-05-31 23:59:59"));
        assertTrue(description.contains("2025-05-31 00:00:01"));
    }

    @Test
    void testCycleValidation() {
        // 测试各种结算周期格式
        String[] validCycles = {"202501", "202512", "202406"};
        String[] invalidCycles = {"2025-06", "20250601", "202513", "202500"};

        for (String cycle : validCycles) {
            assertDoesNotThrow(() -> YearMonth.parse(cycle, CYCLE_FORMATTER),
                    "Valid cycle should not throw exception: " + cycle);
        }

        for (String cycle : invalidCycles) {
            assertThrows(Exception.class, () -> YearMonth.parse(cycle, CYCLE_FORMATTER),
                    "Invalid cycle should throw exception: " + cycle);
        }
    }
}
