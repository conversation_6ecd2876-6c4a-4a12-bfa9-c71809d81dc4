package com.mpolicy.settlement.core.modules.invoice.service.impl;

import com.mpolicy.settlement.core.modules.invoice.service.SettlementReconcileInvoiceService;
import com.mpolicy.settlement.core.modules.invoice.service.SettlementReconcileRedInvoiceService;
import com.mpolicy.settlement.core.modules.invoice.vo.third.FttmInvoiceOrderInfoVo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class SettlementReconcileInvoiceCallbackServiceImplTest {

    @Mock
    private SettlementReconcileInvoiceService mockReconcileInvoiceService;
    @Mock
    private SettlementReconcileRedInvoiceService mockReconcileRedInvoiceService;

    @InjectMocks private SettlementReconcileInvoiceCallbackServiceImpl settlementReconcileInvoiceCallbackServiceImplUnderTest;

    @Test
    public void testHandleCallbackMsg() {
        // Setup
        final FttmInvoiceOrderInfoVo vo = new FttmInvoiceOrderInfoVo();
        final FttmInvoiceOrderInfoVo.InvoiceDetail ddfpxx = new FttmInvoiceOrderInfoVo.InvoiceDetail();
        ddfpxx.setKplx("kplx");
        vo.setDdfpxx(ddfpxx);
        final FttmInvoiceOrderInfoVo.OrderSummary orderSummary = new FttmInvoiceOrderInfoVo.OrderSummary();
        orderSummary.setDdqqlsh("ddqqlsh");
        orderSummary.setDdh("businessNo");
        vo.setDdkzxx(Arrays.asList(orderSummary));

        // Run the test
        settlementReconcileInvoiceCallbackServiceImplUnderTest.handleCallbackMsg(vo);

        // Verify the results
        // Confirm SettlementReconcileInvoiceService.handleBlueCallback(...).
        final FttmInvoiceOrderInfoVo vo1 = new FttmInvoiceOrderInfoVo();
        final FttmInvoiceOrderInfoVo.InvoiceDetail ddfpxx1 = new FttmInvoiceOrderInfoVo.InvoiceDetail();
        ddfpxx1.setKplx("kplx");
        vo1.setDdfpxx(ddfpxx1);
        final FttmInvoiceOrderInfoVo.OrderSummary orderSummary1 = new FttmInvoiceOrderInfoVo.OrderSummary();
        orderSummary1.setDdqqlsh("ddqqlsh");
        orderSummary1.setDdh("businessNo");
        vo1.setDdkzxx(Arrays.asList(orderSummary1));
        verify(mockReconcileInvoiceService).handleBlueCallback(vo1);

        // Confirm SettlementReconcileRedInvoiceService.handleRedCallback(...).
        final FttmInvoiceOrderInfoVo vo2 = new FttmInvoiceOrderInfoVo();
        final FttmInvoiceOrderInfoVo.InvoiceDetail ddfpxx2 = new FttmInvoiceOrderInfoVo.InvoiceDetail();
        ddfpxx2.setKplx("kplx");
        vo2.setDdfpxx(ddfpxx2);
        final FttmInvoiceOrderInfoVo.OrderSummary orderSummary2 = new FttmInvoiceOrderInfoVo.OrderSummary();
        orderSummary2.setDdqqlsh("ddqqlsh");
        orderSummary2.setDdh("businessNo");
        vo2.setDdkzxx(Arrays.asList(orderSummary2));
        verify(mockReconcileRedInvoiceService).handleRedCallback(vo2);
    }
}
