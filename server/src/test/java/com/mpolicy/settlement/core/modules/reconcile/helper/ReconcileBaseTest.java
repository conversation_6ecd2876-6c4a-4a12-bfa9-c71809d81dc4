package com.mpolicy.settlement.core.modules.reconcile.helper;

import cn.hutool.core.collection.CollUtil;
import com.mpolicy.settlement.core.SettlementCoreCenterApplicationTests;
import com.mpolicy.settlement.core.modules.reconcile.dto.rule.ReconcileCompanyInfo;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcileHelperService;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcileInfoService;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcileService;
import com.mpolicy.settlement.core.common.reconcile.CreateReconcileVo;
import com.mpolicy.settlement.core.modules.reconcile.vo.SqlHelperVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
public class ReconcileBaseTest extends SettlementCoreCenterApplicationTests {


    @Autowired
    private SettlementReconcileService settlementReconcileService;


    @Autowired
    private SettlementReconcileInfoService settlementReconcileInfoService;


    @Autowired
    private SettlementReconcileHelperService settlementReconcileHelperService;


    @Test
    public void createCompanyReconcile() {
        // 1 获取【保司配置规则生成为账单日=day的保司对账单】
        List<ReconcileCompanyInfo> reconcileCompanyInfos = ReconcileRuleHelper.queryReconcileCompanyInfoList(10);

        if(CollUtil.isNotEmpty(reconcileCompanyInfos)) {
            log.info("需要生成账单数量为={}", reconcileCompanyInfos.size());
            for (ReconcileCompanyInfo reconcileCompanyInfo : reconcileCompanyInfos) {
                // 执行生成对账单
                String reconcileCode = settlementReconcileService.createReconcile(reconcileCompanyInfo);
                log.info("生成账单的保司完成 对账单号={}",reconcileCode);
            }
        }else{
            log.info("没有需要生成账单的保司");
        }
    }

    @Test
    public void forceCreateReconcile() {
        CreateReconcileVo vo = new  CreateReconcileVo();
        vo.setReconcileMonth("2024年02月");
        vo.setReconcileCompanyCode("SR20230530153144168697");
        settlementReconcileService.forceCreateReconcile(vo);
    }
    @Test
    public void createPolicySummary() {
        settlementReconcileHelperService.createPolicySummary("CR16980556674871215329598","1");
    }
    @Test
    public void sqlHelper() {
        SqlHelperVo sqlHelperVo = new SqlHelperVo();
        sqlHelperVo.setSqlStr("update settlement_event_job set event_request ='{\"customerManagerChannelOrgCode\":\"\",\"businessSignType\":1,\"referrerCode\":\"\",\"policyCode\":\"13256191920240000005\",\"preservationCode\":\"PR20240329145825356236\",\"preservationEffectTime\":\"2024-03-23\",\"customerManagerChannelCode\":\"\",\"preservationWhy\":\"POLICY:PRESERVATION:WHY:COMPANY_DATA_SYN\",\"preservationProject\":\"POLICY:PRESERVATION:TYPE:POLICY_STATUS_CHANGE:SURRENDER\",\"preservationType\":\"POLICY:PRESERVATION:TYPE:POLICY_STATUS_CHANGE\",\"channelReferrerName\":\"\",\"manageOrgName\":\"甘肃渠道业务部\",\"sellChannelCode\":\"zhnx\",\"customerManagerSupervisor\":\"\",\"channelBranchCode\":\"zhnx\",\"referrerName\":\"\",\"policyAgentName\":\"高斐\",\"endorsementNo\":\"13256191920240000005_002\",\"holderName\":\"甘肃省中和农信小额贷款有限责任公司\",\"customerManagerOrgCode\":\"\",\"policyName\":\"甘肃阳光诉讼财产保全责任保险\",\"branchCode\":\"\",\"surrenderCash\":-286.2,\"pushEventCode\":\"PEC20240329145827xJyEfE\",\"customerManagerCode\":\"\",\"policyType\":\"团险\",\"holderIdNo\":\"916229250792985903\",\"policyAgentCode\":\"ag20211029120634uGv5cj\",\"sellChannelName\":\"中和农信\",\"contractCode\":\"ct20240329145725haImM2\",\"manageOrgCode\":\"OR20220211104524cgp00L\",\"preservationStatus\":1,\"channelReferrerCode\":\"\",\"renewalTermPeriod\":1}' where id =1486812");
        sqlHelperVo.setPassword("waSxK4pz8V6yT7Su7PSLB5dLRRVdGBKdJ8b4BAfEBQkg4rdSJefWyZCcT7uJgG4j");
        settlementReconcileInfoService.sqlHelper(sqlHelperVo);
    }


}
