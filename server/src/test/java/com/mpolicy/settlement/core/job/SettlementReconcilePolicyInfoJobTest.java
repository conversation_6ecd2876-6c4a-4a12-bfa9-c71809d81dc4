package com.mpolicy.settlement.core.job;

import com.mpolicy.settlement.core.SettlementCoreCenterApplicationTests;
import com.mpolicy.settlement.core.modules.reconcile.job.SettlementReconcilePolicyInfoJob;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class SettlementReconcilePolicyInfoJobTest extends SettlementCoreCenterApplicationTests {

    @Autowired
  private   SettlementReconcilePolicyInfoJob solutionReconcilePolicyInfoJob;

    @Test
    public void updateReconcilePolicyInfo(){
        solutionReconcilePolicyInfoJob.updateReconcilePolicyInfo();
    }

    @Test
    public void updateChannelDistributionCode(){
        solutionReconcilePolicyInfoJob.updateChannelDistributionCode();
    }
    @Test
    public void updateApplicantIdCard(){
        solutionReconcilePolicyInfoJob.updateApplicantIdCard();
    }
    @Test
    public void updateCompanyInfo(){
        solutionReconcilePolicyInfoJob.updateCompanyInfo();
    }
    @Test
    public void updateInsuranceType(){
        solutionReconcilePolicyInfoJob.updateInsuranceType();
    }

    @Test
    public void reconcileRegulatorySubmit(){
        solutionReconcilePolicyInfoJob.reconcileRegulatorySubmit();
    }

    @Test
    public void handleSettlementMonthReport(){
        solutionReconcilePolicyInfoJob.handleSettlementMonthReport();
    }
}
