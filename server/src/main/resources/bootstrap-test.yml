spring:
  application:
    name: settlement-center-core
  cloud:
    config:
      discovery:
        enabled: true
        service-id: CONFIG
      profile: ${spring.profiles.active}
    # 修复github webhook 只能刷新config server 无法刷新config client的问题
    bus:
      #Workaround for defect in https://github.com/spring-cloud/spring-cloud-bus/issues/124
      id: ${vcap.application.name:${spring.application.name:application}}:${vcap.application.instance_index:${spring.profiles.active:${local.server.port:${server.port:0}}}}:${vcap.application.instance_id:${random.value}}
  # 文件上传大小设置
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 总上传的数据大小
      max-request-size: 100MB
# 端口
server:
  port: 9402
# 注册中心
eureka:
  client:
    service-url:
      defaultZone: http://register-test.internal.xiaowhale.net:8761/eureka/
      # defaultZone: http://ims-eureka-1/eureka
  instance:
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    prefer-ip-address: true
# 消息开关 + 行为注解
xjxh:
  rabbitmq:
    switch: true
  behavior:
    switch: true
# 定时任务 xxl:
xxl:
  job:
    switch: true # 启动xxl-job⾃自动装配
    accessToken: xjxh_job # accessToken ⽬目前固定为xxlJobAccessToken admin:
    addresses: http://xxljob-test.internal.xiaowhale.net:8089/xxl-job-admin # 测试环境
    appname: xxl-job-executor-settlement # 执⾏行行器器名称
    ip: '' # 容器器部署暂时不不⽤用
    port: 8881 # 定时任务调度器器和执⾏行行器器交互端⼝口，⾮非应⽤用
    logpath: logs/jobs # ⽇日志路路径
    logretentiondays: 10 # ⽇日志⽂文件保存天数
# 文件临时存储
insure:
  temp-file-path: logs
# 日志
logging:
  level:
    org.springframework.cloud.netflix.feign: debug
logfile:
  dir: logs
  additivity: true
  include: logback-mq.xml
notify:
  group: InsurPolicyDataGroup
chongho:
  feign:
    operation: http://insurance-operation.tsg.cfpamf.com/
    insurance: http://insurance-admin.tsg.cfpamf.com/
dbc-service:
  url: http://dbc-service.tsg.cfpamf.com
bms:
  api:
    url: http://bms-service.tsg.cfpamf.com/
ms-messagepush-service:
  api:
    url: http://ms-messagepush.tsg.cfpamf.com/
# feign配置
feign:
  hystrix:
    # 开启feign的hystrix支持,默认是false
    enabled: true
  client:
    config:
      default:
        connectTimeout: 15000
        readTimeout: 15000
      product-center:
        connectTimeout: 15000
        readTimeout: 15000
      ins-platform-api:
        connectTimeout: 20000
        readTimeout: 20000
settlement:
  basicCost:
    calcRule:
      newPolicyStartTime: 2023-09-01
      preservationStartTime: 2023-09-01
      renewalStartTime: 2023-09-01
  autoCost:
    settlementDay: 5  #自动结算日，每月第几天结算
  temp-file-path: logs/
hystrix:
  command:
    longServiceMethod:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 30000
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 30000
