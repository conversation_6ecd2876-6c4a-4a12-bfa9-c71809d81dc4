<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.settlement.core.modules.reconcile.dao.SettlementPolicyInfoDao">
    <update id="updateSettlementPolicyFinish">
        update settlement_policy_info s left join settlement_reconcile_policy p
        on s.settlement_code = p.settlement_code and p.reconcile_generate_type  = 1
            set s.reconcile_status = 3,
                s.settlement_month = #{settlementMonth},
                s.settlement_company_code = p.company_code,
                s.settlement_company_name = p.company_name,
                s.settlement_subject_code = p.reconcile_subject_code,
                s.settlement_subject_name = p.reconcile_subject_name,
                s.reconcile_time = now(),
                s.reconcile_code = #{reconcileCode},
                s.settlement_amount = p.settlement_amount,
                s.reconcile_user = #{userName}
        where p.reconcile_code = #{reconcileCode}
          and p.reconcile_status = 3
    </update>
</mapper>