<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mpolicy.settlement.core.modules.reconcile.dao.SettlementInnerCompanyLicenseInfoDao">

    <!-- 批量插入或更新工商信息 -->
    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        INSERT INTO settlement_inner_company_license_info (
            business_license_status,
            business_nature,
            license_name,
            license_type,
            management_type,
            modify_date,
            social_credit_code,
            subject_type,
            deleted,
            create_user,
            create_time,
            update_user,
            update_time,
            revision
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.businessLicenseStatus},
                #{item.businessNature},
                #{item.licenseName},
                #{item.licenseType},
                #{item.managementType},
                #{item.modifyDate},
                #{item.socialCreditCode},
                #{item.subjectType},
                #{item.deleted},
                #{item.createUser},
                #{item.createTime},
                #{item.updateUser},
                #{item.updateTime},
                #{item.revision}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            business_license_status = VALUES(business_license_status),
            business_nature = VALUES(business_nature),
            license_name = VALUES(license_name),
            license_type = VALUES(license_type),
            management_type = VALUES(management_type),
            modify_date = VALUES(modify_date),
            subject_type = VALUES(subject_type),
            update_user = VALUES(update_user),
            update_time = VALUES(update_time),
            revision = revision + 1
    </insert>

</mapper>
