<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.settlement.core.modules.reconcile.dao.BiSettlementIncomeStatisticsDao">

    <select id="findIncomeStatistics" resultType="com.mpolicy.settlement.core.modules.reconcile.vo.IncomeStatisticsOut">
        SELECT  i.`org_code`  ,SUM(i.`settlement_amount`) as settlement_amount
        FROM settlement_policy_info  i
                 left join settlement_policy_info si on i.`source_settlement_code` = si.`settlement_code` and si.`deleted` = 0
        where i.`deleted` = 0 and i.channel_code !='zhnx' and i.`reconcile_execute_status` =1 and  (i.`settlement_event_code`
        in( 'settlement.global.policy.group_add_or_subtract',
        'settlement.global.policy.group_new_policy',
        'settlement.global.policy.hesitate_surrender',
        'settlement.global.policy.personal_new_policy')
            or si.`settlement_event_code`
            in( 'settlement.global.policy.group_add_or_subtract',
            'settlement.global.policy.group_new_policy',
            'settlement.global.policy.hesitate_surrender',
            'settlement.global.policy.personal_new_policy'))
        and i.reconcile_execute_time &gt;=#{beginTime,jdbcType=TIMESTAMP} and i.reconcile_execute_time &lt;#{endTime,jdbcType=TIMESTAMP}
        group by i.`org_code`
    </select>

    <!---->
    <select id="findRenewalTermIncomeStatistics" resultType="com.mpolicy.settlement.core.modules.reconcile.vo.IncomeStatisticsOut">
        SELECT  i.`org_code` ,SUM(i.`settlement_amount`) as settlement_amount
        FROM settlement_policy_info  i
                 left join settlement_policy_info si on i.`source_settlement_code` = si.`settlement_code`
        where  i.`deleted` = 0 and  i.channel_code !='zhnx' and i.`reconcile_execute_status` =1 and
        (i.`settlement_event_code` = 'settlement.global.policy.renewal_term'
        or si.`settlement_event_code` = 'settlement.global.policy.renewal_term')
            and i.reconcile_execute_time &gt;=#{beginTime,jdbcType=TIMESTAMP} and i.reconcile_execute_time &lt;#{endTime,jdbcType=TIMESTAMP}
        group by i.`org_code`
    </select>

</mapper>