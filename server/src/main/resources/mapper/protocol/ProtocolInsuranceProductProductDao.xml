<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.settlement.core.modules.protocol.dao.ProtocolInsuranceProductProductDao">

    <!--获取险种对应的合约.协议产品信息-->
    <select id="findProtocolInsuranceProductList"
            resultType="com.mpolicy.settlement.core.modules.protocol.dto.ProtocolInsuranceProductInfoOut">
        SELECT p.product_code,p.product_name,p.product_plan, i.company_code,i.company_name,
        i.company_short_name,i.insurance_product_code,i.insurance_product_name,p.insurance_product_id,
        i.reconcile_type,p.id as productId
        FROM ep_protocol_insurance_product_product p
        LEFT JOIN ep_protocol_insurance_product i on p.insurance_product_id = i.id
        WHERE i.reconcile_type = #{reconcileType,jdbcType=INTEGER} and p.product_code in
        <foreach collection="productCodeList" close=")" open="(" separator="," item="productCode">
            #{productCode,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="findProtocolInsuranceProductInfo"
            resultType="com.mpolicy.settlement.core.modules.protocol.dto.ProtocolInsuranceProductInfoOut">
        SELECT p.product_code,p.product_name,p.product_plan, i.company_code,i.company_name,
        i.company_short_name,i.insurance_product_code,i.insurance_product_name,p.insurance_product_id,
        i.reconcile_type,p.id as productId
        FROM ep_protocol_insurance_product_product p
        LEFT JOIN ep_protocol_insurance_product i on p.insurance_product_id = i.id
        WHERE i.reconcile_type = #{reconcileType,jdbcType=INTEGER} and p.product_code = #{productCode,jdbcType=VARCHAR}
    </select>
</mapper>