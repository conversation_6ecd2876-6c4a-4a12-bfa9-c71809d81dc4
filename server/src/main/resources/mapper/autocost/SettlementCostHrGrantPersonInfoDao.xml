<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.settlement.core.modules.autocost.dao.SettlementCostHrGrantPersonInfoDao">

    <update id="batchUpdateHrGrantPersonInfo" parameterType="java.util.List">
        update settlement_cost_hr_grant_person_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="send_object_name =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.sendObjectName}
                </foreach>
            </trim>

            <trim prefix="hr_grant_flag =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.hrGrantFlag}
                </foreach>
            </trim>

            <trim prefix="remark =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.remark}
                </foreach>
            </trim>

            <trim prefix="update_user =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.updateUser}
                </foreach>
            </trim>

            <trim prefix="update_time =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.updateTime}
                </foreach>
            </trim>

            <trim prefix="revision =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.revision}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

</mapper>
