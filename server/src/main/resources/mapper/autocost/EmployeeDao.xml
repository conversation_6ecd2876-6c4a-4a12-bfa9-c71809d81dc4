<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.settlement.core.modules.autocost.dao.EmployeeDao">

    <select id="selectEmployees" resultType="com.mpolicy.settlement.core.modules.autocost.dto.employee.EmployeeInfo">
        SELECT
        he.`id` employeeId,
        hpr.`employee_code` employeeCode,
        he.`name` employeeName,

        ho1.code orgCode,
        ho1.`name` orgName,

        hpr.job_postion_id jobId,
        hjr.`name` jobName,
        1 as employeeType,

        hpr.`posting_org` regionId,
        ho2.`name` regionName,
        ho2.code regionCode,

        he1.id managerEmployeeId,
        he1.`name` managerEmployeeName,
        he1.`employee_code` managerEmployeeCode,

        he2.id supervisorId,
        he2.employee_code supervisorCode,
        he2.name supervisorName,
        he3.id leaderEmployeeId,
        he3.employee_code leaderEmployeeCode,
        he3.name leaderEmployeeName
        FROM hr_employee he
        LEFT JOIN hr_posting_record hpr ON hpr.`employee_id`= he.`id`
        LEFT JOIN hr_organization ho1 ON ho1.`id` = hpr.`posting_dept`
        LEFT JOIN hr_organization ho2 ON ho2.`id` = hpr.`posting_org`
        LEFT JOIN hr_employee he1 ON he1.`id` = hpr.`direct_manager_id` AND he1.`enabled_flag`=1
        LEFT JOIN hr_employee he2 ON he2.`id` = hpr.`supervisor_id` AND he2.`enabled_flag`=1
        LEFT JOIN hr_employee he3 ON he3.`id` = hpr.`direct_leader_id` AND he3.`enabled_flag`=1
        LEFT JOIN hr_job_role hjr ON hjr.`id` = hpr.`job_postion_id`
        ${ew.customSqlSegment}
    </select>
</mapper>