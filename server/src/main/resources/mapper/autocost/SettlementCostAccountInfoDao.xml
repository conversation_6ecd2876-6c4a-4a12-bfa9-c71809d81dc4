<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.settlement.core.modules.autocost.dao.SettlementCostAccountInfoDao">

    <update id="batchUpdateCostAccountInfo" parameterType="java.util.List">
        update settlement_cost_account_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="last_month_negative_amount =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.lastMonthNegativeAmount}
                </foreach>
            </trim>

            <trim prefix="amount =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.amount}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>




</mapper>