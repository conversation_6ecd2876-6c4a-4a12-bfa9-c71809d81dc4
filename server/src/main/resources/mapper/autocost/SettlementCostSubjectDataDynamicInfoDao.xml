<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.settlement.core.modules.autocost.dao.SettlementCostSubjectDataDynamicInfoDao">
    <select id="listDifferenceByTotalAndDetail" resultType="com.mpolicy.settlement.core.modules.autocost.dto.dynamic.SubjectDataDynamicAmountDifference">
        select * from (
            select i.apply_code as applyCode,
                   i.employee_code as employeeCode,
                   i.employee_name as employeeName,
                   i.dynamic_subject_code as dynamicSubjectCode,
                   i.dynamic_subject_name as dynamicSubjectName,
                   i.settlement_institution as settlementInstitution,
                   i.settlement_institution_name as settlementInstitutionName,
                   max(i.dynamic_subject_cash) as totalAmount,
                   sum(a.apportion_cash) as itemSumAmount,
                   i.serial_number as serialNumber,
                   i.sheet_index as sheetIndex,
                   i.row_num as rowNum
            from settlement_cost_subject_data_dynamic_info i
            left join settlement_cost_subject_data_dynamic_apportion a
            on i.apply_code = a.apply_code
                and i.employee_code = a.employee_code
                and i.dynamic_subject_code = a.dynamic_subject_code
                and i.settlement_institution = a.settlement_institution
                and i.deleted=0 and a.deleted=0
            where i.apply_code = #{applyCode} and i.dynamic_subject_code=#{subjectCode}
            group by i.employee_code,i.dynamic_subject_code,i.settlement_institution,i.settlement_institution
        ) t
        where t.totalAmount != t.itemSumAmount
    </select>
</mapper>