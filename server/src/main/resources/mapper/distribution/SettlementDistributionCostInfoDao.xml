<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.settlement.core.modules.distribution.dao.SettlementDistributionCostInfoDao">



    <update id="batchUpdateAutoCostInfo" parameterType="java.util.List">
        update settlement_distribution_cost_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="document_code =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.documentCode}
                </foreach>
            </trim>

            <trim prefix="auto_cost_code =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.autoCostCode}
                </foreach>
            </trim>

            <trim prefix="grant_month =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.grantMonth}
                </foreach>
            </trim>

            <trim prefix="cost_settlement_cycle =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.costSettlementCycle}
                </foreach>
            </trim>


        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>
</mapper>