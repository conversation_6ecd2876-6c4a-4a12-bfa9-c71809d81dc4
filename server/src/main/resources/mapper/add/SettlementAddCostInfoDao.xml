<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.settlement.core.modules.add.dao.SettlementAddCostInfoDao">

    <select id="pageAddCostInfo" resultType="com.mpolicy.settlement.core.modules.add.dto.SettlementAddCostInfoDto">
        select
        scpi.applicant_name as applicantName,
        scpi.company_code as companyCode,
        scpi.company_name as companyName,
        scpi.applicant_time as applicantTime,
        scpi.approved_time as approvedTime,
        scpi.order_time as orderTime,
        scpi.enforce_time as enforceTime,
        scpi.rural_proxy_flag as ruralProxyFlag,
        scpi.distribution_flag as distributionFlag,
        scpi.policy_product_type as policyProductType,
        scpi.long_short_flag as mainLongShortFlag,

        scpi.policy_status as policyStatus,
        scpi.revisit_status as revisitStatus,
        scpi.revisit_result as revisitResult,
        scpi.revisit_time as revisitTime,
        scpi.receipt_status as receiptStatus,
        scpi.receipt_time as receiptTime,
        scpi.surrender_term_period as surrenderTermPeriod,
        scpi.termination_product_code as terminationProductCode,
        scpi.vehicle_vessel_tax as vehicleVesselTax,
        scpi.vehicle_vessel_tax_rate as vehicleVesselTaxRate,
        scpi.vehicle_business_score as vehicleBusinessScore,
        sci.add_cost_code as costCode,
        sci.*
        from settlement_add_cost_info sci
        left join settlement_cost_policy_info scpi on sci.contract_code = scpi.contract_code and sci.cost_policy_id = scpi.id and sci.deleted = 0
        ${ew.customSqlSegment}
    </select>




    <select id="listAddCostInfo" resultType="com.mpolicy.settlement.core.modules.add.dto.SettlementAddCostInfoDto">
        select
        scpi.applicant_name as applicantName,
        scpi.company_code as companyCode,
        scpi.company_name as companyName,
        scpi.applicant_time as applicantTime,
        scpi.approved_time as approvedTime,
        scpi.order_time as orderTime,
        scpi.enforce_time as enforceTime,
        scpi.long_short_flag as mainLongShortFlag,

        scpi.rural_proxy_flag as ruralProxyFlag,
        scpi.distribution_flag as distributionFlag,
        scpi.policy_product_type as policyProductType,


        scpi.policy_status as policyStatus,
        scpi.revisit_status as revisitStatus,
        scpi.revisit_result as revisitResult,
        scpi.revisit_time as revisitTime,
        scpi.receipt_status as receiptStatus,
        scpi.receipt_time as receiptTime,
        scpi.surrender_term_period as surrenderTermPeriod,
        scpi.termination_product_code as terminationProductCode,
        scpi.vehicle_vessel_tax as vehicleVesselTax,
        scpi.vehicle_vessel_tax_rate as vehicleVesselTaxRate,
        scpi.vehicle_business_score as vehicleBusinessScore,
        sci.add_cost_code as costCode,
        sci.*
        from settlement_add_cost_info sci
        left join settlement_cost_policy_info scpi on sci.contract_code = scpi.contract_code and sci.cost_policy_id = scpi.id and sci.deleted = 0
        ${ew.customSqlSegment}
    </select>
    <select id="sumSettlementAddCostSmy" resultType="com.mpolicy.settlement.core.modules.add.dto.SettlementAddCostSummaryDto">
        select count(sci.id) as totalQty,
        count(distinct sci.contract_code) as policyQty,
        sum(sci.premium) as totalPremium,
        sum(sci.discount_premium) as discountTotalPremium,
        sum(sci.grant_amount) as settlementAmount,
        sum(case when (scpi.rural_proxy_flag =1 or scpi.distribution_flag=1) then 0 else sci.grant_amount end) as cmsAmount
        from settlement_add_cost_info sci
        left join settlement_cost_policy_info scpi on sci.contract_code = scpi.contract_code and sci.cost_policy_id = scpi.id and sci.deleted = 0
        ${ew.customSqlSegment}
    </select>

    <!--农保微信端推广费专用-->
    <select id="listPolicyAddCostInfo" resultType="com.mpolicy.settlement.core.modules.add.dto.SettlementPolicyAddCostInfoDto">
        select
        sci.business_account_time,
        sci.initial_event_code as initialEventCode,
        scpi.contract_code as contractCode,
        sci.policy_no as policyNo,
        scpi.applicant_name as applicantName,
        sci.insured_name as insuredName,
        scpi.main_product_code as mainProductCode,
        scpi.main_product_name as mainProductName,
        scpi.commodity_name as commodityName,
        sci.renewal_period as renewalPeriod,
        sum(sci.premium) as policyPremium,
        sum(case when (scpi.rural_proxy_flag =1 or scpi.distribution_flag=1) then 0 else sci.grant_amount end) as grantAmount,
        scpi.long_short_flag as longShortFlag,
        scpi.rural_proxy_flag  as ruralProxyFlag,
        scpi.distribution_flag  as distributionFlag

        from settlement_add_cost_info sci
        left join settlement_cost_policy_info scpi on sci.contract_code = scpi.contract_code and sci.cost_policy_id = scpi.id and sci.deleted = 0
        ${ew.customSqlSegment}
    </select>
    <!--农保微信端加用专用-->
    <select id="pagePolicyAddCostInfo" resultType="com.mpolicy.settlement.core.modules.add.dto.SettlementPolicyAddCostInfoDto">
        select
        sci.business_account_time,
        sci.initial_event_code as initialEventCode,
        scpi.contract_code as contractCode,
        sci.policy_no as policyNo,
        scpi.applicant_name as applicantName,
        sci.insured_name as insuredName,
        scpi.main_product_code as mainProductCode,
        scpi.main_product_name as mainProductName,
        scpi.commodity_code as commodityCode,
        scpi.commodity_name as commodityName,
        sci.renewal_period as renewalPeriod,
        sum(sci.premium) as policyPremium,
        sum(case when (scpi.rural_proxy_flag =1 or scpi.distribution_flag=1) then 0 else sci.grant_amount end) as grantAmount,
        scpi.long_short_flag as longShortFlag,
        scpi.rural_proxy_flag  as ruralProxyFlag,
        scpi.distribution_flag  as distributionFlag

        from settlement_add_cost_info sci
        left join settlement_cost_policy_info scpi on sci.contract_code = scpi.contract_code and sci.cost_policy_id = scpi.id and sci.deleted = 0
        ${ew.customSqlSegment}
    </select>









    <select id="listZhnxAddCostInfo" resultType="com.mpolicy.settlement.core.modules.add.dto.SettlementAddCostInfoDto">
        select
        scpi.applicant_name as applicantName,
        scpi.company_code as companyCode,
        scpi.company_name as companyName,
        scpi.applicant_time as applicantTime,
        scpi.approved_time as approvedTime,
        scpi.order_time as orderTime,
        scpi.enforce_time as enforceTime,
        scpi.rural_proxy_flag as ruralProxyFlag,
        scpi.distribution_flag as distributionFlag,
        scpi.policy_product_type as policyProductType,
        scpi.long_short_flag as mainLongShortFlag,
        scpi.policy_status as policyStatus,
        scpi.revisit_status as revisitStatus,
        scpi.revisit_result as revisitResult,
        scpi.revisit_time as revisitTime,
        scpi.receipt_status as receiptStatus,
        scpi.receipt_time as receiptTime,
        scpi.surrender_term_period as surrenderTermPeriod,
        scpi.termination_product_code as terminationProductCode,
        scpi.vehicle_vessel_tax as vehicleVesselTax,
        scpi.vehicle_vessel_tax_rate as vehicleVesselTaxRate,
        scpi.vehicle_business_score as vehicleBusinessScore,

        sci.add_cost_code as costCode,sci.cost_policy_id as costPolicyId, sci.contract_code as contractCode, sci.settlement_institution as settlementInstitution,
        sci.settlement_institution_name as settlementInstitutionName,sci.business_account_time as businessAccountTime, sci.settlement_event_code as settlementEventCode,
        sci.settlement_event_desc as settlementEventDesc,
        sci.initial_event_code as initialEventCode,sci.settlement_subject_code as settlementSubjectCode, sci.settlement_subject_name as settlementSubjectName,
        sci.owner_channel_code as ownerChannelCode,
        sci.owner_code as ownerCode,sci.owner_name as ownerName, sci.owner_org_code as ownerOrgCode, sci.owner_third_code as ownerThirdCode,
        sci.owner_third_org as ownerThirdOrg,sci.owner_third_org_name as ownerThirdOrgName, sci.cost_type as costType,
        sci.cost_rate as costRate,sci.cost_actual_rate as costActualRate, sci.cost_amount as costAmount, sci.grant_rate as grantRate,
        sci.grant_amount as grantAmount,sci.correction_flag as correctionFlag, sci.source_cost_code as sourceCostCode, sci.source_generate_type as sourceGenerateType,
        sci.correction_type as correctionType,sci.correction_user as correctionUser, sci.correction_op_type as correctionOpType, sci.correction_time as correctionTime,
        sci.correction_remark as correctionRemark,sci.insured_code as insuredCode, sci.insured_name as insuredName, sci.insured_id_card as insuredIdCard,
        sci.product_code as productCode,sci.product_name as productName, sci.protocol_product_code as protocolProductCode, sci.insurance_type as insuranceType,
        sci.product_status as productStatus,sci.protocol_product_name as protocolProductName, sci.plan_code as planCode, sci.plan_name as planName,
        sci.long_short_flag as longShortFlag,sci.product_group as productGroup, sci.level2_code as level2Code, sci.level3_code as level3Code,
        sci.product_type as productType,sci.main_insurance as mainInsurance, sci.additional_risks_type as additionalRisksType, sci.effective_date as effectiveDate,
        sci.end_date as endDate,sci.renewal_year as renewalYear, sci.renewal_period as renewalPeriod, sci.payable_time as payableTime,
        sci.reality_time as realityTime,sci.insured_period_type as insuredPeriodType, sci.insured_period as insuredPeriod, sci.period_type as periodType,
        sci.payment_period_type as paymentPeriodType,sci.payment_period as paymentPeriod, sci.premium as premium, sci.business_premium as businessPremium,
        sci.discount_premium as discountPremium,sci.product_premium_total as productPremiumTotal, sci.product_premium as productPremium, sci.preservation_code as preservationCode,
        sci.endorsement_no as endorsementNo,sci.endorsement_time as endorsementTime, sci.preservation_type as preservationType, sci.preservation_project as preservationProject,
        sci.preservation_period as preservationPeriod,sci.preservation_effect_time as preservationEffectTime, sci.surrender_time as surrenderTime, sci.surrender_amount as surrenderAmount,
        sci.hesitate_surrender as hesitateSurrender,sci.agricultural_machinery_flag as agriculturalMachineryFlag, sci.single_propose_flag as singleProposeFlag, sci.policy_no as policyNo,
        sci.company_policy_no as companyPolicyNo

        from settlement_add_cost_info sci
        left join settlement_cost_policy_info scpi on sci.contract_code = scpi.contract_code  and sci.deleted = 0
        ${ew.customSqlSegment}
    </select>

    <select id="countZhnxAddCostInfo" resultType="java.lang.Integer">
        select
        count(sci.id)
        from settlement_add_cost_info sci
        left join settlement_cost_policy_info scpi on sci.contract_code = scpi.contract_code and sci.deleted = 0
        ${ew.customSqlSegment}
    </select>


    <update id="batchUpdateAutoCostInfo" parameterType="java.util.List">
        update settlement_add_cost_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="document_code =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.documentCode}
                </foreach>
            </trim>

            <trim prefix="auto_cost_code =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.autoCostCode}
                </foreach>
            </trim>

            <trim prefix="grant_month =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.grantMonth}
                </foreach>
            </trim>

            <trim prefix="cost_settlement_cycle =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.costSettlementCycle}
                </foreach>
            </trim>

        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>
</mapper>