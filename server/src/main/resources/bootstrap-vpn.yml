spring:
  application:
    name: settlement-center-core
  cloud:
    config:
      profile: ${spring.profiles.active}
      uri:
        - https://api-test.xiaowhale.com/config/
    bus:
      #Workaround for defect in https://github.com/spring-cloud/spring-cloud-bus/issues/124
      id: ${vcap.application.name:${spring.application.name:application}}:${vcap.application.instance_index:${spring.profiles.active:${local.server.port:${server.port:0}}}}:${vcap.application.instance_id:${random.value}}
  # 文件上传大小设置
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 总上传的数据大小
      max-request-size: 100MB
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  rabbitmq:
    host: amqp-test.xiaowhale.net
    port: 5672
    username: MjphbXFwLWNuLWk3bTI2b2h3eTAwNDpMVEFJNXQ5SkRkZDRIMUpXaXRhWmN2dkE=
    password: ODI5QTM5ODMzNjIwNDc1RjFBNzNCRDBEMjUwRTUyNEZGNzNFRTBDMDoxNjIzODM0MDc4MDkx
    virtual-host: ims-test
    # 消息确认模式
    publisher-confirms: true
    publisher-returns: true
    template:
      mandatory: true
    #消费端
    listener:
      simple:
        # 手动签收
        acknowledge-mode: manual
        #初始连接数量
        concurrency: 5
        #最大连接数量
        max-concurrency: 10
        #限流
        prefetch: 1
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.jdbc.Driver
#      url: ************************************************************************************************************************************************************
      url: *********************************************************************************************************************************************************************
      username: mp_business
      password: ahLeicaesie4pi
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      #Oracle需要打开注释
      #validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        #login-username: admin
        #login-password: admin
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true
##多数据源的配置
dynamic:
  datasource:
    master:
      driver-class-name: com.mysql.jdbc.Driver
#      url: *******************************************************************************************************************************************************
      url: *********************************************************************************************************************************************************************
      username: mp_business
      password: ahLeicaesie4pi
    slave:
      driver-class-name: com.mysql.jdbc.Driver
      #      url: ****************************************************************************************************************************************************************
      #      username: mp_business
      #      password: bohsai3Tax7ooz
#      url: *******************************************************************************************************************************************************
      url: *********************************************************************************************************************************************************************
      username: mp_business_read
      password: ut3hicee9Ush9u


#mybatis plus
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.mpolicy.*.entity
  global-config:
    #数据库相关配置
    db-config:
      #主键类型 UTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: AUTO
      #字段策略 IGNORED:"忽略判断",NOT_NULL:"非 NULL 判断"),NOT_EMPTY:"非空判断"
      field-strategy: NOT_NULL
      #驼峰下划线转换
      db-column-underline: true
      #刷新mapper 调试神器
      refresh-mapper: true
      #数据库大写下划线转换
      #capital-mode: true
      # Sequence序列接口实现类配置
      #key-generator: com.baomidou.mybatisplus.incrementer.OracleKeyGenerator
      #逻辑删除配置
      logic-delete-value: -1
      logic-not-delete-value: 0
      #自定义填充策略接口实现
      #meta-object-handler: com.baomidou.springboot.xxx
      #自定义SQL注入器
      sql-injector: com.baomidou.mybatisplus.core.injector.DefaultSqlInjector
    banner: false
  #原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'

#redis
redis:
  model: standalone
  open: true # 缓存服务开关，关闭或者异常都会抛响应异常
  host: redis-test.internal.xiaowhale.net
#  host: redis-test.xiaowhale.net
  password: sunhaijun:kaaYie4rie3juo # 密码（默认为空）
  port: 6379     #  模式的端口
  timeout: 6000  # 连接超时时长（毫秒）
  database: 0 # db
  poolMaxTotal: 64
  poolMaxIdle: 32
  poolMaxWait: 3000

# redisson 配置
redisson:
  redisModel: standalone
  password: sunhaijun:kaaYie4rie3juo
  database: 8
  # 单机配置
#  address: redis://redis-test.xiaowhale.net:6379
  address: redis://redis-test.internal.xiaowhale.net:6379

# 端口
server:
  port: 9402
# 注册中心
eureka:
  client:
    # 表示是否将自己注册在EurekaServer上，默认为true。由于当前应用就是EurekaServer，所以置为false
    register-with-eureka: false
    service-url:
      defaultZone: https://api-test.xiaowhale.com/eureka/eureka/
  instance:
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    prefer-ip-address: true
# 消息开关 + 行为注解
xjxh:
  rabbitmq:
    switch: true
  behavior:
    switch: true
# feign 本地测试
ims:
  feign:
    agent-server: https://api-test.xiaowhale.com/agent-server
    public-api: https://api-test.xiaowhale.com/public-api
    #public-api: http://localhost:8086/
    product-center: https://api-test.xiaowhale.com/product-center/
    #    product-center: http://localhost:8083/
    customer-server: https://api-test.xiaowhale.com/customer-server/
    #policy-center-server: http://localhost:8082/
    policy-center-server: https://api-test.xiaowhale.com/policy-center/
chongho:
  feign:
    operation: http://insurance-operation.tsg.cfpamf.com/
# 定时任务 xxl:
xxl:
  job:
    switch: false # 启动xxl-job⾃自动装配
    accessToken: xjxh_job # accessToken ⽬目前固定为xxlJobAccessToken admin:
    addresses: http://xxljob-test.internal.xiaowhale.net/xxl-job-admin # 测试环境
    appname: xxl-job-executor-settlement # 执⾏行行器器名称
    ip: '' # 容器器部署暂时不不⽤用
    port: 8981 # 定时任务调度器器和执⾏行行器器交互端⼝口，⾮非应⽤用
    logpath: logs/jobs # ⽇日志路路径
    logretentiondays: 10 # ⽇日志⽂文件保存天数
# 文件临时存储
insure:
  temp-file-path: logs/
# 日志
logging:
  level:
    org.springframework.cloud.netflix.feign: debug
    com.mpolicy.policy.client.EpPolicyClient: debug
    # com.mpolicy.open.client.HRMdmOpenApiClient: debug
logfile:
  dir: logs
  additivity: true
  include: logback-mq.xml
# feign配置
feign:
  hystrix:
    # 开启feign的hystrix支持,默认是false
    enabled: true
  client:
    config:
      default:
        connectTimeout: 8000
        readTimeout: 8000
      product-center:
        connectTimeout: 12000
        readTimeout: 12000
      ins-platform-api:
        connectTimeout: 20000
        readTimeout: 20000
      public-api:
        connectTimeout: 12000
        readTimeout: 12000

settlement:
  basicCost:
    calcRule:
      newPolicyStartTime: 2023-09-01
      preservationStartTime: 2023-09-01
      renewalStartTime: 2023-09-01
  autoCost:
    settlementDay: 5  #自动结算日，每月的某日结算
  temp-file-path: logs/


hystrix:
  command:
    longServiceMethod:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 30000
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 30000
