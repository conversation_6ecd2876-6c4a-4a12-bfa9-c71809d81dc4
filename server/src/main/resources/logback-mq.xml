<?xml version="1.0" encoding="UTF-8"?>
<!--
# 接合yml配置 log配置include 来装配扩展日志
logfile:
  include: logback-mq.xml
-->
<included>
    <!-- RECONCILE_CORE  -->
    <appender name="RECONCILE_CORE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 按天回滚 daily -->
            <fileNamePattern>${log_dir}/${server_name}/reconcile-core-%d{yyyy-MM-dd}-%i.log</fileNamePattern>
            <!-- 日志最大的历史 180天 -->
            <maxHistory>${maxHistory}</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!-- maxFileSize:这是活动文件的大小，默认值是10MB，这里设置为50MB -->
                <maxFileSize>${maxFileSize}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>%-12(%d{yyyy-MM-dd HH:mm:ss.SSS}) [%X{EagleEye-TraceID}%X{EagleEye-TraceID-Copy}] %level [%thread] %c[%L] %X{requestId} %X{Gray-Tag} %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 只打印info日志 -->
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- RECONCILE_MQ  -->
    <appender name="RECONCILE_MQ" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 按天回滚 daily -->
            <fileNamePattern>${log_dir}/${server_name}/reconcile-mq-%d{yyyy-MM-dd}-%i.log</fileNamePattern>
            <!-- 日志最大的历史 180天 -->
            <maxHistory>${maxHistory}</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!-- maxFileSize:这是活动文件的大小，默认值是10MB，这里设置为50MB -->
                <maxFileSize>${maxFileSize}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>%-12(%d{yyyy-MM-dd HH:mm:ss.SSS}) [%X{EagleEye-TraceID}%X{EagleEye-TraceID-Copy}] %level [%thread] %c[%L] %X{requestId} %X{Gray-Tag} %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 只打印info日志 -->
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- PROTOCOL_CORE  -->
    <appender name="PROTOCOL_CORE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 按天回滚 daily -->
            <fileNamePattern>${log_dir}/${server_name}/protocol-core%d{yyyy-MM-dd}-%i.log</fileNamePattern>
            <!-- 日志最大的历史 180天 -->
            <maxHistory>${maxHistory}</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!-- maxFileSize:这是活动文件的大小，默认值是10MB，这里设置为50MB -->
                <maxFileSize>${maxFileSize}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>%-12(%d{yyyy-MM-dd HH:mm:ss.SSS}) [%X{EagleEye-TraceID}%X{EagleEye-TraceID-Copy}] %level [%thread] %c[%L] %X{requestId} %X{Gray-Tag} %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 只打印info日志 -->
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- reconcile -->
    <logger name="reconcileCore" additivity="${additivity}">
        <appender-ref ref="RECONCILE_CORE"/>
    </logger>

    <logger name="reconcileMq" additivity="${additivity}">
        <appender-ref ref="RECONCILE_MQ"/>
    </logger>

    <!-- protocol -->
    <logger name="protocolCore" additivity="${additivity}">
        <appender-ref ref="PROTOCOL_CORE"/>
    </logger>
</included>
