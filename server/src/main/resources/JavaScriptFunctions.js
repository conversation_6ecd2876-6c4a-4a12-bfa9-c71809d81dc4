//给字符串增加contains方法
String.prototype.contains = function(obj) {
    if(this.indexOf(obj)>-1){
    		return true;
    } else {
    		return false;
	}
}

//给数组增加contains方法
Array.prototype.contains = function(obj) {
    var i = this.length;
    while (i--) {
        if (this[i] === obj) {
            return true;
        }
    }
    return false;
}

//给数组增加includes方法
Array.prototype.includes = function(obj) {
    var i = this.length;
    while (i--) {
        if (this[i] === obj) {
            return true;
        }
    }
    return false;
}

function inRange(srcValue,strRange){
	var from = eval(strRange.split("~")[0]);
	var to = eval(strRange.split("~")[1]);
	srcValue = eval(srcValue);
	if(srcValue >= from && srcValue <= to){
		return true;
	}else{
		return false;
	}
}


function inList(srcValue,strList){
	return eval((strList.split(",").contains(srcValue)));
}

function notInList(srcValue,strList){
	return eval(!(strList.split(",").contains(srcValue)))
}

function valueInList(srcValue,strList){
	var list = strList.split(",");
	var src = eval(srcValue);
	for(var i=0;i<list.length;i++){
		var dst = eval(list[i]);
		if(src==dst){
			return true;
		}
	}
	return false;
}

function valueNotInList(srcValue,strList){
	var list = strList.split(",");
	var src = eval(srcValue);
	for(var i=0;i<list.length;i++){
		var dst = eval(list[i]);
		if(src==dst){
			return false;
		}
	}
	return true;
}

function outOfRange(srcValue,strRange){
	var from = eval(strRange.split("~")[0]);
	var to = eval(strRange.split("~")[1]);
	srcValue = eval(srcValue);
	if(srcValue < from || srcValue > to){
		return true;
	}else{
		return false;
	}
}

//制保留2位小数，如：2，会在2后面补上00.即2.00
function toDecimal2(x) {
	var f = parseFloat(x);
	if (isNaN(f)) {
		return false;
	}
	var f = Math.round(x * 100) / 100;
	var s = f.toString();
	var rs = s.indexOf('.');
	if (rs < 0) {
		rs = s.length;
		s += '.';
	}
	while (s.length <= rs + 2) {
		s += '0';
	}
	return s;
}

Date.prototype.addDays = function(days) {
    var dat = new Date(this.valueOf());
    dat.setDate(dat.getDate() + days);
    return dat;
}

Date.prototype.minusYear = function(years){
    var dat = new Date(this.valueOf());
    dat.setFullYear(dat.getFullYear()-years);
    return dat;
}

Date.prototype.addYear = function(years){
    var dat = new Date(this.valueOf());
    dat.setFullYear(dat.getFullYear()+years);
    return dat;
}

Date.prototype.addMonth = function(month){
    var dat = new Date(this.valueOf());
    dat.setMonth(dat.getMonth()+month);
    return dat;
}

//前端配置被保人最高ageTop周岁后，获取从现在起往后推的天数
//针对场景：有的保险公司根据保单生效日时（而不是投保当天）的被保人年龄做校验，
//举例：被保人最高17周岁，以当前时间+1天为baseDays,返回的是 【当前时间】到【当前时间-17年】之间的天数
function getDays(baseDays,ageTop){
    var now = new Date();
    var birth = new Date();
    birth = birth.addDays(baseDays).minusYear(ageTop+1).addDays(1);
    var timeDiff = Math.abs(now.getTime() - birth.getTime());
    var diffDays = Math.ceil(timeDiff / (1000 * 3600 * 24));

    return diffDays;
}

// 结算日期和当前时间的间隔期
function getNowDiffDay(date2){
	var now = new Date();
	var newData = new Date(date2);
	var timeDiff = Math.abs(now.getTime() - newData.getTime());
	var diffDays = Math.ceil(timeDiff / (1000 * 3600 * 24));
	return diffDays;
}

//得到指定日期与当天相差的天数
function getDateDays(baseDays){
	s1 = new Date(baseDays);
	s2 = new Date();
	var days = s2.getTime() - s1.getTime();
	var time = parseInt(Math.abs(days) / (1000 * 60 * 60 * 24));

	return time;
}

//算出某日期时，某人的年龄。（精确到天，不考虑时分秒）
function getAge(baseDays, birthDay){
    var now = new Date();
    var birthDate = new Date(birthDay);
    var baseDate = now.addDays(baseDays);

    var yearDiff = baseDate.getFullYear() - birthDate.getFullYear();
    var monthDiff = baseDate.getMonth() - birthDate.getMonth();
    var dayDiff = baseDate.getDate()-birthDate.getDate();

    if(baseDate.getTime()<birthDate.getTime()){
        return -1;
    }

    if(monthDiff>0){
        return yearDiff;
    }
    if(monthDiff<0){
        return yearDiff-1;
    }

    if(dayDiff>=0){
        return yearDiff;
    }

    if(dayDiff<0){
        return yearDiff-1;
    }
}
//算出某日期时，某人的年龄。（精确到天，不考虑时分秒,返回小数）
function getAgeFloatByDate(baseDays, birthDay){
	 var now = new Date();
	  var birthDate = new Date(birthDay);
	  var baseDate = now.addDays(baseDays);
	  var yearDiff = baseDate.getFullYear() - birthDate.getFullYear();
	  var monthDiff = baseDate.getMonth() - birthDate.getMonth();
	  var dayDiff = baseDate.getDate()-birthDate.getDate();
	  if(baseDate.getTime()<birthDate.getTime()){
	      return -1;
	  }
	  var age=yearDiff;
	  if (monthDiff<0) {
		  age=yearDiff-1;
	    } else if (monthDiff==0) {
	      if (dayDiff<0) {
	    	  age=yearDiff-1;
	      }
	    }
	  //计算小数部分
	  var tmp_date = birthDate.addYear(age);
	  var times =baseDate.getTime() - tmp_date.getTime() ;
	  var days = parseInt(Math.abs(times) / (1000 * 60 * 60 * 24));
	  if(days>0){
		  var dot=("000" + days).substr(-3);
		  age= age+"."+dot;
	  }
	  return age;
}




//算出某日期时，某人的年龄。（精确到天，不考虑时分秒,小于1岁返回小数）
function getAgeFloat(baseDays, birthDay){
  var now = new Date();
  var birthDate = new Date(birthDay);
  var baseDate = now.addDays(baseDays);

  var yearDiff = baseDate.getFullYear() - birthDate.getFullYear();
  var monthDiff = baseDate.getMonth() - birthDate.getMonth();
  var dayDiff = baseDate.getDate()-birthDate.getDate();

  if(baseDate.getTime()<birthDate.getTime()){
      return -1;
  }
  if(yearDiff<1){//小于1岁时返回小数
	 var times  = baseDate.getTime() - birthDate.getTime();
	 var days = parseInt(Math.abs(times) / (1000 * 60 * 60 * 24));
	 return  parseFloat(days/1000);
  }

  if(monthDiff>0){
      return yearDiff;
  }
  if(monthDiff<0){
      return yearDiff-1;
  }

  if(dayDiff>=0){
      return yearDiff;
  }

  if(dayDiff<0){
      return yearDiff-1;
  }
}

//根据某个日期，算出某人的年龄。（精确到天，不考虑时分秒）
function getAgeByDate(baseDay, birthDay){
    var now = new Date();
    var birthDate = new Date(birthDay);
    var baseDate = new Date(baseDay);

    var yearDiff = baseDate.getFullYear() - birthDate.getFullYear();
    var monthDiff = baseDate.getMonth() - birthDate.getMonth();
    var dayDiff = baseDate.getDate()-birthDate.getDate();

    if(baseDate.getTime()<birthDate.getTime()){
        return -1;
    }

    if(monthDiff>0){
        return yearDiff;
    }
    if(monthDiff<0){
        return yearDiff-1;
    }

    if(dayDiff>=0){
        return yearDiff;
    }

    if(dayDiff<0){
        return yearDiff-1;
    }
}

//获取年月 当前日期加dayAfter天后所在月的monthAfer个月开始列举month个月 2017-06,2017-07
function getYearMonth(dayAfter,monthAfer,month){
	var result="";
    var now = new Date();
    var starDate = now.addDays(dayAfter).addMonth(monthAfer);
    starDate.setDate(1);
    var nowmonth
    for(i=0;i<month;i++){
    	nowmonth=starDate.getMonth();
    	nowmonth=nowmonth+1;
    	if(nowmonth<=9)
    	nowmonth="0"+nowmonth;
    	result+=starDate.getFullYear()+"-"+nowmonth+",";
    	starDate=starDate.addMonth(1);
    }
    result=result.substr(0,result.length-1)
    return result;
}
/**
 * 获取最低保费，最高保费对应的可选保额列表
 * @param minPrem 最低保费
 * @param maxPrem 最高保费
 * @param step   步长
 * @param multiple 倍数
 * @param rate  费率（最低保额multiple 所对应的保费）
 * @param
 */

function getAmountList(minPrem,maxPrem,step,multiple,rate){
	var result=new Array();
	minPrem=minPrem.replace("万","0000");
	maxPrem=maxPrem.replace("万","0000");
	multiple=multiple.replace("万","0000");
	var minAmount=Math.ceil(minPrem/rate)*multiple;//向上对multiple取整
	var maxAmount=Math.floor(maxPrem/rate)*multiple;//想下对multiple取整
//	console.log("minAmount,maxAmount:"+minAmount+","+maxAmount);
//	var step = getStep(minAmount,maxAmount,multiple);//步长固定，不再计算
	var num=Math.ceil((maxAmount-minAmount)/step);//向上取整
//	console.log("num:"+(maxAmount-minAmount)/step+","+num);
	for(var i=0;i<=num;i++){
		var curAmount=minAmount+step*i;
//		console.log("curAmount:"+curAmount);
		if(curAmount>9999) curAmount=(curAmount/10000)+"万"
		result[i]=curAmount;
	}
	return result.toString();
}
/*
 * 递归计算步长，大于100个时,步长*10倍
 */
function getStep(minAmount,maxAmount,multiple){
	var step=multiple;
	var sub=maxAmount-minAmount;
	if(sub>0){
		if(sub/multiple>100){
			step=getStep(minAmount,maxAmount,step*10);
		}
	}
	return step;
}

//对Date的扩展，将 Date 转化为指定格式的String
//月(M)、日(d)、小时(h)、分(m)、秒(s)、季度(q) 可以用 1-2 个占位符，
//年(y)可以用 1-4 个占位符，毫秒(S)只能用 1 个占位符(是 1-3 位的数字)
//例子：
//(new Date()).Format("yyyy-MM-dd hh:mm:ss.S") ==> 2006-07-02 08:09:04.423
//(new Date()).Format("yyyy-M-d h:m:s.S")      ==> 2006-7-2 8:9:4.18
Date.prototype.Format = function (fmt) { //author: meizz
 var o = {
     "M+": this.getMonth() + 1, //月份
      "d+": this.getDate(), //日
      "h+": this.getHours(), //小时
      "m+": this.getMinutes(), //分
      "s+": this.getSeconds(), //秒
      "q+": Math.floor((this.getMonth() + 3) / 3), //季度
      "S": this.getMilliseconds() //毫秒
  };
  if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
  for (var k in o)
  if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
  return fmt;
}

function getDateAfter(dayAfter){
    var now = new Date();
    var starDate = now.addDays(dayAfter);
    return starDate.Format("yyyy-MM-dd");
}

function getYearAfter(year){
    var now = new Date();
    var starDate = now.addYear(year);
    return starDate.Format("yyyy-MM-dd");
}
/**
 * 天安健康源附加住院津贴，份数的控制
 * @param mainPrem 主险保费
 * @param mainAmount 主险保额
 * 规则：主险保额（万）/份数》=1/2
 * 且1000小于主险保费《3000 份数《5
 * 3000小于主险保费《5000 份数《10
 * 5000小于主险保费《8000 份数《15
 * 8000小于主险保费《10000 份数《20
 * 10000小于主险保费   份数《25
 */
function TianAnFuJiaZhuYuanJinTieNumber(mainPrem,mainAmount){
	var result="1,1,1";
	mainPrem=mainPrem.replace("万","0000").replace("*10000","0000");
	mainAmount=parseInt(mainAmount.replace("万","0000").replace("*10000","0000")/10000);
	var maxNumber=mainAmount*2;//保额限制
	var maxNumber2=maxNumber;//保费限制
	if(mainPrem>1000&&mainPrem<=3000){
		maxNumber2=5;
	}else if(mainPrem>3000&&mainPrem<=5000){
		maxNumber2=10;
	}else if(mainPrem>5000&&mainPrem<=8000){
		maxNumber2=15;
	}else if(mainPrem>8000&&mainPrem<=10000){
		maxNumber2=20;
	}else if(mainPrem>10000){
		maxNumber2=25;
	}
	if(maxNumber>maxNumber2){//取最小值
		maxNumber=maxNumber2;
	}
	result="1,1,"+maxNumber;
	return result;
}

/**
 * 中意电投附加住院津贴 份数控制
 * @param mainPrem 主险保费
 * @param age 主险投保年龄
 * 份数规则：
 * 0-17岁，最大份数为1
 * 18岁以上，1万<=主险保费<=4万 最大份数=主险保费/10000;
 * 18岁以上，主险保费>4万 最大份数=4;
 * <AUTHOR> 20190802
 */
function ZhongYiZhuYuanJinTie(age,mainPrem){
//	var result="1,1,1";
	mainPrem=parseFloat(mainPrem.replace("万","0000").replace("*10000","0000"));
	var maxNumber=parseInt(mainPrem)/10000;
	if(age<18){
		maxNumber=1;
	}else if(age>=18){
		if(mainPrem>40000){
			maxNumber=4;
		}else if(mainPrem<=40000){
			maxNumber=parseInt(mainPrem)/10000;
		}
	}
	var result="1,1,"+maxNumber;
	return result;
}
/**
 * 替换函数
 * @param sourceStr 源字符串
 * @param tag 需要替换
 */
function getRepStr(sourceStr,tag){
	var result=sourceStr.replace(tag,"");
	return result;
}
