spring:
  application:
    name: settlement-center-core
  cloud:
    config:
      profile: ${spring.profiles.active}
      uri:
        - http://ims-config.tsg.xiaowhale.com/
    bus:
      #Workaround for defect in https://github.com/spring-cloud/spring-cloud-bus/issues/124
      id: ${vcap.application.name:${spring.application.name:application}}:${vcap.application.instance_index:${spring.profiles.active:${local.server.port:${server.port:0}}}}:${vcap.application.instance_id:${random.value}}
  # 文件上传大小设置
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 总上传的数据大小
      max-request-size: 100MB
# 端口
server:
  port: 9402
# 注册中心
eureka:
  client:
    # 表示是否将自己注册在EurekaServer上，默认为true。由于当前应用就是EurekaServer，所以置为false
    register-with-eureka: false
    service-url:
      defaultZone: https://api-test.xiaowhale.com/eureka/eureka/
  instance:
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    prefer-ip-address: true
# 消息开关 + 行为注解
xjxh:
  rabbitmq:
    switch: true
  behavior:
    switch: true
# feign 本地测试
ims:
  feign:
    agent-server: https://api-test.xiaowhale.com/agent-server
    public-api: https://api-test.xiaowhale.com/public-api
    #public-api: http://localhost:8086/
    product-center: https://api-test.xiaowhale.com/product-center/
#    product-center: http://localhost:8083/
    customer-server: https://api-test.xiaowhale.com/customer-server/
    #policy-center-server: http://localhost:8082/
    policy-center-server: https://api-test.xiaowhale.com/policy-center/
chongho:
  feign:
    operation: http://insurance-operation.tsg.cfpamf.com/
    insurance: http://insurance-admin.tsg.cfpamf.com/
dbc-service:
  url: http://dbc-service.tsg.cfpamf.com
bms:
  api:
    url: http://bms-service.tsg.cfpamf.com/
ms-messagepush-service:
  api:
    url: http://ms-messagepush.tsg.cfpamf.com/
oms-biz:
  api:
    url: http://oms-biz.tsg.cfpamf.com/
# 定时任务 xxl:
xxl:
  job:
    switch: false # 启动xxl-job⾃自动装配
    accessToken: xjxh_job # accessToken ⽬目前固定为xxlJobAccessToken admin:
    addresses: http://xxljob-test.internal.xiaowhale.net/xxl-job-admin # 测试环境
    appname: xxl-job-executor-settlement # 执⾏行行器器名称
    ip: '' # 容器器部署暂时不不⽤用
    port: 8981 # 定时任务调度器器和执⾏行行器器交互端⼝口，⾮非应⽤用
    logpath: logs/jobs # ⽇日志路路径
    logretentiondays: 10 # ⽇日志⽂文件保存天数
# 文件临时存储
insure:
  temp-file-path: logs/
# 日志
logging:
  level:
    org.springframework.cloud.netflix.feign: debug
    com.mpolicy.policy.client.EpPolicyClient: debug
    # com.mpolicy.open.client.HRMdmOpenApiClient: debug
logfile:
  dir: logs
  additivity: true
  include: logback-mq.xml
# feign配置
feign:
  hystrix:
    # 开启feign的hystrix支持,默认是false
    enabled: true
  client:
    config:
      default:
        connectTimeout: 8000
        readTimeout: 8000
      product-center:
        connectTimeout: 12000
        readTimeout: 12000
      ins-platform-api:
        connectTimeout: 20000
        readTimeout: 20000
      public-api:
        connectTimeout: 15000
        readTimeout: 15000

settlement:
  basicCost:
    calcRule:
      newPolicyStartTime: 2023-09-01
      preservationStartTime: 2023-09-01
      renewalStartTime: 2023-09-01
  autoCost:
    settlementDay: 5  #自动结算日，每月的某日结算
  temp-file-path: logs/

notify:
  group: InsurPolicyDataGroup

hystrix:
  command:
    longServiceMethod:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 30000
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 30000
