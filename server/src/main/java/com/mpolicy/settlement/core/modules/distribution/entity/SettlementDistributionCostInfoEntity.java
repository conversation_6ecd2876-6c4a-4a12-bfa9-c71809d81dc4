package com.mpolicy.settlement.core.modules.distribution.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@TableName("settlement_distribution_cost_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementDistributionCostInfoEntity implements Serializable {


        /**
         * 主键ID，自增
         */
        private Integer id;
        /**
         * 分销佣金编码
         */
        private String distCostCode;

        /**
         * 佣金归属人编码（与 channel_application_referrer 关联），不能为空
         */
        private String ownerCode;

        /**
         * 佣金归属人名称，允许为空
         */
        private String ownerName;

        /**
         * 佣金归属人农保工号，允许为空
         */
        private String ownerThirdCode;

        /**
         * 佣金归属人农保机构编码，允许为空
         */
        private String ownerThirdOrg;

        /**
         * 佣金归属人农保机构名称，允许为空
         */
        private String ownerThirdOrgName;

        /**
         * 保单号，不能为空
         */
        private String policyNo;

        /**
         * 批单号，不能为空
         */
        private String endorsementNo;

        /**
         * 农保订单号，允许为空
         */
        private String orderId;

        /**
         * 发放金额，默认 0.00
         */
        private BigDecimal grantAmount = BigDecimal.valueOf(0.00);

        /**
         * 月批次号，允许为空
         */
        private String monthlyBatchNo;
        /**
         * 同步结果推送分销系统 0未同步，1已同步
         */
        private Integer syncNotifyStatus;

        /**
         * 发放月份，允许为空
         */
        private String grantMonth;

        /**
         * 单据编号，允许为空
         */
        private String documentCode;

        /**
         * 自动结算支出编码，允许为空
         */
        private String autoCostCode;

        /**
         * 结算周期，允许为空
         */
        private String costSettlementCycle;

        /**
         * 确认状态：0-未确认，1-确认中，2-已确认，默认 0
         */
        private Integer confirmStatus = 0;

        /**
         * 确认时间，允许为空
         */
        private Date confirmTime;

        /**
         * 上游记录唯一键，允许为空
         */
        private Long sourceId;

        @ApiModelProperty("佣金归属人编码证件号")
        private String manageCode;

        @ApiModelProperty("分销订单id")
        private Long orderDistributionId;

        /**
         * 保单状态：1-承保成功，4-退保成功，允许为空
         */
        private String sourcePolicyStatus;

        /**
         * 创建时间，默认当前时间
         */
        private Date createTime;

        /**
         * 最后修改时间，默认当前时间并在更新时自动更新
         */
        private Date updateTime;

        /**
         * 删除标记：0-有效，-1-删除，默认 0
         */
        private Integer deleted = 0;

}
