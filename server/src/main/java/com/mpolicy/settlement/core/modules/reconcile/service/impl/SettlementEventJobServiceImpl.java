package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.common.reconcile.RefreshSettlementEventJobVo;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementEventJobDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.event.factory.SettlementEventFactory;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.SettlementEventHandler;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementEventJobService;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 结算交互事件受理表
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:33
 */
@Slf4j
@Service("settlementEventJobService")
public class SettlementEventJobServiceImpl extends ServiceImpl<SettlementEventJobDao, SettlementEventJobEntity> implements SettlementEventJobService {

    @Override
    public void refreshSettlementEventJob(RefreshSettlementEventJobVo params) {
        if (CollUtil.isEmpty(params.getIds()) && CollUtil.isEmpty(params.getPushEventCodeList())) {
            return;
        }
        List<SettlementEventJobEntity> settlementEventJobList = this.lambdaQuery()
                .in(CollUtil.isNotEmpty(params.getIds()), SettlementEventJobEntity::getId, params.getIds())
                .in(CollUtil.isNotEmpty(params.getPushEventCodeList()), SettlementEventJobEntity::getPushEventCode, params.getPushEventCodeList())
                .list();
        if (settlementEventJobList.isEmpty()){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("结算任务不存在"));
        }
        settlementEventJobList.forEach(action -> {
            if (params.getIncomeEventStatus() != null) {
                if (params.getIncomeEventStatus() == 0) {
                    action.setEventStatus(0);
                    action.setIncomeFinishTime(null);
                }
                if (StrUtil.isNotBlank(params.getIncomeEventMessage())) {
                    action.setIncomeEventMessage(params.getIncomeEventMessage());
                }
                action.setIncomeEventStatus(params.getIncomeEventStatus());
            }
            if (params.getContractIncomeEventStatus() != null) {
                if (params.getContractIncomeEventStatus() == 0) {
                    action.setEventStatus(0);
                    action.setContractIncomeFinishTime(null);
                }
                if (StrUtil.isNotBlank(params.getContractIncomeEventMessage())) {
                    action.setContractIncomeEventMessage(params.getContractIncomeEventMessage());
                }
                action.setContractIncomeEventStatus(params.getContractIncomeEventStatus());
            }

            if (params.getCostEventStatus() != null) {
                if (params.getCostEventStatus() == 0) {
                    action.setEventStatus(0);
                    action.setCostFinishTime(null);
                }
                if (StrUtil.isNotBlank(params.getCostEventMessage())) {
                    action.setCostEventMessage(params.getCostEventMessage());
                }
                action.setCostEventStatus(params.getCostEventStatus());
            }
        });
        // 批量更新数据
        updateBatchById(settlementEventJobList);

        //跑一下状态为0的数据
        List<SettlementEventJobEntity> settlementEventJobs = settlementEventJobList.stream().filter(f -> f.getEventStatus() == 0).collect(Collectors.toList());
        log.info("需要处理任务数:{}", settlementEventJobList.size());
        for (int i = 0; i < settlementEventJobs.size(); i++) {
            SettlementEventJobEntity eventJob = settlementEventJobs.get(i);
            log.info("开始处理第[{}]个任务={}", i + 1, JSONUtil.toJsonStr(eventJob));
            SettlementEventHandler settlementEventHandler = SettlementEventFactory.getSettlementEventHandler(eventJob.getEventType());
            if (settlementEventHandler != null) {
                log.info("执行获取需要处理的事件任务,事件类型={}", eventJob.getEventType());
                // 事件执行
                try {
                    settlementEventHandler.handle(eventJob);
                } catch (Exception e) {
                    log.info("处理事件id={}出现异常", eventJob.getId(), e);
                    this.updateById(eventJob);
                }
            } else {
                log.info(StrUtil.format("事件类型类型不支持，事件类型={}", eventJob.getEventType()));
            }
        }

    }

    public List<SettlementEventJobEntity> listSettlementCostFailEventJob(Date startTime){
        return this.baseMapper.listSettlementCostFailEventJob(startTime);
    }

    public List<SettlementEventJobEntity> listSettlementEventFailUnCalcCostEventJob(Date startTime){
        return this.baseMapper.listSettlementEventFailUnCalcCostEventJob(startTime);
    }

    public List<SettlementEventJobEntity> listSettlementCostEventJob(Date startTime){
        return this.baseMapper.listSettlementCostEventJob(startTime);
    }

    public List<SettlementEventJobEntity> listSettlementCostFailRecordByCostErrorCode(String costErrorCode){
        return this.lambdaQuery().eq(SettlementEventJobEntity::getCostErrorCode,costErrorCode)
                .eq(SettlementEventJobEntity::getCostEventStatus,-1)
                .ge(SettlementEventJobEntity::getCreateTime, DateUtil.parseDate("2024-06-01"))
                .orderByAsc(SettlementEventJobEntity::getUpdateTime).list();
    }
}
