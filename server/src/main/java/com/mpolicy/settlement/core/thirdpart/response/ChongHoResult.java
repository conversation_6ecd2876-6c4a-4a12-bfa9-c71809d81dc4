package com.mpolicy.settlement.core.thirdpart.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Objects;

/**
 * @param <T>
 * <AUTHOR>
 */
@Data
public class ChongHoResult<T> {
    /**
     * 默认成功code
     */
    public static final String SUCCESS_CODE = "0";

    /**
     * 默认成功message
     */
    public static final String SUCCESS_MESSAGE = "success";

    /**
     * 默认失败code
     */
    public static final String FAIL_CODE = "999";

    /**
     * 默认失败message
     */
    public static final String FAIL_MESSAGE = "接口异常";

    /**
     * 授权失败code
     */
    public static final String AUTH_FAIL_CODE = "401";

    /**
     * 授权失败message
     */
    public static final String AUTH_FAIL_MESSAGE = "授权失败请登录重试";

    /**
     * code
     */
    private String code;

    /**
     * 接口调用失败时会返回链路Id给前端，前端会存在日志库中；
     */
    private String traceId;
    /**
     * message
     */
    private String message;

    /**
     * 异常
     */
    @JsonIgnore
    private Throwable throwable;

    /**
     * data
     */
    private T data;


    /**
     * 默认成功CommonResult
     */
    public static <T> ChongHoResult<T> ok(T v) {
        ChongHoResult<T> result = new ChongHoResult<>();
        result.setCode(SUCCESS_CODE);
        result.setMessage(SUCCESS_MESSAGE);
        result.setData(v);
        return result;
    }

    public static ChongHoResult nok() {
        ChongHoResult result = new ChongHoResult<>();
        result.setCode(FAIL_CODE);
        result.setMessage(FAIL_MESSAGE);
        return result;
    }

    /**
     * 默认失败CommonResult
     */
    public static ChongHoResult nok(Exception e) {
        ChongHoResult result = new ChongHoResult<>();
        result.setCode(FAIL_CODE);
        result.setMessage(FAIL_MESSAGE);
        result.setThrowable(e);
        return result;
    }

    /**
     * 调用是否成功
     *
     * @return
     */
    public boolean isSuccess() {
        return Objects.equals(code, SUCCESS_CODE);
    }

}
