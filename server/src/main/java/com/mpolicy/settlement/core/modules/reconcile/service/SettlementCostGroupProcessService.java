package com.mpolicy.settlement.core.modules.reconcile.service;

import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.policy.common.ep.policy.qry.details.GroupPreservationInsuredVo;
import com.mpolicy.product.common.base.ProductBase;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.PolicyPreservationDetailDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;

import java.util.List;
import java.util.Map;

public interface SettlementCostGroupProcessService {

    public List<SettlementCostInfoEntity> builderGroupSettlementCostInfoByInsuredList(SettlementEventJobEntity eventJob,
                                                                                      CostSubjectEnum subjectEnum,
                                                                                      SettlementEventTypeEnum eventType,
                                                                                      Map<String, ProductBase> productMap,
                                                                                      EpContractInfoVo policyInfo, PolicyPreservationDetailDto preservationDetail, List<GroupPreservationInsuredVo> insuredInfoVos);
    List<SettlementCostInfoEntity> builderNewGroupSettlementCostInfoByInsuredList(SettlementEventJobEntity eventJob,
                                                                                  CostSubjectEnum subjectEnum,
                                                                                  SettlementEventTypeEnum eventType,
                                                                                  Map<String, ProductBase> productMap,
                                                                                  EpContractInfoVo policyInfo, PolicyPreservationDetailDto preservationDetail);


    List<SettlementCostInfoEntity> builderGroupSettlementCostInfoByProductList(SettlementEventJobEntity eventJob,
                                                                               CostSubjectEnum subjectEnum,
                                                                               SettlementEventTypeEnum eventType,
                                                                               Map<String, ProductBase> productMap,
                                                                               EpContractInfoVo policyInfo,
                                                                               PolicyPreservationDetailDto preservationDetail);

}
