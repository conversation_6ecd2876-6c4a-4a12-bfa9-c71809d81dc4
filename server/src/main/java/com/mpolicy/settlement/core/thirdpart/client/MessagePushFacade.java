package com.mpolicy.settlement.core.thirdpart.client;

import com.mpolicy.settlement.core.thirdpart.request.DingTalkGroupV2Request;
import com.mpolicy.settlement.core.thirdpart.request.DingTalkGroupV3Request;
import com.mpolicy.settlement.core.thirdpart.response.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@FeignClient(name = "ms-messagepush-service", url = "${ms-messagepush-service.api.url}")
@Configuration
public interface MessagePushFacade {

    @PostMapping(value = "/v1/dingTalk/sendDingTalkMessageToGroupV2")
    Result<Void> sendDingTalkMessageToGroupV2(@RequestBody @Valid DingTalkGroupV2Request req);

    @PostMapping({"/v1/dingTalk/sendDingTalkMessageToGroupV3"})
    Result<Void> sendDingTalkMessageToGroupV3(@RequestBody DingTalkGroupV3Request var1);

}
