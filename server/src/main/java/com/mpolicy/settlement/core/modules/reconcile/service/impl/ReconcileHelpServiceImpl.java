package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.policy.common.ep.policy.EpAgentInfoVo;
import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.policy.common.ep.policy.EpPolicyChannelInfoVo;
import com.mpolicy.service.common.woodpecker.dto.MonitorWoodpeckerData;
import com.mpolicy.settlement.core.common.Constant;
import com.mpolicy.settlement.core.common.reconcile.RectificationOneVo;
import com.mpolicy.settlement.core.common.reconcile.RefreshSettlementPolicyInfoCommissionVo;
import com.mpolicy.settlement.core.enums.ReconcileTypeEnum;
import com.mpolicy.settlement.core.helper.SettlementBaseHelper;
import com.mpolicy.settlement.core.modules.channel.entity.ChannelInfoEntity;
import com.mpolicy.settlement.core.modules.channel.service.ChannelInfoService;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementGenerateTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.factory.SettlementEventFactory;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.SettlementEventHandler;
import com.mpolicy.settlement.core.modules.reconcile.service.ReconcileHelpService;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementEventJobService;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementPolicyInfoService;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import com.mpolicy.settlement.core.modules.reconcile.vo.RectificationVo;
import com.mpolicy.settlement.core.modules.reconcile.vo.RefreshPolicyPremiumVo;
import com.mpolicy.settlement.core.modules.referrer.entity.ChannelApplicationReferrerEntity;
import com.mpolicy.settlement.core.modules.referrer.service.ChannelApplicationReferrerService;
import com.mpolicy.settlement.core.service.common.AgentBaseService;
import com.mpolicy.settlement.core.service.common.OpenApiBaseService;
import com.mpolicy.settlement.core.service.common.PolicyCenterBaseClient;
import com.mpolicy.settlement.core.utils.LogUtil;
import com.mpolicy.web.common.utils.Query;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 业务一体化帮助服务实现
 *
 * <AUTHOR>
 * @since 2023-09-14 12:04
 */
@Slf4j
@Service
public class ReconcileHelpServiceImpl implements ReconcileHelpService {

    @Autowired
    private ChannelApplicationReferrerService channelApplicationReferrerService;

    @Autowired
    private ChannelInfoService channelInfoService;

    @Autowired
    private AgentBaseService agentBaseService;

    @Autowired
    private OpenApiBaseService openApiBaseService;

    @Autowired
    private PolicyCenterBaseClient policyCenterBaseClient;

    @Autowired
    private SettlementPolicyInfoService settlementPolicyInfoService;

    @Autowired
    private SettlementEventJobService settlementEventJobService;

    @Autowired
    protected TransactionTemplate transactionTemplate;
    @Override
    public void refreshPolicyInfo(int startPage, int testStatus) {
        log.info("手动更新未结算的保单信息start....");
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        Map<String, Object> params = new HashMap<>();

        int page = startPage > 0 ? startPage : 1;
        params.put("limit", "500");
        int finishModifySize = 0;
        while (true) {
            // 1 分页获取结算保单信息，结算成功的不进行修正了
            params.put("page", String.valueOf(page));
            IPage<SettlementPolicyInfoEntity> sourceList =
                    settlementPolicyInfoService.page(new Query<SettlementPolicyInfoEntity>().getPage(params),
                            new LambdaQueryWrapper<SettlementPolicyInfoEntity>().eq(SettlementPolicyInfoEntity::getReconcileStatus, 0).isNull(SettlementPolicyInfoEntity::getCreateUser).orderByAsc(SettlementPolicyInfoEntity::getId));
            if (sourceList.getRecords().isEmpty()) {
                log.info("订单完成，总执行页数:{}", page);
                break;
            }
            // 2 过滤处理保单相关信息
            List<SettlementPolicyInfoEntity> list =
                    sourceList.getRecords().stream().filter(x -> StringUtils.isNotBlank(x.getPolicyNo())).collect(Collectors.toList());
            log.info("当前处理page={} 本次修正的保单数量为:{}", page, list.size());
            List<SettlementPolicyInfoEntity> needModifyList = list.stream().map(result -> {
                try {
                    EpContractInfoVo policyInfo =
                            policyCenterBaseClient.getPolicyInfoByPolicyCode(result.getPolicyNo(), false);
                    if (policyInfo != null) {
                        result.setCustomerCode(policyInfo.getChannelInfo().getMiniAppCustomerCode());
                        result.setCreateUser("settlement_event");
                        // 1 主代理人信息,getAgentInfoList 获取不到降级去从ContractBaseInfo获取
                        policyInfo.getAgentInfoList().stream().filter(x -> x.getMainFlag() == 1).findFirst().ifPresent(p -> {
                            result.setMainAgentCode(p.getAgentCode());
                            result.setOrgCode(p.getOrgCode());
                        });
                        if (StringUtils.isBlank(result.getMainAgentCode())) {
                            result.setMainAgentCode(policyInfo.getContractBaseInfo().getAgentCode());
                            result.setOrgCode(policyInfo.getContractBaseInfo().getOrgCode());
                        }
                        // 2 渠道推荐人类型
                        EpPolicyChannelInfoVo channelInfo = policyInfo.getChannelInfo();
                        BeanUtils.copyProperties(channelInfo, result);
                        // 3 业务类型 zhnx渠道就为农保渠道
                        if (StringUtils.equalsIgnoreCase(Constant.DEFAULT_CS_CHANNEL,
                                policyInfo.getChannelInfo().getChannelCode())) {
                            result.setBusinessType(2);
                        } else {
                            result.setBusinessType(1);
                        }
                        // 4 获取推荐人人姓名、渠道名称
                        if (StringUtils.isNotBlank(result.getReferrerCode())) {
                            if (result.getReferrerType() != null && result.getReferrerType() == 1) {
                                // 如果为代理人形态，获取代理人姓名
                                if (StringUtils.isNotBlank(result.getReferrerCode())) {
                                    Optional.ofNullable(agentBaseService.getAgentInfoByAgentCode(result.getReferrerCode(), false)).ifPresent(x -> {
                                        result.setReferrerName(x.getAgentName());
                                        result.setReferrerWno(x.getBusinessCode());
                                        result.setChannelBranchCode(x.getOrgCode());
                                        result.setChannelBranchName(x.getOrgName());
                                    });
                                }
                            } else {
                                // 如果为渠道推荐人获取推到人信息
                                if (StringUtils.isNotBlank(result.getReferrerCode())) {
                                    Optional.ofNullable(channelApplicationReferrerService.lambdaQuery().eq(ChannelApplicationReferrerEntity::getReferrerCode, result.getReferrerCode()).one()).ifPresent(x -> {
                                        result.setReferrerName(x.getReferrerName());
                                        result.setReferrerWno(x.getReferrerWno());
                                        result.setReferrerOgrCode(x.getReferrerOgrCode());
                                        result.setReferrerOgrName(x.getReferrerOgrName());
                                    });
                                }
                            }
                        } else {
                            // 添加业务健康数据表
                            SettlementBaseHelper.addMonitorWoodpeckerDataAsync(MonitorWoodpeckerData.builder().businessType("refresh_policy_info").businessName("手动刷新保单").eventData(result.getPolicyNo()).businessDesc(StrUtil.format("保单号={} 未能获取渠道推荐人信息", result.getPolicyNo())).build());
                        }

                        // 10 渠道信息
                        if (StringUtils.isNotBlank(result.getChannelCode())) {
                            Optional.ofNullable(channelInfoService.lambdaQuery().eq(ChannelInfoEntity::getChannelCode
                                    , result.getChannelCode()).one()).ifPresent(x -> {
                                result.setChannelName(x.getChannelName());
                            });
                        }
                        return result;
                    } else {
                        // 添加业务健康数据表
                        SettlementBaseHelper.addMonitorWoodpeckerDataAsync(MonitorWoodpeckerData.builder().businessType("refresh_policy_info").businessName("手动刷新保单").eventData(result.getPolicyNo()).businessDesc(StrUtil.format("保单号={} 获取详情失败，无法进行处理手动修正", result.getPolicyNo())).build());
                    }
                    return null;
                } catch (Exception e) {
                    log.warn("手动更新未结算的保单信息出现异常，保单号={}, 详情={}", result.getPolicyNo(), JSON.toJSONString(result), e);
                    // 添加业务健康数据表
                    SettlementBaseHelper.addMonitorWoodpeckerDataAsync(MonitorWoodpeckerData.builder().businessType(
                            "refresh_policy_info").businessName("手动刷新保单-异常").eventData(StrUtil.format("{}:{}",
                            result.getPolicyNo(), e)).businessDesc(StrUtil.format("保单号={} 手动修正操作异常",
                            result.getPolicyNo())).build());
                    return null;
                }
            }).filter(Objects::nonNull).collect(Collectors.toList());
            // 执行更新
            finishModifySize = finishModifySize + needModifyList.size();
            log.info("手动更新未结算的保单信息, 本次更新保单数量:{}", needModifyList.size());
            settlementPolicyInfoService.saveOrUpdateBatch(needModifyList);
            page++;
        }
        // 设置结束
        stopWatch.stop();
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        log.info("手动更新未结算的保单信息完成...., 总更新保单结算数量= {} 执行时间:{}", finishModifySize, millis);
    }

    @Override
    public void refreshPolicyReconcileSuccess(int startPage, int testStatus) {
        log.info("【完成对账的】手动更新未结算的保单信息start....");
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        Map<String, Object> params = new HashMap<>();

        int page = startPage > 0 ? startPage : 1;
        params.put("limit", "500");
        int finishModifySize = 0;
        while (true) {
            // 1 分页获取结算保单信息，结算成功的不进行修正了
            params.put("page", String.valueOf(page));
            IPage<SettlementPolicyInfoEntity> sourceList =
                    settlementPolicyInfoService.page(new Query<SettlementPolicyInfoEntity>().getPage(params),
                            new LambdaQueryWrapper<SettlementPolicyInfoEntity>().eq(SettlementPolicyInfoEntity::getReconcileStatus, 3).isNull(SettlementPolicyInfoEntity::getCreateUser).orderByAsc(SettlementPolicyInfoEntity::getId));
            if (sourceList.getRecords().isEmpty()) {
                log.info("订单完成，总执行页数:{}", page);
                break;
            }
            // 2 过滤处理保单相关信息
            List<SettlementPolicyInfoEntity> list =
                    sourceList.getRecords().stream().filter(x -> StringUtils.isNotBlank(x.getPolicyNo())).collect(Collectors.toList());
            log.info("【完成对账的】当前处理page={} 本次修正的保单数量为:{}", page, list.size());
            List<SettlementPolicyInfoEntity> needModifyList = list.stream().map(result -> {
                try {
                    EpContractInfoVo policyInfo =
                            policyCenterBaseClient.getPolicyInfoByPolicyCode(result.getPolicyNo(), false);
                    if (policyInfo != null) {
                        result.setCustomerCode(policyInfo.getChannelInfo().getMiniAppCustomerCode());
                        result.setCreateUser("settlement_event");
                        // 1 主代理人信息,getAgentInfoList 获取不到降级去从ContractBaseInfo获取
                        policyInfo.getAgentInfoList().stream().filter(x -> x.getMainFlag() == 1).findFirst().ifPresent(p -> {
                            result.setMainAgentCode(p.getAgentCode());
                            result.setOrgCode(p.getOrgCode());
                        });
                        if (StringUtils.isBlank(result.getMainAgentCode())) {
                            result.setMainAgentCode(policyInfo.getContractBaseInfo().getAgentCode());
                            result.setOrgCode(policyInfo.getContractBaseInfo().getOrgCode());
                        }
                        // 2 渠道推荐人类型
                        EpPolicyChannelInfoVo channelInfo = policyInfo.getChannelInfo();
                        BeanUtils.copyProperties(channelInfo, result);
                        // 3 业务类型 zhnx渠道就为农保渠道
                        if (StringUtils.equalsIgnoreCase(Constant.DEFAULT_CS_CHANNEL,
                                policyInfo.getChannelInfo().getChannelCode())) {
                            result.setBusinessType(2);
                        } else {
                            result.setBusinessType(1);
                        }
                        // 4 获取推荐人人姓名、渠道名称
                        if (StringUtils.isNotBlank(result.getReferrerCode())) {
                            if (result.getReferrerType() != null && result.getReferrerType() == 1) {
                                // 如果为代理人形态，获取代理人姓名
                                if (StringUtils.isNotBlank(result.getReferrerCode())) {
                                    Optional.ofNullable(agentBaseService.getAgentInfoByAgentCode(result.getReferrerCode(), false)).ifPresent(x -> {
                                        result.setReferrerName(x.getAgentName());
                                        result.setReferrerWno(x.getBusinessCode());
                                        result.setChannelBranchCode(x.getOrgCode());
                                        result.setChannelBranchName(x.getOrgName());
                                    });
                                }
                            } else {
                                // 如果为渠道推荐人获取推到人信息
                                if (StringUtils.isNotBlank(result.getReferrerCode())) {
                                    Optional.ofNullable(channelApplicationReferrerService.lambdaQuery().eq(ChannelApplicationReferrerEntity::getReferrerCode, result.getReferrerCode()).one()).ifPresent(x -> {
                                        result.setReferrerName(x.getReferrerName());
                                        result.setReferrerWno(x.getReferrerWno());
                                        result.setReferrerOgrCode(x.getReferrerOgrCode());
                                        result.setReferrerOgrName(x.getReferrerOgrName());
                                    });
                                }
                            }
                        } else {
                            // 添加业务健康数据表
                            SettlementBaseHelper.addMonitorWoodpeckerDataAsync(MonitorWoodpeckerData.builder().businessType("refresh_policy_info").businessName("手动刷新保单").eventData(result.getPolicyNo()).businessDesc(StrUtil.format("保单号={} 未能获取渠道推荐人信息", result.getPolicyNo())).build());
                        }

                        // 10 渠道信息
                        if (StringUtils.isNotBlank(result.getChannelCode())) {
                            Optional.ofNullable(channelInfoService.lambdaQuery().eq(ChannelInfoEntity::getChannelCode
                                    , result.getChannelCode()).one()).ifPresent(x -> {
                                result.setChannelName(x.getChannelName());
                            });
                            if (StringUtils.isBlank(result.getChannelName())) {
                                result.setChannelName("未知渠道");
                            }
                        }
                        return result;
                    } else {
                        // 添加业务健康数据表
                        SettlementBaseHelper.addMonitorWoodpeckerDataAsync(MonitorWoodpeckerData.builder().businessType("refresh_policy_info").businessName("手动刷新保单").eventData(result.getPolicyNo()).businessDesc(StrUtil.format("保单号={} 获取详情失败，无法进行处理手动修正", result.getPolicyNo())).build());
                    }
                    return null;
                } catch (Exception e) {
                    log.warn("【完成对账的】手动更新未结算的保单信息出现异常，保单号={}, 详情={}", result.getPolicyNo(), JSON.toJSONString(result)
                            , e);
                    // 添加业务健康数据表
                    SettlementBaseHelper.addMonitorWoodpeckerDataAsync(MonitorWoodpeckerData.builder().businessType(
                            "refresh_policy_info").businessName("手动刷新保单-异常").eventData(StrUtil.format("{}:{}",
                            result.getPolicyNo(), e)).businessDesc(StrUtil.format("保单号={} 手动修正操作异常",
                            result.getPolicyNo())).build());
                    return null;
                }
            }).filter(Objects::nonNull).collect(Collectors.toList());
            // 执行更新
            finishModifySize = finishModifySize + needModifyList.size();
            log.info("【完成对账的】手动更新未结算的保单信息, 本次更新保单数量:{}", needModifyList.size());
            settlementPolicyInfoService.saveOrUpdateBatch(needModifyList);
            page++;
        }
        // 设置结束
        stopWatch.stop();
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        log.info("手动更新未结算的保单信息完成...., 总更新保单结算数量= {} 执行时间:{}", finishModifySize, millis);
    }


    @Override
    public void refreshPolicyInfoByCodeList(List<String> policyCodes) {
        log.info("【指定保单】手动更新未结算的保单信息,保单号集合=【{}】start....", policyCodes);
        if (CollUtil.isEmpty(policyCodes)) {
            return;
        }
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        List<SettlementPolicyInfoEntity> list =
                settlementPolicyInfoService.lambdaQuery().in(SettlementPolicyInfoEntity::getPolicyNo, policyCodes).list();
        List<SettlementPolicyInfoEntity> needModifyList = list.stream().map(result -> {
            try {
                EpContractInfoVo policyInfo = policyCenterBaseClient.getPolicyInfoByPolicyCode(result.getPolicyNo(),
                        false);
                if (policyInfo != null) {
                    result.setCustomerCode(policyInfo.getChannelInfo().getMiniAppCustomerCode());
                    result.setCreateUser("settlement_event");
                    // 1 主代理人信息,getAgentInfoList 获取不到降级去从ContractBaseInfo获取
                    policyInfo.getAgentInfoList().stream().filter(x -> x.getMainFlag() == 1).findFirst().ifPresent(p -> {
                        result.setMainAgentCode(p.getAgentCode());
                        result.setOrgCode(p.getOrgCode());
                    });
                    if (StringUtils.isBlank(result.getMainAgentCode())) {
                        result.setMainAgentCode(policyInfo.getContractBaseInfo().getAgentCode());
                        result.setOrgCode(policyInfo.getContractBaseInfo().getOrgCode());
                    }
                    // 2 渠道推荐人类型
                    EpPolicyChannelInfoVo channelInfo = policyInfo.getChannelInfo();
                    BeanUtils.copyProperties(channelInfo, result);
                    // 3 业务类型 zhnx渠道就为农保渠道
                    if (StringUtils.equalsIgnoreCase(Constant.DEFAULT_CS_CHANNEL,
                            policyInfo.getChannelInfo().getChannelCode())) {
                        result.setBusinessType(2);
                    } else {
                        result.setBusinessType(1);
                    }
                    // 4 获取推荐人人姓名、渠道名称
                    if (StringUtils.isNotBlank(result.getReferrerCode())) {
                        if (result.getReferrerType() != null && result.getReferrerType() == 1) {
                            // 如果为代理人形态，获取代理人姓名
                            if (StringUtils.isNotBlank(result.getReferrerCode())) {
                                Optional.ofNullable(agentBaseService.getAgentInfoByAgentCode(result.getReferrerCode()
                                        , false)).ifPresent(x -> {
                                    result.setReferrerName(x.getAgentName());
                                    result.setReferrerWno(x.getBusinessCode());
                                    result.setChannelBranchCode(x.getOrgCode());
                                    result.setChannelBranchName(x.getOrgName());
                                });
                            }
                        } else {
                            // 如果为渠道推荐人获取推到人信息
                            if (StringUtils.isNotBlank(result.getReferrerCode())) {
                                Optional.ofNullable(channelApplicationReferrerService.lambdaQuery().eq(ChannelApplicationReferrerEntity::getReferrerCode, result.getReferrerCode()).one()).ifPresent(x -> {
                                    result.setReferrerName(x.getReferrerName());
                                    result.setReferrerWno(x.getReferrerWno());
                                    result.setReferrerOgrCode(x.getReferrerOgrCode());
                                    result.setReferrerOgrName(x.getReferrerOgrName());
                                });
                            }
                        }
                    } else {
                        // 添加业务健康数据表
                        SettlementBaseHelper.addMonitorWoodpeckerDataAsync(MonitorWoodpeckerData.builder().businessType("refresh_policy_info").businessName("指定保单刷新保单").eventData(result.getPolicyNo()).businessDesc(StrUtil.format("保单号={} 未能获取渠道推荐人信息", result.getPolicyNo())).build());
                    }

                    // 10 渠道信息
                    if (StringUtils.isNotBlank(result.getChannelCode())) {
                        Optional.ofNullable(channelInfoService.lambdaQuery().eq(ChannelInfoEntity::getChannelCode,
                                result.getChannelCode()).one()).ifPresent(x -> {
                            result.setChannelName(x.getChannelName());
                        });
                        if (StringUtils.isBlank(result.getChannelName())) {
                            result.setChannelName("未知渠道");
                        }
                    }
                    return result;
                } else {
                    // 添加业务健康数据表
                    SettlementBaseHelper.addMonitorWoodpeckerDataAsync(MonitorWoodpeckerData.builder().businessType(
                            "refresh_policy_info").businessName("指定保单刷新保单").eventData(result.getPolicyNo()).businessDesc(StrUtil.format("保单号={} 获取详情失败，无法进行处理手动修正", result.getPolicyNo())).build());
                }
                return null;
            } catch (Exception e) {
                log.warn("【指定保单】手动更新未结算的保单信息出现异常，保单号={}, 详情={}", result.getPolicyNo(), JSON.toJSONString(result), e);
                // 添加业务健康数据表
                SettlementBaseHelper.addMonitorWoodpeckerDataAsync(MonitorWoodpeckerData.builder().businessType(
                        "refresh_policy_info").businessName("指定保单刷新保单-异常").eventData(StrUtil.format("{}:{}",
                        result.getPolicyNo(), e)).businessDesc(StrUtil.format("保单号={} 手动修正操作异常",
                        result.getPolicyNo())).build());
                return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
        // 执行更新
        log.info("【指定保单】手动更新未结算的保单信息, 本次更新保单数量:{}", needModifyList.size());
        settlementPolicyInfoService.saveOrUpdateBatch(needModifyList);
        // 设置结束
        stopWatch.stop();
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        log.info("手动更新未结算的保单信息完成...., 总更新保单结算数量= {} 执行时间:{}", list.size(), millis);
    }

    @Override
    public void refreshOfflinePolicyOrgInfo() {
        List<SettlementPolicyInfoEntity> offlinePolicyList =
                settlementPolicyInfoService.lambdaQuery().eq(SettlementPolicyInfoEntity::getSalesType, 1).eq(SettlementPolicyInfoEntity::getReconcileStatus, 0).list();
        log.info("获取到业财线下单未完成对账的保单明细纪录，纪录数={}", offlinePolicyList.size());
        if (offlinePolicyList.isEmpty()) {
            return;
        }

        // 获取需要修订的保单明细
        List<SettlementPolicyInfoEntity> needModifyList = offlinePolicyList.stream().map(f -> {
            EpContractInfoVo policyInfo = policyCenterBaseClient.getPolicyInfoByPolicyCode(f.getPolicyNo(), false);
            if (policyInfo != null) {
                EpAgentInfoVo epAgentInfoVo =
                        policyInfo.getAgentInfoList().stream().filter(x -> x.getMainFlag() == 1).findFirst().orElse(null);
                if (epAgentInfoVo != null) {
                    if (!StringUtils.equals(f.getOrgCode(), epAgentInfoVo.getOrgCode())) {
                        f.setMainAgentCode(epAgentInfoVo.getAgentCode());
                        f.setOrgCode(epAgentInfoVo.getOrgCode());
                        return f;
                    }
                } else {
                    // 添加业务健康数据表
                    SettlementBaseHelper.addMonitorWoodpeckerDataAsync(MonitorWoodpeckerData.builder().businessType(
                            "refreshOfflinePolicyOrgInfo").businessName("手动刷新线下保单机构信息").eventData(f.getPolicyNo()).businessDesc(StrUtil.format("保单号={} 手动刷新线下保单机构获取详情为null", f.getPolicyNo())).build());
                }
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        log.info("获取到业财线下单未完成对账的保单明细纪录，需要修正的纪录数={}", needModifyList.size());
        if (!needModifyList.isEmpty()) {
            // 批量写入
            if (needModifyList.size() > 600) {
                List<List<SettlementPolicyInfoEntity>> partition = ListUtils.partition(needModifyList, 600);
                partition.forEach(x -> {
                    settlementPolicyInfoService.saveOrUpdateBatch(x);
                });
            } else {
                settlementPolicyInfoService.saveOrUpdateBatch(needModifyList);
            }
        }
    }


    /**
     * 刷新保费信息
     *
     * @param policyList
     */
    @Override
    public void refreshPolicyPremium(List<RefreshPolicyPremiumVo> policyList) {
        Map<String, BigDecimal> policyMap =
                policyList.stream().collect(Collectors.toMap(RefreshPolicyPremiumVo::getPolicyNo,
                        RefreshPolicyPremiumVo::getPremium, (v1, v2) -> {
                            // 这里处理自己的逻辑
                            return v2;
                        }));

        List<SettlementPolicyInfoEntity> settlementPolicyInfoList =
                settlementPolicyInfoService.lambdaQuery().eq(SettlementPolicyInfoEntity::getRectificationMark,
                        StatusEnum.INVALID.getCode()).in(SettlementPolicyInfoEntity::getPolicyNo,
                        new ArrayList<>(policyMap.keySet())).list();
        if (settlementPolicyInfoList.isEmpty()) {
            return;
        }
        long count = settlementPolicyInfoList.stream().filter(f -> StrUtil.isNotBlank(f.getReconcileCode())).count();
        if (count > 0) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("存在已经结算的数据，不能刷新保费信息"));
        }

        settlementPolicyInfoList.forEach(action -> {
            action.setRectificationMark(StatusEnum.NORMAL.getCode());
        });
        //讲数据变成冲正数据
        settlementPolicyInfoService.updateBatchById(settlementPolicyInfoList);


        List<String> eventSourceCodeList =
                settlementPolicyInfoList.stream().map(SettlementPolicyInfoEntity::getEventSourceCode).collect(Collectors.toList());
        // 封装冲正的数据
        List<SettlementPolicyInfoEntity> rectificationList = settlementPolicyInfoList.stream().map(m -> {
            SettlementPolicyInfoEntity settlementPolicyInfo = BeanUtil.copyProperties(m,
                    SettlementPolicyInfoEntity.class);
            // 将保费取反
            String settlementCode = PolicySettlementUtils.createCodeLastNumber("SC");
            settlementPolicyInfo.setId(null);
            settlementPolicyInfo.setSettlementCode(settlementCode);
            settlementPolicyInfo.setPremium(m.getPremium().negate());
            settlementPolicyInfo.setSettlementAmount(m.getSettlementAmount().negate());
            settlementPolicyInfo.setSettlementGenerateType(SettlementGenerateTypeEnum.AUTO_CORRECTION.getCode());
            return settlementPolicyInfo;
        }).collect(Collectors.toList());
        // 插入冲正数据
        settlementPolicyInfoService.saveList(rectificationList);

        // 封装修正后的数据
        List<SettlementPolicyInfoEntity> correctList = settlementPolicyInfoList.stream().map(m -> {
            BigDecimal premium = policyMap.get(m.getPolicyNo());
            SettlementPolicyInfoEntity settlementPolicyInfo = BeanUtil.copyProperties(m,
                    SettlementPolicyInfoEntity.class);
            // 将保费取反
            String settlementCode = PolicySettlementUtils.createCodeLastNumber("SC");
            settlementPolicyInfo.setId(null);
            settlementPolicyInfo.setSettlementCode(settlementCode);
            settlementPolicyInfo.setPremium(premium);
            settlementPolicyInfo.setSettlementGenerateType(SettlementGenerateTypeEnum.BUSINESS_EVENTS.getCode());
            return settlementPolicyInfo;
        }).collect(Collectors.toList());
        // 插入修正后数据
        settlementPolicyInfoService.saveList(correctList);

        // 重新结算费率信息
        RefreshSettlementPolicyInfoCommissionVo refreshSettlementPolicyInfoCommission =
                new RefreshSettlementPolicyInfoCommissionVo();
        refreshSettlementPolicyInfoCommission.setEventSourceCodeList(eventSourceCodeList);
        settlementPolicyInfoService.refreshSettlementPolicyInfoCommission(refreshSettlementPolicyInfoCommission);


    }

    /**
     * 冲正数据
     *
     * @param rectificationList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rectification(List<RectificationVo> rectificationList) {
        Map<String, RectificationVo> rectificationMap =
                rectificationList.stream().collect(Collectors.toMap(RectificationVo::getEventSourceCode, v -> v, (v1,
                                                                                                                  v2) -> {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("存在相同的事件编码,请检查数据"));
                }));
        //获取非冲正数据
        List<SettlementPolicyInfoEntity> settlementPolicyInfoList =
                settlementPolicyInfoService.lambdaQuery().in(SettlementPolicyInfoEntity::getEventSourceCode,
                        new ArrayList<>(rectificationMap.keySet())).eq(SettlementPolicyInfoEntity::getRectificationMark, StatusEnum.INVALID.getCode()).list();
        if (settlementPolicyInfoList.isEmpty()) {
            return;
        }
        long count = settlementPolicyInfoList.stream().filter(f -> StrUtil.isNotBlank(f.getReconcileCode())).count();
        if (count > 0) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("存在已经结算的数据，不能刷新保费信息"));
        }
        settlementPolicyInfoList.forEach(action -> {
            action.setRectificationMark(StatusEnum.NORMAL.getCode());
            action.setReconcileExecuteStatus(2);
            action.setReconcileExecuteDesc("冲正数据无需对账");
        });
        //讲数据变成冲正数据
        settlementPolicyInfoService.updateBatchById(settlementPolicyInfoList);

        // 封装冲正的数据
        List<SettlementPolicyInfoEntity> saveRectificationList = settlementPolicyInfoList.stream().map(m -> {
            SettlementPolicyInfoEntity settlementPolicyInfo = BeanUtil.copyProperties(m,
                    SettlementPolicyInfoEntity.class);
            // 将保费取反
            String settlementCode = PolicySettlementUtils.createCodeLastNumber("SC");
            settlementPolicyInfo.setId(null);
            settlementPolicyInfo.setSettlementCode(settlementCode);
            settlementPolicyInfo.setPremium(m.getPremium().negate());
            settlementPolicyInfo.setSettlementAmount(m.getSettlementAmount().negate());
            settlementPolicyInfo.setSettlementGenerateType(SettlementGenerateTypeEnum.AUTO_CORRECTION.getCode());
            return settlementPolicyInfo;
        }).collect(Collectors.toList());
        // 插入冲正数据
        settlementPolicyInfoService.saveList(saveRectificationList);


        // 重新执行job
        List<SettlementEventJobEntity> list =
                settlementEventJobService.lambdaQuery().in(SettlementEventJobEntity::getPushEventCode,
                        new ArrayList<>(rectificationMap.keySet())).list();
        if (list.isEmpty()) {
            log.info("没有需要执行的Job任务");
            return;
        }
        list.forEach(action -> {
            RectificationVo rectification = rectificationMap.get(action.getPushEventCode());
            String eventRequest = action.getEventRequest();
            if (rectification != null && StrUtil.isNotBlank(eventRequest) && JSONUtil.isJson(eventRequest)) {
                JSONObject req = JSONUtil.parseObj(action.getEventRequest());
                // 存在被保人信息的直接报错 因为没有处理呢
                if (req.containsKey("insuredInfoList")) {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("存在被保人信息,请检查数据"));
                }
                //变更保费
                if (rectification.getPremium() != null) {
                    if (req.containsKey("surrenderCash")) {
                        req.set("surrenderCash", rectification.getPremium());
                    } else if (req.containsKey("premium")) {
                        req.set("premium", rectification.getPremium());
                    }

                }
                // 变更保全编码
                if (StrUtil.isNotBlank(rectification.getEndorsementNo())) {
                    action.setEndorsementNo(rectification.getEndorsementNo());
                    if (req.containsKey("endorsementNo")) {
                        req.set("endorsementNo", rectification.getEndorsementNo());
                    }
                }
                action.setEventRequest(req.toString());
            }
        });
        // 更新数据
        settlementEventJobService.updateBatchById(list);

        // 判断是否存在费用变更,如果存在费用变更更新一下请求信息中的费用信息
        log.info("执行获取需要处理的事件条数={},", list.size());
        // 2 获取事件工厂服务
        for (int i = 0; i < list.size(); i++) {
            SettlementEventJobEntity eventJob = list.get(i);
            eventJob.setIncomeEventStatus(StatusEnum.INVALID.getCode());
            eventJob.setContractIncomeEventStatus(StatusEnum.INVALID.getCode());
            log.info("开始处理第[{}]个任务id={}", i + 1, eventJob.getId());
            SettlementEventHandler settlementEventHandler =
                    SettlementEventFactory.getSettlementEventHandler(eventJob.getEventType());
            if (settlementEventHandler != null) {
                log.info("执行获取需要处理的事件任务,事件类型={}", eventJob.getEventType());
                // 事件执行
                try {
                    settlementEventHandler.handle(eventJob);
                } catch (Exception e) {
                    String error = StrUtil.format("Exception: {}", LogUtil.printLog(e));
                    log.info("处理事件id={}出现异常", error);
                    settlementEventJobService.updateById(eventJob);
                    // 添加业务健康数据表
                    SettlementBaseHelper.addMonitorWoodpeckerDataAsync(MonitorWoodpeckerData.builder().businessType(eventJob.getEventType()).businessName(eventJob.getEventName()).eventData(JSON.toJSONString(eventJob)).businessDesc(StrUtil.format("事件id={} 结算事件处理出现未知异常", eventJob.getId())).build());
                }
            } else {
                log.warn(StrUtil.format("事件类型类型不支持，事件类型={}", eventJob.getEventType()));
            }
            log.info("执行获取需要处理的事件任务完成,事件id={}", eventJob.getId());
        }
    }

    /**
     * 单一数据处理
     *
     * @param vo
     */
    @Override
    public void rectificationOne(RectificationOneVo vo) {
        if (StrUtil.isBlank(vo.getEventSourceCode()) && CollUtil.isEmpty(vo.getEventSourceCodeList())) {
            return;
        }
        //获取非冲正数据
        List<SettlementPolicyInfoEntity> settlementPolicyInfoList =
                settlementPolicyInfoService.lambdaQuery()
                        .eq(StrUtil.isNotBlank(vo.getEventSourceCode()), SettlementPolicyInfoEntity::getEventSourceCode, vo.getEventSourceCode())
                        .in(CollUtil.isNotEmpty(vo.getEventSourceCodeList()), SettlementPolicyInfoEntity::getEventSourceCode, vo.getEventSourceCodeList())
                        .eq(SettlementPolicyInfoEntity::getRectificationMark, StatusEnum.INVALID.getCode())
                        .eq(vo.getReconcileType() != null, SettlementPolicyInfoEntity::getReconcileType, vo.getReconcileType())
                        .list();
        if (settlementPolicyInfoList.isEmpty()) {
            return;
        }
        long count = settlementPolicyInfoList.stream().filter(f -> StrUtil.isNotBlank(f.getReconcileCode())).count();
        if (count > 0) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("存在已经结算的数据，不能进行冲正处理"));
        }

        settlementPolicyInfoList.forEach(action -> {
            action.setRectificationMark(StatusEnum.NORMAL.getCode());
            action.setReconcileExecuteStatus(2);
            action.setSettlementAmount(action.getSettlementAmount()==null?BigDecimal.ONE:action.getSettlementAmount());
            action.setReconcileExecuteDesc("冲正数据无需对账");
        });
        Boolean sendResult = transactionTemplate.execute((status) -> {
            try {
                //讲数据变成冲正数据
                settlementPolicyInfoService.updateBatchById(settlementPolicyInfoList);

                // 封装冲正的数据
                List<SettlementPolicyInfoEntity> saveRectificationList = settlementPolicyInfoList.stream().map(m -> {
                    SettlementPolicyInfoEntity settlementPolicyInfo = BeanUtil.copyProperties(m,
                            SettlementPolicyInfoEntity.class);
                    // 将保费取反
                    String settlementCode = PolicySettlementUtils.createCodeLastNumber("SC");
                    settlementPolicyInfo.setId(null);
                    settlementPolicyInfo.setSettlementCode(settlementCode);
                    settlementPolicyInfo.setPremium(m.getPremium().negate());
                    settlementPolicyInfo.setProductPremiumTotal(m.getProductPremiumTotal().negate());
                    settlementPolicyInfo.setSettlementAmount(m.getSettlementAmount().negate());
                    settlementPolicyInfo.setSettlementGenerateType(SettlementGenerateTypeEnum.AUTO_CORRECTION.getCode());
                    return settlementPolicyInfo;
                }).collect(Collectors.toList());
                // 插入冲正数据
                settlementPolicyInfoService.saveList(saveRectificationList);

                return true;
            } catch (Exception e) {
                status.setRollbackOnly();
                log.warn("处理冲证数据异常：", e);
                return false;
            }
        });
        if (Boolean.FALSE.equals(sendResult)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("处理冲证数据异常，请稍后重试"));
        }

        if (StatusEnum.INVALID.getCode().equals(vo.getRunJob())) {
            return;
        }
        // 重新执行job
        SettlementEventJobEntity eventJob =
                settlementEventJobService.lambdaQuery().eq(SettlementEventJobEntity::getPushEventCode,
                        vo.getEventSourceCode()).one();
        if (eventJob == null) {
            log.info("没有需要执行的Job任务");
            return;
        }
        // 重新执行对账单任务明细操作
        eventJob.setEventStatus(0);
        if (vo.getReconcileType() != null) {
            if (ReconcileTypeEnum.PROTOCOL.getCode().equals(vo.getReconcileType())) {
                eventJob.setIncomeEventStatus(StatusEnum.INVALID.getCode());
            } else if (ReconcileTypeEnum.CONTRACT.getCode().equals(vo.getReconcileType())) {
                eventJob.setContractIncomeEventStatus(StatusEnum.INVALID.getCode());
            }
        } else {
            eventJob.setIncomeEventStatus(StatusEnum.INVALID.getCode());
            eventJob.setContractIncomeEventStatus(StatusEnum.INVALID.getCode());
        }

        SettlementEventHandler settlementEventHandler =
                SettlementEventFactory.getSettlementEventHandler(eventJob.getEventType());
        if (settlementEventHandler != null) {
            log.info("执行获取需要处理的事件任务,事件类型={}", eventJob.getEventType());
            // 事件执行
            try {
                settlementEventHandler.handle(eventJob);
            } catch (Exception e) {
                String error = StrUtil.format("Exception: {}", LogUtil.printLog(e));
                log.info("处理事件id={}出现异常", error);
                settlementEventJobService.updateById(eventJob);
                // 添加业务健康数据表
                SettlementBaseHelper.addMonitorWoodpeckerDataAsync(MonitorWoodpeckerData.builder().businessType(eventJob.getEventType()).businessName(eventJob.getEventName()).eventData(JSON.toJSONString(eventJob)).businessDesc(StrUtil.format("事件id={} 结算事件处理出现未知异常", eventJob.getId())).build());
            }
        } else {
            log.warn(StrUtil.format("事件类型类型不支持，事件类型={}", eventJob.getEventType()));
        }
    }
}
