package com.mpolicy.settlement.core.modules.distribution.dao;

import com.mpolicy.service.common.mapper.ImsBaseMapper;
import com.mpolicy.settlement.core.modules.add.entity.SettlementAddCostInfoEntity;
import com.mpolicy.settlement.core.modules.distribution.entity.SettlementDistributionCostInfoEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SettlementDistributionCostInfoDao extends ImsBaseMapper<SettlementDistributionCostInfoEntity> {

    int batchUpdateAutoCostInfo(@Param("list") List<SettlementDistributionCostInfoEntity> list);
}
