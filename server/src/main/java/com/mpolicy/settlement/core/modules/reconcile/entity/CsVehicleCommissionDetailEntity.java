package com.mpolicy.settlement.core.modules.reconcile.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;

/**
 * 农保一单一议佣金明细表
 * 
 * <AUTHOR>
 * @date 2023-07-05 21:42:05
 */
@TableName("cs_vehicle_commission_detail")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CsVehicleCommissionDetailEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId
	private Integer id;
	/**
	 * 订单id
	 */
	private String orderId;
	/**
	 * 保单号
	 */
	private String policyNo;
	/**
	 * 被保人证件号
	 */
	private String insuredIdNumber;
	/**
	 * 计划id
	 */
	private Integer planId;
	/**
	 * 险种id
	 */
	private Integer riskId;
	/**
	 * 记账时间
	 */
	private Date accountTime;
	/**
	 * 保单状态，同sm_order_insured中的appStatus
	 */
	private String policyStatus;
	/**
	 * 险种保费
	 */
	private BigDecimal amount;
	/**
	 * 支付佣金id
	 */
	private Integer paymentCommissionId;
	/**
	 * 支付佣金比列
	 */
	private BigDecimal paymentRate;
	/**
	 * 支付佣金
	 */
	private BigDecimal paymentAmount;
	/**
	 * 结算佣金id
	 */
	private Integer settlementCommissionId;
	/**
	 * 结算佣金比例
	 */
	private BigDecimal settlementRate;
	/**
	 * 结算佣金
	 */
	private BigDecimal settlementAmount;
	/**
	 * 折算佣金id
	 */
	private Integer conversionCommissionId;
	/**
	 * 折算佣金比例
	 */
	private BigDecimal conversionRate;
	/**
	 * 折算佣金
	 */
	private BigDecimal conversionAmount;
	/**
	 * 
	 */
	private Integer enabledFlag;
	/**
	 * 
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 
	 */
	private String createBy;
	/**
	 * 
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 
	 */
	private String updateBy;
	/**
	 * 缴费期数
	 */
	private Integer termNum;
	/**
	 * 支付险种json串
	 */
	private String paymentRiskJson;
	/**
	 * 结算险种json串
	 */
	private String settlementRiskJson;
	/**
	 * 折算险种json串
	 */
	private String conversionRiskJson;
	/**
	 * 佣金受益人
	 */
	private String commissionUserId;
	/**
	 * 业务类型 common 普通佣金，renewal 续期佣金，correct批改佣金(增保、减保)
	 */
	private String businessType;
	/**
	 * 业务id
	 */
	private String businessId;
	/**
	 * 是否导入佣金 0:否 1:是
	 */
	private Integer importFlag;
	/**
	 * 订单原始金额
	 */
	private BigDecimal originalAmount;
	/**
	 * 保司保单号
	 */
	private String thPolicyNo;
	/**
	 * 保司批单号
	 */
	private String thEndorsementNo;
}
