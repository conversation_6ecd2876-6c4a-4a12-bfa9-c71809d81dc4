package com.mpolicy.settlement.core.thirdpart.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * OcrIdCard 信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022/6/8
 */
@Data
@ApiModel(value = "OcrIdCardRes", description = "OcrIdCardRes")
public class OcrIdCardResponse implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码", example = "13800138000", required = true)
    private String mobile;
}
