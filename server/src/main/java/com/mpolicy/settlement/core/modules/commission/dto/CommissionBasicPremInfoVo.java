package com.mpolicy.settlement.core.modules.commission.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class CommissionBasicPremInfoVo implements Serializable {

    private static final long serialVersionUID = 4483156482758197096L;
    /**
     * 保单号
     */
    private String policyNo;
    /**
     * 协议产品编码
     */
    private String productCode;
    /**
     * 所属主协议产品编码
     */
    private String mainProductCode;
    /**
     * 结算客户中文名称 必填
     */
    private String settlementSubjectName;
    /**
     * 渠道信息
     */
    private String channelCode;
    /**
     * 协议有效期 yyyy-MM-dd 必填
     */
    private String effectiveDate;
    /**
     * 投保计划
     */
    private String productPlan;
    /**
     * 保障期间
     */
    private String coveragePeriod;
    /**
     * 满期年龄
     */
    private String expireAge;
    /**
     * 缴费方式
     */
    private String paymentType;
    /**
     * 缴费期
     */
    private String paymentPeriod;
    /**
     * 缴费期间类型
     */
    private String paymentPeriodType;
    /**
     * 投保人年龄
     */
    private Integer applicantAge;
    /**
     * 投保人性别:男/女
     */
    private String applicantGender;
    /**
     * 被保人年龄
     */
    private Integer insuredAge;
    /**
     * 被保人性别:男/女
     */
    private String insuredGender;
    /**
     * 投保类nt:新单/ 1:续保
     */
    private Integer insuranceType;
    /**
     * 续期年期
     */
    private Integer renewalYear;
    /**
     * 续期期数
     */
    private Integer renewalPeriod;
    /**
     * 适用分公司
     */
    private String orgCode;
    /**
     * 是否自保件 0:否;1:是
     */
    private String selfPreservation;
}
