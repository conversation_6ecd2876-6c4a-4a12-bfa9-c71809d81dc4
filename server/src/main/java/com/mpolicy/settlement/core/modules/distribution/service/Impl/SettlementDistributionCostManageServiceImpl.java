package com.mpolicy.settlement.core.modules.distribution.service.Impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.common.add.BatchSyncNotifyDto;
import com.mpolicy.settlement.core.modules.add.entity.SettlementAddCostInfoEntity;
import com.mpolicy.settlement.core.modules.add.enums.SyncRecordStatusEnum;
import com.mpolicy.settlement.core.modules.autocost.dto.cache.OrganizationCache;
import com.mpolicy.settlement.core.modules.autocost.enums.ConfirmStatusEnum;
import com.mpolicy.settlement.core.modules.channel.service.ChannelApplicationService;
import com.mpolicy.settlement.core.modules.distribution.dto.UserDetailDto;
import com.mpolicy.settlement.core.modules.distribution.entity.SettlementDistributionCostInfoEntity;
import com.mpolicy.settlement.core.modules.distribution.service.SettlementDistributionCostInfoService;
import com.mpolicy.settlement.core.modules.distribution.service.SettlementDistributionCostManageService;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import com.mpolicy.settlement.core.modules.referrer.entity.ChannelApplicationReferrerEntity;
import com.mpolicy.settlement.core.modules.referrer.service.ChannelApplicationReferrerService;
import com.mpolicy.settlement.core.thirdpart.client.InsuranceFeign;
import com.mpolicy.settlement.core.thirdpart.request.DistributionCommissionQuery;
import com.mpolicy.settlement.core.thirdpart.request.WhaleAddCommissionPushQuery;
import com.mpolicy.settlement.core.thirdpart.response.AddCommissionSettlementPushDto;
import com.mpolicy.settlement.core.thirdpart.response.OrderDistributionCommissionListVo;
import com.mpolicy.settlement.core.thirdpart.response.OrderDistributionCommissionVo;
import com.mpolicy.settlement.core.thirdpart.response.UserDetailVO;
import com.mpolicy.settlement.core.thirdpart.service.BmsClient;
import com.mpolicy.settlement.core.thirdpart.service.InsuranceClient;
import com.netflix.discovery.converters.Auto;
import com.sun.jndi.dns.DnsClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SettlementDistributionCostManageServiceImpl implements SettlementDistributionCostManageService {

    public static Integer QUERY_MAX_NUM = 1000;
    @Autowired
    private InsuranceClient insuranceClient;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private SettlementDistributionCostInfoService settlementDistributionCostInfoService;
    @Autowired
    private ChannelApplicationReferrerService channelApplicationReferrerService;
    @Autowired
    private BmsClient bmsClient;


    public void batchAddSyncDistRecord(String monthlyBatchNo){
        log.info("开始同步月批次号为{}分销佣金明细",monthlyBatchNo);
        //如果任务为未处理，状态为作废，
        Integer pageSize = 1;
        while(true){
            List<SettlementDistributionCostInfoEntity> list = querySettlementDistributionByBatch(monthlyBatchNo,QUERY_MAX_NUM,pageSize);
            log.info("当前第{}页处理记录数:{}",pageSize,list.size());
            //查不到数据后，更新为成功
            if(CollectionUtils.isEmpty(list)){
                break;
            }
            settlementDistributionCostInfoService.saveList(list);

            pageSize++;
        }
        log.info("同步月批次号为{}分销佣金明细同步完成",monthlyBatchNo);
    }

    private List<SettlementDistributionCostInfoEntity> querySettlementDistributionByBatch(String batchNo, int size , int pageSize ){
        DistributionCommissionQuery query = new DistributionCommissionQuery();
        query.setMonthBatchNo(batchNo);
        query.setSize(size);
        query.setPageSize(pageSize);
        List<OrderDistributionCommissionListVo> distList = insuranceClient.getDistributionCommissionByPageV2(query);
        if(CollectionUtils.isEmpty(distList)){
            return Collections.EMPTY_LIST;
        }
        //证件号
        List<String> manageCodes = Lists.newArrayList();
        for(OrderDistributionCommissionListVo list: distList){
            manageCodes.addAll(list.getCommissionVos().stream().map(OrderDistributionCommissionVo::getManageCode).collect(Collectors.toList()));
        }

        //Map<String,ChannelApplicationReferrerEntity> referrerEntityMap = mapChannelApplicationReferrer(manageCodes);
        Map<String,UserDetailDto> userDetailDtoMap = mapReferrer(manageCodes);
        List<SettlementDistributionCostInfoEntity> result = Lists.newArrayList();
        distList.stream().forEach(c->{
            //ChannelApplicationReferrerEntity referrer = referrerEntityMap.get(c.getManageCode());

            c.getCommissionVos().stream().forEach(m->{
                UserDetailDto referrer = userDetailDtoMap.get(m.getManageCode());
                if(Objects.isNull(referrer)){
                    String msg = StrUtil.format("分销佣金同步，未找到manageCode={}的推荐人信息",m.getManageCode());
                    log.warn(msg);
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(msg));
                }
                SettlementDistributionCostInfoEntity entity = SettlementDistributionCostInfoEntity.builder()
                        .distCostCode(PolicySettlementUtils.createCodeLastNumber("DCC"))
                        .manageCode(m.getManageCode())
                        .ownerName(m.getManageName())
                        .ownerThirdCode(referrer.getEmployeeCode())
                        .ownerThirdOrg(referrer.getOrgCode())
                        .ownerThirdOrgName(referrer.getOrgName())
                        .policyNo(m.getPolicyNo())
                        .endorsementNo(m.getEndorsementNo()!=null?m.getEndorsementNo():"")
                        .orderId(m.getOrderId())
                        .grantAmount(m.getCommissionAmt())
                        .monthlyBatchNo(m.getMonthlyBatchNo())
                        .sourceId(c.getId())
                        .sourcePolicyStatus(m.getPolicyStatus())
                        .confirmStatus(0)
                        .deleted(0)
                        .syncNotifyStatus(0)
                        .orderDistributionId(m.getOrderDistributionId())
                        .build();
                result.add(entity);
            });
        });
        return result;
    }

    private Map<String,ChannelApplicationReferrerEntity> mapChannelApplicationReferrer(List<String> referrerCodes){
        if(CollectionUtils.isEmpty(referrerCodes)){
            return Collections.EMPTY_MAP;
        }
        return channelApplicationReferrerService.lambdaQuery()
                .in(ChannelApplicationReferrerEntity::getReferrerCode,referrerCodes)
                .list().stream().collect(Collectors.toMap(ChannelApplicationReferrerEntity::getReferrerCode, Function.identity(), (k1, k2) -> k1));
    }

    private Map<String,UserDetailDto> mapReferrer(List<String> referrerCodes){
        if(CollectionUtils.isEmpty(referrerCodes)){
            return Collections.EMPTY_MAP;
        }

        return referrerCodes.stream().distinct().map(c->{
            UserDetailDto dto = new UserDetailDto();
            log.info("分销佣金同步，调用dms获取证件号为={}的推荐人信息",c);
            UserDetailVO userDetailVO = bmsClient.getUserDetailVOByIdcard(c);
            log.info("分销佣金同步，调用dms获取证件号为={}的推荐人信息",JSON.toJSONString(userDetailVO));
            if(userDetailVO==null){
                return null;
            }
            dto.setEmployeeCode(userDetailVO.getJobNumber());
            dto.setEmployeeName(userDetailVO.getEmployeeName());
            dto.setOrgCode(userDetailVO.getOrgCode());
            dto.setOrgName(userDetailVO.getOrgName());
            dto.setIdCardNo(c);

            return dto;
        }).filter(d->Objects.nonNull(d)).collect(Collectors.toMap(UserDetailDto::getIdCardNo, Function.identity(), (k1, k2) -> k1));

    }

    public void syncConfirmStatusToDist(){

        while(true){
            List<SettlementDistributionCostInfoEntity> list = settlementDistributionCostInfoService.lambdaQuery()
                    .eq(SettlementDistributionCostInfoEntity::getSyncNotifyStatus, 0)
                    .last("limit 500").list();

            if(CollectionUtils.isEmpty(list)){
                break;
            }
            List<Long> sourceIds = list.stream().map(SettlementDistributionCostInfoEntity::getSourceId).distinct().collect(Collectors.toList());
            boolean result = insuranceClient.updateSyncState(sourceIds);
            if(result){
                List<Integer> ids = list.stream().map(SettlementDistributionCostInfoEntity::getId).collect(Collectors.toList());
                settlementDistributionCostInfoService.lambdaUpdate()
                        .set(SettlementDistributionCostInfoEntity::getSyncNotifyStatus,1)
                        .in(SettlementDistributionCostInfoEntity::getId,ids)
                        .update();
            }else{
                log.warn("调用分销异常，跳出循环，等下次job运行");
                break;
            }

        }
    }
}
