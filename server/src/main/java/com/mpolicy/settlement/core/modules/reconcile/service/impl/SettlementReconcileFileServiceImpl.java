package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementReconcileFileDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileFileEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcileFileService;

/**
 * 保司结算对账单关联文件
 *
 * <AUTHOR>
 * @since  2023-05-21 22:28:34
 */
@Slf4j
@Service("settlementReconcileFileService")
public class SettlementReconcileFileServiceImpl extends ServiceImpl<SettlementReconcileFileDao, SettlementReconcileFileEntity> implements SettlementReconcileFileService {

}
