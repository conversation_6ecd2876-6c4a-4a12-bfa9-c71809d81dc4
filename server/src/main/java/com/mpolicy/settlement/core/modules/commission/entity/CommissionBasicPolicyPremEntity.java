package com.mpolicy.settlement.core.modules.commission.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName("commission_basic_policy_prem")
public class CommissionBasicPolicyPremEntity implements Serializable {


    private static final long serialVersionUID = 4176714923563900117L;
    /**
     * id
     */
    @TableId
    private Integer id;
    /**
     * 保单号
     */
    private String policyNo;
    /**
     * 费率编码
     */
    private String premCode;
    /**
     * 保单年期
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer year;
    /**
     * 缴费期次
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer period;
    /**
     * 批单号
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String batchCode;

    /**
     * 险种编码
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String productCode;

    /**
     * 险种名称
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String productName;
    /**
     * 保费
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal premium;
    /**
     * 基础佣金费率
     */
    private BigDecimal commissionRate;
    /**
     * 车船税（元）
     */
    private BigDecimal vehicleVesselTax;
    /**
     * 车船税费率
     */
    private BigDecimal vehicleVesselTaxRate;
    /**
     * 结算保司编码
     */
    private String settlementCompanyCode;
    /**
     * 结算保司名称
     */
    private String settlementCompanyName;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
