package com.mpolicy.settlement.core.modules.distribution.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.settlement.core.modules.distribution.entity.SettlementDistributionCostInfoEntity;

import java.util.Date;
import java.util.List;

public interface SettlementDistributionCostInfoService extends IService<SettlementDistributionCostInfoEntity> {
    int saveList(List<SettlementDistributionCostInfoEntity> list);

    int batchUpdateAutoCostInfo(List<SettlementDistributionCostInfoEntity> list);

    boolean updateConfirmedStatus(String programmeCode, String costSettlementCycle, List<String> costSubjectCodes, String confirmUser, Date confirmTime);
}
