package com.mpolicy.settlement.core.modules.reconcile.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.vo.SqlHelperVo;

import java.util.List;

/**
 * 保司结算对账单
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:39
 */
public interface SettlementReconcileInfoService extends IService<SettlementReconcileInfoEntity> {

    /**
     * 生成对账单明细
     *
     * @param reconcileInfo
     */
    void generateDetail(SettlementReconcileInfoEntity reconcileInfo);


    void settlementPolicyInfoToXls(SettlementReconcileInfoEntity reconcileInfo, List<SettlementPolicyInfoEntity> settlementPolicyInfoList);

    /**
     * sql处理器
     * @param input
     */
    void sqlHelper(SqlHelperVo input);
}

