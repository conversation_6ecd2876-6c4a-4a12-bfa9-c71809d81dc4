package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.common.redis.redisson.RedissLockUtil;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.thread.ThreadUtils;
import com.mpolicy.policy.common.ep.policy.EpContractBaseInfoVo;
import com.mpolicy.policy.common.ep.policy.EpContractExtendInfoVo;
import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.policy.common.ep.policy.EpPolicyChannelInfoVo;
import com.mpolicy.product.common.base.ProductBase;
import com.mpolicy.product.common.company.CompanyInfo;
import com.mpolicy.service.common.service.DicCacheHelper;
import com.mpolicy.settlement.core.common.Constant;
import com.mpolicy.settlement.core.common.keys.AdminCommonKeys;
import com.mpolicy.settlement.core.common.reconcile.CreateReconcileVo;
import com.mpolicy.settlement.core.common.reconcile.company.ReconcileRuleFileTemplate;
import com.mpolicy.settlement.core.common.reconcile.diff.DiffBacklogInput;
import com.mpolicy.settlement.core.common.reconcile.diff.ReconcileAmountAccuracyInput;
import com.mpolicy.settlement.core.common.reconcile.enums.*;
import com.mpolicy.settlement.core.entity.SysDocumentEntity;
import com.mpolicy.settlement.core.enums.ReconcileTypeEnum;
import com.mpolicy.settlement.core.enums.SettlementInvoiceStatusEnum;
import com.mpolicy.settlement.core.helper.DocumentHelper;
import com.mpolicy.settlement.core.modules.channel.entity.ChannelInfoEntity;
import com.mpolicy.settlement.core.modules.channel.service.ChannelInfoService;
import com.mpolicy.settlement.core.modules.protocol.entity.OrgInfoEntity;
import com.mpolicy.settlement.core.modules.protocol.service.OrgInfoService;
import com.mpolicy.settlement.core.modules.reconcile.dto.rule.ReconcileCompanyInfo;
import com.mpolicy.settlement.core.modules.reconcile.dto.rule.ReconcileCompanyRuleInfo;
import com.mpolicy.settlement.core.modules.reconcile.entity.*;
import com.mpolicy.settlement.core.modules.reconcile.enums.PolicyProductTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementGenerateTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.helper.ReconcileBaseHelper;
import com.mpolicy.settlement.core.modules.reconcile.helper.ReconcileRuleHelper;
import com.mpolicy.settlement.core.modules.reconcile.helper.ReconcileSubjectHelper;
import com.mpolicy.settlement.core.modules.reconcile.service.*;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import com.mpolicy.settlement.core.service.common.PolicyCenterBaseClient;
import com.mpolicy.settlement.core.service.common.ProductBaseService;
import com.mpolicy.settlement.core.service.common.SettlementPolicyBaseClient;
import com.mpolicy.settlement.core.utils.PremUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 保司结算对账单服务接口实现
 *
 * <AUTHOR>
 * @since 2023-05-22 18:39
 */
@Service
@Slf4j(topic = "reconcileCore")
public class SettlementReconcileServiceImpl implements SettlementReconcileService {

    @Autowired
    @Qualifier("simpleTaskExecutor")
    private AsyncTaskExecutor taskExecutor;

    @Autowired
    private ProductBaseService productBaseService;

    @Autowired
    private OrgInfoService orgInfoService;

    @Autowired
    protected PolicyCenterBaseClient policyCenterBaseClient;

    @Autowired
    protected SettlementPolicyBaseClient settlementPolicyBaseClient;

    @Autowired
    private SettlementPolicyInfoService settlementPolicyInfoService;

    @Autowired
    private SettlementReconcileInfoService settlementReconcileInfoService;

    @Autowired
    private SettlementReconcileSubjectService settlementReconcileSubjectService;

    @Autowired
    private SettlementReconcileFileService settlementReconcileFileService;

    @Autowired
    private SettlementReconcilePolicyService settlementReconcilePolicyService;

    @Autowired
    private SettlementReconcileCompanyService settlementReconcileCompanyService;

    @Autowired
    private SettlementReconcileConfirmService settlementReconcileConfirmService;

    @Autowired
    private SettlementReconcileDiffBacklogService settlementReconcileDiffBacklogService;

    @Autowired
    private SettlementReconcilePolicyFileService settlementReconcilePolicyFileService;

    @Autowired
    private SettlementReconcileCompanySubjectService settlementReconcileCompanySubjectService;

    @Autowired
    private IRedisService redisService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private BiSettlementIncomeRegulatorySubmitService biSettlementIncomeRegulatorySubmitService;
    @Autowired
    private BiSettlementMonthReportService biSettlementMonthReportService;
    @Autowired
    private ChannelInfoService channelInfoService;

    private static List<String> RECONCILE_SUBJECT_LIST =
            CollUtil.newArrayList(ReconcileSubjectOnlineEnum.FIRST_YR_COMM.getCode(),
                    ReconcileSubjectOnlineEnum.CONSULTING_SERVICE_FEE.getCode(), ReconcileSubjectOnlineEnum.PROMOTION_SERVICE_FEE.getCode(),
                    ReconcileSubjectOnlineEnum.MODERN_SERVICE_FEE.getCode(), ReconcileSubjectOnlineEnum.EXTENSION_SERVICE_FEE.getCode(),
                    ReconcileSubjectOnlineEnum.TECHNICAL_SERVICE_FEE.getCode(),
                    ReconcileSubjectOnlineEnum.SERVICE_FEE.getCode());

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public String createReconcile(ReconcileCompanyInfo reconcileCompanyInfo) {
        if (reconcileCompanyInfo.getCompanyRuleList().isEmpty()) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                    StrUtil.format("无法生成保司对账单，没有规则纪录, " + "保司对账单配置编号={}",
                            reconcileCompanyInfo.getReconcileCompanyCode())));
        }
        DateTime now = DateUtil.date();
        //月末最后一天
        int dayOfMonth = DateUtil.endOfMonth(now).dayOfMonth();
        String reconcileMonth = now.toString("yyyy年MM月");

        // 判断是否已经生成过
        SettlementReconcileInfoEntity settlementReconcileInfo = settlementReconcileInfoService.lambdaQuery()
                .eq(SettlementReconcileInfoEntity::getReconcileCompanyCode, reconcileCompanyInfo.getReconcileCompanyCode())
                .eq(SettlementReconcileInfoEntity::getReconcileMonth, reconcileMonth)
                .eq(SettlementReconcileInfoEntity::getReconcileDay,
                        StatusEnum.NORMAL.getCode().equals(reconcileCompanyInfo.getStatementDateMonthEnd()) ? dayOfMonth
                                : reconcileCompanyInfo.getStatementDate())
                .eq(SettlementReconcileInfoEntity::getMergeCode, reconcileCompanyInfo.getMergeCode()).one();
        if (settlementReconcileInfo != null) {
            log.info("结算编码:{},对账日期:{},科目合并编码:{}已经已经生成对账单:{}",
                    reconcileCompanyInfo.getReconcileCompanyCode(), reconcileMonth, reconcileCompanyInfo.getMergeCode(),
                    settlementReconcileInfo.getReconcileCode());
            return null;
        }
        // 1 生成对账单唯一编号
        String reconcileCode = PolicySettlementUtils.createCodeLastNumber(
                ReconcileTypeEnum.PROTOCOL.getCode().equals(reconcileCompanyInfo.getReconcileType()) ? "PR" : "CR");
        // 2 写入保司对账单信息表
        SettlementReconcileInfoEntity reconcileInfo = new SettlementReconcileInfoEntity();
        BeanUtils.copyProperties(reconcileCompanyInfo, reconcileInfo);
        // 获取上个月对账单的责任人信息
        Optional.ofNullable(settlementReconcileInfoService.lambdaQuery()
                        .eq(SettlementReconcileInfoEntity::getReconcileCompanyCode,
                                reconcileCompanyInfo.getReconcileCompanyCode())
                        .eq(SettlementReconcileInfoEntity::getMergeCode, reconcileCompanyInfo.getMergeCode())
                        .orderByDesc(SettlementReconcileInfoEntity::getId).last("limit 1").one())
                .ifPresent(p -> reconcileInfo.setHeadName(p.getHeadName()));
        reconcileInfo.setReconcileCode(reconcileCode);
        reconcileInfo.setProductCompanyCode(reconcileCompanyInfo.getCompanyCode());
        reconcileInfo.setProductCompanyName(reconcileCompanyInfo.getCompanyName());
        reconcileInfo.setCompanyCode(reconcileCompanyInfo.getSettlementCompanyCode());
        reconcileInfo.setCompanyName(reconcileCompanyInfo.getSettlementCompanyName());
        reconcileInfo.setMergeCode(reconcileCompanyInfo.getMergeCode());
        reconcileInfo.setReconcileType(reconcileCompanyInfo.getReconcileType());
        reconcileInfo.setSubjectRuleCodes(
                reconcileCompanyInfo.getCompanyRuleList().stream().map(ReconcileCompanyRuleInfo::getSubjectRuleCode)
                        .collect(Collectors.joining(",")));
        // 3 对账单名称 + 对账单月度
        reconcileInfo.setReconcileName(reconcileCompanyInfo.getReconcileCompanyName());
        reconcileInfo.setReconcileMonth(reconcileMonth);
        reconcileInfo.setPolicyMonth(now.monthBaseOne());
        // 账单日月末最后一天0:不是 1:是
        if (StatusEnum.NORMAL.getCode().equals(reconcileCompanyInfo.getStatementDateMonthEnd())) {
            reconcileInfo.setReconcileDay(dayOfMonth);
            reconcileInfo.setCreateReconcileDay("每月最后一天");
        } else {
            reconcileInfo.setReconcileDay(reconcileCompanyInfo.getStatementDate());
            reconcileInfo.setCreateReconcileDay(StrUtil.format("每月{}日", reconcileCompanyInfo.getStatementDate()));
        }
        settlementReconcileInfoService.save(reconcileInfo);
        // 4 写入保司对账单科目关联表
        List<SettlementReconcileSubjectEntity> reconcileSubjectList =
                reconcileCompanyInfo.getCompanyRuleList().stream().map(x -> {
                    SettlementReconcileSubjectEntity reconcileSubject = new SettlementReconcileSubjectEntity();
                    reconcileSubject.setSubjectRuleCode(x.getSubjectRuleCode());
                    reconcileSubject.setReconcileCode(reconcileCode);
                    reconcileSubject.setReconcileSubjectCode(x.getReconcileSubjectCode());
                    String searchName = ReconcileSubjectHelper.matchSearchCode(x.getReconcileSubjectCode());
                    if (StrUtil.isBlank(searchName)) {
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                                StrUtil.format("科目编码{}没有配置", x.getReconcileSubjectCode())));
                    }
                    reconcileSubject.setReconcileSubjectName(searchName);
                    reconcileSubject.setCreateUser(Constant.DEFAULT_USER);
                    return reconcileSubject;
                }).collect(Collectors.toList());
        settlementReconcileSubjectService.insertBatchSomeColumn(reconcileSubjectList);
        return reconcileCode;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public List<String> forceCreateReconcile(CreateReconcileVo vo) {
        List<SettlementReconcileCompanySubjectEntity> settlementReconcileCompanySubjectList =
                settlementReconcileCompanySubjectService.lambdaQuery()
                        .eq(SettlementReconcileCompanySubjectEntity::getReconcileCompanyCode, vo.getReconcileCompanyCode())
                        .eq(StrUtil.isNotBlank(vo.getMergeCode()), SettlementReconcileCompanySubjectEntity::getMergeCode,
                                vo.getMergeCode()).list();
        // 获取需要生成账单的账单日
        List<Integer> statementDateList = settlementReconcileCompanySubjectList.stream()
                .map(SettlementReconcileCompanySubjectEntity::getStatementDate).distinct().collect(Collectors.toList());
        // 按照时间去生成对账单
        List<String> resultList = new ArrayList<>();
        statementDateList.forEach(day -> {
            // 2 获取【保司配置规则生成为账单日=day的保司对账单】
            List<ReconcileCompanyInfo> reconcileCompanyInfos = ReconcileRuleHelper.queryReconcileCompanyInfoList(day);
            if (CollUtil.isNotEmpty(reconcileCompanyInfos)) {
                reconcileCompanyInfos.stream().filter(f -> {
                    // 结算保司编码错误的不要
                    if (!vo.getReconcileCompanyCode().equals(f.getReconcileCompanyCode())) {
                        return false;
                    }
                    // 合并编码不对的不要
                    if (StrUtil.isNotBlank(vo.getMergeCode()) && !vo.getMergeCode().equals(f.getMergeCode())) {
                        return false;
                    }
                    // 规则记录为空的数据不要
                    if (CollUtil.isEmpty(f.getCompanyRuleList())) {
                        return false;
                    }
                    return true;
                }).forEach(reconcileCompanyInfo -> {
                    // 执行生成对账单
                    String reconcileCode = this.forceCreateReconcile(vo.getReconcileMonth(), reconcileCompanyInfo);
                    resultList.add(reconcileCode);
                    log.info("[结算配置]手动触发生成账单 会覆盖之前生成的对账单={}", reconcileCode);
                });
            } else {
                log.info("[按月]没有需要生成账单的保司");
            }
        });
        return resultList;
    }

    private String forceCreateReconcile(String reconcileMonth, ReconcileCompanyInfo reconcileCompanyInfo) {
        DateTime reconcileMonthDate = DateUtil.parse(reconcileMonth, "yyyy年MM月");
        int dayOfMonth = DateUtil.endOfMonth(reconcileMonthDate).dayOfMonth();
        int policyMonth = reconcileMonthDate.monthBaseOne();
        // 判断是否已经生成过,如果生成过那么直接删除重新创建
       /* List<String> reconcileCodeList = settlementReconcileInfoService.lambdaQuery()
                        .eq(SettlementReconcileInfoEntity::getReconcileCompanyCode, reconcileCompanyInfo
                        .getReconcileCompanyCode())
                        .eq(SettlementReconcileInfoEntity::getReconcileMonth, reconcileMonth)
                        .eq(SettlementReconcileInfoEntity::getMergeCode, reconcileCompanyInfo.getMergeCode())
                        .eq(SettlementReconcileInfoEntity::getReconcileDay, StatusEnum.NORMAL.getCode().equals
                        (reconcileCompanyInfo.getStatementDateMonthEnd()) ? dayOfMonth : reconcileCompanyInfo
                        .getStatementDate())
                        .list().stream().map(SettlementReconcileInfoEntity::getReconcileCode).collect(Collectors
                        .toList());
        if (CollUtil.isNotEmpty(reconcileCodeList)) {
            settlementReconcileInfoService.lambdaUpdate()
                    .in(SettlementReconcileInfoEntity::getReconcileCode, reconcileCodeList)
                    .remove();
            settlementReconcileSubjectService.lambdaUpdate()
                    .in(SettlementReconcileSubjectEntity::getReconcileCode, reconcileCodeList)
                    .remove();
            log.info("日期:{}因为需要重新生成对账单所以删除了之前的对账信息编码为:{}", DateUtil.date(), JSONUtil.toJsonStr(reconcileCodeList));
        }*/
        // 1 生成对账单唯一编号
        String reconcileCode = PolicySettlementUtils.createCodeLastNumber(
                ReconcileTypeEnum.PROTOCOL.getCode().equals(reconcileCompanyInfo.getReconcileType()) ? "PR" : "CR");
        // 2 写入保司对账单信息表
        SettlementReconcileInfoEntity reconcileInfo = new SettlementReconcileInfoEntity();
        BeanUtils.copyProperties(reconcileCompanyInfo, reconcileInfo);
        // 获取上个月对账单的责任人信息
        Optional.ofNullable(settlementReconcileInfoService.lambdaQuery()
                        .eq(SettlementReconcileInfoEntity::getReconcileCompanyCode,
                                reconcileCompanyInfo.getReconcileCompanyCode())
                        .eq(SettlementReconcileInfoEntity::getMergeCode, reconcileCompanyInfo.getMergeCode())
                        .orderByDesc(SettlementReconcileInfoEntity::getId).last("limit 1").one())
                .ifPresent(p -> reconcileInfo.setHeadName(p.getHeadName()));

        reconcileInfo.setReconcileCode(reconcileCode);
        reconcileInfo.setProductCompanyCode(reconcileCompanyInfo.getCompanyCode());
        reconcileInfo.setProductCompanyName(reconcileCompanyInfo.getCompanyName());
        reconcileInfo.setCompanyCode(reconcileCompanyInfo.getSettlementCompanyCode());
        reconcileInfo.setCompanyName(reconcileCompanyInfo.getSettlementCompanyName());
        reconcileInfo.setSubjectRuleCodes(
                reconcileCompanyInfo.getCompanyRuleList().stream().map(ReconcileCompanyRuleInfo::getSubjectRuleCode)
                        .collect(Collectors.joining(",")));
        // 3 对账单名称 + 对账单月度
        reconcileInfo.setReconcileName(reconcileCompanyInfo.getReconcileCompanyName());
        reconcileInfo.setReconcileMonth(reconcileMonth);
        reconcileInfo.setReconcileType(reconcileCompanyInfo.getReconcileType());
        reconcileInfo.setPolicyMonth(policyMonth);
        // 账单日月末最后一天0:不是 1:是
        if (StatusEnum.NORMAL.getCode().equals(reconcileCompanyInfo.getStatementDateMonthEnd())) {
            reconcileInfo.setCreateReconcileDay("每月最后一天");
            reconcileInfo.setReconcileDay(dayOfMonth);
        } else {
            reconcileInfo.setReconcileDay(reconcileCompanyInfo.getStatementDate());
            reconcileInfo.setCreateReconcileDay(StrUtil.format("每月{}日", reconcileCompanyInfo.getStatementDate()));
        }
        settlementReconcileInfoService.save(reconcileInfo);
        // 4 写入保司对账单科目关联表
        List<SettlementReconcileSubjectEntity> reconcileSubjectList =
                reconcileCompanyInfo.getCompanyRuleList().stream().map(x -> {
                    SettlementReconcileSubjectEntity reconcileSubject = new SettlementReconcileSubjectEntity();
                    reconcileSubject.setSubjectRuleCode(x.getSubjectRuleCode());
                    reconcileSubject.setReconcileCode(reconcileCode);
                    reconcileSubject.setReconcileSubjectCode(x.getReconcileSubjectCode());
                    String searchName = ReconcileSubjectHelper.matchSearchCode(x.getReconcileSubjectCode());
                    if (StrUtil.isBlank(searchName)) {
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                                StrUtil.format("科目编码{}没有配置", x.getReconcileSubjectCode())));
                    }
                    reconcileSubject.setReconcileSubjectName(searchName);
                    reconcileSubject.setCreateUser(Constant.DEFAULT_USER);
                    return reconcileSubject;
                }).collect(Collectors.toList());
        settlementReconcileSubjectService.insertBatchSomeColumn(reconcileSubjectList);
        return reconcileCode;
    }

    @Override
    public SettlementReconcileInfoEntity uploadCompanyReconcileFile(String reconcileCode,
                                                                    ReconcileTemplateEnum reconcileTemplate, String fileCode, String userName) {
        SettlementReconcileInfoEntity reconcileInfo = Optional.ofNullable(settlementReconcileInfoService.lambdaQuery()
                .eq(SettlementReconcileInfoEntity::getReconcileCode, reconcileCode).one()).orElseThrow(
                () -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("保单对账单不存在"))));
        // 1 新增对账关联规则文件纪录
        SettlementReconcileFileEntity subjectFile = new SettlementReconcileFileEntity();
        subjectFile.setReconcileCode(reconcileCode);
        subjectFile.setReconcileFileCode(fileCode);
        subjectFile.setReconcileFileType(reconcileTemplate.getCode());
        SysDocumentEntity documentInfo = DocumentHelper.getDocumentInfo(fileCode);
        subjectFile.setFileName(documentInfo.getFileName());
        subjectFile.setFileUrl(documentInfo.getDomainPath());
        subjectFile.setCreateUser(userName);
        settlementReconcileFileService.save(subjectFile);
        // 2 更新协议对账规则文件数量
        settlementReconcileInfoService.updateById(reconcileInfo);
        // 3 重置协议对账数据
        ReconcileBaseHelper.resetReconcile(reconcileInfo.getReconcileCode());
        // 4 加载规则文件缓存数据
        taskExecutor.submit(() -> {
            List<ReconcileRuleFileTemplate> readFile =
                    ReconcileBaseHelper.loadReconcileRuleFileData(reconcileTemplate, fileCode, reconcileCode);
            // 将文件保存到数据库中
            settlementReconcilePolicyFileService.saveReconcileRuleFileTemplate(reconcileCode, fileCode, readFile);
        });
        return reconcileInfo;
    }

    @Override
    public SettlementReconcileInfoEntity retryUploadCompanyReconcileFile(String reconcileCode,
                                                                         ReconcileTemplateEnum reconcileTemplate, String sourceFileCode, String fileCode, String userName) {
        SettlementReconcileInfoEntity reconcileInfo = Optional.ofNullable(settlementReconcileInfoService.lambdaQuery()
                .eq(SettlementReconcileInfoEntity::getReconcileCode, reconcileCode).one()).orElseThrow(
                () -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("保单对账单不存在"))));
        // 1 删除对账关联规则文件纪录
        settlementReconcileFileService.remove(
                new LambdaQueryWrapper<SettlementReconcileFileEntity>().eq(SettlementReconcileFileEntity::getReconcileCode,
                        reconcileCode).eq(SettlementReconcileFileEntity::getReconcileFileCode, sourceFileCode));
        // 2 新增对账关联规则文件纪录
        SettlementReconcileFileEntity subjectFile = new SettlementReconcileFileEntity();
        subjectFile.setReconcileCode(reconcileCode);
        subjectFile.setReconcileFileCode(fileCode);
        subjectFile.setReconcileFileType(reconcileTemplate.getCode());
        SysDocumentEntity documentInfo = DocumentHelper.getDocumentInfo(fileCode);
        subjectFile.setFileName(documentInfo.getFileName());
        subjectFile.setFileUrl(documentInfo.getDomainPath());
        subjectFile.setCreateUser(userName);
        settlementReconcileFileService.save(subjectFile);
        // 3 重置协议对账数据
        ReconcileBaseHelper.resetReconcile(reconcileCode);
        // 4 加载规则文件缓存数据
        taskExecutor.submit(() -> {
            List<ReconcileRuleFileTemplate> readFile =
                    ReconcileBaseHelper.loadReconcileRuleFileData(reconcileTemplate, fileCode, reconcileCode);
            settlementReconcilePolicyFileService.saveReconcileRuleFileTemplate(reconcileCode, fileCode, readFile);
        });
        return reconcileInfo;
    }

    @Override
    public SettlementReconcileInfoEntity removeReconcileFile(String reconcileCode, String reconcileFileCode,
                                                             String userName) {
        SettlementReconcileInfoEntity reconcileInfo = Optional.ofNullable(settlementReconcileInfoService.lambdaQuery()
                .eq(SettlementReconcileInfoEntity::getReconcileCode, reconcileCode).one()).orElseThrow(
                () -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("保单对账单不存在"))));

        boolean removeStatus = settlementReconcileFileService.remove(
                new LambdaQueryWrapper<SettlementReconcileFileEntity>().eq(SettlementReconcileFileEntity::getReconcileCode,
                        reconcileCode).eq(SettlementReconcileFileEntity::getReconcileFileCode, reconcileFileCode));
        log.info("删除对账单文件成功，reconcileCode={}，reconcileFileCode={}", reconcileCode, reconcileFileCode);
        if (removeStatus) {
            settlementReconcileInfoService.updateById(reconcileInfo);
            // 重置协议对账数据
            ReconcileBaseHelper.resetReconcile(reconcileCode);
        }
        return reconcileInfo;
    }

    /**
     * settlement_reconcile_policy 里面为reconcileGenerateType = 小鲸线下对账单的数据，自动写入到 settlement_policy_info纪录并标记为已完成对账 // todo
     * 这里有耗时很长可以优化 但是需要时间
     *
     * @param reconcileInfo 保司对账单信息
     * @param userName      操作员
     * <AUTHOR>
     * @since 2023/5/24 09:28
     */
    private void autoInsertSettlementPolicy(SettlementReconcileInfoEntity reconcileInfo, String userName) {
        List<OrgInfoEntity> orgList = orgInfoService.list();
        CopyOptions copyOptions = CopyOptions.create().setIgnoreNullValue(true);
        // 获取 settlement_reconcile_policy 里面为reconcileGenerateType = 小鲸线下对账单的数据
        // 对账月份
        DateTime reconcileMonth = DateUtil.parse(reconcileInfo.getReconcileMonth(), "yyyy年MM月");
        // 结算月份是前一个月
        String settlementMonth = DateUtil.offsetMonth(reconcileMonth, -1).toString("yyyy年MM月");
        int id = 0;
        int limit = 1000;
        while (true) {
            List<SettlementReconcilePolicyEntity> reconcilePolicy =
                    settlementReconcilePolicyService.lambdaQuery().gt(SettlementReconcilePolicyEntity::getId, id)
                            .eq(SettlementReconcilePolicyEntity::getReconcileCode, reconcileInfo.getReconcileCode())
                            .eq(SettlementReconcilePolicyEntity::getReconcileGenerateType,
                                    SettlementGenerateTypeEnum.OFFLINE_RECONCILE_CONFIRM.getCode()).last(" limit " + limit)
                            .orderByAsc(SettlementReconcilePolicyEntity::getId).list();
            if (CollUtil.isEmpty(reconcilePolicy)) {
                log.info("需处理ID>{}的精度的对账数据为空，无需处理", id);
                break;
            }
            log.info("开始处理ID>{}的精度差异数据{}条", id, reconcilePolicy.size());
            id = CollUtil.getLast(reconcilePolicy).getId();
            List<SettlementPolicyInfoEntity> authList = reconcilePolicy.stream().map(x -> {
                SettlementPolicyInfoEntity bean = new SettlementPolicyInfoEntity();
                bean.setReconcileType(reconcileInfo.getReconcileType());
                // 扩展其他属性
                String settlementCode = PolicySettlementUtils.createCodeLastNumber("SC");
                // 根据保单号获取保单信息
                EpContractInfoVo policyInfo = policyCenterBaseClient.getPolicyInfoByPolicyCode(x.getPolicyNo());
                EpContractBaseInfoVo contractBaseInfo = policyInfo.getContractBaseInfo();
                EpContractExtendInfoVo contractExtendInfo = policyInfo.getContractExtendInfo();
                bean.setContractCode(policyInfo.getContractCode());
                BeanUtil.copyProperties(contractBaseInfo, bean, copyOptions);
                BeanUtil.copyProperties(contractExtendInfo, bean, copyOptions);
                // 结算信息
                BeanUtil.copyProperties(x, bean, copyOptions);

                bean.setPolicySource(policyInfo.getPolicySource());
                bean.setPolicyProductType(policyInfo.getPolicyProductType());
                // 回执/回访日志
                bean.setReceiptStatus(contractExtendInfo.getIsNeedReceipt());
                bean.setReceiptTime(contractExtendInfo.getReceiptSignTime());
                bean.setRevisitStatus(contractExtendInfo.getIsNeedRevisit());
                bean.setRevisitTime(contractExtendInfo.getRevisitTime());
                // 投保人信息
                bean.setApplicantName(policyInfo.getApplicantInfo().getApplicantName());
                bean.setApplicantMobile(policyInfo.getApplicantInfo().getApplicantMobile());
                bean.setApplicantIdCard(policyInfo.getApplicantInfo().getApplicantIdCard());
                bean.setApplicantGender(policyInfo.getApplicantInfo().getApplicantGender());
                bean.setApplicantBirthday(policyInfo.getApplicantInfo().getApplicantBirthday());
                bean.setApplicantAge(policyInfo.getApplicantInfo().getApplicantAge());
                // 主代理人信息
                policyInfo.getAgentInfoList().stream().filter(p -> p.getMainFlag() == 1).findFirst().ifPresent(p -> {
                    bean.setMainAgentCode(p.getAgentCode());
                    bean.setOrgCode(p.getOrgCode());
                });
                // 渠道推荐人类型
                EpPolicyChannelInfoVo channelInfo = policyInfo.getChannelInfo();
                BeanUtil.copyProperties(channelInfo, bean, copyOptions);
                bean.setSettlementDate(new Date());
                bean.setSettlementTime(new Date());
                bean.setSettlementCode(settlementCode);
                bean.setReconcileCode(reconcileInfo.getReconcileCode());
                bean.setSettlementMonth(settlementMonth);
                bean.setInnerSignatoryCode(reconcileInfo.getInnerSignatoryCode());
                bean.setInnerSignatoryName(reconcileInfo.getInnerSignatoryName());
                bean.setExternalSignatoryType(reconcileInfo.getExternalSignatoryType());
                bean.setExternalSignatoryCode(reconcileInfo.getExternalSignatoryCode());
                bean.setExternalSignatoryName(reconcileInfo.getExternalSignatoryName());
                bean.setSettlementGenerateType(SettlementGenerateTypeEnum.OFFLINE_RECONCILE_CONFIRM.getCode());
                bean.setReconcileTime(new Date());
                // 浮动的奖励保费强制设置为0
                bean.setPremium(BigDecimal.ZERO);
                bean.setReconcileTime(new Date());
                bean.setReconcileUser(userName);
                // 补充字段其他字段
                bean.setEventSourceCode(x.getBillCode());
                bean.setSettlementEventCode(SettlementEventTypeEnum.FLOATING_REWARDS.getEventCode());
                bean.setSettlementEventDesc(SettlementEventTypeEnum.FLOATING_REWARDS.getEventName());
                bean.setSettlementSubjectCode(x.getReconcileSubjectCode());
                bean.setSettlementSubjectName(x.getReconcileSubjectName());
                bean.setReconcileExecuteStatus(1);
                bean.setReconcileExecuteDesc(SettlementEventTypeEnum.FLOATING_REWARDS.getEventName());
                bean.setReconcileExecuteTime(new Date());
                // 可对账单状态设置默认值
                bean.setReconcileStatus(ReconcileStatusEnum.RECONCILE_FINISH.getStatusCode());
                bean.setDeleted(0);
                bean.setRevision(1);
                bean.setChannelDistributionCode(contractBaseInfo.getChannelDistributionCode());
                if (bean.getSelfPreservation() == null) {
                    bean.setSelfPreservation(0);
                }
                bean.setInsuranceType(x.getInsuranceType());
                // 补充报送结构
                bean.setReportOrgCode(Constant.ZB_ORG_CODE);
                bean.setReportOrgName(Constant.ZB_ORG_NAME);
                if (StrUtil.isNotBlank(bean.getOrgCode())) {
                    OrgInfoEntity orgInfo = PremUtil.getOrgCode(bean.getOrgCode(), orgList);
                    if (!orgInfo.getOrgCode().equals(Constant.NATIONWIDE_ORG_CODE) && contractBaseInfo.getSalesType() == 1) {
                        bean.setReportOrgCode(orgInfo.getOrgCode());
                        bean.setReportOrgName(orgInfo.getOrgName());
                    }
                }
                // 8 业务类型 zhnx渠道就为农保渠道
                if (StringUtils.equalsIgnoreCase(Constant.DEFAULT_CS_CHANNEL, policyInfo.getChannelInfo().getChannelCode())) {
                    bean.setBusinessType(2);
                } else {
                    bean.setBusinessType(1);
                }
                return bean;
            }).collect(Collectors.toList());
            // 批量写入
            if (!authList.isEmpty()) {
                settlementPolicyInfoService.saveList(authList);
            }
            ThreadUtils.sleep(500);
        }

    }

    /**
     * 获取保司上传的线上对账单文件
     *
     * @param reconcileInfo 对账单信息
     * @return
     */
    private List<SettlementReconcileCompanyEntity> getSettlementReconcileCompany(
            SettlementReconcileInfoEntity reconcileInfo) {
        List<SettlementReconcilePolicyFileEntity> settlementReconcilePolicyFileList =
                settlementReconcilePolicyFileService.lambdaQuery().eq(SettlementReconcilePolicyFileEntity::getFileType, 0)
                        .eq(SettlementReconcilePolicyFileEntity::getReconcileCode, reconcileInfo.getReconcileCode())
                        .in(SettlementReconcilePolicyFileEntity::getReconcileSubjectCode, RECONCILE_SUBJECT_LIST).list();
        if (CollUtil.isEmpty(settlementReconcilePolicyFileList)){
            return new ArrayList<>();
        }
        List<SettlementReconcileCompanyEntity> reconcileCompany =
                ReconcileBaseHelper.buildSettlementReconcileCompany(reconcileInfo, settlementReconcilePolicyFileList);
        return reconcileCompany;
    }

    /**
     * 获取小鲸线上对账单
     */
    private List<SettlementReconcilePolicyEntity> getSettlementReconcilePolicy(
            SettlementReconcileInfoEntity reconcileInfo) {
        List<SettlementPolicyInfoEntity> settlementPolicyList =
                settlementPolicyInfoService.querySettlementPolicyInfoList(reconcileInfo);
        log.info("获取对账单[{}]的明细合计：{}条", reconcileInfo.getReconcileCode(), settlementPolicyList.size());
        // 2.2 保单明细集合转换为 保司结算对账单关联数据
        List<SettlementReconcilePolicyEntity> reconcilePolicy = settlementPolicyList.stream().map(s -> {
            SettlementReconcilePolicyEntity bean = new SettlementReconcilePolicyEntity();
            // 生成policyBillCode 编号 + 系统生成标记
            bean.setPolicyBillCode(PolicySettlementUtils.createCodeLastUuid("PB"));
            bean.setReconcileGenerateType(SettlementGenerateTypeEnum.BUSINESS_EVENTS.getCode());
            bean.setReconcileSubjectCode(s.getSettlementSubjectCode());
            BeanUtils.copyProperties(s, bean);
            String searchName = ReconcileSubjectHelper.matchSearchCode(s.getSettlementSubjectCode());
            if (StrUtil.isBlank(searchName)) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                        StrUtil.format("科目编码{}没有配置", s.getSettlementSubjectCode())));
            }
            bean.setReconcileSubjectName(searchName);
            bean.setReconcileCode(reconcileInfo.getReconcileCode());
            bean.setReconcileName(reconcileInfo.getReconcileName());
            bean.setReconcileMonth(reconcileInfo.getReconcileMonth());
            bean.setCompanyCode(reconcileInfo.getCompanyCode());
            bean.setCompanyName(reconcileInfo.getCompanyName());
            bean.setRevision(1);
            // 第三方保单号真实保单号处理
            if (StrUtil.isNotBlank(s.getThirdPolicyNo())) {
                bean.setPolicyNo(s.getThirdPolicyNo());
            }
            return bean;
        }).collect(Collectors.toList());
        // 更新一下明细数据
        settlementReconcileInfoService.settlementPolicyInfoToXls(reconcileInfo, settlementPolicyList);
        return reconcilePolicy;
    }

    /**
     * 开始对账 1: 获取对账【配置规则科目】 2: 解析【配置规则科目】 对应的保单明细纪录，写入到 settlement_reconcile_policy 纪录表（标记为线上对账） 3: 解析【小鲸手工对账单文件】 写入到
     * settlement_reconcile_policy 纪录表（标记为线下对账） 4: 解析【保司对账单文件-保司】 最终写入到 settlement_reconcile_company（包含线上和线下的保司对账单） 5:
     * 生成对账单汇总纪录 settlement_reconcile_confirm 6: 更新settlement_reconcile_info 为对账单及差异金额 小鲸金额/保司金额 7: 更新对账单纪录为对账中
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startReconcile(String reconcileCode, String userName, String uuid) {
        String msg = "2";
        // 加一个锁 防止有进行中的冲正事件
        String tryLockKey = StrUtil.format("START_RECONCILE-{}", reconcileCode);
        boolean lock = RedissLockUtil.tryLock(tryLockKey, 3600, -1);
        if (!lock) {
            return;
        }
        SettlementReconcileInfoEntity reconcileInfo = settlementReconcileInfoService.lambdaQuery()
                .eq(SettlementReconcileInfoEntity::getReconcileCode, reconcileCode).one();
        if (reconcileInfo == null) {
            msg = "对账单不存在";
            redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, msg);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("对账单不存在")));
        }
        // 如果是不可开票状态 设置为可开票
        if (SettlementInvoiceStatusEnum.CANNOT_BE_INVOICED.getCode().equals(reconcileInfo.getInvoiceStatus())) {
            reconcileInfo.setInvoiceStatus(SettlementInvoiceStatusEnum.PENDING_INVOICING.getCode());
        }
        try {
            StopWatch stopWatch = new StopWatch();
            // 设置开始
            stopWatch.start();
            reconcileInfo.setUpdateUser(userName);
            ReconcileStatusEnum reconcileStatusEnum = ReconcileStatusEnum.decode(reconcileInfo.getReconcileStatus());
            if (reconcileStatusEnum == ReconcileStatusEnum.RECONCILE_FINISH) {
                msg = "对账单已经完成对账，无法再次对账";
                throw new GlobalException(
                        BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("对账单已经完成对账，无法再次对账")));
            }
            // 重置对账单状态
            ReconcileBaseHelper.resetReconcile(reconcileCode);
            // 开始执行对账操作
            reconcileInfo.setReconcileStatus(ReconcileStatusEnum.GENERATING_GENERATE_ING.getStatusCode());
            settlementReconcileInfoService.updateById(reconcileInfo);
            // 获取对账单文件
            Integer count = settlementReconcileFileService.lambdaQuery()
                    .eq(SettlementReconcileFileEntity::getReconcileCode, reconcileCode).count();
            if (count == null || count == 0) {
                // 保司对账文件不存在的时候设置待对账
                // 设置开票状态为不可开票 金额设置为0
                reconcileInfo.setInvoiceAmount(BigDecimal.ZERO);//开票中金额
                reconcileInfo.setInvoicableAmount(BigDecimal.ZERO);//可开票金额
                reconcileInfo.setInvoicedAmount(BigDecimal.ZERO);// 已开票金额
                reconcileInfo.setReconcileStatus(ReconcileStatusEnum.TO_BE_RECONCILE.getStatusCode());
            }
            reconcileInfo.setCompanyBillCount(count);
            // 3 获取保司上传的线上对账单文件
            List<SettlementReconcileCompanyEntity> reconcileCompany = getSettlementReconcileCompany(reconcileInfo);
            // 2.1 根据规则获取结算保单明细
            List<SettlementReconcilePolicyEntity> reconcilePolicy = getSettlementReconcilePolicy(reconcileInfo);
            log.info("对账单编码[{}]我司线上对账单明细数量[{}],保司对线上账单明细数量[{}]",
                    reconcileInfo.getReconcileCode(), reconcilePolicy.size(), reconcileCompany.size());
            // 5 生成对账单确认单
            List<SettlementReconcileConfirmEntity> confirmList = ReconcileBaseHelper.buildSettlementReconcileConfirm(reconcileInfo, reconcilePolicy, reconcileCompany);
            log.info("开始将对账单={}明细记录写入到数据库中,条数={}", reconcileCode, confirmList.size());
            // 6 更新settlement_reconcile_info 为对账单及差异金额 小鲸金额/保司金额
            if (!confirmList.isEmpty()) {
                // 批量写入对账汇总纪录表
                settlementReconcileConfirmService.saveList(confirmList);
                confirmList.stream().map(SettlementReconcileConfirmEntity::getXiaowhaleAmount).reduce(BigDecimal::add)
                        .ifPresent(reconcileInfo::setXiaowhaleAmount);
                confirmList.stream().map(SettlementReconcileConfirmEntity::getCompanyAmount).reduce(BigDecimal::add)
                        .ifPresent(reconcileInfo::setCompanyAmount);
                reconcileInfo.setDiffAmount(
                        reconcileInfo.getXiaowhaleAmount().subtract(reconcileInfo.getCompanyAmount()));
            }
            if (!reconcilePolicy.isEmpty()) {
                settlementReconcilePolicyService.saveList(reconcilePolicy);
            }
            if (!reconcileCompany.isEmpty()) {
                settlementReconcileCompanyService.saveList(reconcileCompany);
            }
            log.info("成功将对账单={}明细记录写入到数据库中,更新对账单状态", reconcileCode);
            // 7 更新对账单为对账中
            reconcileInfo.setReconcileStatus(ReconcileStatusEnum.RECONCILE_ING.getStatusCode());
            // 设置结束
            stopWatch.stop();
            //如果是待开票 初始化开票金额
            if (SettlementInvoiceStatusEnum.PENDING_INVOICING.getCode().equals(reconcileInfo.getInvoiceStatus())) {
                reconcileInfo.setInvoiceAmount(BigDecimal.ZERO);//开票中金额
                reconcileInfo.setInvoicableAmount(reconcileInfo.getCompanyAmount());//可开票金额 保司对账单金额
                reconcileInfo.setInvoicedAmount(BigDecimal.ZERO);// 已开票金额
            }
            // 获取执行毫秒值
            long millis = stopWatch.getTotalTimeMillis();
            log.info("开始对账完成...耗时：{}", millis);
        } catch (GlobalException e) {
            log.warn("对账单={}对账失败,失败原因={}", reconcileCode, e.getMsg());
            msg = e.getMsg();
            reconcileInfo.setReconcileStatus(ReconcileStatusEnum.GENERATING_RECONCILED_ERROR.getStatusCode());
        } catch (Exception e) {
            reconcileInfo.setReconcileStatus(ReconcileStatusEnum.GENERATING_RECONCILED_ERROR.getStatusCode());
            log.warn("对账单={}对账失败,失败原因", reconcileCode, e);
            msg = "开始对账异常,请联系管理员";
        } finally {
            RedissLockUtil.unlock(tryLockKey);
            settlementReconcileInfoService.updateById(reconcileInfo);
            redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, msg);

        }

    }

    /**
     * 完成对账(异步) 1 校验是否可以操作完成对账 2 settlement_reconcile_info 设置为对账完 3 settlement_reconcile_subject 更新科目对应汇总信息(读取) 4
     * settlement_reconcile_policy 线上对账关联数据对账完成 5 settlement_reconcile_policy 里面为reconcileGenerateType = 小鲸线上对账单的数据
     * 关联settlement_policy_info为已对账完成 6 settlement_reconcile_policy 里面为reconcileGenerateType = 小鲸线下对账单的数据，自动写入到
     * settlement_policy_info纪录 7 获取所有精度处理的数据，进行自动写入到关联settlement_policy_info为已对账完成 8 settlement_reconcile_diff_backlog
     * 差异事项设置启用状态
     */
    @Async
    @Override
    public void finishReconcile(String reconcileCode, String userName, String uuid) {
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus transactionStatus) {
                String msg = "2";
                try {
                    // 加一个锁 防止有进行中的冲正事件
                    boolean lock =
                            RedissLockUtil.tryLock(StrUtil.format("FINISH_RECONCILE-{}", reconcileCode), 3600, -1);
                    if (!lock) {
                        msg = "获取完成对账锁失败啦";
                        return;
                    }
                    log.info("[{}]开始处理对账单[{}]完成对账的操作.....", userName, reconcileCode);
                    StopWatch stopWatch = new StopWatch();
                    // 设置开始
                    stopWatch.start();
                    SettlementReconcileInfoEntity reconcileInfo = settlementReconcileInfoService.lambdaQuery()
                            .eq(SettlementReconcileInfoEntity::getReconcileCode, reconcileCode).one();
                    if (reconcileInfo == null) {
                        log.info("对账单{}不存在,直接返回成功", reconcileCode);
                        msg = StrUtil.format("对账单{}不存在,直接返回成功", reconcileCode);
                        return;
                    }
                    // 对账月份
                    DateTime reconcileMonth = DateUtil.parse(reconcileInfo.getReconcileMonth(), "yyyy年MM月");
                    String settlementMonth = DateUtil.offsetMonth(reconcileMonth, -1).toString("yyyy年MM月");
                    // 1-1 判断是否已经为最终状态
                    ReconcileStatusEnum reconcileStatusEnum =
                            ReconcileStatusEnum.decode(reconcileInfo.getReconcileStatus());
                    if (reconcileStatusEnum == ReconcileStatusEnum.RECONCILE_FINISH || reconcileStatusEnum == ReconcileStatusEnum.RECONCILE_CLOSE) {
                        log.info("对账单[{}]已为已完成对账或已关闭，无法操作完成", reconcileCode);
                        msg = StrUtil.format("对账单[{}]已为已完成对账或已关闭，无法操作完成", reconcileCode);
                        return;
                    }
                    // 获取线上科目信息,如果科目类型大于1 那是不可能的 一个对账单只会存在一个线上科目
                    List<SettlementReconcileSubjectEntity> subjectList = settlementReconcileSubjectService.lambdaQuery()
                            .eq(SettlementReconcileSubjectEntity::getReconcileCode, reconcileCode).list();
                    if (subjectList.isEmpty()) {
                        log.info("对账单[{}]结算科目信息不存在,请检查数据 ", reconcileCode);
                        msg = StrUtil.format("对账单[{}]结算科目信息不存在,请检查数据 ", reconcileCode);
                        return;
                    }
                    long count = subjectList.stream().map(SettlementReconcileSubjectEntity::getReconcileSubjectCode)
                            .distinct().count();
                    if (count > 1) {
                        log.info("对账单[{}]结算科目信息类型大于1,请检查数据 ", reconcileCode);
                        msg = StrUtil.format("对账单[{}]结算科目信息类型大于1,请检查数据 ", reconcileCode);
                        return;
                    }
                    // 获取科目信息
                    // 1-2 校验差异金额与冲正金额运算后为0
                    if (reconcileInfo.getDiffAmount().add(reconcileInfo.getReversalAmount())
                            .compareTo(BigDecimal.ZERO) != 0) {
                        log.info("对账单[{}]存在差异金额未完成处理，无法完成对账操作", reconcileCode);
                        msg = StrUtil.format("对账单[{}]存在差异金额未完成处理，无法完成对账操作", reconcileCode);
                        return;
                    }
                    // 1-3 是否存在未处理的差异纪录
                    Integer diffConfirmCount = settlementReconcileConfirmService.lambdaQuery()
                            .eq(SettlementReconcileConfirmEntity::getReconcileCode, reconcileCode)
                            .eq(SettlementReconcileConfirmEntity::getDiffFlag, 1)
                            .eq(SettlementReconcileConfirmEntity::getDiffStatus, 0).count();
                    if (diffConfirmCount != null && diffConfirmCount > 0) {
                        log.info("对账单[{}]存在未完成的差异纪录，待处理的差异纪录条数 ={} ", reconcileCode,
                                diffConfirmCount);
                        msg = StrUtil.format("对账单[{}]存在未完成的差异纪录，待处理的差异纪录条数 ={} ", reconcileCode,
                                diffConfirmCount);
                        return;
                    }


                    // 获取所有有效的对账汇总 + 有效的对账单号
                    List<String> confirmEnableBillCodes = settlementReconcileConfirmService.lambdaQuery()
                            .select(SettlementReconcileConfirmEntity::getBillCode)
                            .eq(SettlementReconcileConfirmEntity::getReconcileCode, reconcileCode)
                            .eq(SettlementReconcileConfirmEntity::getBillEnable, StatusEnum.NORMAL.getCode()).list()
                            .stream().map(SettlementReconcileConfirmEntity::getBillCode).collect(Collectors.toList());
                    if (confirmEnableBillCodes.isEmpty()) {
                        log.info("对账单[{}]对应的确认单数据不存在,不允许完成对账,可以设置关闭对账单 ", reconcileCode);
                        msg = StrUtil.format("对账单[{}]对应的确认单数据不存在,不允许完成对账,可以设置关闭对账单 ",
                                reconcileCode);
                        return;
                    }
                    settlementReconcilePolicyService.lambdaUpdate()
                            .set(SettlementReconcilePolicyEntity::getReconcileStatus,
                                    ReconcileStatusEnum.RECONCILE_FINISH.getStatusCode())
                            .eq(SettlementReconcilePolicyEntity::getReconcileCode, reconcileCode)
                            .in(SettlementReconcilePolicyEntity::getBillCode, confirmEnableBillCodes).update();

                    // 5 settlement_reconcile_policy 里面为reconcileGenerateType = 小鲸线上对账单的数据

                    // 关联settlement_policy_info为已对账完成
                    log.info("对账单编码={}不用分片实现 直接操作数据库开始", reconcileCode);
                    settlementPolicyInfoService.settlementPolicyFinish(reconcileCode, userName, settlementMonth);
                    log.info("对账单编码={}不用分片实现 直接操作数据库完成", reconcileCode);

                    // 6 将线下单生成保单结算明细入库
                    log.info("对账单编码={}将线下单生成保单结算明细入库开始", reconcileCode);
                    autoInsertSettlementPolicy(reconcileInfo, userName);
                    log.info("对账单编码={}将线下单生成保单结算明细入库完成", reconcileCode);


                    //  进行自动冲正到明细表(非精度)
                    log.info("对账单编码={}进行自动冲正到明细表(非精度)开始", reconcileCode);
                    autoReversalSettlementPolicy(reconcileInfo, userName);
                    log.info("对账单编码={}进行自动冲正到明细表(非精度)完成", reconcileCode);


                    log.info("对账单编码={}处理精度重整数据入库到明细表开始", reconcileCode);
                    autoAmountAccuracySettlement(reconcileInfo, userName);
                    log.info("对账单编码={}处理精度重整数据入库到明细表中完成", reconcileCode);

                    log.info("对账单编码={}处理仅报送保费数据入库到明细表开始", reconcileCode);
                    autoOnlySubmitPremium(reconcileInfo, userName);
                    log.info("对账单编码={}处理仅报送保费数据入库到明细表完成", reconcileCode);

                    // 9 待办事项设置为启用状态
                    settlementReconcileDiffBacklogService.lambdaUpdate()
                            .set(SettlementReconcileDiffBacklogEntity::getBacklogEnable, 1)
                            .eq(SettlementReconcileDiffBacklogEntity::getReconcileCode, reconcileCode)
                            .eq(SettlementReconcileDiffBacklogEntity::getUpdateUser, userName).update();
                    log.info("对账单差异待办事项启动完成...");

                    // 2 设置对账单的状态 + 操作人 + 操作时间
                    reconcileInfo.setReconcileStatus(ReconcileStatusEnum.RECONCILE_FINISH.getStatusCode());
                    reconcileInfo.setReconcileUser(userName);
                    reconcileInfo.setReconcileTime(new Date());
                    // 刚完成对账,可开票金额就是保司对账单的金额
                    reconcileInfo.setInvoicableAmount(reconcileInfo.getCompanyAmount());
                    settlementReconcileInfoService.updateById(reconcileInfo);
                    // 将挂起的数据释放出来
                    settlementPolicyInfoService.lambdaUpdate()
                            .eq(SettlementPolicyInfoEntity::getHangReconcileCode, reconcileInfo.getReconcileCode())
                            .set(SettlementPolicyInfoEntity::getHangStatus, 0)
                            .update();
                    // 11.生成明细
                    stopWatch.stop();
                    // 获取执行毫秒值
                    long millis = stopWatch.getTotalTimeMillis();
                    log.info("完成对账结束...耗时：{}", millis);
                    // 触发监管报送
                    String uuid = IdUtil.fastSimpleUUID();
                    redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, "1");
                } catch (GlobalException e) {
                    log.info("[{}]开始处理对账单[{}]完成对账的操作异常={}", userName, reconcileCode, e.getMsg());
                    //回滚
                    redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, e.getMsg());
                    transactionStatus.setRollbackOnly();
                } catch (Exception e) {
                    log.info("[{}]开始处理对账单[{}]完成对账的操作异常", userName, reconcileCode, e);
                    //回滚
                    redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, "操作完成对账异常,请联系管理员");
                    transactionStatus.setRollbackOnly();
                } finally {
                    RedissLockUtil.unlock(StrUtil.format("FINISH_RECONCILE-{}", reconcileCode));
                    redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, msg);
                }
            }
        });

    }

    /**
     * 差异处理自动冲正(非精度差异数据) 如果是非小鲸缺失的，都需要做补偿信息处理
     *
     * @param reconcileInfo 对账单信息
     * @param userName      操作用户
     * <AUTHOR>
     * @since 2023/6/8
     */
    private void autoReversalSettlementPolicy(SettlementReconcileInfoEntity reconcileInfo, String userName) {
        // 对账月份
        DateTime reconcileMonth = DateUtil.parse(reconcileInfo.getReconcileMonth(), "yyyy年MM月");
        // 结算月份是前一个月
        String settlementMonth = DateUtil.offsetMonth(reconcileMonth, -1).toString("yyyy年MM月");
        int id = 0;
        int limit = 5000;
        while (true) {
            // 7 读取差异纪录，进行自动冲正到明细表
            List<SettlementReconcileConfirmEntity> reversalConfirmList =
                    settlementReconcileConfirmService.lambdaQuery().gt(SettlementReconcileConfirmEntity::getId, id)
                            .eq(SettlementReconcileConfirmEntity::getReconcileCode, reconcileInfo.getReconcileCode())
                            .ne(SettlementReconcileConfirmEntity::getDiffType, ReconcileDiffTypeEnum.AMOUNT_ACCURACY.getCode())
                            .eq(SettlementReconcileConfirmEntity::getDiffFlag, 1)
                            .eq(SettlementReconcileConfirmEntity::getBillEnable, 1).last(" limit " + limit)
                            .orderByAsc(SettlementReconcileConfirmEntity::getId).list();
            if (CollUtil.isEmpty(reversalConfirmList)) {
                log.info("需处理ID>{}的非精度差异数据为空，无需处理", id);
                break;
            }
            log.info("开始处理ID>{}的非精度差异数据{}条", id, reversalConfirmList.size());
            id = CollUtil.getLast(reversalConfirmList).getId();

            List<String> billCodes = reversalConfirmList.stream().map(SettlementReconcileConfirmEntity::getBillCode)
                    .collect(Collectors.toList());

            List<SettlementReconcilePolicyEntity> reconcilePolicyList = settlementReconcilePolicyService.lambdaQuery()
                    .in(SettlementReconcilePolicyEntity::getBillCode, billCodes).list();

            Map<String, SettlementReconcilePolicyEntity> reconcilePolicyMap = reconcilePolicyList.stream().collect(
                    Collectors.toMap(SettlementReconcilePolicyEntity::getBillCode, Function.identity(), (x, y) -> x));

            List<String> settlementCodes =
                    reconcilePolicyList.stream().map(SettlementReconcilePolicyEntity::getSettlementCode)
                            .collect(Collectors.toList());
            // 结算明细
            Map<String, SettlementPolicyInfoEntity> finalSettlementPolicyMap = settlementPolicyInfoService.lambdaQuery()
                    .in(SettlementPolicyInfoEntity::getSettlementCode, settlementCodes).list().stream().collect(
                            Collectors.toMap(SettlementPolicyInfoEntity::getSettlementCode, Function.identity(), (x, y) -> x));
            // 开始封装数据 入库
            List<SettlementPolicyInfoEntity> authList = reversalConfirmList.stream().map(x -> {
                SettlementPolicyInfoEntity bean = new SettlementPolicyInfoEntity();
                BeanUtils.copyProperties(x, bean);
                bean.setSettlementMonth(settlementMonth);
                bean.setReconcileType(reconcileInfo.getReconcileType());
                // 设置保司信息和内外签署方
                bean.setCompanyCode(reconcileInfo.getCompanyCode());
                bean.setCompanyName(reconcileInfo.getCompanyName());
                bean.setInnerSignatoryCode(reconcileInfo.getInnerSignatoryCode());
                bean.setInnerSignatoryName(reconcileInfo.getInnerSignatoryName());
                bean.setExternalSignatoryType(reconcileInfo.getExternalSignatoryType());
                bean.setExternalSignatoryCode(reconcileInfo.getExternalSignatoryCode());
                bean.setExternalSignatoryName(reconcileInfo.getExternalSignatoryName());
                // 扩展其他属性
                String settlementCode = PolicySettlementUtils.createCodeLastNumber("SC");
                // 非小鲸缺失的，都需要做补偿信息处理
                if (ReconcileDiffTypeEnum.getReconcileDiffType(x.getDiffType()) != ReconcileDiffTypeEnum.XIAOWHALE_MISSED) {
                    // 精度处理补充险种相关信息
                    SettlementReconcilePolicyEntity reconcilePolicy = reconcilePolicyMap.get(x.getBillCode());
                    if (reconcilePolicy == null) {
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                                StrUtil.format("差异处理对账确认单，未能获取到对保单信息，billCode={}", x.getBillCode())));
                    }
                    SettlementPolicyInfoEntity settlementPolicyInfoEntity =
                            finalSettlementPolicyMap.get(reconcilePolicy.getSettlementCode());
                    if (settlementPolicyInfoEntity == null) {
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                                StrUtil.format("差异处理对账确认单，未能获取到对账明细原纪录，settlementCode={}",
                                        reconcilePolicy.getSettlementCode())));
                    }
                    // 赋值基本信息 + 重置一些特殊信息, 保费和折算保费需要为0
                    BeanUtils.copyProperties(settlementPolicyInfoEntity, bean);
                    // 设置为系统基础冲正
                    bean.setSettlementEventCode(SettlementEventTypeEnum.REVERSAL_AMOUNT.getEventCode());
                    bean.setSettlementEventDesc(SettlementEventTypeEnum.REVERSAL_AMOUNT.getEventName());
                    bean.setSourceSettlementCode(reconcilePolicy.getSettlementCode());
                    bean.setSourceSettlementDesc(SettlementEventTypeEnum.REVERSAL_AMOUNT.getEventName());
                    bean.setSourceSettlementType("3");
                    bean.setSettlementRate(x.getXiaowhaleSettlementRate());
                    // 非小鲸签约差异结算明细，保费、险种保费为0
                    bean.setPremium(BigDecimal.ZERO);
                    bean.setProductPremiumTotal(BigDecimal.ZERO);
                    // 折算保费
                    ReconcileBaseHelper.buildDiscountPremium(x.getPolicyNo(), x.getProductCode(),
                            reconcilePolicy.getInsuredPolicyAge(),
                            PolicyProductTypeEnum.getProdTypeEnum(x.getPolicyProductType()),
                            reconcilePolicy.getLongShortFlag(), x.getDiffPremium(), x.getPeriodType(),
                            x.getPaymentPeriodType(), x.getPaymentPeriod(), bean);
                } else {
                    // 1 获取保单详情
                    EpContractInfoVo policyInfo = null;
                    try {
                        policyInfo = policyCenterBaseClient.getPolicyInfoByPolicyCode(x.getPolicyNo(), false);
                    } catch (GlobalException e) {
                        log.info("保单号={}保单号在保单中未命中到信息", x.getPolicyNo());
                    }
                    // 2 保单信息不为空
                    if (policyInfo != null) {
                        // 保单来源
                        bean.setPolicyProductType(policyInfo.getPolicyProductType());
                        bean.setPolicySource(policyInfo.getPolicySource());
                        bean.setSelfPreservation(policyInfo.getContractBaseInfo().getSelfPreservation());
                        // 保单基本信息
                        EpContractBaseInfoVo contractBaseInfo = policyInfo.getContractBaseInfo();
                        EpContractExtendInfoVo contractExtendInfo = policyInfo.getContractExtendInfo();
                        bean.setContractCode(policyInfo.getContractCode());
                        BeanUtils.copyProperties(contractBaseInfo, bean);
                        BeanUtils.copyProperties(contractExtendInfo, bean);
                        // 回执/回访日志
                        bean.setReceiptStatus(contractExtendInfo.getIsNeedReceipt());
                        bean.setReceiptTime(contractExtendInfo.getReceiptSignTime());
                        bean.setRevisitStatus(contractExtendInfo.getIsNeedRevisit());
                        bean.setRevisitTime(contractExtendInfo.getRevisitTime());
                        // 投保人信息
                        bean.setApplicantName(policyInfo.getApplicantInfo().getApplicantName());
                        bean.setApplicantMobile(policyInfo.getApplicantInfo().getApplicantMobile());
                        bean.setApplicantIdCard(policyInfo.getApplicantInfo().getApplicantIdCard());
                        bean.setApplicantGender(policyInfo.getApplicantInfo().getApplicantGender());
                        bean.setApplicantBirthday(policyInfo.getApplicantInfo().getApplicantBirthday());
                        bean.setApplicantAge(policyInfo.getApplicantInfo().getApplicantAge());
                        // 主代理人信息
                        policyInfo.getAgentInfoList().stream().filter(a -> a.getMainFlag() == 1).findFirst()
                                .ifPresent(p -> {
                                    bean.setMainAgentCode(p.getAgentCode());
                                    bean.setOrgCode(p.getOrgCode());
                                });
                        // 渠道推荐人类型
                        EpPolicyChannelInfoVo channelInfo = policyInfo.getChannelInfo();
                        BeanUtils.copyProperties(channelInfo, bean);
                        // 3 业务类型 zhnx渠道就为农保渠道
                        if (StringUtils.equalsIgnoreCase(Constant.DEFAULT_CS_CHANNEL,
                                policyInfo.getChannelInfo().getChannelCode())) {
                            bean.setBusinessType(2);
                        } else {
                            bean.setBusinessType(1);
                        }
                    }
                    // 3 获取小鲸险种信息
                    ProductBase productBase = productBaseService.getProductInfo(x.getProductCode());
                    if (productBase == null) {
                        throw new GlobalException(
                                BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + x.getProductCode()));
                    }
                    // 4 险种信息+基本信息
                    bean.setProductCode(productBase.getProductCode());
                    bean.setProductName(productBase.getProductName());
                    bean.setProductGroup(productBase.getProductGroup());
                    bean.setCompanyName(productBase.getCompanyName());
                    bean.setCompanyCode(productBase.getCompanyCode());
                    bean.setProductType(productBase.getProductType());
                    bean.setLevel2Code(productBase.getLevel2Code());
                    bean.setLevel3Code(productBase.getLevel3Code());
                    bean.setProtocolProductCode(x.getProtocolProductCode());
                    bean.setProtocolProductName(x.getProtocolProductName());
                    if (StrUtil.isBlank(productBase.getLevel2Code())) {
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                                "险种编码=" + productBase.getProductCode() + "不存在二级分类"));
                    }
                    // 5 是否存在批改单号
                    if (StringUtils.isBlank(x.getEndorsementNo())) {
                        log.info("无批改单号系统判断为新契约");
                        bean.setSettlementEventCode(
                                SettlementEventTypeEnum.REVERSAL_XIAOWHALE_MISSED_POLICY.getEventCode());
                        bean.setSettlementEventDesc(
                                SettlementEventTypeEnum.REVERSAL_XIAOWHALE_MISSED_POLICY.getEventName());
                    } else {
                        log.info("无批改单号系统判断为保全项");
                        bean.setSettlementEventCode(SettlementEventTypeEnum.REVERSAL_XIAOWHALE_MISSED.getEventCode());
                        bean.setSettlementEventDesc(SettlementEventTypeEnum.REVERSAL_XIAOWHALE_MISSED.getEventName());
                    }
                    // 差额保费
                    bean.setPremium(x.getDiffPremium());
                    bean.setProductPremiumTotal(x.getDiffPremium());
                    bean.setDiscountPremium(new BigDecimal("-1"));
                }
                bean.setSettlementRate(x.getCompanySettlementRate());
                bean.setSettlementDate(new Date());
                bean.setSettlementTime(new Date());
                bean.setSettlementCode(settlementCode);
                bean.setReconcileCode(reconcileInfo.getReconcileCode());
                bean.setSettlementGenerateType(SettlementGenerateTypeEnum.AUTO_CORRECTION.getCode());
                bean.setSettlementSubjectCode(x.getReconcileSubjectCode());
                bean.setSettlementSubjectName(x.getReconcileSubjectName());
                bean.setEventSourceCode(x.getBillCode());
                // 设置为结算完成状态
                bean.setReconcileStatus(ReconcileStatusEnum.RECONCILE_FINISH.getStatusCode());
                bean.setReconcileTime(new Date());
                bean.setReconcileUser(userName);
                bean.setSettlementAmount(x.getReversalAmount());
                // 可对账单状态设置默认值
                bean.setReconcileExecuteStatus(1);
                bean.setReconcileExecuteDesc("系统对账完成自动补充为可对账");
                bean.setReconcileExecuteTime(new Date());
                bean.setDeleted(0);
                bean.setRevision(1);
                if (bean.getSelfPreservation() == null) {
                    bean.setSelfPreservation(0);
                }
                // 8 业务类型 zhnx渠道就为农保渠道
                if (StringUtils.equalsIgnoreCase(Constant.DEFAULT_CS_CHANNEL, bean.getChannelCode())) {
                    bean.setBusinessType(2);
                } else {
                    bean.setBusinessType(1);
                }
                return bean;
            }).collect(Collectors.toList());
            settlementPolicyInfoService.saveList(authList);
        }
    }

    /**
     * 精度处理自动冲正
     *
     * @param reconcileInfo 对账单信息
     * @param userName      操作用户
     * <AUTHOR>
     * @since 2023/6/8
     */
    private void autoAmountAccuracySettlement(SettlementReconcileInfoEntity reconcileInfo, String userName) {
        // 对账月份
        DateTime reconcileMonth = DateUtil.parse(reconcileInfo.getReconcileMonth(), "yyyy年MM月");
        // 结算月份是前一个月
        String settlementMonth = DateUtil.offsetMonth(reconcileMonth, -1).toString("yyyy年MM月");
        int id = 0;
        int limit = 5000;
        while (true) {
            List<SettlementReconcileConfirmEntity> reversalConfirmList = settlementReconcileConfirmService.lambdaQuery()
                    .eq(SettlementReconcileConfirmEntity::getReconcileCode, reconcileInfo.getReconcileCode())
                    .eq(SettlementReconcileConfirmEntity::getDiffType, ReconcileDiffTypeEnum.AMOUNT_ACCURACY.getCode())
                    .gt(SettlementReconcileConfirmEntity::getId, id).last(" limit " + limit)
                    .orderByAsc(SettlementReconcileConfirmEntity::getId).list();
            if (CollUtil.isEmpty(reversalConfirmList)) {
                log.info("需处理ID>{}的精度的对账数据为空，无需处理", id);
                break;
            }
            log.info("开始处理ID>{}的精度差异数据{}条", id, reversalConfirmList.size());
            id = CollUtil.getLast(reversalConfirmList).getId();
            List<String> billCodes = reversalConfirmList.stream().map(SettlementReconcileConfirmEntity::getBillCode)
                    .collect(Collectors.toList());
            List<SettlementReconcilePolicyEntity> reconcilePolicyList = settlementReconcilePolicyService.lambdaQuery()
                    .in(SettlementReconcilePolicyEntity::getBillCode, billCodes).list();
            Map<String, SettlementReconcilePolicyEntity> reconcilePolicyMap = reconcilePolicyList.stream().collect(
                    Collectors.toMap(SettlementReconcilePolicyEntity::getBillCode, Function.identity(), (x, y) -> x));

            List<String> settlementCodes =
                    reconcilePolicyList.stream().map(SettlementReconcilePolicyEntity::getSettlementCode)
                            .collect(Collectors.toList());

            Map<String, SettlementPolicyInfoEntity> settlementPolicyMap = settlementPolicyInfoService.lambdaQuery()
                    .in(SettlementPolicyInfoEntity::getSettlementCode, settlementCodes).list().stream().collect(
                            Collectors.toMap(SettlementPolicyInfoEntity::getSettlementCode, Function.identity(), (x, y) -> x));

            List<SettlementPolicyInfoEntity> authList = reversalConfirmList.stream().map(x -> {
                SettlementPolicyInfoEntity bean = new SettlementPolicyInfoEntity();
                BeanUtils.copyProperties(x, bean);
                bean.setSettlementMonth(settlementMonth);
                bean.setReconcileType(reconcileInfo.getReconcileType());
                // 扩展其他属性
                String settlementCode = PolicySettlementUtils.createCodeLastNumber("SC");
                // 精度处理补充险种相关信息
                SettlementReconcilePolicyEntity reconcilePolicy = reconcilePolicyMap.get(x.getBillCode());
                if (reconcilePolicy == null) {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                            StrUtil.format("精度处理对账确认单，未能获取到对保单信息，billCode={}", x.getBillCode())));
                }
                SettlementPolicyInfoEntity settlementPolicyInfoEntity =
                        settlementPolicyMap.get(reconcilePolicy.getSettlementCode());
                if (settlementPolicyInfoEntity == null) {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                            StrUtil.format("精度处理对账确认单，未能获取到对账明细原纪录，SettlementCode={}",
                                    reconcilePolicy.getSettlementCode())));
                }
                // 赋值基本信息 + 重置一些特殊信息 + 差额保费
                BeanUtils.copyProperties(settlementPolicyInfoEntity, bean);
                bean.setSourceSettlementCode(reconcilePolicy.getSettlementCode());
                bean.setSourceSettlementType("3");
                bean.setSourceSettlementDesc(SettlementEventTypeEnum.AMOUNT_ACCURACY.getEventName());
                bean.setSettlementRate("0");
                bean.setPremium(x.getDiffPremium());
                // 结算信息
                bean.setSettlementDate(new Date());
                bean.setSettlementTime(new Date());
                bean.setSettlementCode(settlementCode);
                bean.setReconcileCode(reconcileInfo.getReconcileCode());
                bean.setSettlementGenerateType(SettlementGenerateTypeEnum.AUTO_CORRECTION.getCode());
                // 补充字段其他字段
                bean.setSettlementEventCode(SettlementEventTypeEnum.AMOUNT_ACCURACY.getEventCode());
                bean.setSettlementEventDesc(SettlementEventTypeEnum.AMOUNT_ACCURACY.getEventName());
                bean.setSettlementSubjectCode(x.getReconcileSubjectCode());
                bean.setSettlementSubjectName(x.getReconcileSubjectName());
                bean.setEventSourceCode(x.getBillCode());
                // 设置为结算完成状态
                bean.setReconcileStatus(ReconcileStatusEnum.RECONCILE_FINISH.getStatusCode());
                bean.setReconcileTime(new Date());
                bean.setReconcileUser(userName);
                bean.setSettlementAmount(x.getReversalAmount());
                // 可对账单状态设置默认值
                bean.setReconcileExecuteStatus(1);
                bean.setReconcileExecuteDesc("系统对账完成自动补充为可对账");
                bean.setReconcileExecuteTime(new Date());
                // 折算保费为0
                bean.setDiscountPremium(BigDecimal.ZERO);
                bean.setDeleted(0);
                bean.setRevision(1);
                if (bean.getSelfPreservation() == null) {
                    bean.setSelfPreservation(0);
                }
                // 8 业务类型 zhnx渠道就为农保渠道
                if (StringUtils.equalsIgnoreCase(Constant.DEFAULT_CS_CHANNEL, bean.getChannelCode())) {
                    bean.setBusinessType(2);
                } else {
                    bean.setBusinessType(1);
                }
                return bean;
            }).collect(Collectors.toList());
            settlementPolicyInfoService.saveList(authList);
        }
    }

    /**
     * 处理仅报送保费的数据
     *
     * @param reconcileInfo 对账单信息
     * @param userName      操作人员
     */
    private void autoOnlySubmitPremium(SettlementReconcileInfoEntity reconcileInfo, String userName) {
        // 对账月份
        DateTime reconcileMonth = DateUtil.parse(reconcileInfo.getReconcileMonth(), "yyyy年MM月");
        // 结算月份是前一个月
        String settlementMonth = DateUtil.offsetMonth(reconcileMonth, -1).toString("yyyy年MM月");
        int id = 0;
        int limit = 5000;
        while (true) {
            List<SettlementReconcileConfirmEntity> reversalConfirmList = settlementReconcileConfirmService.lambdaQuery()
                    .eq(SettlementReconcileConfirmEntity::getReconcileCode, reconcileInfo.getReconcileCode())
                    .eq(SettlementReconcileConfirmEntity::getDiffType, ReconcileDiffTypeEnum.ONLY_SUBMIT_PREMIUM.getCode())
                    .gt(SettlementReconcileConfirmEntity::getId, id).last(" limit " + limit)
                    .orderByAsc(SettlementReconcileConfirmEntity::getId).list();
            if (CollUtil.isEmpty(reversalConfirmList)) {
                log.info("需处理ID>{}的仅报送保费的数据为空，无需处理", id);
                break;
            }
            log.info("开始处理ID>{}的仅报送保费的数据差异数据{}条", id, reversalConfirmList.size());
            id = CollUtil.getLast(reversalConfirmList).getId();
            List<String> billCodes = reversalConfirmList.stream().map(SettlementReconcileConfirmEntity::getBillCode)
                    .collect(Collectors.toList());
            List<String> settlementCodes = settlementReconcilePolicyService.lambdaQuery()
                    .in(SettlementReconcilePolicyEntity::getBillCode, billCodes).list()
                    .stream().map(SettlementReconcilePolicyEntity::getSettlementCode)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(settlementCodes)) {
                // 设置状态为仅报送保费
                settlementPolicyInfoService.lambdaUpdate()
                        .set(SettlementPolicyInfoEntity::getRegulatoryReportType, 2)
                        .in(SettlementPolicyInfoEntity::getSettlementCode, settlementCodes)
                        .update();
            }

        }
    }

    /**
     * 异步批量处理精度
     *
     * @param reconcileAmountAccuracyInput 批量处理对账单精度信息
     * @param uuid
     */
    @Async
    @Override
    public void reconcileAmountAccuracy(ReconcileAmountAccuracyInput reconcileAmountAccuracyInput, String uuid) {
        try {
            // 加一个锁 防止有进行中的冲正事件
            boolean lock = RedissLockUtil.tryLock(
                    StrUtil.format("RECONCILE_AMOUNT_ACCURACY:{}", reconcileAmountAccuracyInput.getReconcileCode()), 3600,
                    -1);
            if (!lock) {
                redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, "2");
                return;
            }
            log.info("开始处理对账单={}批量精度处理 start", reconcileAmountAccuracyInput.getReconcileCode());
            // 1 获取账单信息
            SettlementReconcileInfoEntity reconcileInfo = Optional.ofNullable(
                            settlementReconcileInfoService.lambdaQuery().eq(SettlementReconcileInfoEntity::getReconcileCode,
                                    reconcileAmountAccuracyInput.getReconcileCode()).one())
                    .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("对账单纪录不存在")));
            int id = 0;
            int limit = 5000;
            while (true) {
                // 分片处理精度差异
                List<SettlementReconcileConfirmEntity> reconcileConfirmList =
                        settlementReconcileConfirmService.lambdaQuery().gt(SettlementReconcileConfirmEntity::getId, id)
                                .eq(SettlementReconcileConfirmEntity::getReconcileCode, reconcileInfo.getReconcileCode())
                                .eq(SettlementReconcileConfirmEntity::getDiffStatus, 0)
                                .eq(SettlementReconcileConfirmEntity::getBillEnable, 1)
                                .eq(SettlementReconcileConfirmEntity::getDiffType,
                                        ReconcileDiffTypeEnum.AMOUNT_ACCURACY.getCode())
                                .orderByAsc(SettlementReconcileConfirmEntity::getId).last("LIMIT " + limit).list();
                if (reconcileConfirmList.isEmpty()) {
                    log.info("需处理ID>{}的精度的对账数据为空，无需处理", id);
                    break;
                }
                log.info("开始处理ID>{}的精度差异数据{}条", id, reconcileConfirmList.size());

                // 3 调整明细，批量精度无需进入待办事项
                reconcileConfirmList.forEach(x -> {
                    x.setSettlementAmount(x.getCompanyAmount());
                    x.setSettlementPremium(x.getCompanyPremium());
                    x.setSettlementRate(x.getCompanySettlementRate());
                    x.setReversalAmount(x.getCompanyAmount().subtract(x.getXiaowhaleAmount()));
                    x.setDiffStatus(1);
                    x.setDiffOpeUserName(reconcileAmountAccuracyInput.getOpeUserName());
                    x.setDiffDesc(reconcileAmountAccuracyInput.getDiffDesc());
                    x.setOpeType(0);
                    x.setDiffWhy(reconcileAmountAccuracyInput.getDiffWhy());
                });
                // 4 批量受理
                settlementReconcileConfirmService.updateBatchById(reconcileConfirmList, limit);
                // 获取最后一条数据的ID
                id = CollUtil.getLast(reconcileConfirmList).getId();
                //休息500毫秒再继续 稍微减轻一下数据库的压力
                ThreadUtils.sleep(500);
            }
            log.info("开始更新差额数据......");
            // 5 处理对账单差额信息
            List<SettlementReconcileConfirmEntity> confirmList = settlementReconcileConfirmService.lambdaQuery()
                    .eq(SettlementReconcileConfirmEntity::getReconcileCode, reconcileAmountAccuracyInput.getReconcileCode())
                    .eq(SettlementReconcileConfirmEntity::getDiffFlag, 1).list();

            BigDecimal diffAmount = confirmList.stream().map(SettlementReconcileConfirmEntity::getDiffAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal reversalAmount = confirmList.stream().map(SettlementReconcileConfirmEntity::getReversalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            log.info("对账单={},差异金额={},冲正金额={}", reconcileAmountAccuracyInput.getReconcileCode(), diffAmount,
                    reversalAmount);
            settlementReconcileInfoService.lambdaUpdate()
                    .eq(SettlementReconcileInfoEntity::getReconcileCode, reconcileAmountAccuracyInput.getReconcileCode())
                    .set(SettlementReconcileInfoEntity::getDiffAmount, diffAmount)
                    .set(SettlementReconcileInfoEntity::getReversalAmount, reversalAmount).update();
            log.info("差额数据更新完成.....");
        } catch (GlobalException e) {
            log.warn("对账单={}对账失败,失败原因={}", reconcileAmountAccuracyInput.getReconcileCode(), e.getMsg());
        } catch (Exception e) {
            log.warn("处理结算={}数据精度差异异常", reconcileAmountAccuracyInput.getReconcileCode(), e);
        } finally {
            redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, "2");
            RedissLockUtil.unlock(
                    StrUtil.format("RECONCILE_AMOUNT_ACCURACY:{}", reconcileAmountAccuracyInput.getReconcileCode()));
            log.info("处理对账单={}开始处理批量精度处理 end", reconcileAmountAccuracyInput.getReconcileCode());
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void diffBacklog(DiffBacklogInput diffBacklogInput) {
        log.info("差异处理 start");
        // 1 获取账单信息
        SettlementReconcileInfoEntity reconcileInfo = Optional.ofNullable(settlementReconcileInfoService.lambdaQuery()
                        .eq(SettlementReconcileInfoEntity::getReconcileCode, diffBacklogInput.getReconcileCode()).one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("对账单纪录不存在")));

        // 2 获取结算汇总明细纪录 >> 保单明细(线上) + 没有
        List<SettlementReconcileConfirmEntity> settlementReconcileConfirmList =
                settlementReconcileConfirmService.lambdaQuery()
                        .in(SettlementReconcileConfirmEntity::getBillCode, diffBacklogInput.getBillCodeList())
                        .eq(SettlementReconcileConfirmEntity::getDiffStatus, 0).list();
        if (settlementReconcileConfirmList.isEmpty()) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                    StrUtil.format("BillConfirmCode=[{}]信息不存在," + "或已操作差异提交",
                            JSONUtil.toJsonStr(diffBacklogInput.getBillCodeList()))));
        }
        settlementReconcileConfirmList.forEach(reconcileConfirm -> {
            // 获取差异类型
            ReconcileDiffTypeEnum reconcileDiffType =
                    ReconcileDiffTypeEnum.getReconcileDiffType(reconcileConfirm.getDiffType());
            // 获取差异原因类型
            ReconcileDiffWhyEnum whyEnum = ReconcileDiffWhyEnum.getReconcileDiffWhy(diffBacklogInput.getDiffWhy());
            if (diffBacklogInput.getAssignAcceptUser() == 1) {
                log.info("需要进入待办..");
                SettlementReconcileDiffBacklogEntity diffBacklog = new SettlementReconcileDiffBacklogEntity();
                String backlogCode = PolicySettlementUtils.createCodeLastNumber("DBL");
                diffBacklog.setBacklogCode(backlogCode);
                diffBacklog.setReconcileCode(reconcileInfo.getReconcileCode());
                diffBacklog.setBillCode(reconcileConfirm.getBillCode());
                diffBacklog.setDiffType(reconcileDiffType.getCode());
                diffBacklog.setDiffName(reconcileDiffType.getName());
                // 差异处理原因
                diffBacklog.setDiffWhy(whyEnum.getCode());
                diffBacklog.setDiffName(whyEnum.getName());
                diffBacklog.setPolicyNo(reconcileConfirm.getPolicyNo());
                diffBacklog.setAcceptUserCode(diffBacklogInput.getAcceptUserCode());
                diffBacklog.setAcceptUserName(diffBacklogInput.getAcceptUserName());
                diffBacklog.setDiffDesc(diffBacklogInput.getDiffDesc());
                // 待办事项状态
                diffBacklog.setFollowStatus(0);
                diffBacklog.setDiffStatus(0);
                diffBacklog.setBacklogEnable(1);
                settlementReconcileDiffBacklogService.save(diffBacklog);
                // 更新对账差异到对账明细
                reconcileConfirm.setBacklogCode(backlogCode);
            }
            // 3 更新对账单汇总纪录
            reconcileConfirm.setDiffStatus(1);
            reconcileConfirm.setBillEnable(1);
            reconcileConfirm.setDiffDesc(diffBacklogInput.getDiffDesc());
            reconcileConfirm.setDiffWhy(diffBacklogInput.getDiffWhy());
            reconcileConfirm.setOpeType(diffBacklogInput.getOpeType());
            reconcileConfirm.setDiffOpeUserName(diffBacklogInput.getOpeUserName());
            // 以小鲸为准/以保司为准
            if (diffBacklogInput.getOpeType() == 0) {
                // 如果是保司缺失 + 以保司为准
                if (reconcileDiffType == ReconcileDiffTypeEnum.COMPANY_MISSED) {
                    // 以保司为准 + 保司缺失,设置无效的，需要完成对账时，自动生成
                    reconcileConfirm.setBillEnable(0);
                }
                reconcileConfirm.setSettlementAmount(reconcileConfirm.getCompanyAmount());
                reconcileConfirm.setSettlementRate(reconcileConfirm.getCompanySettlementRate());
                reconcileConfirm.setSettlementPremium(reconcileConfirm.getCompanyPremium());
            } else {
                reconcileConfirm.setSettlementAmount(reconcileConfirm.getXiaowhaleAmount());
                reconcileConfirm.setSettlementRate(reconcileConfirm.getXiaowhaleSettlementRate());
                reconcileConfirm.setSettlementPremium(reconcileConfirm.getPremium());
            }
            // 设置冲正金额
            reconcileConfirm.setReversalAmount(
                    reconcileConfirm.getSettlementAmount().subtract(reconcileConfirm.getXiaowhaleAmount()));
            settlementReconcileConfirmService.updateById(reconcileConfirm);
        });
        // 4 处理对账单差额信息
        List<SettlementReconcileConfirmEntity> confirmList = settlementReconcileConfirmService.lambdaQuery()
                .eq(SettlementReconcileConfirmEntity::getReconcileCode, diffBacklogInput.getReconcileCode())
                .eq(SettlementReconcileConfirmEntity::getDiffFlag, 1).list();
        // 设置差异金额和冲正金额
        confirmList.stream().map(SettlementReconcileConfirmEntity::getDiffAmount).reduce(BigDecimal::add)
                .ifPresent(reconcileInfo::setDiffAmount);
        confirmList.stream().map(SettlementReconcileConfirmEntity::getReversalAmount).reduce(BigDecimal::add)
                .ifPresent(reconcileInfo::setReversalAmount);
        settlementReconcileInfoService.updateById(reconcileInfo);
        log.info("差异处理 end");
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void finishReconcileHelp(String reconcileCode, String userName) {
        SettlementReconcileInfoEntity reconcileInfo = Optional.ofNullable(settlementReconcileInfoService.lambdaQuery()
                        .eq(SettlementReconcileInfoEntity::getReconcileCode, reconcileCode).one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("对账单纪录不存在")));

        // 1 settlement_reconcile_policy 里面为reconcileGenerateType = 小鲸线下对账单的数据，自动写入到 settlement_policy_info纪录并标记为已完成对账
        autoInsertSettlementPolicy(reconcileInfo, userName);
        log.info("{} 小鲸线下对账单的数据，自动写入到 settlement_policy_info纪录并标记为已完成", reconcileCode);
        // 2 读取差异纪录(非精度数据 )，进行自动冲正到明细表
        autoReversalSettlementPolicy(reconcileInfo, userName);

        // 4 获取所有精度处理的数据，进行自动写入到关联settlement_policy_info为已对账完成
        autoAmountAccuracySettlement(reconcileInfo, userName);
    }

    /**
     * 处理监管报送
     *
     * @param reconcileCode 对账单编码
     * @param uuid          唯一标识
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reconcileRegulatorySubmit(String reconcileCode, String uuid) {
        log.info("处理对账单={} 监管报送 start", reconcileCode);
        String tryKey = StrUtil.format("RECONCILE_REGULATORY_SUBMIT:{}", reconcileCode);
        try {
            SettlementReconcileInfoEntity reconcileInfo = Optional.ofNullable(
                            settlementReconcileInfoService.lambdaQuery()
                                    .eq(SettlementReconcileInfoEntity::getReconcileCode, reconcileCode).one())
                    .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("对账单纪录不存在")));
            if (reconcileInfo.getReconcileStatus() != 3) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("未完成对账,不允许触发监管报送"));
            }
            if (reconcileInfo.getReconcileType() != 0) {
                log.info("对账单={},不是协议对账单不需要报送", reconcileCode);
                return;
            }
            // 加一个锁 防止有进行中的冲正事件
            boolean lock = RedissLockUtil.tryLock(tryKey, 3600, -1);
            if (!lock) {
                throw new GlobalException(
                        BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("获取监管报送锁={}失败", tryKey)));
            }

            String dayStr = DateUtil.date().toDateStr();
            // 开始处理数据
            //1.删除已经报送的数据
            biSettlementIncomeRegulatorySubmitService.lambdaUpdate()
                    .eq(BiSettlementIncomeRegulatorySubmitEntity::getReconcileCode, reconcileCode).remove();
            //2.开始处理同步数据
            long current = 1;
            long size = 5000;
            while (true) {
                List<SettlementPolicyInfoEntity> settlementPolicyInfoList = settlementPolicyInfoService.lambdaQuery()
                        .eq(SettlementPolicyInfoEntity::getReconcileCode, reconcileCode)
                        .page(new Page<>(current, size, false)).getRecords();
                if (settlementPolicyInfoList.isEmpty()) {
                    break;
                }
                List<String> companyCodeList =
                        settlementPolicyInfoList.stream().map(SettlementPolicyInfoEntity::getCompanyCode).distinct()
                                .collect(Collectors.toList());
                // 获取保险公司信息
                Map<String, CompanyInfo> companyInfoMap =
                        productBaseService.findInsuranceCompanyList(companyCodeList).stream()
                                .collect(Collectors.toMap(CompanyInfo::getCompanyCode, Function.identity()));
                current += 1;
                List<BiSettlementIncomeRegulatorySubmitEntity> saveList =
                        settlementPolicyInfoList.stream().map(settlementPolicyInfo -> {
                            BiSettlementIncomeRegulatorySubmitEntity result = BeanUtil.copyProperties(settlementPolicyInfo,
                                    BiSettlementIncomeRegulatorySubmitEntity.class);
                            result.setDayStr(dayStr);
                            result.setLevel2Name(DicCacheHelper.getValue(settlementPolicyInfo.getLevel2Code()));
                            result.setLevel3Name(DicCacheHelper.getValue(settlementPolicyInfo.getLevel3Code()));
                            if (companyInfoMap.containsKey(settlementPolicyInfo.getCompanyCode())) {
                                CompanyInfo companyInfo = companyInfoMap.get(settlementPolicyInfo.getCompanyCode());
                                result.setLawType(companyInfo.getLawType());
                                result.setLawTypeDesc(DicCacheHelper.getValue(companyInfo.getLawType()));
                            }
                            // 判断是否仅报送收入 分销渠道编码以”xj"开头且 != “xJXH001”，属于只报送收入不报送保费。
                            if (StrUtil.isNotBlank(
                                    settlementPolicyInfo.getChannelDistributionCode()) && settlementPolicyInfo.getChannelDistributionCode()
                                    .startsWith("XJ")) {
                                result.setOnlyReportIncome(
                                        settlementPolicyInfo.getChannelDistributionCode().equals("XJXH001") ? 0 : 1);
                            } else {
                                result.setOnlyReportIncome(0);
                            }
                            return result;
                        }).collect(Collectors.toList());
                // 批量插入数据
                biSettlementIncomeRegulatorySubmitService.saveList(saveList);
            }
        } catch (GlobalException e) {
            redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, "2");
            log.warn("对账单={}处理监管报送,失败原因={}", reconcileCode, e.getMsg());
        } catch (Exception e) {
            log.warn("处理结算={}处理监管报送异常", reconcileCode, e);
            redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, "2");
        } finally {
            // 释放锁
            RedissLockUtil.unlock(tryKey);
            log.info("处理对账单={} 监管报送 end", reconcileCode);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleSettlementMonthReport(String reconcileCode, String uuid) {
        log.info("处理对账单={} 结算月度报表 start", reconcileCode);
        String tryKey = StrUtil.format("RECONCILE_REGULATORY_SUBMIT:{}", reconcileCode);
        try {
            SettlementReconcileInfoEntity reconcileInfo = Optional.ofNullable(
                            settlementReconcileInfoService.lambdaQuery()
                                    .eq(SettlementReconcileInfoEntity::getReconcileCode, reconcileCode).one())
                    .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("对账单纪录不存在")));
            if (reconcileInfo.getReconcileStatus() != 3) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("未完成对账,不允许触发监管报送"));
            }
            // 加一个锁 防止有进行中的冲正事件
            boolean lock = RedissLockUtil.tryLock(tryKey, 3600, -1);
            if (!lock) {
                throw new GlobalException(
                        BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("获取监管报送锁={}失败", tryKey)));
            }
            Map<String, String> channeMap = channelInfoService.list().stream().collect(Collectors.toMap(ChannelInfoEntity::getChannelCode, ChannelInfoEntity::getChannelName));
            String dayStr = DateUtil.date().toDateStr();
            // 开始处理数据
            //1.删除已经报送的数据
            biSettlementMonthReportService.lambdaUpdate()
                    .eq(BiSettlementMonthReportEntity::getReconcileCode, reconcileCode).remove();
            //2.开始处理同步数据
            long current = 1;
            long size = 5000;
            log.info("开始处理对账单={}", reconcileCode);
            while (true) {
                List<SettlementPolicyInfoEntity> settlementPolicyInfoList = settlementPolicyInfoService.lambdaQuery()
                        .eq(SettlementPolicyInfoEntity::getReconcileCode, reconcileCode)
                        .page(new Page<>(current, size, false)).getRecords();
                if (settlementPolicyInfoList.isEmpty()) {
                    log.info("处理对账单={}, 无对账数据", reconcileCode);
                    break;
                }
                List<String> companyCodeList =
                        settlementPolicyInfoList.stream().map(SettlementPolicyInfoEntity::getCompanyCode).distinct()
                                .collect(Collectors.toList());
                // 获取保险公司信息
                Map<String, CompanyInfo> companyInfoMap =
                        productBaseService.findInsuranceCompanyList(companyCodeList).stream()
                                .collect(Collectors.toMap(CompanyInfo::getCompanyCode, Function.identity()));
                current += 1;
                List<BiSettlementMonthReportEntity> saveList =
                        settlementPolicyInfoList.stream().map(settlementPolicyInfo -> {
                            BiSettlementMonthReportEntity result = BeanUtil.copyProperties(settlementPolicyInfo,
                                    BiSettlementMonthReportEntity.class);
                            result.setDayStr(dayStr);
                            result.setLevel2Name(DicCacheHelper.getValue(settlementPolicyInfo.getLevel2Code()));
                            result.setLevel3Name(DicCacheHelper.getValue(settlementPolicyInfo.getLevel3Code()));
                            if (companyInfoMap.containsKey(settlementPolicyInfo.getCompanyCode())) {
                                CompanyInfo companyInfo = companyInfoMap.get(settlementPolicyInfo.getCompanyCode());
                                result.setLawType(companyInfo.getLawType());
                            }
                            // 判断是否仅报送收入 分销渠道编码以”xj"开头且 != “xJXH001”，属于只报送收入不报送保费。
                            if (StrUtil.isNotBlank(
                                    settlementPolicyInfo.getChannelDistributionCode()) && settlementPolicyInfo.getChannelDistributionCode()
                                    .startsWith("XJ")) {
                                result.setOnlyReportIncome(
                                        settlementPolicyInfo.getChannelDistributionCode().equals("XJXH001") ? 0 : 1);
                            } else {
                                result.setOnlyReportIncome(0);
                            }
                            result.setSettlementCompanyName(reconcileInfo.getCompanyName());
                            result.setInnerSignatoryCode(reconcileInfo.getInnerSignatoryCode());
                            result.setInnerSignatoryName(reconcileInfo.getInnerSignatoryName());
                            result.setChannelCode(settlementPolicyInfo.getChannelCode());
                            result.setChannelName(channeMap.get(settlementPolicyInfo.getChannelCode()));
                            result.setSettlementMonth(reconcileInfo.getReconcileMonth());
                            return result;
                        }).collect(Collectors.toList());
                // 批量插入数据
                log.info("插入对账单数据={}, 大小为={}", reconcileCode, saveList.size());
                biSettlementMonthReportService.saveList(saveList);
            }
        } catch (GlobalException e) {
            log.warn("对账单={}结算月度报表,失败原因={}", reconcileCode, e.getMsg());
            redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, "2");
        } catch (Exception e) {
            log.warn("处理结算={}处理结算月度报表送异常", reconcileCode, e);
            redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, "2");
        } finally {
            // 释放锁
            RedissLockUtil.unlock(tryKey);
            log.info("处理对账单={} 结算月度报表 end", reconcileCode);
        }

    }
}