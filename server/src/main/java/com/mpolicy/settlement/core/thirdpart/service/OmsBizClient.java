package com.mpolicy.settlement.core.thirdpart.service;

import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.thirdpart.client.OmsBizFeign;
import com.mpolicy.settlement.core.thirdpart.dto.omsbiz.LicenseSyncRequest;
import com.mpolicy.settlement.core.thirdpart.dto.omsbiz.LicenseSyncResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * oms-biz服务客户端
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Component
@Slf4j
public class OmsBizClient {

    @Autowired
    private OmsBizFeign omsBizFeign;

    /**
     * 同步工商信息
     * 
     * @param request 同步请求参数
     * @return 同步响应结果
     */
    public LicenseSyncResponse syncLicenseInfo(LicenseSyncRequest request) {
        LicenseSyncResponse result = null;
        try {
            log.info("调用oms-biz服务同步工商信息，请求参数：{}", request);
            result = omsBizFeign.syncLicenseInfo(request);
        } catch (Exception e) {
            log.warn("调用oms-biz服务同步工商信息异常", e);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("调用oms-biz服务同步工商信息异常，请开发人员及时排查问题"));
        }
        
        if (result == null || !Boolean.TRUE.equals(result.getSuccess())) {
            log.warn("调用oms-biz服务同步工商信息失败，响应结果：{}", result);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("调用oms-biz服务同步工商信息失败"));
        }
        
        return result;
    }
}
