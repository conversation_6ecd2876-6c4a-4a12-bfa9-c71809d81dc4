package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.common.reconcile.RefreshSettlementEventJobVo;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementEventJobDao;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementStandardPremiumEventJobDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementStandardPremiumEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.event.factory.SettlementEventFactory;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.SettlementEventHandler;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementEventJobService;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementStandardPremiumEventJobService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 标准保费交互事件受理表
 *
 * <AUTHOR>
 * @date 2024-11-21 22:28:33
 */
@Slf4j
@Service("settlementStandardPremiumEventJobService")
public class SettlementStandardPremiumEventJobServiceImpl extends ServiceImpl<SettlementStandardPremiumEventJobDao, SettlementStandardPremiumEventJobEntity> implements SettlementStandardPremiumEventJobService {


}
