package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementReconcileCompanySubjectPolicyMethodDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileCompanySubjectPolicyMethodEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcileCompanySubjectPolicyMethodService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service("settlementReconcileCompanySubjectPolicyMethodService")
public class SettlementReconcileCompanySubjectPolicyMethodServiceImpl  extends ServiceImpl<SettlementReconcileCompanySubjectPolicyMethodDao, SettlementReconcileCompanySubjectPolicyMethodEntity> implements SettlementReconcileCompanySubjectPolicyMethodService {
}
