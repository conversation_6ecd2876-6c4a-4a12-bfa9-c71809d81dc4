package com.mpolicy.settlement.core.modules.reconcile.service;

import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.product.common.base.ProductBase;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.PolicyPreservationDetailDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;

import java.util.List;
import java.util.Map;

public interface SettlementCostSurrenderProcessService {
    /**
     *
     * @param eventJob
     * @param subjectEnum
     * @param eventType
     * @param productMap
     * @param policyInfo
     * @param preservationDetail
     * @return
     */
    List<SettlementCostInfoEntity> builderPolicyHesitateSurrenderCostInfo(SettlementEventJobEntity eventJob, CostSubjectEnum subjectEnum, SettlementEventTypeEnum eventType, Map<String, ProductBase> productMap, EpContractInfoVo policyInfo, PolicyPreservationDetailDto preservationDetail);

    /**
     *
     * @param eventJob
     * @param subjectEnum
     * @param eventType
     * @param productMap
     * @param policyInfo
     * @param preservationDetail
     * @return
     */
    List<SettlementCostInfoEntity> builderStandardSurrenderCostInfo(SettlementEventJobEntity eventJob, CostSubjectEnum subjectEnum, SettlementEventTypeEnum eventType, Map<String, ProductBase> productMap, EpContractInfoVo policyInfo, PolicyPreservationDetailDto preservationDetail);

    /**
     *
     * @param eventJob
     * @param subjectEnum
     * @param eventType
     * @param productMap
     * @param policyInfo
     * @param preservationDetail
     * @return
     */
    List<SettlementCostInfoEntity> builderProtocolTerminationCostInfo(SettlementEventJobEntity eventJob, CostSubjectEnum subjectEnum, SettlementEventTypeEnum eventType, Map<String, ProductBase> productMap, EpContractInfoVo policyInfo, PolicyPreservationDetailDto preservationDetail);
}
