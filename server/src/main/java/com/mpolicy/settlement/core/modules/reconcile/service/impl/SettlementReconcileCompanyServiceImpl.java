package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementReconcileCompanyDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileCompanyEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcileCompanyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 保司结算对账单关联保司对账单数据
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:34
 */
@Slf4j
@Service("settlementReconcileCompanyService")
public class SettlementReconcileCompanyServiceImpl extends ServiceImpl<SettlementReconcileCompanyDao, SettlementReconcileCompanyEntity> implements SettlementReconcileCompanyService {

    private final int limit = 1000;

    @Override
    public void saveList(List<SettlementReconcileCompanyEntity> listData) {
        // 批量写入
        if (listData.size() > limit) {
            List<List<SettlementReconcileCompanyEntity>> partition = ListUtils.partition(listData, limit);
            partition.forEach(this::insertBatchSomeColumn);
        } else {
            insertBatchSomeColumn(listData);
        }
    }

    private void insertBatchSomeColumn(List<SettlementReconcileCompanyEntity> listData) {
        try {
            baseMapper.insertBatchSomeColumn(listData);
        } catch (Exception e) {
            log.warn("批量写入失败数据={}", JSONUtil.toJsonStr(listData), e);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("写入保司对账单数据失败!")));
        }
    }
}
