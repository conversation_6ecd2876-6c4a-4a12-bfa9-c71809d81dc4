package com.mpolicy.settlement.core.modules.reconcile.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.other.BigDecimalUtils;
import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.policy.common.ep.policy.EpInsuredInfoVo;
import com.mpolicy.policy.common.ep.policy.EpPolicyActivityVo;
import com.mpolicy.policy.common.ep.policy.EpProductInfoVo;
import com.mpolicy.product.common.base.ProductBase;
import com.mpolicy.settlement.core.common.Constant;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.ConfirmStatusEnum;
import com.mpolicy.settlement.core.modules.protocol.dto.ProtocolInsuranceProductInfoOut;
import com.mpolicy.settlement.core.modules.protocol.helper.ProtocolBaseHelper;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.*;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.CostBasicCommissionConfigDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.*;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyProductPremInput;
import com.mpolicy.settlement.core.modules.reconcile.helper.ReconcileBaseHelper;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/6 8:49 下午
 * @Version 1.0
 */
@Slf4j
public abstract class AbstractSettlementCostProcessService {

    protected static Integer autoCostSettlementDay;
    protected static String COMPULSORY_INSURANCE = "PRODUCT:LEVEL2_CAT:CX:3:1";


    protected static List<String> LIST_RENEWAL_INSURANCE = Arrays.asList(SettlementEventTypeEnum.RENEWAL_TERM_POLICY.getEventCode(), SettlementEventTypeEnum.RENEWAL_POLICY.getEventCode());

    /**
     * 城市业务保费为0的事件类型编码
     */
    public static List<String> LIST_CITY_ZERO_PREMIUM_EVENT = Arrays.asList(SettlementEventTypeEnum.STANDARD_SURRENDER.getEventCode(), SettlementEventTypeEnum.TERMINATION_PRODUCT.getEventCode());
    /**
     * 保单来源为线下单导入方式
     */
    protected static List<String> LIST_MANUAL_IMPORT = Arrays.asList(PolicySourceEnum.SYNC_POLICY.getSourceCode(), PolicySourceEnum.MANUAL_POLICY.getSourceCode());

    @Autowired
    protected SettlementCostOwnerService settlementCostOwnerService;



    /**
     * 克隆基础佣金，并更新costCode;
     * @param bean
     * @return
     */
    protected SettlementCostInfoEntity cloneCostInfoEntity(SettlementCostInfoEntity bean){
        if(Objects.isNull(bean)){
            return null;
        }
        SettlementCostInfoEntity newObj = bean.clone();
        newObj.setCostCode(PolicySettlementUtils.createCodeLastNumber("CC"));
        return newObj;
    }

    /**
     * 支出端-初始化支出8大基础佣金记录
     *
     * @param eventJob
     * @param subjectEnum
     * @return
     */
    protected SettlementCostInfoEntity initSettlementCostInfo(SettlementEventJobEntity eventJob, CostSubjectEnum subjectEnum, SettlementEventTypeEnum eventType, String contractCode, String policyNo, Integer insuranceType) {
        SettlementCostInfoEntity cost = new SettlementCostInfoEntity();
        cost.setCostCode(PolicySettlementUtils.createCodeLastNumber("CC"));
        //事件编号
        cost.setEventSourceCode(eventJob.getPushEventCode());
        //事件信息
        if (Objects.nonNull(eventType)) {
            cost.setSettlementEventCode(eventType.getEventCode());
            //八大基础佣金事件编码时与SettlementEventCode一直
            cost.setInitialEventCode(eventType.getEventCode());
            cost.setSettlementEventDesc(eventType.getEventDesc());
        }
        //科目信息
        cost.setSettlementSubjectCode(subjectEnum.getCode());
        cost.setSettlementSubjectName(subjectEnum.getName());
        cost.setSettlementGenerateType(1);
        cost.setContractCode(contractCode);
        cost.setInsuranceType(insuranceType);
        cost.setPolicyNo(policyNo);
        cost.setCompanyPolicyNo(policyNo);
        //设置佣金类型
        setCommissionTypeByEventType(eventType, cost);

        return cost;
    }

    protected SettlementCostInfoEntity initSettlementCostInfo(SettlementEventJobEntity eventJob, CostSubjectEnum subjectEnum, SettlementEventTypeEnum eventType, EpContractInfoVo policyInfo, Integer insuranceType) {
        SettlementCostInfoEntity cost = new SettlementCostInfoEntity();
        cost.setCostCode(PolicySettlementUtils.createCodeLastNumber("CC"));
        //事件编号
        cost.setEventSourceCode(eventJob.getPushEventCode());
        //事件信息
        if (Objects.nonNull(eventType)) {
            cost.setSettlementEventCode(eventType.getEventCode());
            //八大基础佣金事件编码时与SettlementEventCode一直
            cost.setInitialEventCode(eventType.getEventCode());
            cost.setSettlementEventDesc(eventType.getEventDesc());
        }
        //科目信息
        cost.setSettlementSubjectCode(subjectEnum.getCode());
        cost.setSettlementSubjectName(subjectEnum.getName());
        cost.setSettlementGenerateType(1);
        cost.setContractCode(policyInfo.getContractCode());
        cost.setInsuranceType(insuranceType);
        cost.setPolicyNo(policyInfo.getContractBaseInfo().getPolicyNo());
        if(StringUtils.isNotBlank(policyInfo.getContractBaseInfo().getThirdPolicyNo())){
            cost.setCompanyPolicyNo(policyInfo.getContractBaseInfo().getThirdPolicyNo());
        }else {
            cost.setCompanyPolicyNo(cost.getPolicyNo());
        }
        //设置佣金类型
        setCommissionTypeByEventType(eventType, cost);
        //设置四级分销与整村推进信息
        builderCostActivityInfos(policyInfo,cost);
        //回访结果
        cost.setRevisitResult(policyInfo.getContractExtendInfo().getRevisitResult());



        return cost;
    }

    /**
     * 1.四级分销单:表中ep_policy_activity存在记录，且type为200
     * 2.整村推进保单 表中ep_policy_activity存在记录，且type为100，且activity_code有值。
     * 3.活动保单 表中ep_policy_activity存在记录，且type为100，且activity_code有值，且product_activity_code有值。(活动保单属于整村推荐保单中的一中)
     * @param policyInfo
     * @param entity
     */
    protected void builderCostActivityInfos(EpContractInfoVo policyInfo,SettlementCostInfoEntity entity){
        entity.setDistributionFlag(0);
        entity.setRuralProxyFlag(0);
        if(CollectionUtils.isNotEmpty(policyInfo.getEpPolicyActivitys())){
            for(EpPolicyActivityVo activityVo : policyInfo.getEpPolicyActivitys()){
                //四级分销
                if(Objects.equals(EpPolicyActivityTypeEnum.FOURTH_LEVEL_DISTRIBUTION.getCode(),activityVo.getType())){
                    entity.setDistributionFlag(1);
                }
                //整村推荐
                if(Objects.equals(EpPolicyActivityTypeEnum.WHOLE_VILLAGE_PROMOTION.getCode(),activityVo.getType())
                        && StringUtils.isNotBlank(activityVo.getActivityCode())
                        && StringUtils.isNotBlank(activityVo.getProductActivityCode())){
                    entity.setRuralProxyFlag(1);
                }

            }
        }
    }

    protected void setCommissionTypeByEventType(SettlementEventTypeEnum eventType, SettlementCostInfoEntity cost) {
        switch (eventType) {
            case PERSONAL_NEW_POLICY:
            case RENEWAL_POLICY:
            case GROUP_NEW_POLICY:
            case POLICY_PRODUCT_CHANGE:
                cost.setCommissionType(CommissionTypeEnum.COMMON.getCode());
                break;
            case RENEWAL_TERM_POLICY:
                cost.setCommissionType(CommissionTypeEnum.RENEWAL.getCode());
                break;
            case GROUP_ADD_OR_SUBTRACT:
                cost.setCommissionType(CommissionTypeEnum.CORRECT_INC_OR_DEC.getCode());
                break;
            case STANDARD_SURRENDER:
            case HESITATE_SURRENDER:
            case TERMINATION_PRODUCT:
            case PROTOCOL_TERMINATION:
                cost.setCommissionType(CommissionTypeEnum.CORRECT_SURRENDER.getCode());
                break;
            case VEHICLE_PREMIUM_INFO_CHANGE:
            case POLICY_SUPPLEMENT_PREMIUM:
                cost.setCommissionType(CommissionTypeEnum.CORRECT_SPECIAL_DEDUCTION.getCode());

                break;
            default:
                log.warn("该佣金类型未知！！！");
        }
    }

    protected SettlementCostInfoEntity builderCompulsoryInsuranceCostInfo(SettlementEventTypeEnum eventType,SettlementCostInfoEntity bean,CostBasicCommissionConfigDto config){
        SettlementCostInfoEntity costInfo = cloneCostInfoEntity(bean);
        costInfo.setSettlementSubjectCode(CostSubjectEnum.VEHICLE_VESSEL_TAX.getCode());
        costInfo.setSettlementSubjectName(CostSubjectEnum.VEHICLE_VESSEL_TAX.getName());
        if(Objects.nonNull(eventType)) {
            costInfo.setSettlementEventCode(eventType.getEventCode());
            //八大基础佣金事件编码时与SettlementEventCode一直
            costInfo.setInitialEventCode(eventType.getEventCode());
            costInfo.setSettlementEventDesc(eventType.getEventDesc());
        }
        costInfo.setCostType(CostTypeEnum.VEHICLE_VESSEL_TAX.getCode());
        if(ProductStatusEnum.getSurrenderStatusEnums().contains(ProductStatusEnum.getPolicyContractStatusEnum(costInfo.getProductStatus()))){
            costInfo.setPremium(config.getVehicleVesselTax().negate());
            costInfo.setBusinessPremium(config.getVehicleVesselTax().negate());
        }else{
            costInfo.setPremium(config.getVehicleVesselTax());
            costInfo.setBusinessPremium(config.getVehicleVesselTax());
        }

        costInfo.setCostRate(config.getVehicleVesselTaxRate());
        //计算基础佣金
        builderBasicCommission(costInfo.getSettlementInstitution(),
                costInfo.getSettlementInstitutionName(),config.getVehicleVesselTax(),
                config.getVehicleVesselTaxRate(),config.getPremCode(),CostTypeEnum.VEHICLE_VESSEL_TAX,costInfo);
        //折算保费
        builderDiscountPremium(config.getPolicyNo(),config.getPolicyProductTypeEnum().getCode(),costInfo);
        return costInfo;
    }

    /**
     * 车险交强险退保处理
     * @param old
     * @return
     */
    protected SettlementCostInfoEntity builderCompulsoryInsuranceSurrenderCostInfo(SettlementEventTypeEnum eventType,SettlementCostInfoEntity old){
        SettlementCostInfoEntity bean = new SettlementCostInfoEntity();
        BeanUtils.copyProperties(old,bean);
        bean.setId(null);
        bean.setCostCode(PolicySettlementUtils.createCodeLastNumber("CC"));
        if(Objects.nonNull(eventType)) {
            bean.setSettlementEventCode(eventType.getEventCode());
            //八大基础佣金事件编码时与SettlementEventCode一直
            bean.setInitialEventCode(eventType.getEventCode());
            bean.setSettlementEventDesc(eventType.getEventDesc());
        }

        bean.setPremium(old.getPremium().negate());
        bean.setBusinessPremium(old.getBusinessPremium().negate());
        bean.setCostAmount(old.getCostAmount().negate());
        bean.setGrantAmount(old.getGrantAmount().negate());
        bean.setDiscountPremium(old.getDiscountPremium().negate());
        bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
        if(Objects.equals(old.getConfirmStatus(),ConfirmStatusEnum.CONFIRMED.getCode())
                || Objects.equals(old.getImmutableFlag(),1)){
            bean.setImmutableFlag(1);
        }

        return bean;
    }

    protected void setInsuredInfo(EpInsuredInfoVo insured, SettlementCostInfoEntity bean) {
        bean.setInsuredCode(insured.getInsuredCode());
        bean.setInsuredBirthday(insured.getInsuredBirthday());
        bean.setInsuredGender(insured.getInsuredGender());
        bean.setInsuredIdCard(insured.getInsuredIdCard());
        bean.setInsuredMobile(insured.getInsuredMobile());
        bean.setInsuredName(insured.getInsuredName());
    }
    protected void setInsuredInfo(EpPreserveSurrenderInsuredDto insured, SettlementCostInfoEntity bean) {
        bean.setInsuredCode(insured.getInsuredCode());
        bean.setInsuredBirthday(insured.getInsuredBirthday());
        bean.setInsuredGender(insured.getInsuredGender());
        bean.setInsuredIdCard(insured.getInsuredIdCard());
        bean.setInsuredMobile(insured.getInsuredMobile());
        bean.setInsuredName(insured.getInsuredName());
    }

    /**
     * 判断是否交强险
     * @param policyProductType 产品类型为车险
     * @param productBase       ProductBase中Level3Code为PRODUCT:LEVEL2_CAT:CX:3:1
     * @return
     */
    protected boolean isCompulsoryInsurance(String policyProductType, ProductBase productBase){
        return Objects.equals(PolicyProductTypeEnum.VEHICLE.getCode(),policyProductType)
                &&  Objects.equals(productBase.getLevel3Code(),COMPULSORY_INSURANCE);
    }


    /**
     * 创建折标保费
     * @param policyNo
     * @param policyProductType
     * @param cost
     */
    public void builderDiscountPremium(String policyNo,String policyProductType,SettlementCostInfoEntity cost){
        PolicyProductTypeEnum policyProductTypeEnum = PolicyProductTypeEnum.getProdTypeEnum(policyProductType);

        BigDecimal discountPremium = ReconcileBaseHelper.calcDiscountPremium(policyNo, cost.getProductCode(), cost.getInsuredPolicyAge(),
                policyProductTypeEnum, cost.getLongShortFlag(), cost.getBusinessPremium(), cost.getPeriodType(), cost.getPaymentPeriodType(),
                cost.getPaymentPeriod());

        cost.setDiscountPremium(discountPremium);
    }

    /**
     * 计算基础佣金
     * @param policyNo
     * @param config
     * @param bean
     */
    protected void calcBasicCommission(String policyNo,  CostBasicCommissionConfigDto config, SettlementCostInfoEntity bean) {
        calcBasicCommission(policyNo,null,config,bean);
    }

    /**
     * 计算基础佣金
     * @param policyNo
     * @param config
     * @param bean
     */
    protected void calcBasicCommission(String policyNo,String endorsementNo,  CostBasicCommissionConfigDto config, SettlementCostInfoEntity bean) {
        bean.setSingleProposeFlag(config.getIsCustomYearRate());
        builderBasicCommission(config.getSettlementCompanyCode(),config.getSettlementCompanyName(),
                bean.getPremium(), config.getYearRate(), config.getPremCode(), CostTypeEnum.BASIC, bean);


        if (bean.getCostAmount() == null) {
            log.warn("支出端-计算基础佣金,计算出的支出佣金为空,佣金配置未找到,保单号={},批单号={}", policyNo,endorsementNo);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-计算基础佣金-保单号={},计算出的支出佣金为空,佣金配置未找到", policyNo)));
        }
    }

    /**
     * 基础佣金
     *
     * @param settlementInstitution
     * @param premium
     * @param rate
     * @param costType
     * @param bean
     */
    protected void builderBasicCommission(String settlementInstitution, String settlementInstitutionName, BigDecimal premium, BigDecimal rate, String configKey, CostTypeEnum costType, SettlementCostInfoEntity bean) {

        bean.setSettlementInstitution(settlementInstitution);
        bean.setSettlementInstitutionName(settlementInstitutionName);
        bean.setCostType(costType.getCode());
        //todo 活动编码保单中心暂时未返回
        //bean.setCostActivityCode();
        bean.setCostRate(rate);
        //默认不是联合展业,联合展业的时候，后面在设置佣金归属人时候再设置具体分佣比例
        bean.setCostDivideRate(BigDecimal.ONE);
        bean.setCostActualRate(rate);
        BigDecimal commissionAmount = BigDecimalUtils.mul(premium.doubleValue(), rate.doubleValue());
        bean.setCostAmount(commissionAmount);
        //8大事件基础佣金默认100%发放
        bean.setGrantRate(new BigDecimal("100"));
        bean.setGrantAmount(commissionAmount);
        //配置key
        bean.setCostConfigKey(configKey);
    }

    /**
     * 佣金记录险种信息构建
     * @param policyInfo
     * @param productMap
     * @param product
     * @param bean
     * @param renewalYear
     * @param renewalPeriod
     */
    protected void builderCostProductInfo(EpContractInfoVo policyInfo, Map<String, ProductBase> productMap, EpProductInfoVo product, SettlementCostInfoEntity bean, Integer renewalYear, Integer renewalPeriod) {
        // 协议险种信息 + 是否可对账 todo 是否关系是否为非小鲸险种....
        ProtocolInsuranceProductInfoOut insuranceProductInfo = ProtocolBaseHelper.queryProtocolInsuranceProductInfoByProductCode(product.getProductCode(),0);
        if (insuranceProductInfo != null) {
            // 协议产品信息
            bean.setProtocolProductCode(insuranceProductInfo.getInsuranceProductCode());
            bean.setProtocolProductName(insuranceProductInfo.getInsuranceProductName());
        }
        //险种分类信息
        if (productMap.containsKey(product.getProductCode())) {
            /*bean.setProductGroup(productMap.get(product.getProductCode()).getProductGroup());
            bean.setLevel2Code(productMap.get(product.getProductCode()).getLevel2Code());
            bean.setLevel3Code(productMap.get(product.getProductCode()).getLevel3Code());*/
            ProductBase productBase = productMap.get(product.getProductCode());
            bean.setProductType(productBase.getProductType());
            bean.setProductGroup(productBase.getProductGroup());
            bean.setLevel2Code(productBase.getLevel2Code());
            bean.setLevel3Code(productBase.getLevel3Code());
            bean.setLongShortFlag(productBase.getLongShortFlag());
            bean.setProductChannel(productBase.getProductChannel());
        } else {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + product.getProductCode()));
        }
        bean.setProductCode(product.getProductCode());
        bean.setProductName(product.getProductName());
        bean.setProductStatus(product.getProductStatus());

        //对于个险、团险、续保的保单不管状态，产品状态强转为生效状态，（因为没有快照，状态是实时状态）
        if(Objects.equals(bean.getInitialEventCode(), SettlementEventTypeEnum.RENEWAL_POLICY.getEventCode())
                || Objects.equals(bean.getInitialEventCode(),SettlementEventTypeEnum.PERSONAL_NEW_POLICY.getEventCode())
                || Objects.equals(bean.getInitialEventCode(),SettlementEventTypeEnum.GROUP_NEW_POLICY.getEventCode())){
            bean.setProductStatus(ProductStatusEnum.ACTIVE.getCode());
        }
        bean.setPlanCode(product.getPlanCode());
        bean.setPlanName(product.getPlanName());
        bean.setMainInsurance(product.getMainInsurance());
        bean.setAdditionalRisksType(product.getAdditionalRisksType());
        //险种的生效事件必须去ep_policy_product_insured_map表或者ep_policy_product_info表中生效时间
        //bean.setEffectiveDate(policyInfo.getContractExtendInfo().getEnforceTime());
        bean.setEffectiveDate(product.getEffectiveDate());

        bean.setEndDate(PolicySettlementUtils.getInsuredEndDate(bean.getEffectiveDate(), product.getInsuredPeriod(), product.getInsuredPeriodType()));
        bean.setRenewalYear(renewalYear);
        bean.setRenewalPeriod(renewalPeriod);
        bean.setCoverage(product.getCoverage());
        bean.setCoverageUnit(product.getCoverageUnit());
        bean.setCoverageUnitName(product.getCoverageUnitName());
        bean.setInsuredPeriodType(product.getInsuredPeriodType());
        bean.setInsuredPeriod(product.getInsuredPeriod());
        bean.setPeriodType(product.getPeriodType());
        bean.setPaymentPeriodType(product.getPaymentPeriodType());
        bean.setPaymentPeriod(product.getPaymentPeriod());
        bean.setDrawAge(product.getAnnDrawAge());
        bean.setAgriculturalMachineryFlag(productMap.get(product.getProductCode()).getAgriculturalMachineryFlag());
    }

    /**
     * 佣金记录险种信息构建
     * @param policyInfo
     * @param productMap
     * @param product
     * @param bean
     * @param renewalYear
     * @param renewalPeriod
     */
    protected void builderCostProductInfo(EpContractInfoVo policyInfo, Map<String, ProductBase> productMap, EpPreserveProductDto product, SettlementCostInfoEntity bean, Integer renewalYear, Integer renewalPeriod) {
        // 协议险种信息 + 是否可对账 todo 是否关系是否为非小鲸险种....
        ProtocolInsuranceProductInfoOut insuranceProductInfo = ProtocolBaseHelper.queryProtocolInsuranceProductInfoByProductCode(product.getProductCode(),0);
        if (insuranceProductInfo != null) {
            // 协议产品信息
            bean.setProtocolProductCode(insuranceProductInfo.getInsuranceProductCode());
            bean.setProtocolProductName(insuranceProductInfo.getInsuranceProductName());
        }
        //险种分类信息
        if (productMap.containsKey(product.getProductCode())) {
            /*bean.setProductGroup(productMap.get(product.getProductCode()).getProductGroup());
            bean.setLevel2Code(productMap.get(product.getProductCode()).getLevel2Code());
            bean.setLevel3Code(productMap.get(product.getProductCode()).getLevel3Code());*/
            ProductBase productBase = productMap.get(product.getProductCode());
            bean.setProductType(productBase.getProductType());
            bean.setProductGroup(productBase.getProductGroup());
            bean.setLevel2Code(productBase.getLevel2Code());
            bean.setLevel3Code(productBase.getLevel3Code());
            bean.setLongShortFlag(productBase.getLongShortFlag());
            bean.setProductChannel(productBase.getProductChannel());
        } else {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + product.getProductCode()));
        }
        bean.setProductCode(product.getProductCode());
        bean.setProductName(product.getProductName());
        bean.setProductStatus(product.getProductStatus());
        //对于个险、团险、续保的保单不管状态，产品状态强转为生效状态，（因为没有快照，状态是实时状态）
        if(Objects.equals(bean.getInitialEventCode(), SettlementEventTypeEnum.RENEWAL_POLICY.getEventCode())
                || Objects.equals(bean.getInitialEventCode(),SettlementEventTypeEnum.PERSONAL_NEW_POLICY.getEventCode())
                || Objects.equals(bean.getInitialEventCode(),SettlementEventTypeEnum.GROUP_NEW_POLICY.getEventCode())){
            bean.setProductStatus(ProductStatusEnum.ACTIVE.getCode());
        }
        bean.setPlanCode(product.getPlanCode());
        bean.setPlanName(product.getPlanName());
        bean.setMainInsurance(product.getMainInsurance());
        bean.setAdditionalRisksType(product.getAdditionalRisksType());

        bean.setEffectiveDate(product.getEffectiveDate());

        bean.setEndDate(PolicySettlementUtils.getInsuredEndDate(bean.getEffectiveDate(), product.getInsuredPeriod(), product.getInsuredPeriodType()));
        bean.setRenewalYear(renewalYear);
        bean.setRenewalPeriod(renewalPeriod);
        //bean.setCoverage(product.getCoverage());
        //bean.setCoverageUnit(product.getCoverageUnit());
        //bean.setCoverageUnitName(product.getCoverageUnitName());
        bean.setInsuredPeriodType(product.getInsuredPeriodType());
        bean.setInsuredPeriod(product.getInsuredPeriod());
        bean.setPeriodType(product.getPeriodType());
        bean.setPaymentPeriodType(product.getPaymentPeriodType());
        bean.setPaymentPeriod(product.getPaymentPeriod());
        bean.setDrawAge(product.getAnnDrawAge());
        bean.setAgriculturalMachineryFlag(productMap.get(product.getProductCode()).getAgriculturalMachineryFlag());
    }


    /**
     * 保全信息补全
     * @param preservationDetail
     * @param surrenderPremium
     * @param bean
     */
    protected void builderPreservationInfo(PolicyPreservationDetailDto preservationDetail, BigDecimal surrenderPremium, SettlementCostInfoEntity bean) {
        bean.setPreservationCode(preservationDetail.getPreservationCode());
        bean.setEndorsementNo(preservationDetail.getEndorsementNo());

        bean.setPreservationType(preservationDetail.getPreservationType());
        bean.setPreservationProject(preservationDetail.getPreservationProject());
        bean.setPreservationPeriod(preservationDetail.getRenewalTermPeriod());
        bean.setPreservationEffectTime(preservationDetail.getPreservationEffectTime());
        if (PreservationProjectEnum.isSurrender(preservationDetail.getPreservationProject())) {
            bean.setSurrenderTime(preservationDetail.getPreservationEffectTime());
            bean.setSurrenderAmount(surrenderPremium);
            bean.setHesitateSurrender(Objects.equals(preservationDetail.getPreservationProject(), PreservationProjectEnum.HESITATION_SURRENDER.getCode()) ? 1 : 0);
        }
    }

    protected List<SettlementCostInfoEntity> builderInsuredCostInfoByOldCostInfo(BaseSurrenderDto param,
                                                       EpPreserveSurrenderInsuredDto insured,
                                                       EpProductInfoVo product,
                                                       List<SettlementCostInfoEntity> oldCostInfoList,
                                                       BigDecimal calculatePremium,
                                                       BigDecimal surrenderPremium, Integer renewalPeriod){
        log.info("支出端-基础佣金--根据历史佣金记录计算【短险】退保佣金");
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();
        SettlementCostInfoEntity bean =null;
        for (SettlementCostInfoEntity old : oldCostInfoList) {
            //车船税特殊处理
            if(Objects.equals(old.getSettlementSubjectCode(),CostSubjectEnum.VEHICLE_VESSEL_TAX.getCode())){
                bean = builderCompulsoryInsuranceSurrenderCostInfo(param.getEventType(),old);
                //记账时间处理
                bean.setSettlementTime(getBasicSettlementTime(param.getEventType(), param.getPolicyInfo()));
                bean.setSettlementDate(bean.getSettlementTime());
                bean.setBusinessAccountTime(getBusinessAccountTime(param.getEventType(), param.getPolicyInfo(), param.getPreservationDetail()));
                costInfoEntities.add(bean);
                continue;
            }
            bean = initSettlementCostInfo(param.getEventJob(), param.getSubjectEnum(), param.getEventType(), param.getPolicyInfo(), param.getInsuranceType());
            //记录佣金匹配时间，便于冲正流程中获取
            bean.setCostConfigMatchTime(old.getCostConfigMatchTime());
            //记账时间处理
            bean.setSettlementTime(getBasicSettlementTime(param.getEventType(), param.getPolicyInfo()));
            bean.setSettlementDate(bean.getSettlementTime());
            //设置保费,退保保费为在结算表里为负数、和实际用于计算的保费
            bean.setPremium(calculatePremium.abs().negate());
            bean.setProductPremium(product.getPremium());
            bean.setSurrenderAmount(surrenderPremium.abs().negate());
            bean.setBusinessPremium(surrenderPremium.abs().negate());
            bean.setBusinessAccountTime(getBusinessAccountTime(param.getEventType(), param.getPolicyInfo(), param.getPreservationDetail()));
            //佣金信息
            builderBasicCommission(old.getSettlementInstitution(),old.getSettlementInstitutionName(), bean.getPremium(), old.getCostRate(), old.getCostConfigKey(), CostTypeEnum.BASIC, bean);
            bean.setSingleProposeFlag(old.getSingleProposeFlag());
            bean.setGrantRate(old.getGrantRate());
            bean.setGrantAmount(PolicySettlementUtils.calcAmtByPercent(bean.getCostAmount(),old.getGrantRate(),2));
            //初始化确认信息
            cleanConfirmInfo(bean);

            //被保人信息
            if(Objects.nonNull(insured)) {
                setInsuredInfo(insured, bean);
            }
            //险种信息
            builderCostProductInfo(param.getPolicyInfo(), param.getProductMap(), product, bean, renewalPeriod, renewalPeriod);
            //保全信息
            builderPreservationInfo(param.getPreservationDetail(), surrenderPremium.abs().negate(), bean);
            //归属人
            settlementCostOwnerService.builderOwnerInfoByOldCostInfo(old, bean);
            //折算保费
            builderDiscountPremium(param.getPolicyNo(), param.getPolicyInfo().getPolicyProductType(), bean);
            //佣金配置是否可变(已确认 或者 被退保记录就是不可变（手工冲正）)
            if(Objects.equals(old.getConfirmStatus(),ConfirmStatusEnum.CONFIRMED.getCode())
                    || Objects.equals(old.getImmutableFlag(),1)){
                bean.setImmutableFlag(1);
            }

            costInfoEntities.add(bean);
        }
        return costInfoEntities;
    }

    /**
     * 根据佣金配置创建佣金信息
     * 注意：团险时归属人信息已在在EpPreserveSurrenderInsuredDto返回，个险未返回，原因是：保单中心未统一导致
     * @param param
     * @param insured
     * @param product
     * @param input
     * @param configList
     * @param calculatePremium  计算用退保保费(与业务场景有关)
     * @param surrenderPremium  实际退保保费
     * @param renewalPeriod
     * @param costInfoEntities
     * @param isSetOwnerInfo
     */
    protected void builderInsuredCostInfoByCostRateConfig(BaseSurrenderDto param,
                                                          EpPreserveSurrenderInsuredDto insured,
                                                          EpProductInfoVo product,
                                                          PolicyProductPremInput input,
                                                          List<CostBasicCommissionConfigDto> configList,
                                                          BigDecimal calculatePremium,
                                                          BigDecimal surrenderPremium,
                                                          Integer renewalPeriod,
                                                          List<SettlementCostInfoEntity> costInfoEntities,
                                                          boolean isSetOwnerInfo){
        for (CostBasicCommissionConfigDto config : configList) {
            //初始化，生成科目信息
            SettlementCostInfoEntity bean = initSettlementCostInfo(param.getEventJob(), param.getSubjectEnum(), param.getEventType(), param.getPolicyInfo(), param.getInsuranceType());
            //记录佣金匹配时间，便于冲正流程中获取
            bean.setCostConfigMatchTime(input.getApprovedTime());
            //记账时间处理
            bean.setSettlementTime(getBasicSettlementTime(param.getEventType(), param.getPolicyInfo()));
            bean.setSettlementDate(bean.getSettlementTime());
            /*******设置相关保费 begin********/
            //险种保费
            bean.setProductPremium(product.getPremium());
            //设置实际计算用的保费
            bean.setPremium(calculatePremium.abs().negate());
            //设置退保保费，退保保费为在结算表里为负数
            bean.setSurrenderAmount(surrenderPremium.abs().negate());
            bean.setBusinessPremium(surrenderPremium.abs().negate());
            /********** end 设置保费 ********************/
            bean.setBusinessAccountTime(getBusinessAccountTime(param.getEventType(), param.getPolicyInfo(), param.getPreservationDetail()));
            //佣金信息
            calcBasicCommission(param.getPolicyNo(), config, bean);
            //calcBasicCommission(param.getPolicyNo(), null, product, config, bean);
            //初始化确认信息
            bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
            //被保人信息,保单中心存在有被保人保单和无被保人保单的情况
            if(Objects.nonNull(insured)) {
                setInsuredInfo(insured, bean);
            }
            //险种信息
            builderCostProductInfo(param.getPolicyInfo(), param.getProductMap(), product, bean, renewalPeriod, renewalPeriod);
            //保全信息
            builderPreservationInfo(param.getPreservationDetail(), surrenderPremium.abs().negate(), bean);

            //折算保费
            builderDiscountPremium(param.getPolicyNo(), param.getPolicyInfo().getPolicyProductType(), bean);
            //团险设置佣金归属人
            if(isSetOwnerInfo){
                settlementCostOwnerService.builderGroupPolicySurrenderOwnerInfoByInsured(insured.getChannelCode(),insured,bean);
            }

            costInfoEntities.add(bean);


            //是否存在车船税
            if(config.isVehicleInsurance() && Objects.equals(config.getExistVehicleVesselTax(),Boolean.TRUE)){
                //是否交强险
                if(isCompulsoryInsurance(param.getPolicyInfo().getPolicyProductType(),param.getProductMap().get(product.getProductCode()))){
                    costInfoEntities.add(builderCompulsoryInsuranceCostInfo(param.getEventType(),bean,config));
                }
            }

        }
    }

    public Date getBusinessAccountTime(SettlementEventTypeEnum eventType, EpContractInfoVo policyInfo, Object obj) {

        if (eventType == null) {
            return new Date();
        }
        switch (eventType) {
            case PERSONAL_NEW_POLICY:
            case GROUP_NEW_POLICY:
            case RENEWAL_POLICY:{
                if(LIST_MANUAL_IMPORT.contains(policyInfo.getPolicySource())){
                    //todo 下线导入的单子需要取保单创建时间
                    return getCorrectionBusinessAccountTime(policyInfo.getContractBaseInfo().getOrderTime());
                }else {

                    //return Objects.equals(policyInfo.getChannelInfo().getChannelCode(), Constant.ZHNX_CHANNEL_CODE) ? policyInfo.getContractExtendInfo().getApprovedTime() : policyInfo.getContractExtendInfo().getOrderTime();
                    //新契约-交单时间，对应农保系统的新契约同步进系统的时间（应该即支付时间）；
                    return getCorrectionBusinessAccountTime(policyInfo.getContractExtendInfo().getOrderTime());
                }
            }
            case RENEWAL_TERM_POLICY: {
//                PolicyRenewalTermDto termDto = (PolicyRenewalTermDto) obj;
//                return Objects.equals(policyInfo.getChannelInfo().getChannelCode(), Constant.ZHNX_CHANNEL_CODE)?
//                        termDto.getPaymentSubmitTime():termDto.getPaymentTime();
                PolicyRenewalTermDto termDto = (PolicyRenewalTermDto) obj;
                Date date = termDto.getPaymentSubmitTime();
                Date paymentTime = termDto.getPaymentTime();
                //临时解决2024-03-26 14:59:53历史 报文中没有getPaymentSubmitTime的数据
                if(date == null){
                    if(paymentTime.before(DateUtil.parseDateTime("2024-03-26 14:59:53"))){
                        date = paymentTime;
                    }else {
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                                "支出端-长险续期PaymentSubmitTime为空，保单号=" + termDto.getPolicyNo() + "期数=" + termDto.getPeriod()));
                    }
                }
                return getCorrectionBusinessAccountTime(Objects.equals(policyInfo.getChannelInfo().getChannelCode(), Constant.ZHNX_CHANNEL_CODE)? date:termDto.getPaymentTime());


            }
            case STANDARD_SURRENDER:
            case HESITATE_SURRENDER:
            case PROTOCOL_TERMINATION:
            case GROUP_ADD_OR_SUBTRACT:
            case VEHICLE_PREMIUM_INFO_CHANGE:
            case TERMINATION_PRODUCT:{

//                PolicyPreservationDetailDto preservationDetailDto = (PolicyPreservationDetailDto)obj;
//                //return preservationDetailDto.getPreservationEffectTime();
//                return Objects.equals(policyInfo.getChannelInfo().getChannelCode(), Constant.ZHNX_CHANNEL_CODE)?
//                        preservationDetailDto.getInputTime():preservationDetailDto.getPreservationEffectTime();
                PolicyPreservationDetailDto preservationDetailDto = (PolicyPreservationDetailDto)obj;
                log.info("保全{}记录的入库时间为{}", preservationDetailDto.getPreservationCode(),
                        preservationDetailDto.getInputTime());
                if(preservationDetailDto.getInputTime()==null){
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                            "保全{}记录的入库时间为空,保全编号=" + preservationDetailDto.getPreservationCode()));
                }
                //return preservationDetailDto.getPreservationEffectTime();
                return getCorrectionBusinessAccountTime(Objects.equals(policyInfo.getChannelInfo().getChannelCode(), Constant.ZHNX_CHANNEL_CODE)?
                        preservationDetailDto.getInputTime():preservationDetailDto.getPreservationEffectTime());

            }

            default:
                return new Date();
        }
    }
    public Date getBasicSettlementTime(SettlementEventTypeEnum eventType, EpContractInfoVo policyInfo) {
        return new Date();

    }
    public static Date getCorrectionBusinessAccountTime(Date oldBusinessAccountTime){
        if(oldBusinessAccountTime == null){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("冲正源数据的业务记账日期为空")));
        }

        Date now = DateUtil.date();
        if(oldBusinessAccountTime.after(now)){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("冲正源数据的业务记账日期{}晚于当前时间{}",oldBusinessAccountTime,now)));
        }
        if(DateUtil.isSameMonth(now,oldBusinessAccountTime)){
            return oldBusinessAccountTime;
        }
        Date thisMonth = DateUtil.beginOfMonth(now);
        Date oldMonth = DateUtil.beginOfMonth(oldBusinessAccountTime);
        if(DateUtil.betweenMonth(oldMonth,thisMonth,false)==1){
            Date settlementDate = DateUtil.offsetDay(thisMonth,autoCostSettlementDay-1);
            if(now.after(settlementDate)){
                return now;
            }else{
                return oldBusinessAccountTime;
            }
        }else{
            return now;
        }

    }

    /**
     * 增加员的保全信息补全
     * @param preservationDetail
     * @param surrendered
     * @param surrenderPremium
     * @param bean
     */
    protected void builderGroupAddOrSubtractPreservationInfo(PolicyPreservationDetailDto preservationDetail, Integer surrendered,BigDecimal surrenderPremium, SettlementCostInfoEntity bean){
        builderPreservationInfo(preservationDetail,surrenderPremium,bean);
        if(Objects.equals(surrendered,1) && preservationDetail.getPreservationSubtractEffectTime()!=null){
            bean.setPreservationEffectTime(preservationDetail.getPreservationSubtractEffectTime());
        }
    }

    protected void cleanConfirmInfo(SettlementCostInfoEntity bean){
        bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
        bean.setConfirmUser(null);
        bean.setConfirmTime(null);
        bean.setConfirmGrantTime(null);
        bean.setDocumentCode(null);
        bean.setAutoCostCode(null);
        bean.setCostSettlementCycle(null);
    }
}
