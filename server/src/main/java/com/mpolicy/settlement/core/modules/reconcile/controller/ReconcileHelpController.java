package com.mpolicy.settlement.core.modules.reconcile.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.settlement.core.common.commission.dto.ValidateCostConfigParam;
import com.mpolicy.settlement.core.common.commission.dto.ValidateResultDto;
import com.mpolicy.settlement.core.common.reconcile.RectificationOneVo;
import com.mpolicy.settlement.core.common.reconcile.SettlementStatusInput;
import com.mpolicy.settlement.core.common.reconcile.SettlementStatusOut;
import com.mpolicy.settlement.core.modules.reconcile.dto.rule.ReconcileCompanyInfo;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.event.factory.SettlementEventFactory;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.SettlementEventHandler;
import com.mpolicy.settlement.core.modules.reconcile.helper.ReconcileBaseHelper;
import com.mpolicy.settlement.core.modules.reconcile.helper.ReconcileRuleHelper;
import com.mpolicy.settlement.core.modules.reconcile.service.*;
import com.mpolicy.settlement.core.modules.reconcile.vo.*;
import com.mpolicy.web.common.annotation.PassToken;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 结算帮助服务 controller
 *
 * <AUTHOR>
 * @since 2022-11-18 12:30
 */
@Slf4j
@Validated
@RestController
@Api(tags = "结算帮助服务")
@RequestMapping("/settlement/reconcile/help")
public class ReconcileHelpController {

    @Autowired
    @Qualifier("simpleTaskExecutor")
    private AsyncTaskExecutor taskExecutor;

    /**
     * 协议服务
     */
    @Autowired
    private SettlementReconcileService settlementReconcileService;

    @Autowired
    private SettlementReconcileInfoService settlementReconcileInfoService;

    @Autowired
    private SettlementEventJobService settlementEventJobService;

    @Autowired
    private SettlementReconcileCompanySubjectService settlementReconcileCompanySubjectService;

    @Autowired
    private SettlementPolicyInfoService settlementPolicyInfoService;

    @Autowired
    private ReconcileHelpService reconcileHelpService;

    @Autowired
    private SettlementBasicCommValidService commValidService;

    /**
     * 手动触发生成账单
     */
    @ApiOperation(value = "手动触发生成账单", notes = "手动触发生成账单")
    @PostMapping("/create_reconcile_day")
    @PassToken
    public Result<String> createReconcile(@RequestParam @ApiParam(name = "day", value = "账单生成日") int day) {
        // 1 获取当前日期的：日
        int lastDay = DateUtil.thisMonthEnum().getLastDay(DateUtil.isLeapYear(DateUtil.thisYear()));
        if (day == lastDay) {
            log.info("当前day为本月最后一天，执行强制修改为【99】");
            // 99的配置对应协协议对账生成勾选的 最后日
            day = 99;
        }
        // 2 获取【保司配置规则生成为账单日=day的保司对账单】
        List<ReconcileCompanyInfo> reconcileCompanyInfos = ReconcileRuleHelper.queryReconcileCompanyInfoList(day);
        if (CollUtil.isNotEmpty(reconcileCompanyInfos)) {
            log.info("需要生成账单数量为={}", reconcileCompanyInfos.size());
            for (ReconcileCompanyInfo reconcileCompanyInfo : reconcileCompanyInfos) {
                // 执行生成对账单
                String reconcileCode = settlementReconcileService.createReconcile(reconcileCompanyInfo);
                log.info("生成账单的保司完成 对账单号={}", reconcileCode);
            }
        } else {
            log.info("没有需要生成账单的保司");
        }
        return Result.success();
    }

    /**
     * [按月]手动触发生成账单
     */
    @ApiOperation(value = "[按月]手动触发生成账单", notes = "[按月]手动触发生成账单")
    @PostMapping("/create_reconcile_month_day")
    @PassToken
    public Result<String> createReconcilePolicyMonth(
            @RequestParam @ApiParam(name = "policyMonth", value = "账单生成日") int policyMonth,
            @RequestParam @ApiParam(name = "day", value = "账单生成日") int day) {
        // 1 获取当前日期的：日
        int lastDay = DateUtil.thisMonthEnum().getLastDay(DateUtil.isLeapYear(DateUtil.thisYear()));
        if (day == lastDay) {
            log.info("[按月]当前day为本月最后一天，执行强制修改为【99】");
            // 99的配置对应协协议对账生成勾选的 最后日
            day = 99;
        }

        // 2 获取【保司配置规则生成为账单日=day的保司对账单】
        List<ReconcileCompanyInfo> reconcileCompanyInfos = ReconcileRuleHelper.queryReconcileCompanyInfoList(day);
        if (CollUtil.isNotEmpty(reconcileCompanyInfos)) {
            log.info("[按月]需要生成账单数量为={}", reconcileCompanyInfos.size());
            for (ReconcileCompanyInfo reconcileCompanyInfo : reconcileCompanyInfos) {
                // 执行生成对账单 这个方法不用了 禁用!!!!!!!

                // String reconcileCode = settlementReconcileService.createReconcile(policyMonth, reconcileCompanyInfo);
                //log.info("[按月]生成账单的保司完成 对账单号={}", reconcileCode);
            }
        } else {
            log.info("[按月]没有需要生成账单的保司");
        }
        return Result.success();
    }

    /**
     * 团险增减员保全生效时间
     */
    @ApiOperation(value = "团险增减员保全生效时间", notes = "团险增减员保全生效时间")
    @PostMapping("/group_add_or_subtract/preservation_effect_time")
    @PassToken
    public Result<String> preservationEffectTime() {

        List<SettlementPolicyInfoEntity> list = settlementPolicyInfoService.lambdaQuery()
                .eq(SettlementPolicyInfoEntity::getSettlementEventCode, "settlement.global.policy.group_add_or_subtract")
                .list();

        log.info("团险增员纪录信息，{} 条", list.size());

        list.forEach(bean -> {
            SettlementEventJobEntity eventJob = settlementEventJobService.lambdaQuery()
                    .eq(SettlementEventJobEntity::getPushEventCode, bean.getEventSourceCode()).one();
            if (eventJob != null) {
                JSONObject eventData = JSONObject.parseObject(eventJob.getEventRequest());
                bean.setPreservationType(eventData.getString("preservationType"));
                bean.setPreservationProject(eventData.getString("preservationProject"));
                bean.setPreservationWhy(eventData.getString("preservationWhy"));
                // 设置保全生效时间
                bean.setPreservationEffectTime(eventData.getDate("preservationEffectTime"));
                bean.setEndorsementNo(eventJob.getEndorsementNo());
                bean.setPreservationCode(eventData.getString("preservationCode"));
                bean.setPreservationPeriod(eventData.getInteger("renewalTermPeriod"));
            }
        });
        settlementPolicyInfoService.saveOrUpdateBatch(list);
        return Result.success();
    }

    /**
     * 手动触发生事件执行器
     */
    @ApiOperation(value = "手动触发生事件执行器", notes = "手动触发生事件执行器")
    @PostMapping("/event_job")
    @PassToken
    public Result<String> eventJobRun() {
        // 1 获取需要处理的事件
        List<SettlementEventJobEntity> list =
                settlementEventJobService.lambdaQuery().eq(SettlementEventJobEntity::getEventStatus, 0).list();
        log.info("执行获取需要处理的事件条数={},", list.size());
        // 2 获取事件工厂服务
        for (SettlementEventJobEntity eventJob : list) {
            SettlementEventHandler settlementEventHandler =
                    SettlementEventFactory.getSettlementEventHandler(eventJob.getEventType());
            if (settlementEventHandler != null) {
                log.info("执行获取需要处理的事件任务,事件类型={}", eventJob.getEventType());
                // 事件执行
                settlementEventHandler.handle(eventJob);
            } else {
                log.warn(StrUtil.format("事件类型类型不支持，事件类型={}", eventJob.getEventType()));
            }
            log.info("执行获取需要处理的事件任务完成,事件id={}", eventJob.getId());
        }
        return Result.success();
    }

    /**
     * 手动触发结算完成补偿
     */
    @ApiOperation(value = "手动触发结算完成补偿", notes = "手动触发结算完成补偿")
    @PostMapping("/finish_help")
    @PassToken
    public Result<String> finishHelp() {
        // 1 获取需要处理的事件
        List<SettlementReconcileInfoEntity> list =
                settlementReconcileInfoService.lambdaQuery().eq(SettlementReconcileInfoEntity::getReconcileStatus, 3)
                        .gt(SettlementReconcileInfoEntity::getId, 3).list();

        list.forEach(x -> {
            settlementReconcileService.finishReconcileHelp(x.getReconcileCode(), "刘佳");
        });
        return Result.success();
    }

    /**
     * 线下单刷新代理人和机构编码
     */
    @ApiOperation(value = "[手动]线下单刷新代理人和机构编码", notes = "[手动]线下单刷新代理人和机构编码")
    @PostMapping("/refresh_offline_policy_org")
    @PassToken
    public Result<String> refreshOfflinePolicyOrgInfo() {
        // 异步执行申请开始对账
        taskExecutor.submit(() -> {
            try {
                reconcileHelpService.refreshOfflinePolicyOrgInfo();
            } catch (Exception e) {
                log.warn("线下单刷新代理人和机构编码失败：", e);
            }
        });
        return Result.success("success");
    }

    /**
     * 刷新结算保单信息： 【数据量规格很大，执行需谨慎】
     */
    @ApiOperation(value = "[手动]刷新结算保单信息", notes = "[手动]刷新结算保单信息")
    @PostMapping("/refresh_policy_info")
    @PassToken
    public Result<String> refreshPolicyInfo(
            @RequestParam @ApiParam(name = "startPage", value = "开始page") int startPage,
            @RequestParam @ApiParam(name = "testStatus", value = "是否试跑") int testStatus) {
        // 异步执行申请开始对账
        taskExecutor.submit(() -> {
            try {
                reconcileHelpService.refreshPolicyInfo(startPage, testStatus);
            } catch (Exception e) {
                log.warn("刷新结算保单信息出现异常：", e);
            }
        });
        return Result.success("success");
    }

    /**
     * 刷新已完成结算保单信息： 【数据量规格很大，执行需谨慎】
     */
    @ApiOperation(value = "[手动]刷新已完成结算保单信息", notes = "[手动]刷新已完成结算保单信息")
    @PostMapping("/refresh_policy_info/reconcile_success")
    @PassToken
    public Result<String> refreshPolicyReconcileSuccess(
            @RequestParam @ApiParam(name = "startPage", value = "开始page") int startPage,
            @RequestParam @ApiParam(name = "testStatus", value = "是否试跑") int testStatus) {
        // 异步执行申请开始对账
        taskExecutor.submit(() -> {
            try {
                reconcileHelpService.refreshPolicyReconcileSuccess(startPage, testStatus);
            } catch (Exception e) {
                log.warn("刷新结算保单信息出现异常：", e);
            }
        });
        return Result.success("success");
    }

    /**
     * 指定保单刷新结算保单信息
     */
    @ApiOperation(value = "[手动]指定保单刷新结算保单信息", notes = "[手动]指定保单刷新结算保单信息")
    @PostMapping("/refresh_policy_info/policy_code")
    @PassToken
    public Result<String> refreshPolicyInfoByPolicyCode(
            @RequestBody @ApiParam(name = "policyCodes", value = "保单号集合") List<String> policyCodes) {
        // 异步执行申请开始对账
        taskExecutor.submit(() -> {
            try {
                reconcileHelpService.refreshPolicyInfoByCodeList(policyCodes);
            } catch (Exception e) {
                log.warn("【指定保单】刷新结算保单信息出现异常：", e);
            }
        });
        return Result.success("success");
    }

    @PassToken
    @PostMapping("handleSettlementEventJob")
    @ApiOperation(value = "[手动]处理结算事件Job", notes = "[手动]处理结算事件Job")
    public Result<String> handleSettlementEventJob(@RequestBody @Valid HandleSettlementEventJobVo params) {
        if (CollUtil.isEmpty(params.getIds()) && CollUtil.isEmpty(params.getPushEventCodeList())) {
            return Result.error(BasicCodeMsg.PARAMETER_ERROR.setMsg("ids和pushEventCodeList最少满足一个条件"));
        }
        List<SettlementEventJobEntity> settlementEventJobList = settlementEventJobService.lambdaQuery()
                .in(CollUtil.isNotEmpty(params.getIds()), SettlementEventJobEntity::getId, params.getIds())
                .in(CollUtil.isNotEmpty(params.getPushEventCodeList()), SettlementEventJobEntity::getPushEventCode,
                        params.getPushEventCodeList()).list();
        log.info("需要处理任务数:{}", settlementEventJobList.size());
        for (int i = 0; i < settlementEventJobList.size(); i++) {
            SettlementEventJobEntity eventJob = settlementEventJobList.get(i);
            log.info("开始处理第[{}]个任务={}", i + 1, JSONUtil.toJsonStr(eventJob));
            SettlementEventHandler settlementEventHandler =
                    SettlementEventFactory.getSettlementEventHandler(eventJob.getEventType());
            if (settlementEventHandler != null) {
                log.info("执行获取需要处理的事件任务,事件类型={}", eventJob.getEventType());
                // 事件执行
                try {
                    settlementEventHandler.handle(eventJob);
                } catch (Exception e) {
                    log.info("处理事件id={}出现异常", eventJob.getId(), e);
                    settlementEventJobService.updateById(eventJob);
                }
            } else {
                log.info(StrUtil.format("事件类型类型不支持，事件类型={}", eventJob.getEventType()));
            }
        }

        return Result.success("success");
    }

    @ApiOperation(value = "[手动]刷新保单费率信息", notes = "[手动]刷新保单费率信息")
    @PostMapping("refreshPolicyPremium")
    @PassToken
    public Result<String> refreshPolicyPremium(
            @RequestBody @NotEmpty(message = "保单数据不能为空") List<RefreshPolicyPremiumVo> policyList) {
        reconcileHelpService.refreshPolicyPremium(policyList);
        return Result.success("success");
    }

    /**
     * 冲正处理
     *
     * @param policyList
     * @return
     */
    @ApiOperation(value = "[手动]冲正数据", notes = "[手动]冲正数据")
    @PostMapping("rectification")
    @PassToken
    public Result<String> rectification(
            @RequestBody @NotEmpty(message = "保单数据不能为空") List<RectificationVo> policyList) {
        reconcileHelpService.rectification(policyList);
        return Result.success("success");
    }

    @PassToken
    @ApiOperation(value = "[手动]单一数据冲正", notes = "[手动]单一数据冲正")
    @PostMapping("rectificationOne")
    public Result<String> rectificationOne(@RequestBody @Valid RectificationOneVo vo) {
        reconcileHelpService.rectificationOne(vo);
        return Result.success("success");
    }

    @ApiOperation(value = "生成明细数据", notes = "生成明细数据")
    @PostMapping("generateDetail/{reconcileCode}")
    @PassToken
    public Result generateDetail(@PathVariable String reconcileCode) {
        SettlementReconcileInfoEntity reconcileInfo = settlementReconcileInfoService.lambdaQuery()
                .eq(SettlementReconcileInfoEntity::getReconcileCode, reconcileCode).one();
        if (reconcileInfo != null) {
            settlementReconcileInfoService.generateDetail(reconcileInfo);
        }

        return Result.success("success");
    }

    @PassToken
    @PostMapping("sqlHelper")
    @ApiOperation(value = "sql处理器", notes = "sql处理器")
    public Result sqlHelper(@RequestBody @Valid SqlHelperVo input) {
        settlementReconcileInfoService.sqlHelper(input);
        return Result.success("success");
    }

    @PassToken
    @PostMapping("getPolicySettlementStatus")
    @ApiOperation(value = "获取保单结算状态", notes = "获取保单结算状态")
    public Result getPolicySettlementStatus(@RequestBody @NotEmpty(message = "结算数据不能为空") List<SettlementStatusInput> input) {
        List<SettlementStatusOut> resultList = ReconcileBaseHelper.getPolicySettlementStatus(input);
        return Result.success(resultList);
    }


    @PassToken
    @PostMapping("updatePostponedMonth")
    @ApiOperation(value = "更新延期对账时间", notes = "获取保单结算状态")
    public Result updatePostponedMonth(@RequestBody @Valid UpdatePostponedMonthVo params) {
        if (CollUtil.isEmpty(params.getIds()) && CollUtil.isEmpty(params.getEventSourceCodeList())) {
            return Result.success();
        }
        // 更新数据
        settlementPolicyInfoService.lambdaUpdate()
                .in(CollUtil.isNotEmpty(params.getEventSourceCodeList()), SettlementPolicyInfoEntity::getEventSourceCode, params.getEventSourceCodeList())
                .in(CollUtil.isNotEmpty(params.getIds()), SettlementPolicyInfoEntity::getId, params.getIds())
                .set(SettlementPolicyInfoEntity::getPostponedMonth, params.getPolicyMonth())
                .update();
        return Result.success();
    }

    @PassToken
    @PostMapping("/imcome/policy/valid")
    @ApiOperation(value = "收入端佣金配置验证", notes = "收入端佣金配置验证")
    public Result<List<ValidateResultDto>> validateIncomeConfig(@RequestBody List<ValidateCostConfigParam> params) {
        return Result.success(commValidService.validateIncomeConfig(params));
    }


    @PassToken
    @PutMapping("/imcome/valid/white/{policyNo}")
    @ApiOperation(value = "保单号校验白名单", notes = "保单号校验白名单")
    public Result<Void> validateIncomeConfig(@PathVariable("policyNo") String params, int op) {
        commValidService.addFilterWhitePolicy(params, op);
        return Result.success();
    }


}
