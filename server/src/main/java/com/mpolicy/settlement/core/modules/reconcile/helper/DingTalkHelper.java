package com.mpolicy.settlement.core.modules.reconcile.helper;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 协议管理基础辅助服务
 *
 * <AUTHOR>
 * @since 2023/05/20
 */
@Component
@Slf4j
public class DingTalkHelper {

    public static DingTalkHelper dingTalkHelper;

    // private static final String url = "https://oapi.dingtalk.com/robot/send?access_token=b203d9fb49d0f52f8c31b797164e19bf091f69916b3cb76329313d256f8b0353";
    private static final String url = "https://oapi.dingtalk.com/robot/send?access_token=9cec718efebed6c09c4f9da9b4988a5790b07b887e1dd3a11994f25c5e2754b4";

    /**
     * 处理事假任务
     *
     * @param eventJob 任务
     */
    public static void handleEventJob(SettlementEventJobEntity eventJob) {
        if (eventJob == null || (eventJob.getIncomeEventStatus() != -1 && eventJob.getContractIncomeEventStatus() != -1)) {
            log.info("事件无需推送钉钉消息");
            return;
        }
//        String template = "---\n" +
//                ">生成明细失败\n" +
//                "--- \n" +
//                "| 合同编码 | 保单号 |  操作类型   | 协议 | 合约 |\n" +
//                "|:----:|:---:|:-------:|:--:|:--:|\n" +
//                "|  {}  | {}  | {} | {} | {} |";
        String template = "| 推送标识 | {}  \n" +
                "|:----:|:--:|\n" +
                "| 操作类型 | {}\n" +
                "| 合同编码 | {} \n" +
                "| 保单号  | {} \n" +
                "|  协议  | {} \n" +
                "|  合约  | {} ";
        Map<String, String> markdownMap = new HashMap<>();
        markdownMap.put("title", "【测试】");
        markdownMap.put("text", StrUtil.format(template,
                eventJob.getPushEventCode(),
                eventJob.getEventDesc(),
                eventJob.getContractCode(),
                eventJob.getEventBusinessCode(),
                eventJob.getIncomeEventMessage(),
                eventJob.getContractIncomeEventMessage()));
        Map<String, Object> map = new HashMap<>();
        map.put("msgtype", "markdown");
        map.put("markdown", markdownMap);
        String post = HttpUtil.post(url, JSONUtil.toJsonStr(map));
        System.out.println(post);
    }

    public static void main(String[] args) {
        SettlementEventJobEntity settlementEventJob = new SettlementEventJobEntity();
        settlementEventJob.setPushEventCode("C00000000000000001");
        settlementEventJob.setContractCode("C00000000000000001");
        settlementEventJob.setEventBusinessCode("PO0000000000000002");
        settlementEventJob.setEventDesc("测试");
        settlementEventJob.setIncomeEventStatus(-1);
        settlementEventJob.setContractIncomeEventStatus(-1);
        settlementEventJob.setIncomeEventMessage("险种编码未配置险种编码未配置险种编码未配置");
        settlementEventJob.setContractIncomeEventMessage("未匹配到费率表");
        DingTalkHelper.handleEventJob(settlementEventJob);
    }

    @PostConstruct
    public void init() {
        dingTalkHelper = this;
    }
}
