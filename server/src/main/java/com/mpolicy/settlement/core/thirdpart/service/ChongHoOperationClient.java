package com.mpolicy.settlement.core.thirdpart.service;

import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.thirdpart.client.ChongHoOperationFeign;
import com.mpolicy.settlement.core.thirdpart.request.WhaleAddCommissionPushQuery;
import com.mpolicy.settlement.core.thirdpart.response.AddCommissionSettlementPushDto;
import com.mpolicy.settlement.core.thirdpart.response.ChongHoResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 农保-加佣服务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ChongHoOperationClient {
    @Autowired
    private ChongHoOperationFeign chongHoOperationFeign;

    /**
     * 分页获取加佣明细数据，约定按id升序排列（为了断点续传）
     * @param query
     * @return
     */
    public List<AddCommissionSettlementPushDto> getSettlementAddDataByPage(WhaleAddCommissionPushQuery query){
        ChongHoResult<List<AddCommissionSettlementPushDto>> result = null;
        try {
            result = chongHoOperationFeign.getSettlementDataByPage(query);
        } catch (Exception e) {
            log.warn("获取加佣数据异常,请开发人员及时排查问题", e);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("获取加佣数据异常,请开发人员及时排查问题"));
        }
        if (!result.isSuccess()) {
            log.warn("获取加佣数据不成功,请开发人员及时排查问题", result);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("获取加佣数据不成功，请联系开发人员"));
        }
        return result.getData();
    }

    public boolean syncSettlementStatus(List<String> marketingBusinessCodes){

        try {
           chongHoOperationFeign.syncSettlementStatus(marketingBusinessCodes);
           return true;
        } catch (Exception e) {
            log.warn("获取加佣数据异常,请开发人员及时排查问题", e);
            return false;
        }
    }
}
