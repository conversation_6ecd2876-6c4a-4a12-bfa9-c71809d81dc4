package com.mpolicy.settlement.core.modules.reconcile.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.settlement.core.modules.autocost.dto.SettlementCostInfoDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileSubjectEntity;

import java.util.List;

/**
 * 结算支出端保单信息表
 *
 * <AUTHOR>
 */
public interface SettlementCostInfoService extends IService<SettlementCostInfoEntity> {
    /**
     * 根据源事件编号获取支出佣金记录个数
     * @param eventSourceCode
     * @return
     */
    Integer countEventCostInfoByEventSourceCode(String eventSourceCode);
    /**
     * 根据保单号和事件编码获取支出佣金记录个数
     * @param policyNo
     * @param settlementEventCode
     * @return
     */
    Integer countEventCostInfoByPolicyNo(String policyNo, String settlementEventCode,Integer renewalPeriod);

    /**
     * 根据合同号列表查询未冲正标记数据
     * @param contractCodes
     * @return
     */
    List<SettlementCostInfoEntity> listUnCorrectionCostInfoByContractCodes(List<String> contractCodes);

    /**
     * 根据合同号查询未冲正标记数据(已冲正数据已经一正一负抵消了)
     * @param contractCode
     * @return
     */
    List<SettlementCostInfoEntity> listUnCorrectionCostInfoByContractCode(String contractCode);

    /**
     * 根据佣金配置keys查询未冲正数据且未确认的记录数
     * @param configKeys
     * @return
     */
    Integer countUnCorrectionPolicyCostInfoByCostConfigKeys(List<String> configKeys);

    /**
     * 根据佣金配置key列表查询所对应的合同编号
     * @param configKeys
     * @return
     */
    List<String> listContractCodeByCostConfigKeys(List<String> configKeys);
    /**
     * 根据佣金配置keys查询未冲正数据且未确认的单子
     * @param configKeys
     * @param pageSize
     * @param pageCount
     * @return
     */
    List<SettlementCostInfoEntity> pageUnCorrectionPolicyCostInfoByCostConfigKeys(List<String> configKeys,Integer pageSize,Integer pageCount);
    /**
     * 按合同编号、佣金配置key查询所对应的佣金记录（不包括佣金记录佣金比例不可变记录）
     * @param contractCodes
     * @param configKeys
     * @return
     */
    List<SettlementCostInfoEntity> listUnCorrectionPolicyCostInfoByCostConfigKeys(List<String> contractCodes,List<String> configKeys);

    /**
     * 按保单号查询所对应的佣金记录
     * @param policyNo
     * @return
     */
    List<SettlementCostInfoEntity> listUnCorrectionPolicyCostInfoByPolicyNo(String policyNo);

    /**
     * 根据被保人编号列表查询未冲正标记数据
     * @param contractCode
     * @param insuredCodes
     * @return
     */
    List<SettlementCostInfoEntity> listUnCorrectionCostInfoByInsuredCodes(String contractCode,List<String> insuredCodes);

    /**
     * 获取团险历史的分单明细
     * @param contractCode
     * @param insuredCodes
     * @return
     */
    List<SettlementCostInfoEntity> listUnCorrectionGroupPolicyCostInfoByInsuredCodes(String contractCode,List<String> insuredCodes);

    /**
     * 根据险种编码获取
     * @param contractCode
     * @param productCodes
     * @return
     */
    List<SettlementCostInfoEntity> listUnCorrectionPolicyCostInfoByProductCodes(String contractCode,List<String> productCodes);

    /**
     * 根据合同号、渠道编码查询未冲正数据，已冲正数据已经一正一负抵消了（渠道变更）
     * 注意：包括已确认的单子
     * @param contractCode
     * @return
     */
    List<SettlementCostInfoEntity> listUnCorrectionByContractCodeAndChannelCode(String contractCode,String channelCode);

    /**
     * 根据合同编号和佣金归属人编码获取
     * @param contractCode
     * @return
     */
    List<SettlementCostInfoEntity> listUnCorrectionPolicyCostInfoByContractCode(String contractCode);

    /**
     *
     * @param contractCode
     * @param productCodes
     * @return
     */
    List<SettlementCostInfoEntity> listUnCorrectionByContractCodeAndProductCodes(String contractCode,List<String> productCodes);

    /**
     * 更新佣金配置key
     * @param ids
     * @param newConfigKey
     * @return
     */
    Boolean updateCostInfoConfigKey(List<Integer> ids,String newConfigKey);
    /**
     * 根据id设置冲正标志为已冲正
     * @param ids
     * @return
     */
    Boolean updateCorrectionFlagDoneByIds(List<Integer> ids);

    /**
     * 基础佣金批量插入
     * @param list
     * @return
     */
    int saveList(List<SettlementCostInfoDto> list);

    /**
     * 根据个险合同号查询未冲正数据原始新契约数据
     * 注意：包括已确认的单子
     * @param contractCode
     * @return
     */
    List<SettlementCostInfoEntity> listPersonNewPolicyCostInfoByContractCode(String contractCode);

    /**
     * 退保查询历史的未冲正的佣金的记录(退保事件专用)
     * @param contractCode
     * @return 返回结果包括的科目有：首续年佣金、长险暂发、长险补发、车船税
     */
    List<SettlementCostInfoEntity> listLongPolicyUnCorrectionCostInfoByContractCode(String contractCode);

    /**
     * 根据保单号查询所有未冲正记录
     * @param policyNos
     * @return
     */
    List<SettlementCostInfoEntity> listUnCorrectionCostInfoByPolicyNos(List<String> policyNos);

    /**
     * 根据唯一编码查询基础佣金信息
     * @param costCode
     * @return
     */
    SettlementCostInfoEntity getByCostCode(String costCode);

    /**
     * 根据event_source_code查询基础佣金明细
     * @param eventSourceCode
     * @return
     */
    List<SettlementCostInfoEntity> listCostInfoByEventSourceCode(String eventSourceCode);

    /**
     * 批量更新佣金归属人第三方在职状态信息（用于历史数据补偿处理）
     * @param list
     * @return
     */
    int batchUpdateOwnerThirdStatus(List<SettlementCostInfoEntity> list);

    /**
     * 根据合同编号和期数获取佣金记录
     * @param contractCode
     * @param renewalPeriod
     * @return
     */
    List<SettlementCostInfoEntity> listUnCorrectionPolicyCostInfoByContractCodeAndRenewal(String contractCode,Integer renewalPeriod);
}

