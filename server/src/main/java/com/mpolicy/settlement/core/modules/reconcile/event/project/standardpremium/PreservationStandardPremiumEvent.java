package com.mpolicy.settlement.core.modules.reconcile.event.project.standardpremium;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.policy.common.enums.PreservationProjectEnum;
import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.policy.common.standard.PolicyStandardPremiumVo;
import com.mpolicy.policy.common.standard.PreservationStandardPremiumVo;
import com.mpolicy.policy.common.standard.RenewalTermStandardPremiumVo;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.reconcile.dto.standardpremium.*;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementStandardPremiumEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementStandardPremiumEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.common.AbsSettlementStandardPremiumEvent;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventCheckResult;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PreservationStandardPremiumEvent extends AbsSettlementStandardPremiumEvent {
    @Override
    public HandleEventCheckResult handleEventCheck(SettlementStandardPremiumEventJobEntity eventJob) {
        JSONObject params = JSONObject.parseObject(eventJob.getEventRequest());
        String preservationProject = params.getString("preservationProject");
        if(StringUtils.isNotBlank(preservationProject)){
            PreservationProjectEnum projectEnum = PreservationProjectEnum.decode(preservationProject);
            if(projectEnum == null){
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("无法识别保全类型，preservationProject={}", preservationProject)));
            }
            switch (projectEnum){
                case POLICY_REFUND:
                    return HandleEventCheckResult.builder().checkStatus(false).checkMsg("该保全操作无需计算折标保费").build();
                default:
                    return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();
            }
        }
        return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();

    }

    @Override
    public String handleEvent(SettlementStandardPremiumEventJobEntity eventJob) {
        String contractCode = eventJob.getContractCode();
        EpContractInfoVo policyInfo =
                policyCenterBaseClient.getPolicyInfoByContractCode(eventJob.getContractCode());
        if(policyInfo.getContractBaseInfo().getLongShortFlag() == 1){
            return "长险相关的标保保全暂时无法处理success";
        }

        JSONObject params = JSONObject.parseObject(eventJob.getEventRequest());
        String preservationCode = params.getString("preservationCode");
        if(StringUtils.isBlank(preservationCode)){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("合同号{}的保全标保事件，保全编号为空，{}", eventJob.getContractCode(),eventJob.getEventRequest())));
        }
        PreservationStandardPremiumDto premiumDto = builderPreservationStandardPremium(preservationCode);


        //冲正标志为1的标保都设置为0，
        Boolean result = transactionTemplate.execute(new TransactionCallback<Boolean>() {
            @Override
            public Boolean doInTransaction(TransactionStatus transactionStatus) {
                try {
                    UpdateWrapper<SettlementCostInfoEntity> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.lambda().set(SettlementCostInfoEntity::getBusinessDiscountPremium, BigDecimal.ZERO)
                            .eq(SettlementCostInfoEntity::getContractCode,contractCode)
                            .eq(SettlementCostInfoEntity::getPreservationCode,preservationCode)
                            .in(SettlementCostInfoEntity::getInitialEventCode,handlerEventType().getSettlementEventCodeList())
                            .eq(SettlementCostInfoEntity::getCorrectionFlag,1)
                            .eq(SettlementCostInfoEntity::getSettlementSubjectCode, CostSubjectEnum.FIRST_BASIC_COMM.getCode());
                    settlementCostInfoService.getBaseMapper().update(null, updateWrapper);
                    if(CollectionUtils.isNotEmpty(premiumDto.getProductInsuredMapList())){
                        premiumDto.getProductInsuredMapList().stream().forEach(i->{
                            UpdateWrapper<SettlementCostInfoEntity> updateWrapper1 = new UpdateWrapper<>();
                            updateWrapper1.lambda().set(SettlementCostInfoEntity::getBusinessDiscountPremium,i.getStandardPremium())
                                    .eq(SettlementCostInfoEntity::getContractCode,contractCode)
                                    .eq(SettlementCostInfoEntity::getPreservationCode,preservationCode)
                                    .in(SettlementCostInfoEntity::getInitialEventCode,handlerEventType().getSettlementEventCodeList())
                                    .eq(SettlementCostInfoEntity::getCorrectionFlag,0)
                                    .eq(SettlementCostInfoEntity::getSettlementSubjectCode, CostSubjectEnum.FIRST_BASIC_COMM.getCode())
                                    .eq(SettlementCostInfoEntity::getInsuredCode,i.getInsuredCode())
                                    .eq(SettlementCostInfoEntity::getProductCode,i.getProductCode())

                                    .eq(SettlementCostInfoEntity::getRenewalPeriod,1);
                            int result1 = settlementCostInfoService.getBaseMapper().update(null, updateWrapper1);
                            if(result1 == 0){
                                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("合同号{}的保全记录的标保未更新到,{}", eventJob.getContractCode(), JSON.toJSONString(i))));
                            }
                        });
                    }else{
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("合同号{}的标保续期险种明细不存在", eventJob.getContractCode())));
                    }
                    return Boolean.TRUE;
                } catch (Exception e){
                    //回滚
                    log.warn("合同号{}回滚保全标保事件",contractCode,e);
                    transactionStatus.setRollbackOnly();
                    return Boolean.FALSE;
                }
            }
        });
        if(!result){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("合同号{}的保全标保事件处理失败", eventJob.getContractCode())));
        }

        return  "success";
    }

    @Override
    public SettlementStandardPremiumEventTypeEnum handlerEventType() {
        return SettlementStandardPremiumEventTypeEnum.STANDARD_PREMIUM_PRESERVATION;
    }

    private PreservationStandardPremiumDto builderPreservationStandardPremium(String preservationCode){
        PreservationStandardPremiumVo vo = super.policyCenterBaseClient.queryCorrectedStandardPremium(preservationCode);
        PreservationStandardPremiumDto dto = new PreservationStandardPremiumDto();
        BeanUtils.copyProperties(vo,dto);
        if(CollectionUtils.isNotEmpty(vo.getProductInsuredMapList())){
            List<PolicyProductInsuredStandardPremiumDto> insuredList = vo.getProductInsuredMapList().stream()
                    .map(m->{
                        PolicyProductInsuredStandardPremiumDto insuredStandardPremiumDto = new PolicyProductInsuredStandardPremiumDto();
                        BeanUtils.copyProperties(m,insuredStandardPremiumDto);
                        return insuredStandardPremiumDto;
                    }).collect(Collectors.toList());
            dto.setProductInsuredMapList(insuredList);

        }
        return dto;
    }
}
