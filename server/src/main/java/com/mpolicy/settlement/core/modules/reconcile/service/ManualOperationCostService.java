package com.mpolicy.settlement.core.modules.reconcile.service;

import com.mpolicy.settlement.core.common.reconcile.policy.ManualCorrectionCost;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.ManualCostCodeDto;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.ManualCostInfoDto;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.ManualCustomerManagerChangeDto;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.ManualProductChange;

import java.util.List;

/**
 * 手工操作佣金服务(手工冲正、excel导入)
 *
 * <AUTHOR>
 */
public interface ManualOperationCostService {
    /**
     * 手工冲正
     * @param correction
     */
    void manualCorrection(ManualCorrectionCost correction);

    /**
     * 手工冲正推荐人信息
     * @param correction
     */
    void manualCorrectionCustomerManager(ManualCustomerManagerChangeDto correction);

    void manualModifyCostInfo(List<ManualCostInfoDto> dtos);

    /**
     * 手工冲正险种变更
     */
    void manualProductChange(ManualProductChange change);

    /**
     * 修改状态为无需确认
     * @param dto
     */
    void manualNoNeedConfirmStatus(ManualCostCodeDto dto);

    /**
     * 修改状态为待需确认
     * @param dto
     */
    void manualWaitConfirmStatus(ManualCostCodeDto dto);

}
