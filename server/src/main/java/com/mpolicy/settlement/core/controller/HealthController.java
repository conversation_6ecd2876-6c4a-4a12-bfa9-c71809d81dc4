package com.mpolicy.settlement.core.controller;

import com.mpolicy.common.result.Result;
import com.mpolicy.web.common.annotation.PassToken;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 健康检查 controller
 *
 * <AUTHOR>
 * @date 2019-06-20 10:27:48
 */
@RestController
public class HealthController {

    /**
     * 服务探活检测服务
     */
    @RequestMapping("/ins_health_check")
    @PassToken
    public Result<String> healthCheck(){

        return Result.success("success");
    }
}
