package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementReconcileCompanyInfoDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileCompanyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcileCompanyInfoService;

/**
 * 结算保司配置表
 *
 * <AUTHOR>
 * @date 2023-05-22 09:19:53
 */
@Slf4j
@Service("settlementReconcileCompanyInfoService")
public class SettlementReconcileCompanyInfoServiceImpl extends ServiceImpl<SettlementReconcileCompanyInfoDao, SettlementReconcileCompanyInfoEntity> implements SettlementReconcileCompanyInfoService {

}
