package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileCompanySubjectEntity;
import com.mpolicy.settlement.core.modules.reconcile.helper.ReconcileSubjectHelper;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcileCompanySubjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementReconcileSubjectDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileSubjectEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcileSubjectService;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 保司结算对账单科目
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:34
 */
@Slf4j
@Service("settlementReconcileSubjectService")
public class SettlementReconcileSubjectServiceImpl
        extends ServiceImpl<SettlementReconcileSubjectDao, SettlementReconcileSubjectEntity>
        implements SettlementReconcileSubjectService {

    @Autowired
    private SettlementReconcileSubjectDao settlementReconcileSubjectDao;

    @Autowired
    private SettlementReconcileCompanySubjectService settlementReconcileCompanySubjectService;
    @Autowired
    private ReconcileSubjectHelper reconcileSubjectHelper;

    /**
     * 批量插入
     *
     * @param entityList
     */
    @Override
    public void insertBatchSomeColumn(List<SettlementReconcileSubjectEntity> entityList) {
        if (CollUtil.isNotEmpty(entityList)) {
            settlementReconcileSubjectDao.insertBatchSomeColumn(entityList);
        }

    }

    /**
     * 更新对账科目信息
     *
     * @param reconcileCode 对账单编码
     */
    @Override
    public void updateSettlementReconcileSubject(String reconcileCode) {
        List<SettlementReconcileSubjectEntity> list =
                lambdaQuery().eq(SettlementReconcileSubjectEntity::getReconcileCode, reconcileCode).list();
        if (list.isEmpty()) {
            return;
        }
        List<String> subjectRuleCode =
                list.stream().map(SettlementReconcileSubjectEntity::getSubjectRuleCode).collect(Collectors.toList());
        Map<String, SettlementReconcileCompanySubjectEntity> subjectRuleMap =
                settlementReconcileCompanySubjectService.lambdaQuery()
                        .in(SettlementReconcileCompanySubjectEntity::getSubjectRuleCode, subjectRuleCode).list().stream()
                        .collect(
                                Collectors.toMap(SettlementReconcileCompanySubjectEntity::getSubjectRuleCode, Function.identity()));
        // 更新对账科目信息
        list.forEach(action -> {
            if (subjectRuleMap.containsKey(action.getSubjectRuleCode())) {
                SettlementReconcileCompanySubjectEntity settlementReconcileCompanySubject =
                        subjectRuleMap.get(action.getSubjectRuleCode());
                String subjectName = ReconcileSubjectHelper.matchSearchCode(settlementReconcileCompanySubject.getReconcileSubjectCode());
                if (StrUtil.isBlank(subjectName)) {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("科目编码" + settlementReconcileCompanySubject.getReconcileSubjectCode() + "没有配置"));
                }
                action.setReconcileSubjectName(subjectName);
                action.setReconcileSubjectCode(settlementReconcileCompanySubject.getReconcileSubjectCode());
                action.setSubjectPolicyCount(0);
                action.setSubjectTotalAmount(BigDecimal.ZERO);
                action.setSubjectSettingData(JSONUtil.toJsonStr(settlementReconcileCompanySubject));
                settlementReconcileSubjectDao.updateById(action);
            } else {
                settlementReconcileSubjectDao.deleteById(action.getId());
            }
        });
    }
}
