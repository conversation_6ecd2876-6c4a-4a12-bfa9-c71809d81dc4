package com.mpolicy.settlement.core.thirdpart.enums;

import java.beans.ConstructorProperties;

public enum DingTalkEnum {
    TEXT("Text", "文本消息"),
    IMAGE("Image", "图片消息"),
    VOICE("Voice", "语音消息"),
    FILE("File", "文件消息"),
    LINK("Link", "链接消息"),
    OA("OA", "OA消息"),
    MARKDOWN("MarkDown", "markdown消息"),
    ACTIONCARD("ActionCard", "活动卡片消息"),
    ROBOTACTIONCARD("RobotActionCard", "活动卡片消息");

    private String msgType;
    private String explain;

    public static DingTalkEnum getEnum(String msgType) {
        if (msgType != null) {
            DingTalkEnum[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                DingTalkEnum item = var1[var3];
                if (item.getMsgType().equals(msgType)) {
                    return item;
                }
            }
        }

        throw new IllegalArgumentException("value is illegal");
    }

    @ConstructorProperties({"msgType", "explain"})
    private DingTalkEnum(String msgType, String explain) {
        this.msgType = msgType;
        this.explain = explain;
    }

    public String getMsgType() {
        return this.msgType;
    }

    public String getExplain() {
        return this.explain;
    }
}
