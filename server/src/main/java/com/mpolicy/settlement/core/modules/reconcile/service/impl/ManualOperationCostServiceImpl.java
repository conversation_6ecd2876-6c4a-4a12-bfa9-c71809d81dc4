package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.other.BigDecimalUtils;
import com.mpolicy.policy.common.ep.policy.EpInsuredInfoVo;
import com.mpolicy.product.common.base.ProductBase;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.common.reconcile.policy.ManualCorrectionCost;
import com.mpolicy.settlement.core.modules.autocost.enums.ConfirmStatusEnum;
import com.mpolicy.settlement.core.modules.protocol.dto.ProtocolInsuranceProductInfoOut;
import com.mpolicy.settlement.core.modules.protocol.helper.ProtocolBaseHelper;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.*;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.CommissionTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.PolicyProductTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementGenerateTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyProductPremInput;
import com.mpolicy.settlement.core.modules.reconcile.helper.ReconcileBaseHelper;
import com.mpolicy.settlement.core.modules.reconcile.service.*;
import com.mpolicy.settlement.core.modules.reconcile.utils.BeanObjUtils;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import com.mpolicy.settlement.core.modules.referrer.entity.ChannelApplicationReferrerEntity;
import com.mpolicy.settlement.core.modules.referrer.service.ChannelApplicationReferrerService;
import com.mpolicy.settlement.core.utils.SecurityUtil;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.*;

import static com.mpolicy.settlement.core.common.Constant.SYSTEM_CORRECTION_USER;
import static com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum.MANUAL_CORRECTION;
import static com.mpolicy.settlement.core.modules.reconcile.service.impl.SettlementCostCorrectionServiceImpl.builderBasicCommissionConfigParamByOldCostInfo;
import static com.mpolicy.settlement.core.modules.reconcile.service.impl.SettlementCostProcessServiceImpl.getCorrectionBusinessAccountTime;
import static com.mpolicy.settlement.core.modules.reconcile.service.impl.SettlementCostProcessServiceImpl.getPolicyCommissionConfig;

/**
 * 手工操作佣金服务(手工冲正、excel导入)
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ManualOperationCostServiceImpl extends AbstractSettlementCostProcessService implements ManualOperationCostService {
    @Autowired
    private SettlementCostCorrectionService settlementCostCorrectionService;

    @Autowired
    private SettlementCostInfoService settlementCostInfoService;
    @Autowired
    private SettlementCostPolicyInfoService settlementCostPolicyInfoService;

    @Autowired
    protected SettlementCostProcessService settlementCostProcessService;
    @Autowired
    protected SettlementCostOwnerService settlementCostOwnerService;
    @Autowired
    protected CostBasicCommissionConfigService commissionConfigService;
    @Autowired
    private ChannelApplicationReferrerService channelApplicationReferrerService;


    public void manualCorrection(ManualCorrectionCost correction) {
        //入参验证
        validateParam(correction);

        //验证业务数据
        SettlementCostInfoEntity oldEntity = settlementCostInfoService.getByCostCode(correction.getCostCode());
        validateBusinessData(correction,oldEntity);

        SettlementCostPolicyInfoEntity policyInfo = settlementCostPolicyInfoService.getById(oldEntity.getCostPolicyId());
        BeanObjUtils.copyPropertiesIgnoreNull(correction.getNewCostInfo(),policyInfo);

        CostCorrectionDto costCorrectionDto = new CostCorrectionDto();
        List<SettlementCostInfoEntity> newCostList = Lists.newArrayList();
        List<Integer> correctionOldIds = Lists.newArrayList();
        costCorrectionDto.setNewCostList(newCostList);
        costCorrectionDto.setCorrectionOldIds(correctionOldIds);
        costCorrectionDto.setPolicy(policyInfo);
        //加锁
        //记录冲正历史id
        correctionOldIds.add(oldEntity.getId());
        log.info("correctionOldIds={}",correctionOldIds);
        //生成一条对冲信息
        SettlementCostInfoEntity offsetCost =
                settlementCostProcessService.builderOffsetCostInfoBySourceCode(correction.getCostCode(), MANUAL_CORRECTION, oldEntity,new Date(),
                        correction.getCorrectionUser(), correction.getCause(), Boolean.TRUE);
        newCostList.add(offsetCost);
        log.info("【手动冲正】对冲记录={}",offsetCost);
        //生成一条新结算信息
        if(correction.getOnlyCorrection() == 0) {
            if (Objects.equals(correction.getSupportRerunCalCostRate(), 1)) {
                log.info("生成新的重跑记录-{}", correction.getCostCode());
                List<SettlementCostInfoEntity> newReRunCostList = this.builderNewCostInfoRecalCostByManualCorrectionInfo(policyInfo, oldEntity, correction);
                newCostList.addAll(newReRunCostList);
                log.info("【手动冲正】新记录={}",JSON.toJSONString(newReRunCostList));
            } else {
                SettlementCostInfoEntity newCost = this.builderNewCostInfoByManualCorrectionInfo(policyInfo, oldEntity, correction);
                newCostList.add(newCost);
                log.info("【手动冲正】新记录={}",newCost);
            }


        }

        //入库操作
        settlementCostCorrectionService.saveCostCommission(costCorrectionDto);
    }

    public void manualNoNeedConfirmStatus(ManualCostCodeDto correction) {
        if(Objects.isNull(correction) || CollectionUtils.isEmpty(correction.getCostCode())){
            return ;
        }
        settlementCostInfoService.lambdaUpdate()
                .set(SettlementCostInfoEntity::getConfirmStatus,-1)
                .in(SettlementCostInfoEntity::getCostCode,correction.getCostCode())
                .update();
    }

    /**
     * 只能从无需确认改成待确认
     * @param correction
     */
    public void manualWaitConfirmStatus(ManualCostCodeDto correction) {
        if(Objects.isNull(correction) || CollectionUtils.isEmpty(correction.getCostCode())){
            return ;
        }
        settlementCostInfoService.lambdaUpdate()
                .set(SettlementCostInfoEntity::getConfirmStatus,0)
                .in(SettlementCostInfoEntity::getCostCode,correction.getCostCode())
                .eq(SettlementCostInfoEntity::getConfirmStatus,-1)
                .update();
    }

    public void manualCorrectionCustomerManager(ManualCustomerManagerChangeDto correction) {
        //入参验证
        if(Objects.isNull(correction) || CollectionUtils.isEmpty(correction.getCostCode())){
            return ;
        }
        if(Objects.isNull(correction.getOldManagerCode()) || Objects.isNull(correction.getNewManagerCode())){
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR);
        }
        if(!Objects.equals(correction.getChannelCode(),"zhnx")){
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("只支持zhnx渠道推荐人变更"));
        }
        List<SettlementCostInfoEntity> olds = settlementCostInfoService.lambdaQuery()
                .in(SettlementCostInfoEntity::getCostCode,correction.getCostCode())
                .eq(SettlementCostInfoEntity::getOwnerThirdCode,correction.getOldManagerCode())
                .eq(SettlementCostInfoEntity::getOwnerChannelCode,correction.getChannelCode())
                .eq(SettlementCostInfoEntity::getCorrectionFlag,0)
                .eq(SettlementCostInfoEntity::getSettlementSubjectCode, CostSubjectEnum.FIRST_BASIC_COMM.getCode())
                .list();
        if(CollectionUtils.isEmpty(olds)){
            return ;
        }

        List<ChannelApplicationReferrerEntity> referrerEntityList = channelApplicationReferrerService.lambdaQuery().eq(ChannelApplicationReferrerEntity::getReferrerWno,correction.getNewManagerCode() )
                .list();
        if(StringUtils.isNotBlank(correction.getNewManagerCode()) && CollectionUtils.isEmpty(referrerEntityList)){
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("只支持zhnx渠道推荐人变更"));
        }
        ChannelApplicationReferrerEntity referrerEntity = CollectionUtils.isEmpty(referrerEntityList)?new ChannelApplicationReferrerEntity():referrerEntityList.get(0);
        CostCorrectionDto costCorrectionDto = new CostCorrectionDto();
        List<SettlementCostInfoEntity> newCostList = Lists.newArrayList();
        List<Integer> correctionOldIds = Lists.newArrayList();
        costCorrectionDto.setNewCostList(newCostList);
        costCorrectionDto.setCorrectionOldIds(correctionOldIds);
        olds.stream().forEach(old->{
            //记录冲正历史id
            correctionOldIds.add(old.getId());
            //生成一条对冲信息
            SettlementCostInfoEntity offsetCost =
                    settlementCostProcessService.builderOffsetCostInfoBySourceCode(old.getCostCode(), MANUAL_CORRECTION, old,new Date(),
                            correction.getCorrectionUser(), correction.getCause(), Boolean.TRUE);
            newCostList.add(offsetCost);

            //生成一条新结算信息
            SettlementCostInfoEntity newSettlementCostInfo = new SettlementCostInfoEntity();
            BeanUtils.copyProperties(old,newSettlementCostInfo);
            newSettlementCostInfo.setId(null);

            newSettlementCostInfo.setCostCode(PolicySettlementUtils.createCodeLastNumber("CC"));
            //记账时间处理
            newSettlementCostInfo.setSettlementTime(new Date());
            newSettlementCostInfo.setSettlementDate(newSettlementCostInfo.getSettlementTime());

            //事件编号
            newSettlementCostInfo.setEventSourceCode(old.getCostCode());

            //事件信息
            newSettlementCostInfo.setSettlementEventCode(MANUAL_CORRECTION.getEventCode());
            newSettlementCostInfo.setSettlementEventDesc(MANUAL_CORRECTION.getEventDesc());

            //是否冲正
            //业务记账时间处理
            newSettlementCostInfo.setBusinessAccountTime(getCorrectionBusinessAccountTime(old.getBusinessAccountTime()));
            newSettlementCostInfo.setSourceCostCode(old.getCostCode());
            newSettlementCostInfo.setCorrectionTime(new Date());
            newSettlementCostInfo.setCorrectionFlag(0);

            newSettlementCostInfo.setCorrectionOpType(1);
            newSettlementCostInfo.setCorrectionUser(correction.getCorrectionUser());
            newSettlementCostInfo.setCorrectionRemark(correction.getCause());

            //清除确认信息
            newSettlementCostInfo.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
            newSettlementCostInfo.setConfirmUser(null);
            newSettlementCostInfo.setConfirmTime(null);
            newSettlementCostInfo.setConfirmGrantTime(null);
            newSettlementCostInfo.setDocumentCode(null);
            newSettlementCostInfo.setAutoCostCode(null);
            newSettlementCostInfo.setCostSettlementCycle(null);

            //推荐人
            newSettlementCostInfo.setOwnerCode(Objects.isNull(referrerEntity.getReferrerCode())?"":referrerEntity.getReferrerCode());
            newSettlementCostInfo.setOwnerOrgCode(referrerEntity.getBranchCode());
            newSettlementCostInfo.setOwnerThirdCode(Objects.isNull(referrerEntity.getReferrerWno())?"":referrerEntity.getReferrerWno());
            newSettlementCostInfo.setOwnerThirdOrg(referrerEntity.getReferrerOgrCode());


            newCostList.add(newSettlementCostInfo);
        });

        log.info("correctionOldIds={}",correctionOldIds);
        log.info("【手动冲正推荐人】生成新记录={}",newCostList);

        //入库操作
        settlementCostCorrectionService.saveCostCommission(costCorrectionDto);
    }

    /**
     * 验证入参
     * @param manualCorrectionCost
     */
    public void validateParam(ManualCorrectionCost manualCorrectionCost){

        //签名验证 todo 暂时不做修改项验签
        try {
            String sign = SecurityUtil.signMD5(manualCorrectionCost.getSource(), manualCorrectionCost.getCorrectionUser());
            log.info("手工冲正生成的签名是：{}",sign);
            if (!Objects.equals(sign, manualCorrectionCost.getSign())) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("验签失败"));
            }
        }catch (Exception e){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("签名生成异常"));
        }


        if(Objects.isNull(manualCorrectionCost)){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("冲正参数为空"));
        }
        if(StringUtils.isBlank(manualCorrectionCost.getCostCode())){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("冲正基础佣金编号为空"));
        }
        if(manualCorrectionCost.getOnlyCorrection() == 0 && Objects.isNull(manualCorrectionCost.getNewCostInfo())){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("冲正新记录为空"));
        }
    }

    /**
     * 验证业务数据
     * @param correction
     * @param oldEntity
     */
    public void validateBusinessData(ManualCorrectionCost correction,SettlementCostInfoEntity oldEntity){
        if(Objects.isNull(oldEntity)){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("单据编号{}对应的佣金信息不存在，请核对", correction.getCostCode())));
        }
        if(Objects.equals(oldEntity.getCorrectionFlag(),1)){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("单据编号{}对应记录已冲正", correction.getCostCode())));
        }
        //验证保单号与批单号
        if(!Objects.equals(oldEntity.getPolicyNo(),correction.getNewCostInfo().getPolicyNo())){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("单据编号{}对应的保单号{}与传入的保单号{}不一致", correction.getCostCode(),oldEntity.getPremium(),correction.getNewCostInfo().getPolicyNo())));
        }
        String oldEnder = StringUtils.isBlank(oldEntity.getEndorsementNo())?"":oldEntity.getEndorsementNo();
        String newEnder = StringUtils.isBlank(correction.getNewCostInfo().getEndorsementNo())?"":correction.getNewCostInfo().getEndorsementNo();
        if(!Objects.equals(oldEnder,newEnder)){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("单据编号{}对应的批单号{}与传入的批单号{}不一致", correction.getCostCode(),oldEntity.getEndorsementNo(),correction.getNewCostInfo().getEndorsementNo())));
        }

    }

    private SettlementCostInfoEntity builderNewCostInfoByManualCorrectionInfo(SettlementCostPolicyInfoEntity policyInfo,SettlementCostInfoEntity oldEntity,ManualCorrectionCost correction){
        SettlementCostInfoEntity newSettlementCostInfo = new SettlementCostInfoEntity();
        BeanUtils.copyProperties(oldEntity,newSettlementCostInfo);

        BeanObjUtils.copyPropertiesIgnoreNull(correction.getNewCostInfo(), newSettlementCostInfo);

        newSettlementCostInfo.setId(null);
        if(StringUtils.isNotBlank(correction.getNewCostInfo().getPolicyNo())) {
            newSettlementCostInfo.setPolicyNo(correction.getNewCostInfo().getPolicyNo());
            newSettlementCostInfo.setCompanyPolicyNo(correction.getNewCostInfo().getPolicyNo());
        }
        newSettlementCostInfo.setCostCode(PolicySettlementUtils.createCodeLastNumber("CC"));
        //记账时间处理
        newSettlementCostInfo.setSettlementTime(new Date());
        newSettlementCostInfo.setSettlementDate(newSettlementCostInfo.getSettlementTime());

        //事件编号
        newSettlementCostInfo.setEventSourceCode(correction.getCostCode());

        //事件信息
        newSettlementCostInfo.setSettlementEventCode(MANUAL_CORRECTION.getEventCode());
        newSettlementCostInfo.setSettlementEventDesc(MANUAL_CORRECTION.getEventDesc());
        //如果修改了保费和费率，则为特殊冲正，且不可变标志为设置为1(不可变)，要变只能通过手工冲正的方式处理
        if(correction.getModifyCalcParam()!=null && correction.getModifyCalcParam() == 1){//特殊冲正
            newSettlementCostInfo.setSettlementGenerateType(SettlementGenerateTypeEnum.SPECIAL_MANUAL_CORRECTION.getCode());
            newSettlementCostInfo.setImmutableFlag(1);
        }else{
            newSettlementCostInfo.setSettlementGenerateType(SettlementGenerateTypeEnum.COMMON_MANUAL_CORRECTION.getCode());
        }
        //冲正金额
        log.info("开始冲正金额");
        if(correction.getNewCostInfo().getPremium()!=null){
            newSettlementCostInfo.setPremium(correction.getNewCostInfo().getPremium());

            if(correction.getNewCostInfo().getBusinessPremium()!=null){
                newSettlementCostInfo.setBusinessPremium(correction.getNewCostInfo().getBusinessPremium());
            }else{
                newSettlementCostInfo.setBusinessPremium(correction.getNewCostInfo().getPremium());
            }
            if(correction.getNewCostInfo().getIsSurrender() == 1){
                newSettlementCostInfo.setSurrenderAmount(correction.getNewCostInfo().getPremium());
            }
            //计算折算保费
            newSettlementCostInfo.setDiscountPremium(ReconcileBaseHelper.calcDiscountPremium(newSettlementCostInfo.getPolicyNo(), newSettlementCostInfo.getProductCode(),
                    newSettlementCostInfo.getInsuredPolicyAge(), PolicyProductTypeEnum.getProdTypeEnum(policyInfo.getPolicyProductType()),
                    newSettlementCostInfo.getLongShortFlag(), newSettlementCostInfo.getBusinessPremium(), newSettlementCostInfo.getPeriodType(),
                    newSettlementCostInfo.getPaymentPeriodType(), newSettlementCostInfo.getPaymentPeriod()));

        }
        if(correction.getNewCostInfo().getCostRate()!=null){
            newSettlementCostInfo.setCostRate(correction.getNewCostInfo().getCostRate());
        } else if(correction.getNewCostInfo().getCostAmount()!=null){
            newSettlementCostInfo.setCostRate(correction.getNewCostInfo().getCostAmount().divide(newSettlementCostInfo.getPremium(),2, BigDecimal.ROUND_HALF_UP));
        } else {
            newSettlementCostInfo.setCostRate(oldEntity.getCostRate());
        }
        if(correction.getNewCostInfo().getCostAmount()!=null){
            newSettlementCostInfo.setCostAmount(correction.getNewCostInfo().getCostAmount());
        }else{
            newSettlementCostInfo.setCostAmount(newSettlementCostInfo.getPremium().multiply(newSettlementCostInfo.getCostRate()).setScale(2,BigDecimal.ROUND_HALF_UP));
        }
        newSettlementCostInfo.setGrantAmount(newSettlementCostInfo.getCostAmount().multiply(newSettlementCostInfo.getGrantRate()).divide(new BigDecimal("100"),2,BigDecimal.ROUND_HALF_UP));
        log.info("结束冲正金额");
        //是否冲正
        //业务记账时间处理
        newSettlementCostInfo.setBusinessAccountTime(getCorrectionBusinessAccountTime(oldEntity.getBusinessAccountTime()));
        newSettlementCostInfo.setSourceCostCode(oldEntity.getCostCode());
        newSettlementCostInfo.setCorrectionTime(new Date());
        newSettlementCostInfo.setCorrectionFlag(0);

        newSettlementCostInfo.setCorrectionOpType(1);
        newSettlementCostInfo.setCorrectionUser(correction.getCorrectionUser());
        newSettlementCostInfo.setCorrectionRemark(correction.getCause());

        //清除确认信息
        newSettlementCostInfo.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
        newSettlementCostInfo.setConfirmUser(null);
        newSettlementCostInfo.setConfirmTime(null);
        newSettlementCostInfo.setConfirmGrantTime(null);
        newSettlementCostInfo.setDocumentCode(null);
        newSettlementCostInfo.setAutoCostCode(null);
        newSettlementCostInfo.setCostSettlementCycle(null);
        return newSettlementCostInfo;
    }



    private List<SettlementCostInfoEntity> builderNewCostInfoRecalCostByManualCorrectionInfo(SettlementCostPolicyInfoEntity policyInfo,SettlementCostInfoEntity oldEntity,ManualCorrectionCost correction){

        List<SettlementCostInfoEntity> resultList = new ArrayList<>();
        PolicyProductPremInput input = builderBasicCommissionConfigParamByOldCostInfo(policyInfo, oldEntity, oldEntity.getOwnerChannelCode());
        List<CostBasicCommissionConfigDto> configList =  commissionConfigService.getCostBasicCommissionConfig(oldEntity.getPolicyNo(),policyInfo.getPolicyProductType(),input);
        if (CollectionUtils.isEmpty(configList)) {
            log.warn("未获取到佣金配置-{}", JSON.toJSONString(input));
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("未获取到佣金配置-{}", policyInfo.getPolicyNo())));
        }
        for (CostBasicCommissionConfigDto config : configList) {

            if(config.getIsCustomYearRate() == 1){
                getPolicyCommissionConfig(policyInfo.getPolicyNo(),oldEntity.getEndorsementNo(),
                                          oldEntity.getRenewalYear(),oldEntity.getRenewalPeriod(), config);
            }

            SettlementCostInfoEntity newSettlementCostInfo = new SettlementCostInfoEntity();
            BeanUtils.copyProperties(oldEntity,newSettlementCostInfo);

            BeanObjUtils.copyPropertiesIgnoreNull(correction.getNewCostInfo(), newSettlementCostInfo);

            newSettlementCostInfo.setId(null);
            if(StringUtils.isNotBlank(correction.getNewCostInfo().getPolicyNo())) {
                newSettlementCostInfo.setPolicyNo(correction.getNewCostInfo().getPolicyNo());
                newSettlementCostInfo.setCompanyPolicyNo(correction.getNewCostInfo().getPolicyNo());
            }
            newSettlementCostInfo.setCostCode(PolicySettlementUtils.createCodeLastNumber("CC"));
            //记账时间处理
            newSettlementCostInfo.setSettlementTime(new Date());
            newSettlementCostInfo.setSettlementDate(newSettlementCostInfo.getSettlementTime());

            //事件编号
            newSettlementCostInfo.setEventSourceCode(correction.getCostCode());

            //事件信息
            newSettlementCostInfo.setSettlementEventCode(MANUAL_CORRECTION.getEventCode());
            newSettlementCostInfo.setSettlementEventDesc(MANUAL_CORRECTION.getEventDesc());


            //佣金信息
            newSettlementCostInfo.setSingleProposeFlag(config.getIsCustomYearRate());
            newSettlementCostInfo.setSettlementInstitution(config.getSettlementCompanyCode());
            newSettlementCostInfo.setSettlementInstitutionName(config.getSettlementCompanyName());
            newSettlementCostInfo.setCostRate(config.getYearRate());
            newSettlementCostInfo.setCostConfigKey(config.getPremCode());
            newSettlementCostInfo.setCostActualRate(BigDecimalUtils.mul(oldEntity.getCostDivideRate().doubleValue(), config.getYearRate().doubleValue()));

            //如果修改了保费和费率，则为特殊冲正，且不可变标志为设置为1(不可变)，要变只能通过手工冲正的方式处理
            if(correction.getModifyCalcParam()!=null && correction.getModifyCalcParam() == 1){//特殊冲正
                newSettlementCostInfo.setSettlementGenerateType(SettlementGenerateTypeEnum.SPECIAL_MANUAL_CORRECTION.getCode());
                newSettlementCostInfo.setImmutableFlag(1);
            }else{
                newSettlementCostInfo.setSettlementGenerateType(SettlementGenerateTypeEnum.COMMON_MANUAL_CORRECTION.getCode());
            }
            //冲正金额
            log.info("开始冲正金额");
            if(correction.getNewCostInfo().getPremium()!=null){
                newSettlementCostInfo.setPremium(correction.getNewCostInfo().getPremium());

                if(correction.getNewCostInfo().getBusinessPremium()!=null){
                    newSettlementCostInfo.setBusinessPremium(correction.getNewCostInfo().getBusinessPremium());
                }else{
                    newSettlementCostInfo.setBusinessPremium(correction.getNewCostInfo().getPremium());
                }
                if(correction.getNewCostInfo().getIsSurrender() == 1){
                    newSettlementCostInfo.setSurrenderAmount(correction.getNewCostInfo().getPremium());
                }
                //计算折算保费
                newSettlementCostInfo.setDiscountPremium(ReconcileBaseHelper.calcDiscountPremium(newSettlementCostInfo.getPolicyNo(), newSettlementCostInfo.getProductCode(),
                                                                                                 newSettlementCostInfo.getInsuredPolicyAge(), PolicyProductTypeEnum.getProdTypeEnum(policyInfo.getPolicyProductType()),
                                                                                                 newSettlementCostInfo.getLongShortFlag(), newSettlementCostInfo.getBusinessPremium(), newSettlementCostInfo.getPeriodType(),
                                                                                                 newSettlementCostInfo.getPaymentPeriodType(), newSettlementCostInfo.getPaymentPeriod()));

            }
//            if(correction.getNewCostInfo().getCostRate()!=null){
//                newSettlementCostInfo.setCostRate(correction.getNewCostInfo().getCostRate());
//            } else if(correction.getNewCostInfo().getCostAmount()!=null){
//                newSettlementCostInfo.setCostRate(correction.getNewCostInfo().getCostAmount().divide(newSettlementCostInfo.getPremium(),2, BigDecimal.ROUND_HALF_UP));
//            } else {
//                newSettlementCostInfo.setCostRate(oldEntity.getCostRate());
//            }
            if(correction.getNewCostInfo().getCostAmount()!=null){
                newSettlementCostInfo.setCostAmount(correction.getNewCostInfo().getCostAmount());
            }else{
                newSettlementCostInfo.setCostAmount(newSettlementCostInfo.getPremium().multiply(newSettlementCostInfo.getCostRate()).setScale(2,BigDecimal.ROUND_HALF_UP));
            }
            newSettlementCostInfo.setGrantAmount(newSettlementCostInfo.getCostAmount().multiply(newSettlementCostInfo.getGrantRate()).divide(new BigDecimal("100"),2,BigDecimal.ROUND_HALF_UP));
            log.info("结束冲正金额");
            //是否冲正
            //业务记账时间处理
            newSettlementCostInfo.setBusinessAccountTime(getCorrectionBusinessAccountTime(oldEntity.getBusinessAccountTime()));
            newSettlementCostInfo.setSourceCostCode(oldEntity.getCostCode());
            newSettlementCostInfo.setCorrectionTime(new Date());
            newSettlementCostInfo.setCorrectionFlag(0);

            newSettlementCostInfo.setCorrectionOpType(1);
            newSettlementCostInfo.setCorrectionUser(correction.getCorrectionUser());
            newSettlementCostInfo.setCorrectionRemark(correction.getCause());

            //清除确认信息
            newSettlementCostInfo.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
            newSettlementCostInfo.setConfirmUser(null);
            newSettlementCostInfo.setConfirmTime(null);
            newSettlementCostInfo.setConfirmGrantTime(null);
            newSettlementCostInfo.setDocumentCode(null);
            newSettlementCostInfo.setAutoCostCode(null);
            newSettlementCostInfo.setCostSettlementCycle(null);
            resultList.add(newSettlementCostInfo);

        }
        return resultList;
    }






    public void manualModifyCostInfo(List<ManualCostInfoDto> dtos) {
        if(CollectionUtils.isEmpty(dtos)){
            return ;
        }
        for(ManualCostInfoDto m : dtos){
            if(StringUtils.isBlank(m.getCostCode())){
                continue;
            }
            if(Objects.equals(m.getModType(),1)) {
                settlementCostInfoService.lambdaUpdate()
                        .set(StringUtils.isNotBlank(m.getOwnerChannelCode()),SettlementCostInfoEntity::getOwnerChannelCode,m.getOwnerChannelCode())
                        .set(SettlementCostInfoEntity::getOwnerThirdCode,m.getOwnerThirdCode())
                        .set(SettlementCostInfoEntity::getOwnerCode,m.getOwnerCode())
                        .set(SettlementCostInfoEntity::getOwnerThirdOrg,m.getOwnerThirdOrg())
                        .set(SettlementCostInfoEntity::getOwnerOrgCode,m.getOwnerOrgCode())
                        .eq(SettlementCostInfoEntity::getCostCode,m.getCostCode())
                        .update();
            }else if(Objects.equals(m.getModType(),0)){
                settlementCostInfoService.lambdaUpdate()
                        .set(SettlementCostInfoEntity::getBusinessAccountTime,m.getBusinessAccountTime())
                        .eq(SettlementCostInfoEntity::getCostCode,m.getCostCode())
                        .update();
            }
        }
    }

    public void manualProductChange(ManualProductChange change){
        List<SettlementCostInfoEntity> oldList =  settlementCostInfoService.listUnCorrectionByContractCodeAndProductCodes(change.getContractCode(), Arrays.asList(change.getOldProductCode()));

        Map<String, ProductBase> productMap =
                settlementCostProcessService.mapProductBaseByProductCodes(Arrays.asList(change.getNewProductCode()));
        SettlementCostPolicyInfoEntity costPolicy =
                settlementCostPolicyInfoService.getCostPolicyInfoEntityByContractCode(change.getContractCode());
        List<Integer> correctionOldIds = Lists.newArrayList();
        Date newBusinessTime = new Date();
        List<SettlementCostInfoEntity> newCostList = Lists.newArrayList();
        oldList.stream().forEach(old->{
            correctionOldIds.add(old.getId());

            SettlementCostInfoEntity bean = new SettlementCostInfoEntity();
            BeanUtils.copyProperties(old, bean);
            bean.setId(null);
            bean.setCostCode(PolicySettlementUtils.createCodeLastNumber("CC"));
            //记账时间处理
            bean.setSettlementTime(new Date());
            bean.setSettlementDate(bean.getSettlementTime());
            //事件编号
            bean.setEventSourceCode(old.getCostCode());
            bean.setSettlementEventCode(SettlementEventTypeEnum.POLICY_PRODUCT_CHANGE.getEventCode());
            bean.setSettlementEventDesc(SettlementEventTypeEnum.POLICY_PRODUCT_CHANGE.getEventDesc());
            bean.setInitialEventCode(old.getInitialEventCode());

            if(productMap.get(change.getNewProductCode()).getMainProductFlag() == 1){
                //修改settlement_cost_policy_info表中的主险信息
                ProductBase productBase = productMap.get(change.getNewProductCode());
                costPolicy.setMainProductCode(change.getNewProductCode());
                costPolicy.setMainProductName(change.getNewProductName());
                costPolicy.setLongShortFlag(productBase.getLongShortFlag());
                //costPolicy.setPolicyProductType(productBase.getpro());

            }



            bean.setCommissionType(CommissionTypeEnum.COMMON.getCode());
            //科目信息与被冲正记录保持一直
            //bean.setSettlementSubjectCode(subjectEnum.getCode());
            //bean.setSettlementSubjectName(subjectEnum.getName());
            bean.setSettlementGenerateType(1);
            //是否冲正

            //业务记账时间处理
            bean.setBusinessAccountTime(getCorrectionBusinessAccountTime(old.getBusinessAccountTime()));
            bean.setSourceCostCode(old.getCostCode());
            bean.setCorrectionTime(new Date());
            bean.setCorrectionFlag(1);
            bean.setCorrectionUser(change.getOpUser());
            bean.setCorrectionOpType(1);
            bean.setCorrectionRemark(change.getRemark());

            //清除确认信息
            bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
            bean.setConfirmUser(null);
            bean.setConfirmTime(null);
            bean.setConfirmGrantTime(null);
            bean.setDocumentCode(null);
            bean.setAutoCostCode(null);
            bean.setCostSettlementCycle(null);
            //冲正金额字段
            bean.setPremium(old.getPremium().negate());
            bean.setBusinessPremium(old.getBusinessPremium().negate());
            if(old.getDiscountPremium()!=null) {
                bean.setDiscountPremium(old.getDiscountPremium().negate());
            }
            if(bean.getSurrenderAmount()!=null) {
                bean.setSurrenderAmount(bean.getSurrenderAmount().negate());
            }
            bean.setCostAmount(old.getCostAmount().negate());
            bean.setGrantAmount(old.getGrantAmount().negate());

            newCostList.add(bean);



            PolicyProductPremInput input = new PolicyProductPremInput();
            input.setChannelCode(old.getOwnerChannelCode());
            input.setPolicyNo(old.getPolicyNo());
            input.setMainProductCode(change.getMainProductCode());
            input.setProductCode(change.getNewProductCode());
            input.setPlantCode(change.getNewPlanCode());
            input.setInsuredPeriodType(old.getInsuredPeriodType());
            input.setInsuredPeriod(old.getInsuredPeriod());
            input.setPeriodType(old.getPeriodType());
            input.setPaymentPeriodType(old.getPaymentPeriodType());
            input.setPaymentPeriod(old.getPaymentPeriod());
            input.setInsuranceType(old.getInsuranceType());
            //input.setApprovedTime(policyInfo.getApprovedTime());
            //佣金配置需要根据历史记录的配置时间去获取，不能直接用保单投保时间获取（续期与增减员就不是）
            input.setApprovedTime(old.getCostConfigMatchTime());
            input.setRenewalYear(1);
            input.setRenewalPeriod(1);
            input.setApplicantGender(costPolicy.getApplicantGender());
            input.setApplicantBirthday(costPolicy.getApplicantBirthday());

            input.setSalesType(costPolicy.getSalesType());
            // 是否自保件
            input.setSelfPreservation(costPolicy.getSelfPreservation());

            List<CostBasicCommissionConfigDto>  configList =  commissionConfigService.getCostBasicCommissionConfig(costPolicy.getPolicyNo(),costPolicy.getPolicyProductType(),input);

            for (CostBasicCommissionConfigDto config : configList) {
                SettlementCostInfoEntity newBean = new SettlementCostInfoEntity();
                BeanUtils.copyProperties(old, newBean);
                newBean.setId(null);
                newBean.setCostCode(PolicySettlementUtils.createCodeLastNumber("CC"));

                //事件编号
                newBean.setEventSourceCode(old.getCostCode());
                newBean.setSettlementEventCode(SettlementEventTypeEnum.POLICY_PRODUCT_CHANGE.getEventCode());
                newBean.setSettlementEventDesc(SettlementEventTypeEnum.POLICY_PRODUCT_CHANGE.getEventDesc());
                //记账时间
                newBean.setSettlementTime(new Date());
                newBean.setSettlementDate(newBean.getSettlementTime());
                newBean.setBusinessAccountTime(getCorrectionBusinessAccountTime(old.getBusinessAccountTime()));
                newBean.setSourceCostCode(old.getCostCode());
                //清除确认信息
                newBean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                newBean.setConfirmUser(null);
                newBean.setConfirmTime(null);
                newBean.setConfirmGrantTime(null);
                newBean.setDocumentCode(null);
                newBean.setAutoCostCode(null);
                newBean.setCostSettlementCycle(null);

                //佣金信息
                calcBasicCommission(costPolicy.getPolicyNo(), config, newBean);
                //确认信息
                newBean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());

                //险种信息
                ProtocolInsuranceProductInfoOut insuranceProductInfo = ProtocolBaseHelper.queryProtocolInsuranceProductInfoByProductCode(change.getNewProductCode(),0);
                if (insuranceProductInfo != null) {
                    // 协议产品信息
                    newBean.setProtocolProductCode(insuranceProductInfo.getInsuranceProductCode());
                    newBean.setProtocolProductName(insuranceProductInfo.getInsuranceProductName());
                }
                ProductBase productBase = productMap.get(change.getNewProductCode());
                newBean.setProductType(productBase.getProductType());
                newBean.setProductGroup(productBase.getProductGroup());
                newBean.setLevel2Code(productBase.getLevel2Code());
                newBean.setLevel3Code(productBase.getLevel3Code());
                newBean.setLongShortFlag(productBase.getLongShortFlag());
                newBean.setProductChannel(productBase.getProductChannel());
                newBean.setProductCode(change.getNewProductCode());
                newBean.setProductName(change.getNewProductName());
                newBean.setPlanCode(change.getNewPlanCode());
                newBean.setPlanName(change.getNewPlanName());
                newBean.setAgriculturalMachineryFlag(productBase.getAgriculturalMachineryFlag());
                //折算保费
                builderDiscountPremium(old.getPolicyNo(),costPolicy.getPolicyProductType(),newBean);

                newCostList.add(newBean);
            }

        });

        CostCorrectionDto dto = CostCorrectionDto.builder()
                .correctionOldIds(correctionOldIds)
                .newCostList(newCostList)
                .policy(costPolicy)
                .build();


        //处理农保员工在职状态
        settlementCostOwnerService.handlerOwnerThirdStatus(costPolicy,dto.getNewCostList());
        settlementCostCorrectionService.saveCostCommission(dto);
    }


}
