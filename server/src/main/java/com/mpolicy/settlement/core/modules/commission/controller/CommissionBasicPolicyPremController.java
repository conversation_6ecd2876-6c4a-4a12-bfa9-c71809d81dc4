package com.mpolicy.settlement.core.modules.commission.controller;

import com.mpolicy.common.result.Result;
import com.mpolicy.settlement.core.common.commission.dto.CommissionBasicPolicyPrem;
import com.mpolicy.settlement.core.modules.commission.service.CommissionManageService;
import com.mpolicy.web.common.annotation.PassToken;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 基础佣金费率-一单一议
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 16:08:41
 */
@RestController
@RequestMapping("settlement/commission")
@Api(tags = "基础佣金费率-一单一议")
@Slf4j
public class CommissionBasicPolicyPremController {

    @Autowired
    private CommissionManageService commissionManageService;




    @ApiOperation(value = "从保单中心批量同步一单一议费率信息", notes = "从保单中心批量同步一单一议费率信息")
    @PostMapping("/syncPolicyPrem")
    @PassToken
    public Result<String> syncPolicyPremFromPolicyCenter(@RequestBody List<CommissionBasicPolicyPrem> policyPremList,
                                                     @RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员", example = "张三")String userName ){
        log.info("从保单中心批量同步一单一议费率信息个数:{}", CollectionUtils.isEmpty(policyPremList)?0:policyPremList.size());
        commissionManageService.doPolicyPremHandler(policyPremList,userName);
        return Result.success("success");
    }
}
