package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.policy.common.ep.policy.EpInsuredInfoVo;
import com.mpolicy.policy.common.ep.policy.EpPersonalProductInfoVo;
import com.mpolicy.policy.common.ep.policy.EpProductInfoVo;
import com.mpolicy.product.common.base.ProductBase;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.ConfirmStatusEnum;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.EpPreserveProductDto;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.EpPreserveSurrenderInsuredDto;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.EpPreserveSurrenderProductDto;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.PolicyPreservationDetailDto;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.CostBasicCommissionConfigDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.ProductStatusEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyProductPremInput;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementSpecialDeductionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
@Slf4j
@Service("settlementSpecialDeductionService")
public class SettlementSpecialDeductionServiceImpl extends SettlementCostProcessServiceImpl implements SettlementSpecialDeductionService {
    /**
     * 特殊补扣款事件处理
     * @param eventJob
     * @param subjectEnum
     * @param eventType
     * @param productMap
     * @param policyInfo
     * @param preservationDetail
     */
    public List<SettlementCostInfoEntity> builderSpecialDeductionByInsuredInfo(SettlementEventJobEntity eventJob,
                                                                               CostSubjectEnum subjectEnum,
                                                                               SettlementEventTypeEnum eventType,
                                                                               Map<String, ProductBase> productMap,
                                                                               EpContractInfoVo policyInfo,
                                                                               PolicyPreservationDetailDto preservationDetail,List<EpPreserveSurrenderInsuredDto> surrenderDetails) {
        log.info("支出端-基础佣金--根据被保人、险种计算【短险】特殊补扣款事件");
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();

        //List<EpPreserveSurrenderInsuredDto> surrenderDetails = preservationDetail.getPolicySupplementDetail().getInsuredList();
        Integer insuranceType = getInsuranceType(eventType, policyInfo.getSourcePolicyNo());

        for (int i = 0; i < surrenderDetails.size(); i++) {
            EpPreserveSurrenderInsuredDto dto = surrenderDetails.get(i);
            Optional<EpInsuredInfoVo> opt = policyInfo.getInsuredInfoList().stream().filter(o -> Objects.equals(o.getInsuredCode(), dto.getInsuredCode())).findFirst();
            if (!opt.isPresent()) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-特殊补扣款事件处理人员明细明细在保单详情明细中不存在-保单号={},被保人：{}", eventJob.getEventBusinessCode(), dto.getInsuredCode())));
            }
            EpInsuredInfoVo insured = opt.get();

            List<EpPreserveProductDto> surrenderProductDtos = dto.getInsuredProductList();
            for (int j = 0; j < surrenderProductDtos.size(); j++) {
                EpPreserveProductDto productDto = surrenderProductDtos.get(j);

                Optional<EpPersonalProductInfoVo> productOpt = insured.getProductInfoList().stream().filter(p -> Objects.equals(p.getProductCode(), productDto.getProductCode())).findFirst();
                EpProductInfoVo product = productOpt.get();
                //计算退保保费
                BigDecimal businessPremium = productDto.getBusinessPremium();

                Integer renewalPeriod = preservationDetail.getRenewalPeriod()!=null?preservationDetail.getRenewalPeriod():preservationDetail.getRenewalTermPeriod();

                PolicyProductPremInput input = commissionConfigService.buildBasicCommissionConfigParam(policyInfo, insuranceType, product,insured.getInsuredPolicyAge(), renewalPeriod, renewalPeriod);
                List<CostBasicCommissionConfigDto> configList = commissionConfigService.getCostBasicCommissionConfig(policyInfo.getContractBaseInfo().getPolicyNo(),dto.getAddEndorsementNo(),policyInfo.getPolicyProductType(), input);

                for (CostBasicCommissionConfigDto config : configList) {
                    //初始化，生成科目信息
                    SettlementCostInfoEntity bean = initSettlementCostInfo(eventJob, subjectEnum, eventType, policyInfo, insuranceType);
                    //记录佣金匹配时间，便于冲正流程中获取
                    bean.setCostConfigMatchTime(input.getApprovedTime());
                    //记账时间处理
                    bean.setSettlementTime(getBasicSettlementTime(eventType, policyInfo));
                    bean.setSettlementDate(bean.getSettlementTime());
                    /*******设置相关保费 begin********/
                    //险种保费
                    bean.setProductPremium(product.getPremium());
                    //设置实际计算用的保费
                    bean.setPremium(businessPremium);
                    //险种状态为退保状态测是退保补退费，如果状态是承保状态则为承保补退费
                    if(ProductStatusEnum.isSurrenderStatus(productDto.getProductStatus())) {
                        bean.setSurrenderAmount(businessPremium);
                    }
                    bean.setBusinessPremium(businessPremium);
                    /********** end 设置保费 ********************/
                    bean.setBusinessAccountTime(getBusinessAccountTime(eventType, policyInfo, preservationDetail));
                    //佣金信息
                    calcBasicCommission(policyInfo.getContractBaseInfo().getPolicyNo(), config, bean);
                    //calcBasicCommission(param.getPolicyNo(), null, product, config, bean);
                    //初始化确认信息
                    bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                    //被保人信息,保单中心存在有被保人保单和无被保人保单的情况
                    if(Objects.nonNull(insured)) {
                        setInsuredInfo(insured, bean);
                    }
                    //险种信息
                    builderCostProductInfo(policyInfo, productMap, product, bean, renewalPeriod, renewalPeriod);
                    //保全信息
                    builderPreservationInfo(preservationDetail, bean.getSurrenderAmount(), bean);

                    //折算保费
                    builderDiscountPremium(policyInfo.getContractBaseInfo().getPolicyNo(), policyInfo.getPolicyProductType(), bean);


                    costInfoEntities.add(bean);




                }
            }
        }
        return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);

    }

    public List<SettlementCostInfoEntity> builderSpecialDeductionByProductInfo(SettlementEventJobEntity eventJob,
                                                                               CostSubjectEnum subjectEnum,
                                                                               SettlementEventTypeEnum eventType,
                                                                               Map<String, ProductBase> productMap,
                                                                               EpContractInfoVo policyInfo,
                                                                               PolicyPreservationDetailDto preservationDetail,List<EpPreserveSurrenderProductDto> surrenderDetails) {

        log.info("支出端-基础佣金--根据被保人、险种计算【短险】特殊补扣款事件");
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();
        Integer insuranceType = getInsuranceType(eventType, policyInfo.getSourcePolicyNo());
        //List<EpPreserveSurrenderProductDto> surrenderDetails = preservationDetail.getPolicySupplementDetail().getProductList();

        for (int i = 0; i < surrenderDetails.size(); i++) {
            EpPreserveSurrenderProductDto dto = surrenderDetails.get(i);
            Optional<EpProductInfoVo> opt = policyInfo.getProductInfoList().stream().filter(o -> Objects.equals(o.getProductCode(), dto.getProductCode())).findFirst();
            if (!opt.isPresent()) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-退保险种明细在保单详情明细中不存在-保单号={},险种编码：{}", eventJob.getEventBusinessCode(), dto.getProductCode())));
            }
            EpProductInfoVo product = opt.get();
            Integer renewalPeriod =preservationDetail.getRenewalPeriod()!=null?preservationDetail.getRenewalPeriod():preservationDetail.getRenewalTermPeriod();
            BigDecimal businessPremium = dto.getBusinessPremium();

            //按佣金配置进行退保
            log.info("支出端-基础佣金-历史佣金记录不存在，按佣金配置进行退保计算");
            PolicyProductPremInput input = commissionConfigService.buildBasicCommissionConfigParam(policyInfo, insuranceType, product,null, renewalPeriod, renewalPeriod);
            List<CostBasicCommissionConfigDto> configList = commissionConfigService.getCostBasicCommissionConfig(policyInfo.getContractBaseInfo().getPolicyNo(),policyInfo.getPolicyProductType(), input);
            for (CostBasicCommissionConfigDto config : configList) {
                //初始化，生成科目信息
                SettlementCostInfoEntity bean = initSettlementCostInfo(eventJob, subjectEnum, eventType, policyInfo, insuranceType);
                //记录佣金匹配时间，便于冲正流程中获取
                bean.setCostConfigMatchTime(input.getApprovedTime());
                //记账时间处理
                bean.setSettlementTime(getBasicSettlementTime(eventType, policyInfo));
                bean.setSettlementDate(bean.getSettlementTime());
                /*******设置相关保费 begin********/
                //险种保费
                bean.setProductPremium(product.getPremium());
                //设置实际计算用的保费
                bean.setPremium(businessPremium);
                //险种状态为退保状态测是退保补退费，如果状态是承保状态则为承保补退费
                if (ProductStatusEnum.isSurrenderStatus(dto.getProductStatus())) {
                    bean.setSurrenderAmount(businessPremium);
                }
                bean.setBusinessPremium(businessPremium);
                /********** end 设置保费 ********************/
                bean.setBusinessAccountTime(getBusinessAccountTime(eventType, policyInfo, preservationDetail));
                //佣金信息
                calcBasicCommission(policyInfo.getContractBaseInfo().getPolicyNo(), config, bean);

                //初始化确认信息
                bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());

                //险种信息
                builderCostProductInfo(policyInfo, productMap, product, bean, renewalPeriod, renewalPeriod);
                //保全信息
                builderPreservationInfo(preservationDetail, bean.getSurrenderAmount(), bean);
                //折算保费
                builderDiscountPremium(policyInfo.getContractBaseInfo().getPolicyNo(), policyInfo.getPolicyProductType(), bean);

                costInfoEntities.add(bean);


            }

        }

        return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);
    }
}
