package com.mpolicy.settlement.core.thirdpart.client;


import com.mpolicy.settlement.core.thirdpart.request.DistributionCommissionQuery;
import com.mpolicy.settlement.core.thirdpart.response.ChongHoResult;
import com.mpolicy.settlement.core.thirdpart.response.OrderDistributionCommissionListVo;
import com.mpolicy.settlement.core.thirdpart.response.OrderDistributionCommissionVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 农保-分销api
 *
 * <AUTHOR>
 */
@FeignClient(name = "insurance-service", url = "${chongho.feign.insurance}")
@Configuration
public interface InsuranceFeign {

    /**
     * 加佣明细分页查询
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "分销佣金明细分页查询")
    @PostMapping("/back/whaleCommission/query/getDistributionCommissionByPage")
    ChongHoResult<List<OrderDistributionCommissionVo>> getDistributionCommissionByPage(@RequestBody DistributionCommissionQuery query);


    /**
     * 更新结算同步状态
     *
     * @param ids
     */
    @ApiOperation(value = "更新结算同步状态")
    @PostMapping("/back/whaleCommission/update/updateSyncState")
    ChongHoResult<Void> updateSyncState(@RequestParam("ids") List<Long> ids);


    /**
     * 加佣明细分页查询
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "分销佣金明细分页查询")
    @PostMapping("/back/whaleCommission/query/getDistributionCommissionByPageV2")
    ChongHoResult<List<OrderDistributionCommissionListVo>> getDistributionCommissionByPageV2(@RequestBody DistributionCommissionQuery query);
}
