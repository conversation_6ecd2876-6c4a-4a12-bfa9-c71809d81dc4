package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.other.BigDecimalUtils;
import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.policy.common.ep.policy.EpInsuredInfoVo;
import com.mpolicy.policy.common.ep.policy.EpProductInfoVo;
import com.mpolicy.product.common.base.ProductBase;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.enums.SettlementExceptionEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.ConfirmStatusEnum;
import com.mpolicy.settlement.core.modules.govern.dto.proejct.ChangePolicyReferrerData;
import com.mpolicy.settlement.core.modules.govern.dto.proejct.EpChannelManagerVo;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.*;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.CostBasicCommissionConfigDto;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.CostCorrectionDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.OwnerTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyProductPremInput;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyProductPremResult;
import com.mpolicy.settlement.core.modules.reconcile.helper.ReconcileBaseHelper;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementCostCorrectionService;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementCostInfoService;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mpolicy.settlement.core.common.Constant.*;

/**
 * <AUTHOR>
 * @description 支出冲正服务
 * @date 2023/10/4 3:41 下午
 * @Version 1.0
 */
@Service
@Slf4j
public class SettlementCostCorrectionServiceImpl extends SettlementCostProcessServiceImpl implements SettlementCostCorrectionService {


    @Autowired
    protected SettlementCostInfoService settlementCostInfoService;

    /**
     * 费率变更冲正
     * @param eventJob
     * @param eventType
     * @param contactCodes
     * @param deleteKeys
     * @return
     */
    @Override
    public CostCorrectionDto builderConfigChangeCostInfo(SettlementEventJobEntity eventJob,
                                                         SettlementEventTypeEnum eventType,Date newBusinessTime,List<String> contactCodes,List<String> deleteKeys){

        List<SettlementCostInfoEntity> beforeAllList = settlementCostInfoService.listUnCorrectionPolicyCostInfoByCostConfigKeys(contactCodes,null);
        //todo 临时处理逻辑，新老切换这个月数据确认后，不能改以前的佣金配置,切换完以后，这段逻辑要去掉
        beforeAllList = beforeAllList.stream().filter(c->c.getBusinessAccountTime()!=null && c.getBusinessAccountTime().after(DateUtil.parseDateTime("2024-05-31 23:59:59"))).collect(Collectors.toList());
        List<SettlementCostInfoEntity> beforeList = beforeAllList.stream().filter(o->deleteKeys.contains(o.getCostConfigKey())).collect(Collectors.toList());
        List<String> contracts = beforeList.stream().map(SettlementCostInfoEntity::getContractCode).distinct().collect(Collectors.toList());
        List<SettlementCostPolicyInfoEntity> costPolicyList = settlementCostPolicyInfoService.listCostPolicyInfoEntityByContractCodes(contracts);
        return builderPremChangeCostInfo(eventJob,eventType,newBusinessTime,costPolicyList,beforeAllList,beforeList,"基础佣金费率配置变更");
    }
    /**
     * 一单一议汇率变更
     * @param eventJob
     * @param eventType
     * @return
     */
    @Override
    public CostCorrectionDto builderPolicyPremChangeCostInfo(SettlementEventJobEntity eventJob,
                                                             SettlementEventTypeEnum eventType,
                                                             Date newBusinessTime,
                                                             List<String> contactCodes,List<String> configKeys){

        /*List<SettlementCostPolicyInfoEntity> costPolicyList = settlementCostPolicyInfoService.listCostPolicyInfoEntityByPolicyNos(policyNos);

        List<String> contractCodes = costPolicyList.stream().map(SettlementCostPolicyInfoEntity::getPolicyNo).collect(Collectors.toList());
        List<SettlementCostInfoEntity> beforeList = settlementCostInfoService.listUnCorrectionCostInfoByContractCodes(contractCodes);*/
        //未冲正记录(包括已确认结算)
        /*List<SettlementCostInfoEntity> beforeList = settlementCostInfoService.listUnCorrectionCostInfoByPolicyNos(policyNos);
        List<String> contractCodes = beforeList.stream().map(SettlementCostInfoEntity::getContractCode).distinct().collect(Collectors.toList());
        List<SettlementCostPolicyInfoEntity> costPolicyList = settlementCostPolicyInfoService.listCostPolicyInfoEntityByContractCodes(contractCodes);*/
        List<SettlementCostInfoEntity> beforeAllList = settlementCostInfoService.listUnCorrectionPolicyCostInfoByCostConfigKeys(contactCodes,null);
        //todo 临时处理逻辑，新老切换这个月数据确认后，不能改以前的佣金配置,切换完以后，这段逻辑要去掉
        beforeAllList = beforeAllList.stream().filter(c->c.getBusinessAccountTime()!=null && c.getBusinessAccountTime().after(DateUtil.parseDateTime("2024-05-31 23:59:59"))).collect(Collectors.toList());
        List<SettlementCostInfoEntity> beforeList = beforeAllList.stream().filter(o->configKeys.contains(o.getCostConfigKey())).collect(Collectors.toList());
        List<String> contracts = beforeList.stream().map(SettlementCostInfoEntity::getContractCode).distinct().collect(Collectors.toList());
        List<SettlementCostPolicyInfoEntity> costPolicyList = settlementCostPolicyInfoService.listCostPolicyInfoEntityByContractCodes(contracts);


        return builderPremChangeCostInfo(eventJob,eventType,newBusinessTime,costPolicyList,beforeList,beforeList,"一单一议费率变更");
    }

    private CostCorrectionDto builderPremChangeCostInfo(SettlementEventJobEntity eventJob,
                                                        SettlementEventTypeEnum eventType,
                                                        Date newBusinessTime,
                                                        List<SettlementCostPolicyInfoEntity> costPolicyList,
                                                        List<SettlementCostInfoEntity> beforeAllList,List<SettlementCostInfoEntity> beforeList,String opDesc){
        CostCorrectionDto dto = new CostCorrectionDto();
        //获取这一分组的保单信息
        Map<String,SettlementCostPolicyInfoEntity> costPolicyMap = costPolicyList.stream().collect(Collectors.toMap(SettlementCostPolicyInfoEntity::getContractCode, Function.identity(),(key1, key2) -> key2));
        //按合同号分组
        Map<String,List<SettlementCostInfoEntity>> costAllMap = beforeAllList.stream()
                .collect(Collectors.groupingBy(SettlementCostInfoEntity::getContractCode));
        Map<String,List<SettlementCostInfoEntity>> costMap = beforeList.stream()
                .collect(Collectors.groupingBy(SettlementCostInfoEntity::getContractCode));
        //新的佣金记录
        List<SettlementCostInfoEntity> newList = Lists.newArrayList();

        //需要更新key的佣金记录(只记录id和变更后的key)
        List<SettlementCostInfoEntity> updateKeyList = Lists.newArrayList();

        //需要更新冲正标志的记录id
        List<Integer> correctionOldIds = Lists.newArrayList();

        Set<String> existKey = Sets.newHashSet();
        for(String key : costMap.keySet()){
            SettlementCostPolicyInfoEntity costPolicy = costPolicyMap.get(key);
            List<SettlementCostInfoEntity> costList = costMap.get(key);

            for(SettlementCostInfoEntity old : costList){
                //已经处理则
                String k = getConfigChangeRepeatKey(old);
                if(existKey.contains(k)){
                    continue;
                }
                PolicyProductPremInput input = builderBasicCommissionConfigParamByOldCostInfo(costPolicy, old,old.getOwnerChannelCode());
                //List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(costPolicy.getPolicyNo(), input);
                List<CostBasicCommissionConfigDto>  configList =  commissionConfigService.getCostBasicCommissionConfig(old.getPolicyNo(),costPolicy.getPolicyProductType(),input);
                //源记录的结算机构判断
                Optional<CostBasicCommissionConfigDto> configOpt = configList.stream().filter(c->Objects.equals(old.getSettlementInstitution(),c.getSettlementCompanyCode())).findFirst();
                //old的结算机构已不存在，需要对冲历史记录
                if(!configOpt.isPresent()){
                    correctionOldIds.add(old.getId());
                    newList.add(builderOffsetCostInfo(eventJob,eventType,old,newBusinessTime,SYSTEM_CORRECTION_USER,opDesc+"冲正(该"+old.getSettlementInstitution()+"结算机构已删除)",Boolean.TRUE));
                }else{
                    //如果是一单一议，则需要获取一下佣金费率设置到config中
                    CostBasicCommissionConfigDto config = configOpt.get();
                    /*if(config.getIsCustomYearRate() == 1){
                        getPolicyCommissionConfig(costPolicy.getPolicyNo(),old.getEndorsementNo(),
                                old.getRenewalYear(),old.getRenewalPeriod(),
                                config);
                    }*/
                    if (old.getCostRate().compareTo(config.getYearRate()) == 0 && !Objects.equals(config.getPremCode(),old.getCostConfigKey())) {
                        SettlementCostInfoEntity updateKey = new SettlementCostInfoEntity();
                        updateKey.setId(old.getId());
                        updateKey.setCostConfigKey(config.getPremCode());
                        updateKeyList.add(updateKey);
                    }  else if(old.getCostRate().compareTo(config.getYearRate()) != 0) {
                        log.info("合同编号{}愿记录id={}的历史佣金比例为{},新佣金比例为config.getYearRate={}",old.getContractCode(),old.getId(),old.getCostRate(),config.getYearRate());
                        correctionOldIds.add(old.getId());
                        newList.add(builderOffsetCostInfo(eventJob, eventType, old,newBusinessTime, SYSTEM_CORRECTION_USER, opDesc+"冲正", Boolean.TRUE));
                        newList.add(builderConfigChangeNewCostInfo(eventJob, eventType,newBusinessTime, old, config));

                    }
                }
                existKey.add(k);
                //要搞清楚是否存在新增的结算机构
                List<CostBasicCommissionConfigDto> cList = configList.stream().filter(c->!Objects.equals(old.getSettlementInstitution(),c.getSettlementCompanyCode())).collect(Collectors.toList());
                //佣金配置中存在的其他结算机构处理；(存在部分佣金)
                //List<SettlementCostInfoEntity> otherInstitutionList = costList.stream().filter(o ->
                List<SettlementCostInfoEntity> otherInstitutionList = costAllMap.get(key).stream().filter(o->
                        Objects.equals(o.getContractCode(), old.getContractCode())
                                && Objects.equals(o.getInitialEventCode(), old.getInitialEventCode())
                                && Objects.equals(o.getSettlementSubjectCode(), old.getSettlementSubjectCode())
                                && Objects.equals(o.getProductCode(), old.getProductCode())
                                && Objects.equals(o.getProductStatus(), old.getProductStatus())
                                && Objects.equals(o.getInsuredCode(), old.getInsuredCode())
                                && Objects.equals(o.getRenewalPeriod(), old.getRenewalPeriod())
                                && Objects.equals(o.getPreservationCode(), old.getPreservationCode())

                                && !Objects.equals(o.getSettlementInstitution(), old.getSettlementInstitution())
                ).collect(Collectors.toList());

                for(CostBasicCommissionConfigDto otherConfig : cList){
                    Optional<SettlementCostInfoEntity> opt = otherInstitutionList.stream().filter(p->Objects.equals(p.getSettlementInstitution(),otherConfig.getSettlementCompanyCode())).findFirst();
                    if(opt.isPresent()){
                        SettlementCostInfoEntity obj = opt.get();
                        //如果是一单一议，则需要获取一下佣金费率设置到config中
                        if(otherConfig.getIsCustomYearRate() == 1){
                            getPolicyCommissionConfig(costPolicy.getPolicyNo(),obj.getEndorsementNo(),
                                    obj.getRenewalYear(),obj.getRenewalPeriod(),otherConfig);
                        }
                        if(obj.getCostRate().compareTo(otherConfig.getYearRate())==0 && !Objects.equals(obj.getCostConfigKey(),otherConfig.getPremCode())){
                            SettlementCostInfoEntity updateKey = new SettlementCostInfoEntity();
                            updateKey.setId(obj.getId());
                            updateKey.setCostConfigKey(otherConfig.getPremCode());
                            updateKeyList.add(updateKey);
                        }else if(obj.getCostRate().compareTo(otherConfig.getYearRate())!=0){
                            log.info("合同编号{}愿记录id={}的历史佣金比例为{},新佣金比例为otherConfig.getYearRate={}",obj.getContractCode(),obj.getId(),obj.getCostRate(),otherConfig.getYearRate());
                            correctionOldIds.add(obj.getId());
                            newList.add(builderOffsetCostInfo(eventJob,eventType,obj,newBusinessTime,SYSTEM_CORRECTION_USER,opDesc+"冲正",Boolean.TRUE));
                            newList.add(builderConfigChangeNewCostInfo(eventJob,eventType,newBusinessTime,obj,otherConfig));
                        }
                        existKey.add(getConfigChangeRepeatKey(obj));
                    }else{
                        SettlementCostInfoEntity newCost = builderConfigChangeNewCostInfo(eventJob,eventType,newBusinessTime,old,otherConfig);
                        newList.add(newCost);
                        existKey.add(getConfigChangeRepeatKey(newCost));
                    }
                }

            }
        }
        dto.setNewCostList(newList);
        dto.setUpdateKeyList(updateKeyList);
        dto.setCorrectionOldIds(correctionOldIds);
        return dto;
    }

    private SettlementCostInfoEntity builderConfigChangeNewCostInfo(SettlementEventJobEntity eventJob,
                                                                    SettlementEventTypeEnum eventType,
                                                                    Date newBusinessTime,
                                                                    SettlementCostInfoEntity old,CostBasicCommissionConfigDto config){
        SettlementCostInfoEntity bean = new SettlementCostInfoEntity();
        BeanUtils.copyProperties(old,bean);
        bean.setId(null);
        bean.setCostCode(PolicySettlementUtils.createCodeLastNumber("CC"));
        //记账时间处理
        bean.setSettlementTime(new Date());
        bean.setSettlementDate(bean.getSettlementTime());
        bean.setBusinessAccountTime(getCorrectionBusinessAccountTime(old.getBusinessAccountTime(),newBusinessTime));
        //事件编号
        bean.setEventSourceCode(eventJob.getPushEventCode());

        //事件信息
        if(Objects.nonNull(eventType)) {
            bean.setSettlementEventCode(eventType.getEventCode());
            bean.setSettlementEventDesc(eventType.getEventDesc());

        }

        bean.setSettlementGenerateType(1);
        bean.setSourceCostCode(old.getCostCode());

        //冲正信息（冲正标志需设置为未冲正）
        bean.setCorrectionFlag(0);
        bean.setCorrectionUser(null);
        bean.setCorrectionOpType(null);
        bean.setCorrectionRemark(null);

        //清除确认信息
        cleanConfirmInfo(bean);

        //佣金信息
        bean.setSingleProposeFlag(config.getIsCustomYearRate());
        bean.setSettlementInstitution(config.getSettlementCompanyCode());
        bean.setSettlementInstitutionName(config.getSettlementCompanyName());
        bean.setCostRate(config.getYearRate());
        bean.setCostConfigKey(config.getPremCode());

        bean.setPremium(old.getPremium());
        bean.setCostType(old.getCostType());
        bean.setCostActivityCode(old.getCostActivityCode());
        bean.setCostDivideRate(old.getCostDivideRate());

        bean.setCostActualRate(BigDecimalUtils.mul(old.getCostDivideRate().doubleValue(), config.getYearRate().doubleValue()));
        BigDecimal commissionAmount = BigDecimalUtils.mul(bean.getPremium().doubleValue(), bean.getCostActualRate().doubleValue());
        bean.setCostAmount(commissionAmount);
        bean.setGrantRate(old.getGrantRate());
        bean.setGrantAmount(PolicySettlementUtils.calcGrantAmount(commissionAmount,old.getGrantRate()));

        return bean;
    }

    private String getConfigChangeRepeatKey(SettlementCostInfoEntity old){
        StringBuilder b = new StringBuilder();
        b.append(old.getContractCode())
                .append(old.getSettlementInstitution())
                //.append(old.getSettlementEventCode())
                .append(old.getInitialEventCode())
                .append(old.getSettlementSubjectCode())
                .append(old.getProductCode())
                .append(old.getProductStatus())
                .append(old.getInsuredCode()!=null?old.getInsuredCode():"")
                .append(old.getRenewalPeriod()!=null?old.getRenewalPeriod():"")
                .append(old.getPreservationCode()!=null?old.getPreservationCode():"");
        return b.toString();
    }


    /**
     * 初始推荐人变更
     * @param eventJob
     * @param eventType
     * @param costPolicy
     * @param preservationDetail
     * @return
     */
    @Override
    public CostCorrectionDto builderCustomerManagerChangeCostInfo(SettlementEventJobEntity eventJob,
                                                                  SettlementEventTypeEnum eventType,
                                                                  SettlementCostPolicyInfoEntity costPolicy,
                                                                  PolicyPreservationDetailDto preservationDetail){
        //这个地方不能用合同号与佣金归属人编号联合查询，因为查询的记录为空的情况下，不知道是没有算，还是记录为空
        List<SettlementCostInfoEntity> beforeAll = settlementCostInfoService.listUnCorrectionPolicyCostInfoByContractCode(preservationDetail.getContractCode());
        if(CollectionUtils.isEmpty(beforeAll)){
            return null;
        }
        //获取原始推推荐人
        String ownerCode = settlementCostOwnerService.getPreservationOwnerCodeByPreservationDetail(preservationDetail);
        log.info("【初始推荐人变更】--获取原始推荐人:{}",ownerCode);
        List<SettlementCostInfoEntity> newCostList = Lists.newArrayList();
        List<Integer> correctionOldIds = Lists.newArrayList();
        List<SettlementCostInfoEntity> personNewPolicyCosts = settlementCostInfoService.listPersonNewPolicyCostInfoByContractCode(preservationDetail.getContractCode());

        //2024-04-29 去掉初始推荐人为空的判断，分销单需要计算数据
        /*if(StringUtil.isBlank(ownerCode)){
            //throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-初始推荐人变更-保单号={},保全号={}, 根据保全记录获取推荐人为空", eventJob.getEventBusinessCode(),preservationDetail.getPreservationCode())));
        }else {
            String newChannelCode = preservationDetail.getAfterCustomerManager().getAfterChannelCode();

            //如果未冲正的新契约的推荐人信息与变更后的客户经理已经是一直了，就不需要变更了

            if (CollectionUtils.isNotEmpty(personNewPolicyCosts)) {
                if (Objects.equals(personNewPolicyCosts.get(0).getOwnerThirdCode(), preservationDetail.getAfterCustomerManager().getAfterCustomerManagerChannelCode())) {
                    log.warn("支出端-合同号{}的初始推荐人变更，变更后的推荐人{}与新契约推荐人{}一直", preservationDetail.getContractCode(), preservationDetail.getAfterCustomerManager().getAfterCustomerManagerChannelCode(), personNewPolicyCosts.get(0).getOwnerThirdCode());
                    throw new GlobalException(SettlementExceptionEnum.CORRECTION_AFTER_CUSTOMER_MANAGER_EXIST.getException(StrUtil.format("支出端-合同号{}的初始推荐人变更，变更后的推荐人{}与新契约推荐人{}一直", preservationDetail.getContractCode(), preservationDetail.getAfterCustomerManager().getAfterCustomerManagerChannelCode(), personNewPolicyCosts.get(0).getOwnerThirdCode())));
                }
            }

            doCustomerManagerChangeCostInfo(eventJob, eventType, costPolicy, beforeAll, preservationDetail.getPreservationCode(), null, ownerCode, newChannelCode, preservationDetail.getAfterCustomerManager(), newCostList, correctionOldIds);
        }*/
        String newChannelCode = preservationDetail.getAfterCustomerManager().getAfterChannelCode();
        log.info("【初始推荐人变更】--获取新推荐人:{}",newChannelCode);
        //如果未冲正的新契约的推荐人信息与变更后的客户经理已经是一直了，就不需要变更了
        if (CollectionUtils.isNotEmpty(personNewPolicyCosts)) {
            if (Objects.equals(personNewPolicyCosts.get(0).getOwnerThirdCode(), preservationDetail.getAfterCustomerManager().getAfterCustomerManagerChannelCode())) {
                log.warn("支出端-合同号{}的初始推荐人变更，变更后的推荐人{}与新契约推荐人{}一直", preservationDetail.getContractCode(), preservationDetail.getAfterCustomerManager().getAfterCustomerManagerChannelCode(), personNewPolicyCosts.get(0).getOwnerThirdCode());
                throw new GlobalException(SettlementExceptionEnum.CORRECTION_AFTER_CUSTOMER_MANAGER_EXIST.getException(StrUtil.format("支出端-合同号{}的初始推荐人变更，变更后的推荐人{}与新契约推荐人{}一直", preservationDetail.getContractCode(), preservationDetail.getAfterCustomerManager().getAfterCustomerManagerChannelCode(), personNewPolicyCosts.get(0).getOwnerThirdCode())));
            }
        }
        doCustomerManagerChangeCostInfo(eventJob, eventType, costPolicy, beforeAll, preservationDetail.getPreservationCode(), null, ownerCode, newChannelCode,preservationDetail.getInputTime(), preservationDetail.getAfterCustomerManager(), newCostList, correctionOldIds);
        CostCorrectionDto costCorrectionDto = CostCorrectionDto.builder()
                .correctionOldIds(correctionOldIds)
                .newCostList(newCostList)
                .build();

        return costCorrectionDto;
    }


    @Override
    public CostCorrectionDto builderChangeEventCustomerManagerChangeCostInfo(SettlementEventJobEntity eventJob,
                                                                             SettlementEventTypeEnum eventType,
                                                                             SettlementCostPolicyInfoEntity costPolicy,
                                                                             ChangePolicyReferrerData policyReferrerData){
        //这个地方不能用合同号与佣金归属人编号联合查询，因为查询的记录为空的情况下，不知道是没有算，还是记录为空
        List<SettlementCostInfoEntity> beforeAll = settlementCostInfoService.listUnCorrectionPolicyCostInfoByContractCode(policyReferrerData.getContractCode());
        if(CollectionUtils.isEmpty(beforeAll)){
            return null;
        }

        EpChannelManagerVo oldManager = policyReferrerData.getSourceReferrer();
        String ownerCode = oldManager.getCustomerManagerCode();

        if(StringUtil.isBlank(ownerCode)){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-初始推荐人变更-OaCode={}, 根据保全记录获取推荐人为空", policyReferrerData.getOaCode())));
        }
        EpChannelManagerVo newManager = policyReferrerData.getNewReferrer();
        String newChannelCode = newManager.getCustomerManagerChannelCode();

        List<SettlementCostInfoEntity> newCostList = Lists.newArrayList();
        List<Integer> correctionOldIds = Lists.newArrayList();

        //如果未冲正的新契约的推荐人信息与变更后的客户经理已经是一直了，就不需要变更了
        List<SettlementCostInfoEntity> personNewPolicyCosts =  settlementCostInfoService.listPersonNewPolicyCostInfoByContractCode(policyReferrerData.getContractCode());
        if(CollectionUtils.isNotEmpty(personNewPolicyCosts)){
            if(Objects.equals(personNewPolicyCosts.get(0).getOwnerThirdCode(), ownerCode)){
                log.warn("支出端-合同号{}的初始推荐人变更，变更后的推荐人{}与新契约推荐人{}一致", policyReferrerData.getContractCode(), policyReferrerData.getPolicyCode(), personNewPolicyCosts.get(0).getOwnerThirdCode());
                throw new GlobalException(SettlementExceptionEnum.CORRECTION_AFTER_CUSTOMER_MANAGER_EXIST.getException(StrUtil.format("支出端-合同号{}的初始推荐人变更，变更后的推荐人{}与新契约推荐人{}一直",policyReferrerData.getContractCode(), newManager.getCustomerManagerCode(),personNewPolicyCosts.get(0).getOwnerThirdCode())));
            }
        }


        PreservationCustomerChangeDto customerChangeDto = new PreservationCustomerChangeDto();

//        customerChangeDto.setAfterChannelCode(newManager.getChannelCode());
        customerChangeDto.setAfterCustomerManagerCode(newManager.getCustomerManagerCode());
        customerChangeDto.setAfterCustomerManagerChannelCode(newManager.getCustomerManagerChannelCode());
        customerChangeDto.setAfterCustomerManagerOrgCode(newManager.getCustomerManagerOrgCode());
        customerChangeDto.setAfterCustomerManagerChannelOrgCode(newManager.getCustomerManagerChannelOrgCode());
        customerChangeDto.setAfterCustomerManagerSupervisor(newManager.getCustomerManagerSupervisor());
//        customerChangeDto.setAfterPolicyChannelBranchCode(newManager.getBranchCode());
        //刷数保全事件没有业务操作时间，则直接用时间创建时间
        Date newBusinessTime = eventJob.getCreateTime();
        doCustomerManagerChangeCostInfo(eventJob,eventType, costPolicy,beforeAll, policyReferrerData.getOaCode(),null, ownerCode,newChannelCode,newBusinessTime, customerChangeDto, newCostList,correctionOldIds);

        return CostCorrectionDto.builder()
                .correctionOldIds(correctionOldIds)
                .newCostList(newCostList)
                .build();
    }
    private void doCustomerManagerChangeCostInfo(SettlementEventJobEntity eventJob,
                                                 SettlementEventTypeEnum eventType,
                                                 SettlementCostPolicyInfoEntity costPolicy,
                                                 List<SettlementCostInfoEntity> beforeAll,
                                                 String preservationCode,
                                                 String insuredCode,
                                                 String ownerCode,
                                                 String newChannelCode,
                                                 Date newBusinessTime,
                                                 PreservationCustomerChangeDto afterCustomerManager,
                                                 List<SettlementCostInfoEntity> newCostList,
                                                 List<Integer> correctionOldIds){
        List<SettlementCostInfoEntity> oldCostList = null;
        log.info("保全编号：{},佣金归属人编码：{}",preservationCode,ownerCode);
        if(StringUtils.isBlank(insuredCode)) {
            oldCostList = beforeAll.stream().filter(o -> Objects.equals(ownerCode, o.getOwnerCode())).collect(Collectors.toList());
        }else{
            oldCostList = beforeAll.stream().filter(o -> Objects.equals(ownerCode, o.getOwnerCode()) && Objects.equals(insuredCode,o.getInsuredCode())).collect(Collectors.toList());
        }
        if(CollectionUtils.isEmpty(oldCostList)){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-初始/分单推荐人变更/导入变更/续期管护经理变更-保单号={},保全号={}, 获取{}的佣金数据为空，请核查", eventJob.getEventBusinessCode(),preservationCode,ownerCode)));
        }

        //去重用，举例：新契约，zhnx渠道，存在两个结算账户的佣金记录，转成gdyw时，只有一个结算账户，这样两条历史记录只需要生成一条新的佣金记录
        Set<String> existSet = Sets.newHashSet();

        for(SettlementCostInfoEntity old : oldCostList){
            //将原始记录设置为已冲正
            old.setCorrectionFlag(1);
            correctionOldIds.add(old.getId());
            //生成源记录的对冲记录

            newCostList.add(builderOffsetCostInfo(eventJob,eventType,old,newBusinessTime,SYSTEM_CORRECTION_USER,"初始/分单渠道推荐人变更冲正",Boolean.TRUE));
            //生成正确的佣金记录,判断渠道是否前后是否一样，如果不一样则新记录需要重新获取佣金配置进行计算，一样则佣金信息不需要重算(发放金额与续期率有关的记录除外)
            String naturalKey = getNaturalKey(old);
            if(!existSet.contains(naturalKey)) {

                if (Objects.equals(newChannelCode, old.getOwnerChannelCode())) {

                    newCostList.add(builderCustomerManagerChangeNewCostInfo(eventJob, eventType, old,newBusinessTime, afterCustomerManager));
                } else {

                    PolicyProductPremInput input = builderBasicCommissionConfigParamByOldCostInfo(costPolicy, old,afterCustomerManager.getAfterChannelCode());
                    List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(costPolicy.getPolicyNo(), input);
                    for (PolicyProductPremResult config : configList) {
                        SettlementCostInfoEntity bean = builderNewCostInfo(eventJob, eventType, costPolicy.getPolicyNo(),costPolicy, old,newBusinessTime, config, afterCustomerManager);

                        newCostList.add(bean);
                    }
                    existSet.add(naturalKey);
                }
            }
        }
    }

    /**
     * 初始推荐人变更去重判断
     * @param old
     * @return
     */
    private String getNaturalKey(SettlementCostInfoEntity old){
        StringBuilder b = new StringBuilder();
        b.append(old.getContractCode())
                //.append(old.getSettlementInstitution())
                //.append(old.getSettlementEventCode())
                .append(old.getInitialEventCode())
                .append(old.getSettlementSubjectCode())
                .append(old.getProductCode())
                .append(old.getProductStatus())
                .append(old.getInsuredCode()!=null?old.getInsuredCode():"")
                .append(old.getRenewalPeriod()!=null?old.getRenewalPeriod():"")
                .append(old.getPreservationCode()!=null?old.getPreservationCode():"");
        return b.toString();
    }

    /**
     * 分单推荐人变更
     * @param eventJob
     * @param eventType
     * @param costPolicy
     * @param preservationDetail
     * @return
     */
    @Override
    public CostCorrectionDto builderSplitCustomerManagerChangeCostInfo(SettlementEventJobEntity eventJob,
                                                                       SettlementEventTypeEnum eventType,
                                                                       SettlementCostPolicyInfoEntity costPolicy,
                                                                       PolicyPreservationDetailDto preservationDetail){
        List<PreserveChannelReferrerChangeDto> oldManagers = preservationDetail.getChannelReferrerChangeList();
        List<String> insuredCodes = oldManagers.stream().map(PreserveChannelReferrerChangeDto::getInsuredCode).collect(Collectors.toList());

        List<SettlementCostInfoEntity> beforeAll = settlementCostInfoService.listUnCorrectionCostInfoByInsuredCodes(preservationDetail.getContractCode(),insuredCodes);
        if(CollectionUtils.isEmpty(beforeAll)){
            return null;
        }
        List<SettlementCostInfoEntity> newCostList = Lists.newArrayList();
        List<Integer> correctionOldIds = Lists.newArrayList();
        for(PreserveChannelReferrerChangeDto manager : oldManagers){
            String ownerCode = settlementCostOwnerService.getSplitPolicyOwnerCodeByPreservationDetail(manager);
            /*if(StringUtil.isBlank(ownerCode)){
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-分单推荐人变更-保单号={},保全号={}, 根据分单被保人编码{}保全记录获取推荐人为空", eventJob.getEventBusinessCode(),preservationDetail.getPreservationCode(),manager.getInsuredCode())));
            }*/
            if(StringUtil.isBlank(manager.getInsuredCode())){
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-分单推荐人变更-保单号={},保全号={}, 根据分单被保人编码不能为空", eventJob.getEventBusinessCode(),preservationDetail.getPreservationCode(),manager.getInsuredCode())));
            }
            doCustomerManagerChangeCostInfo(eventJob,eventType,costPolicy,beforeAll,preservationDetail.getPreservationCode(),manager.getInsuredCode(),ownerCode,preservationDetail.getSellChannelCode(),preservationDetail.getInputTime(),preservationDetail.getAfterCustomerManager(),newCostList,correctionOldIds);

        }

        CostCorrectionDto costCorrectionDto = CostCorrectionDto.builder()
                .correctionOldIds(correctionOldIds)
                .newCostList(newCostList)
                .build();

        return costCorrectionDto;




    }

    /**
     * 分单推荐人导入变更(非保全逻辑)
     * @param eventJob
     * @param eventType
     * @param costPolicy
     * @param changeDto
     * @return
     */
    @Override
    public CostCorrectionDto builderGroupPolicyRecommenderChangeCostInfo(SettlementEventJobEntity eventJob,
                                                                       SettlementEventTypeEnum eventType,
                                                                       SettlementCostPolicyInfoEntity costPolicy,
                                                                       GroupPolicyRecommenderChangeDto changeDto){
        List<PreserveChannelReferrerChangeDto> oldManagers = changeDto.getInsuredList();
        List<String> insuredCodes = oldManagers.stream().map(PreserveChannelReferrerChangeDto::getInsuredCode).collect(Collectors.toList());

        List<SettlementCostInfoEntity> beforeAll = settlementCostInfoService.listUnCorrectionCostInfoByInsuredCodes(changeDto.getContractCode(),insuredCodes);
        if(CollectionUtils.isEmpty(beforeAll)){
            return null;
        }
        SettlementCostInfoEntity costInfo = beforeAll.stream().filter(entity->StringUtils.isNotBlank(entity.getOwnerCode())).findFirst().orElse(null);
        if(Objects.nonNull(costInfo)){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-分单推荐人导入变更，存在明细记录有佣金归属人信息,合同号={},佣金归属人:{}/{}", costInfo.getContractCode(),costInfo.getOwnerCode(),costInfo.getOwnerThirdCode())));
        }

        List<SettlementCostInfoEntity> newCostList = Lists.newArrayList();
        List<Integer> correctionOldIds = Lists.newArrayList();

        PreservationCustomerChangeDto customerChangeDto = new PreservationCustomerChangeDto();
        customerChangeDto.setAfterChannelCode(changeDto.getChannelCode());
        customerChangeDto.setAfterCustomerManagerCode(changeDto.getCustomerManagerCode());
        customerChangeDto.setAfterCustomerManagerChannelCode(changeDto.getCustomerManagerChannelCode());
        customerChangeDto.setAfterCustomerManagerOrgCode(changeDto.getCustomerManagerOrgCode());
        customerChangeDto.setAfterCustomerManagerChannelOrgCode(changeDto.getCustomerManagerChannelOrgCode());
        customerChangeDto.setAfterCustomerManagerSupervisor(changeDto.getCustomerManagerSupervisor());

        for(PreserveChannelReferrerChangeDto manager : oldManagers){
            String ownerCode = settlementCostOwnerService.getSplitPolicyOwnerCodeByPreservationDetail(manager);

            if(StringUtil.isBlank(manager.getInsuredCode())){
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-分单推荐人变更-保单号={}, 根据分单被保人编码不能为空", eventJob.getEventBusinessCode())));
            }
            doCustomerManagerChangeCostInfo(eventJob,eventType,costPolicy,beforeAll,null,manager.getInsuredCode(),ownerCode,changeDto.getChannelCode(),changeDto.getCreateTime(),customerChangeDto,newCostList,correctionOldIds);

        }

        CostCorrectionDto costCorrectionDto = CostCorrectionDto.builder()
                .correctionOldIds(correctionOldIds)
                .newCostList(newCostList)
                .build();

        return costCorrectionDto;
    }


    /**
     * 保单维度渠道推荐人（对应农保管护经理)导入变更(非保全逻辑)
     * @param eventJob
     * @param eventType
     * @param costPolicy
     * @param changeDto
     * @return
     */
    @Override
    public CostCorrectionDto builderPolicyRecommenderChangeCostInfo(SettlementEventJobEntity eventJob,
                                                                    SettlementEventTypeEnum eventType,
                                                                    SettlementCostPolicyInfoEntity costPolicy,
                                                                    PolicyRecommenderChangeDto changeDto){
        //这个地方不能用合同号与佣金归属人编号联合查询，因为查询的记录为空的情况下，不知道是没有算，还是记录为空
        List<SettlementCostInfoEntity> beforeAll = settlementCostInfoService.listUnCorrectionPolicyCostInfoByContractCode(costPolicy.getContractCode());
        if(CollectionUtils.isEmpty(beforeAll)){
            return null;
        }
        SettlementCostInfoEntity ent = beforeAll.stream().filter(o->StringUtils.isNotBlank(o.getOwnerCode())).findFirst().orElse( null);
        if(ent!=null){
            throw new GlobalException(SettlementExceptionEnum.CORRECTION_AFTER_CUSTOMER_MANAGER_EXIST.getException(StrUtil.format("支出端-合同号{}的渠道推荐人导入变更，原始推荐人不为空", changeDto.getContractCode())));
        }
        //获取原始推推荐人
        String ownerCode = changeDto.getBeforeCustomerManagerCode();
        log.info("【保单推荐人导入变更】--保单推荐人导入变更:{}",ownerCode);
        List<SettlementCostInfoEntity> newCostList = Lists.newArrayList();
        List<Integer> correctionOldIds = Lists.newArrayList();
        List<SettlementCostInfoEntity> personNewPolicyCosts = settlementCostInfoService.listPersonNewPolicyCostInfoByContractCode(changeDto.getContractCode());

        PreservationCustomerChangeDto afterCustomerManager = policyRecommenderChangeDtoToPreservationCustomerChangeDto(changeDto);

        String newChannelCode = changeDto.getChannelCode();
        log.info("【初始推荐人变更】--获取新推荐人:{}",newChannelCode);
        //如果未冲正的新契约的推荐人信息与变更后的客户经理已经是一直了，就不需要变更了
        if (CollectionUtils.isNotEmpty(personNewPolicyCosts)) {
            if (Objects.equals(personNewPolicyCosts.get(0).getOwnerThirdCode(), afterCustomerManager.getAfterCustomerManagerChannelCode())) {
                log.warn("支出端-合同号{}的初始推荐人变更，变更后的推荐人{}与新契约推荐人{}一直", changeDto.getContractCode(), afterCustomerManager.getAfterCustomerManagerChannelCode(), personNewPolicyCosts.get(0).getOwnerThirdCode());
                throw new GlobalException(SettlementExceptionEnum.CORRECTION_AFTER_CUSTOMER_MANAGER_EXIST.getException(StrUtil.format("支出端-合同号{}的初始推荐人变更，变更后的推荐人{}与新契约推荐人{}一直", changeDto.getContractCode(), afterCustomerManager.getAfterCustomerManagerChannelCode(), personNewPolicyCosts.get(0).getOwnerThirdCode())));
            }
        }
        doCustomerManagerChangeCostInfo(eventJob, eventType, costPolicy, beforeAll, null, null, ownerCode, newChannelCode,changeDto.getCreateTime(),afterCustomerManager, newCostList, correctionOldIds);
        CostCorrectionDto costCorrectionDto = CostCorrectionDto.builder()
                .correctionOldIds(correctionOldIds)
                .newCostList(newCostList)
                .build();

        return costCorrectionDto;
    }

    /**
     * 续期保单-推荐人变更事件
     * @param eventJob
     * @param eventType
     * @param costPolicy
     * @param changeDto
     * @return
     */
    @Override
    public CostCorrectionDto builderPolicyTermReferrerChangeCostInfo(SettlementEventJobEntity eventJob,
                                                                    SettlementEventTypeEnum eventType,
                                                                    SettlementCostPolicyInfoEntity costPolicy,
                                                                    PolicyRecommenderChangeDto changeDto){
        //这个地方不能用合同号与佣金归属人编号联合查询，因为查询的记录为空的情况下，不知道是没有算，还是记录为空
        List<SettlementCostInfoEntity> oldCosts = settlementCostInfoService.listUnCorrectionPolicyCostInfoByContractCodeAndRenewal(costPolicy.getContractCode(),changeDto.getPeriod());
        if(CollectionUtils.isEmpty(oldCosts)){
            return null;
        }

        //获取原始推推荐人
        String ownerCode = changeDto.getBeforeCustomerManagerCode();
        log.info("保单续期原管护人:{}",ownerCode);
        List<SettlementCostInfoEntity> newCostList = Lists.newArrayList();
        List<Integer> correctionOldIds = Lists.newArrayList();

        PreservationCustomerChangeDto afterCustomerManager = policyRecommenderChangeDtoToPreservationCustomerChangeDto(changeDto);

        String newChannelCode = changeDto.getChannelCode();
        log.info("【初始推荐人变更】--获取新渠道:{}",newChannelCode);
        //如果未冲正的新契约的推荐人信息与变更后的客户经理已经是一直了，就不需要变更了

        if (Objects.equals(oldCosts.get(0).getOwnerThirdCode(), afterCustomerManager.getAfterCustomerManagerChannelCode())) {
            log.warn("支出端-合同号{}的初始推荐人变更，变更后的推荐人{}与新契约推荐人{}一直", changeDto.getContractCode(), afterCustomerManager.getAfterCustomerManagerChannelCode(), oldCosts.get(0).getOwnerThirdCode());
        }

        doCustomerManagerChangeCostInfo(eventJob, eventType, costPolicy, oldCosts, null, null, ownerCode, newChannelCode,changeDto.getCreateTime(),afterCustomerManager, newCostList, correctionOldIds);
        CostCorrectionDto costCorrectionDto = CostCorrectionDto.builder()
                .correctionOldIds(correctionOldIds)
                .newCostList(newCostList)
                .build();

        return costCorrectionDto;
    }

    private PreservationCustomerChangeDto policyRecommenderChangeDtoToPreservationCustomerChangeDto(PolicyRecommenderChangeDto changeDto){
        PreservationCustomerChangeDto afterCustomerManager = new PreservationCustomerChangeDto();
        afterCustomerManager.setAfterCustomerManagerChannelOrgCode(changeDto.getCustomerManagerChannelOrgCode());
        afterCustomerManager.setAfterCustomerManagerCode(changeDto.getCustomerManagerCode());
        afterCustomerManager.setAfterCustomerManagerSupervisor(changeDto.getCustomerManagerSupervisor());
        afterCustomerManager.setAfterCustomerManagerChannelCode(changeDto.getCustomerManagerChannelCode());
        afterCustomerManager.setAfterCustomerManagerOrgCode(changeDto.getCustomerManagerOrgCode());
        afterCustomerManager.setAfterChannelCode(changeDto.getChannelCode());
        afterCustomerManager.setAfterPolicyChannelBranchCode(changeDto.getChannelCode());
        afterCustomerManager.setChangeSubPolicy(1);
        return afterCustomerManager;
    }









    /**
     * 生成初始推荐人变更冲正新记录
     * @return
     */
    public SettlementCostInfoEntity builderCustomerManagerChangeNewCostInfo(SettlementEventJobEntity eventJob,
                                                                            SettlementEventTypeEnum eventType,
                                                                            SettlementCostInfoEntity old,
                                                                            Date newBusinessTime,
                                                                            PreservationCustomerChangeDto afterCustomerManager){
        SettlementCostInfoEntity bean = new SettlementCostInfoEntity();
        BeanUtils.copyProperties(old,bean);
        bean.setId(null);
        bean.setCostCode(PolicySettlementUtils.createCodeLastNumber("CC"));
        //记账时间处理
        bean.setSettlementTime(new Date());
        bean.setSettlementDate(bean.getSettlementTime());
        bean.setBusinessAccountTime(getCorrectionBusinessAccountTime(old.getBusinessAccountTime(),newBusinessTime));

        //事件编号
        bean.setEventSourceCode(eventJob.getPushEventCode());

        //事件信息
        if(Objects.nonNull(eventType)) {
            bean.setSettlementEventCode(eventType.getEventCode());
            bean.setSettlementEventDesc(eventType.getEventDesc());

        }

        bean.setSettlementGenerateType(1);
        bean.setSourceCostCode(old.getCostCode());
        //归属人信息
        settlementCostOwnerService.builderOwnerInfoByCustomerManageChange(afterCustomerManager,bean);

        //冲正信息（冲正标志需设置为未冲正）
        bean.setCorrectionFlag(0);
        bean.setCorrectionUser(null);
        bean.setCorrectionOpType(null);
        bean.setCorrectionRemark(null);

        //清除确认信息
        cleanConfirmInfo(bean);

        //冲正金额字段
        bean.setPremium(old.getPremium());
        bean.setCostAmount(old.getCostAmount());
        // todo 长险推广费,需要查询变更后的推荐人的续期率来计算他的发放金额。
        bean.setGrantAmount(old.getGrantAmount());

        bean.setBusinessDiscountPremium(old.getBusinessDiscountPremium());

        return bean;
    }

    public static PolicyProductPremInput builderBasicCommissionConfigParamByOldCostInfo(SettlementCostPolicyInfoEntity policyInfo, SettlementCostInfoEntity old,String newChannelCode){
        PolicyProductPremInput input = new PolicyProductPremInput();
        input.setChannelCode(newChannelCode);
        input.setPolicyNo(policyInfo.getPolicyNo());
        input.setMainProductCode(policyInfo.getMainProductCode());
        input.setProductCode(old.getProductCode());
        input.setPlantCode(old.getPlanCode());
        input.setInsuredPeriodType(old.getInsuredPeriodType());
        input.setInsuredPeriod(old.getInsuredPeriod());
        input.setPeriodType(old.getPeriodType());
        input.setPaymentPeriodType(old.getPaymentPeriodType());
        input.setPaymentPeriod(old.getPaymentPeriod());
        input.setInsuranceType(old.getInsuranceType());
        //input.setApprovedTime(policyInfo.getApprovedTime());
        //佣金配置需要根据历史记录的配置时间去获取，不能直接用保单投保时间获取（续期与增减员就不是）
        input.setApprovedTime(old.getCostConfigMatchTime());
        input.setRenewalYear(old.getRenewalYear());
        input.setRenewalPeriod(old.getRenewalPeriod());
        input.setApplicantGender(policyInfo.getApplicantGender());
        input.setApplicantBirthday(policyInfo.getApplicantBirthday());

        input.setSalesType(policyInfo.getSalesType());
        // 是否自保件
        input.setSelfPreservation(policyInfo.getSelfPreservation());
        // 代理人机构编码
        input.setOrgCode(policyInfo.getOrgCode());

        return input;
    }

    /**
     * 全部重算新记录
     * @param eventJob
     * @param eventType
     * @param old
     * @param afterCustomerManager
     * @return
     */
    private SettlementCostInfoEntity builderNewCostInfo(SettlementEventJobEntity eventJob,
                                                        SettlementEventTypeEnum eventType,
                                                        String policyNo,
                                                        SettlementCostPolicyInfoEntity costPolicy,
                                                        SettlementCostInfoEntity old,
                                                        Date newBusinessTime,
                                                        PolicyProductPremResult config,
                                                        PreservationCustomerChangeDto afterCustomerManager){
        SettlementCostInfoEntity bean = new SettlementCostInfoEntity();
        BeanUtils.copyProperties(old,bean);
        bean.setId(null);
        bean.setCostCode(PolicySettlementUtils.createCodeLastNumber("CC"));
        //记账时间处理
        bean.setSettlementTime(new Date());
        bean.setSettlementDate(bean.getSettlementTime());
        bean.setBusinessAccountTime(getCorrectionBusinessAccountTime(old.getBusinessAccountTime(),newBusinessTime));
        //事件编号
        bean.setEventSourceCode(eventJob.getPushEventCode());

        //事件信息
        if(Objects.nonNull(eventType)) {
            bean.setSettlementEventCode(eventType.getEventCode());
            bean.setSettlementEventDesc(eventType.getEventDesc());

        }

        bean.setSettlementGenerateType(1);
        bean.setSourceCostCode(old.getCostCode());
        //归属人信息
        settlementCostOwnerService.builderOwnerInfoByCustomerManageChange(afterCustomerManager,bean);

        //冲正信息（冲正标志需设置为未冲正）
        bean.setCorrectionFlag(0);
        bean.setCorrectionUser(null);
        bean.setCorrectionOpType(null);
        bean.setCorrectionRemark(null);

        //清除确认信息
        cleanConfirmInfo(bean);

        //保费
        bean.setPremium(calcCorrectionNewPremium(costPolicy,old,afterCustomerManager.getAfterChannelCode()));
        //佣金信息
        EpProductInfoVo product = new EpProductInfoVo();
        BeanUtils.copyProperties(old,product);
        calcBasicCommission(policyNo,old.getEndorsementNo(),product,config,bean);
        bean.setBusinessDiscountPremium(old.getBusinessDiscountPremium());
        return bean;
    }

    /**
     * 计算冲正新记录保费
     * 目前农保和非农保的长险佣金计算规则不一致
     * @param costPolicy
     * @param old
     * @param channelCode
     * @return
     */
    private BigDecimal calcCorrectionNewPremium(SettlementCostPolicyInfoEntity costPolicy,SettlementCostInfoEntity old,
                                                String channelCode){
        //保单维度是否长险
        if(Objects.equals(costPolicy.getLongShortFlag(),1)){
            if(Objects.equals(channelCode,ZHNX_CHANNEL_CODE)){
                if(Objects.equals(old.getInitialEventCode(),SettlementEventTypeEnum.STANDARD_SURRENDER.getEventCode())){
                    return old.getProductPremium();
                }else if(Objects.equals(old.getInitialEventCode(),SettlementEventTypeEnum.TERMINATION_PRODUCT.getEventCode())){
                    return old.getSurrenderAmount();
                }
            }else{
                if(LIST_CITY_ZERO_PREMIUM_EVENT.contains(old.getInitialEventCode())){
                    return BigDecimal.ZERO;
                }
            }
        }

        return old.getPremium();
    }


    /**
     * 支出端-佣金信息保存(事件触发)
     * @param eventJob
     * @param costCorrectionDto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveCostCommissionRecord(SettlementEventJobEntity eventJob, CostCorrectionDto costCorrectionDto){
//        if(CollectionUtils.isEmpty(costCorrectionDto.getNewCostList())){
//            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-佣金信息保存-保单号={}, 佣金记录为空", eventJob.getEventBusinessCode())));
//        }
        saveCostCommission(costCorrectionDto);

    }
    /**
     * 支出端-佣金信息保存(事件触发)
     * @param costCorrectionDto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveCostCommission(CostCorrectionDto costCorrectionDto){
        SettlementCostPolicyInfoEntity policy = costCorrectionDto.getPolicy();
        if(costCorrectionDto.getPolicy()!= null){
            if(Objects.isNull(policy.getId())) {
                settlementCostPolicyInfoService.save(policy);
            }else{
                settlementCostPolicyInfoService.updateById(policy);
            }
            costCorrectionDto.getNewCostList().stream().forEach(c->{
                c.setCostPolicyId(policy.getId());
            });
        }
        //历史冲正记录打上冲正标签
        if(CollectionUtils.isNotEmpty(costCorrectionDto.getCorrectionOldIds())){
            settlementCostInfoService.updateCorrectionFlagDoneByIds(costCorrectionDto.getCorrectionOldIds());
        }
        //冲正更新新的佣金配置key
        if(CollectionUtils.isNotEmpty(costCorrectionDto.getUpdateKeyList())){
            List<SettlementCostInfoEntity> updateList = costCorrectionDto.getUpdateKeyList();
            Map<String,List<SettlementCostInfoEntity>> updateMap = updateList.stream().collect(Collectors.groupingBy(SettlementCostInfoEntity::getCostConfigKey));
            for(String configKey : updateMap.keySet()){
                settlementCostInfoService.updateCostInfoConfigKey(updateMap.get(configKey).stream().map(SettlementCostInfoEntity::getId).collect(Collectors.toList()), configKey);
            }
        }
        //插入新增的佣金记录
        if(CollectionUtils.isNotEmpty(costCorrectionDto.getNewCostList())) {
            settlementCostInfoService.saveBatch(costCorrectionDto.getNewCostList());
        }

    }

    /**
     * 创建险种变更保全，该保全只针对非团险的新契约操作
     * 其他场景，当前险种变更保全详情就需要调整优化
     * 如果后面要开发增减员的险种变更，你的接口详情就不支持了（新增险种编码都需要知道原险种编码或者对应的原事件操作），
     * 1、团险存在同一个险种编码，在新契约和增减员的时候费率不一致的情况，就需要指明新契约对应的原始险种编码是哪些？
     * 2、哪些险种是新增的，且对应的操作是哪种(是新契约，还是增减员，或是其他保全)？
     * @param eventJob
     * @param subjectEnum
     * @param eventType
     * @param productMap
     * @param policyInfo
     * @param preservationDetail
     * @return
     */
    @Override
    public CostCorrectionDto builderProductChangeEventCorrectionCostInfo(SettlementEventJobEntity eventJob,
                                                                    CostSubjectEnum subjectEnum,
                                                                    SettlementEventTypeEnum eventType, EpContractInfoVo policyInfo,
                                                                    Map<String, ProductBase> productMap,
                                                                    SettlementCostPolicyInfoEntity costPolicy,
                                                                    PolicyPreservationDetailDto preservationDetail){

//        List<EpPreserveProductDto> before = preservationDetail.getProductChangeDto().getBeforeProductList();
//        List<EpPreserveProductDto> after = preservationDetail.getProductChangeDto().getCorrectedProductList();
//        Date businessAccountTime = preservationDetail.getInputTime();
//        return builderProductChangeCorrectionCostInfo(eventJob,subjectEnum,eventType,productMap,policyInfo,costPolicy,businessAccountTime,before,after);
        Date businessAccountTime = preservationDetail.getInputTime();

        List<EpPreserveProductChangeGroupDto> groups = preservationDetail.getProductChangeDto().getProductChangeGroup();
        List<Integer> correctionOldIds = Lists.newArrayList();
        List<SettlementCostInfoEntity> newCostList = Lists.newArrayList();
        Integer insuranceType = StringUtils.isBlank(policyInfo.getSourcePolicyNo())?0:1;
        List<String> beforeProductCodes = groups.stream().map(EpPreserveProductChangeGroupDto::getBeforeProduct).collect(Collectors.toList())
                .stream().map(EpPreserveProductDto::getProductCode).collect(Collectors.toList());

        List<String> afterProductCodes = groups.stream().map(EpPreserveProductChangeGroupDto::getAfterProduct).collect(Collectors.toList())
                .stream().map(EpPreserveProductDto::getProductCode).collect(Collectors.toList());
        List<SettlementCostInfoEntity> beforeOldList = listByContractCodeAndProductCode(costPolicy.getContractCode(), beforeProductCodes);
        Optional<SettlementCostInfoEntity> nonNewOpt = beforeOldList.stream().filter(c->!(Objects.equals(c.getInitialEventCode(),SettlementEventTypeEnum.PERSONAL_NEW_POLICY.getEventCode())
                || Objects.equals(c.getInitialEventCode(),SettlementEventTypeEnum.RENEWAL_POLICY.getEventCode())
                || Objects.equals(c.getInitialEventCode(),SettlementEventTypeEnum.GROUP_NEW_POLICY.getEventCode()))).findFirst();
        if(nonNewOpt.isPresent()){
            String msg = StrUtil.format("支出端-险种变更保全当前支持原始保全为新契约或者续投事件，合同编号{}存在其他事件{}",nonNewOpt.get().getContractCode(),nonNewOpt.get().getInitialEventCode());
            log.warn(msg);
            throw new GlobalException(SettlementExceptionEnum.C_B_PRODUCT_CHANGE_ONLY_SUPPORT_NEW_EVENT.getException(msg));
        }
        List<SettlementCostInfoEntity> afterExistList = listByContractCodeAndProductCode(costPolicy.getContractCode(), afterProductCodes);
        List<SettlementCostInfoEntity> increase = Lists.newArrayList();
        for(EpPreserveProductChangeGroupDto g: groups){
            EpPreserveProductDto before = g.getBeforeProduct();
            List<SettlementCostInfoEntity> oldInsuredList = beforeOldList.stream().filter(b->Objects.equals(before.getProductCode(),b.getProductCode())
                    && Objects.equals(before.getInsuredCode(),b.getInsuredCode())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(oldInsuredList)){
                oldInsuredList.stream().forEach(o->{
                        correctionOldIds.add(o.getId());
                        newCostList.add(builderOffsetCostInfo(eventJob,eventType,o,businessAccountTime,SYSTEM_CORRECTION_USER,"险种变更冲正",Boolean.TRUE));
                });
            }else{
                //
                List<SettlementCostInfoEntity> existInsuredList = afterExistList.stream().filter(b->Objects.equals(g.getAfterProduct().getProductCode(),b.getProductCode())
                        && Objects.equals(g.getAfterProduct().getInsuredCode(),b.getInsuredCode())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(existInsuredList)){
                    log.info("支出端-险种变更保全当前新老险种已存在");
                    continue;
                }else if (CollectionUtils.isEmpty(existInsuredList)){
                    String msg = StrUtil.format("支出端-险种变更保全当前新老险种都不存在");
                    log.warn(msg);
                    throw new GlobalException(SettlementExceptionEnum.C_B_PRODUCT_CHANGE_ONLY_SUPPORT_NEW_EVENT.getException(msg));
                }
            }
            //新险种处理
            log.info("险种变更开始处理新险种{}/{}",g.getAfterProduct().getProductName(),g.getAfterProduct().getProductCode());
            EpPreserveProductDto a = g.getAfterProduct();
            if(a.getMainInsurance() == 1){
                //修改settlement_cost_policy_info表中的主险信息
                ProductBase productBase = productMap.get(a.getProductCode());
                costPolicy.setMainProductCode(a.getProductCode());
                costPolicy.setMainProductName(a.getProductName());
                costPolicy.setLongShortFlag(productBase.getLongShortFlag());
                //costPolicy.setPolicyProductType(productBase.getProductType());

            }

            PolicyProductPremInput input = builderConfigParamByPolicyAndCostPolicy(policyInfo,costPolicy,insuranceType,a);
            List<CostBasicCommissionConfigDto>  configList =  commissionConfigService.getCostBasicCommissionConfig(policyInfo.getContractBaseInfo().getPolicyNo(),policyInfo.getPolicyProductType(),input);

            for (CostBasicCommissionConfigDto config : configList) {
                SettlementCostInfoEntity bean = builderProductChangeNewCostInfo(eventJob,subjectEnum, eventType, productMap,businessAccountTime,policyInfo,insuranceType,config,preservationDetail,a);

                increase.add(bean);
            }

        }
        //设置佣金归属人信息并返回
        settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, increase);
        newCostList.addAll(increase);
        return CostCorrectionDto.builder()
                .correctionOldIds(correctionOldIds)
                .newCostList(newCostList)
                .policy(costPolicy)
                .build();

    }


    private SettlementCostInfoEntity builderProductChangeNewCostInfo(SettlementEventJobEntity eventJob,
                                                                     CostSubjectEnum subjectEnum,
                                                                     SettlementEventTypeEnum eventType,
                                                                     Map<String, ProductBase> productMap,
                                                                     Date businessAccountTime,
                                                                     EpContractInfoVo policyInfo,
                                                                     Integer insuranceType,
                                                                     CostBasicCommissionConfigDto config,PolicyPreservationDetailDto preservationDetail,EpPreserveProductDto after){
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        //初始化，生成科目信息
        SettlementCostInfoEntity bean = initSettlementCostInfo(eventJob, subjectEnum, eventType, policyInfo, insuranceType);
        //修改原始事件编码为新契约或者续保
        bean.setInitialEventCode(insuranceType == 0?SettlementEventTypeEnum.PERSONAL_NEW_POLICY.getEventCode() : SettlementEventTypeEnum.RENEWAL_POLICY.getEventCode());
        bean.setCostConfigMatchTime(policyInfo.getContractExtendInfo().getApprovedTime());
        //记账时间
        bean.setSettlementTime(getBasicSettlementTime(eventType, policyInfo));
        bean.setSettlementDate(bean.getSettlementTime());

        //设置保费
        bean.setProductPremium(after.getPremium());
        bean.setPremium(after.getPremium());
        bean.setBusinessPremium(after.getPremium());
        bean.setBusinessAccountTime(businessAccountTime);


        //佣金信息
        calcBasicCommission(policyNo, config, bean);
        //确认信息
        bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
        //被保人信息
        EpInsuredInfoVo insured = policyInfo.getInsuredInfoList().stream().filter(f->Objects.equals(f.getInsuredCode(),after.getInsuredCode())).findFirst().orElseGet(null);
        if(insured != null) {
            setInsuredInfo(insured, bean);
        }
        //险种信息
        builderCostProductInfo(policyInfo, productMap, after, bean, 1, 1);
        //折算保费
        builderDiscountPremium(policyNo,policyInfo.getPolicyProductType(),bean);

        bean.setPreservationCode(preservationDetail.getPreservationCode());
        bean.setEndorsementNo(preservationDetail.getEndorsementNo());

        bean.setPreservationType(preservationDetail.getPreservationType());
        bean.setPreservationProject(preservationDetail.getPreservationProject());
        bean.setPreservationPeriod(preservationDetail.getRenewalTermPeriod());
        bean.setPreservationEffectTime(preservationDetail.getPreservationEffectTime());
        return bean;
    }

    public PolicyProductPremInput builderConfigParamByPolicyAndCostPolicy(EpContractInfoVo policy,SettlementCostPolicyInfoEntity costPolicy,Integer insuranceType, EpPreserveProductDto after){
        PolicyProductPremInput input = new PolicyProductPremInput();
        input.setChannelCode(policy.getChannelInfo().getChannelCode());
        input.setPolicyNo(policy.getContractBaseInfo().getPolicyNo());
        input.setMainProductCode(policy.getContractBaseInfo().getMainProductCode());
        input.setProductCode(after.getProductCode());
        input.setPlantCode(after.getPlanCode());
        input.setInsuredPeriodType(after.getInsuredPeriodType());
        input.setInsuredPeriod(after.getInsuredPeriod());
        input.setPeriodType(after.getPeriodType());
        input.setPaymentPeriodType(after.getPaymentPeriodType());
        input.setPaymentPeriod(after.getPaymentPeriod());
        input.setInsuranceType(insuranceType);
        //input.setApprovedTime(policyInfo.getApprovedTime());
        //佣金配置需要根据历史记录的配置时间去获取，不能直接用保单投保时间获取（续期与增减员就不是）
        input.setApprovedTime(policy.getContractExtendInfo().getApprovedTime());
        input.setRenewalYear(1);
        input.setRenewalPeriod(1);
        input.setApplicantGender(costPolicy.getApplicantGender());
        input.setApplicantBirthday(costPolicy.getApplicantBirthday());

        input.setSalesType(policy.getContractBaseInfo().getSalesType());
        // 是否自保件
        input.setSelfPreservation(policy.getContractBaseInfo().getSelfPreservation());
        // 代理人机构编码
        if (policy.getAgentInfoList() != null && policy.getAgentInfoList().size() > 0) {
            policy.getAgentInfoList().stream().filter(a -> a.getMainFlag() == 1).findFirst()
                    .ifPresent(epAgentInfoVo -> input.setOrgCode(epAgentInfoVo.getOrgCode()));
        }

        return input;
    }


    private List<SettlementCostInfoEntity> listByContractCodeAndProductCode(String contractCode,List<String> productCodes){

        return settlementCostInfoService.listUnCorrectionByContractCodeAndProductCodes(contractCode,productCodes);

    }


    /**
     * 创建渠道变更保全，该保全只针对非团险的新契约操作
     * 其他场景，暂不支持
     * @param eventJob
     * @param subjectEnum
     * @param eventType
     * @param productMap
     * @param policyInfo
     * @param preservationDetail
     * @return
     */
    @Override
    public CostCorrectionDto builderChannelChangeEventCorrectionCostInfo(SettlementEventJobEntity eventJob,
                                                                         CostSubjectEnum subjectEnum,
                                                                         SettlementEventTypeEnum eventType, EpContractInfoVo policyInfo,
                                                                         Map<String, ProductBase> productMap,
                                                                         SettlementCostPolicyInfoEntity costPolicy,
                                                                         PolicyPreservationDetailDto preservationDetail) {


        Date businessAccountTime = preservationDetail.getInputTime();

        PolicyChannelChangeDto channelChangeDto = preservationDetail.getChannelChangeDto();
        List<Integer> correctionOldIds = Lists.newArrayList();
        List<SettlementCostInfoEntity> newCostList = Lists.newArrayList();

        List<SettlementCostInfoEntity> oldCostList = settlementCostInfoService.listUnCorrectionByContractCodeAndChannelCode(costPolicy.getContractCode(), channelChangeDto.getCurrentChannelCode());
        //老渠道不存在
        if(CollectionUtils.isEmpty(oldCostList)){
            List<SettlementCostInfoEntity> newChannelOldList = settlementCostInfoService.listUnCorrectionByContractCodeAndChannelCode(costPolicy.getContractCode(), channelChangeDto.getAfterChannelCode());
            if(CollectionUtils.isNotEmpty(newChannelOldList)){
                return new CostCorrectionDto();
            }else{
                String msg = StrUtil.format("支出端-渠道变更保全未找到新老渠道的未冲正的佣金记录，合同编号{}，渠道编码为老{}新{}",costPolicy.getContractCode(),channelChangeDto.getCurrentChannelCode(),channelChangeDto.getAfterChannelCode());
                throw new GlobalException(SettlementExceptionEnum.C_B_CHANNEL_CHANGE_OLD_RECORD_NOT_EXIST.getException(msg));
            }
        }

        Optional<SettlementCostInfoEntity> nonNewOpt = oldCostList.stream().filter(c->!(Objects.equals(c.getInitialEventCode(),SettlementEventTypeEnum.PERSONAL_NEW_POLICY.getEventCode())
                || Objects.equals(c.getInitialEventCode(),SettlementEventTypeEnum.RENEWAL_POLICY.getEventCode())
                || Objects.equals(c.getInitialEventCode(),SettlementEventTypeEnum.GROUP_NEW_POLICY.getEventCode()))).findFirst();
        if(nonNewOpt.isPresent()){
            String msg = StrUtil.format("支出端-渠道变更保全当前支持原始保全为新契约或者续投事件，合同编号{}存在其他事件{}",nonNewOpt.get().getContractCode(),nonNewOpt.get().getInitialEventCode());
            log.warn(msg);
            throw new GlobalException(SettlementExceptionEnum.C_B_CHANNEL_CHANGE_ONLY_SUPPORT_NEW_EVENT.getException(msg));
        }
        //是否需要验证推荐人
        //去重用，举例：新契约，zhnx渠道，存在两个结算账户的佣金记录，转成gdyw时，只有一个结算账户，这样两条历史记录只需要生成一条新的佣金记录
        Set<String> existSet = Sets.newHashSet();
        for(SettlementCostInfoEntity old : oldCostList){
            //将原始记录设置为已冲正
            old.setCorrectionFlag(1);
            correctionOldIds.add(old.getId());
            //生成源记录的对冲记录
            newCostList.add(builderOffsetCostInfo(eventJob,eventType,old,businessAccountTime,SYSTEM_CORRECTION_USER,"渠道变更冲正",Boolean.TRUE));
            //生成正确的佣金记录,判断渠道是否前后是否一样，如果不一样则新记录需要重新获取佣金配置进行计算，一样则佣金信息不需要重算(发放金额与续期率有关的记录除外)
            String naturalKey = getNaturalKey(old);
            if(!existSet.contains(naturalKey)) {
                PolicyProductPremInput input = builderBasicCommissionConfigParamByOldCostInfo(costPolicy, old,channelChangeDto.getAfterChannelCode());

                List<CostBasicCommissionConfigDto>  configList =  commissionConfigService.getCostBasicCommissionConfig(policyInfo.getContractBaseInfo().getPolicyNo(),policyInfo.getPolicyProductType(),input);
                for (CostBasicCommissionConfigDto config : configList) {
                    SettlementCostInfoEntity bean = builderNewCostInfoByOldCostInfo(eventJob, eventType,channelChangeDto.getAfterChannelCode(), costPolicy.getPolicyNo(),businessAccountTime,config,costPolicy, old);
                    //处理推荐人信息
                    settlementCostOwnerService.builderSinglePreservationCostOwnerInfo(channelChangeDto.getAfterChannelCode(),preservationDetail,bean);

                    newCostList.add(bean);
                }
                existSet.add(naturalKey);
            }
        }


        return CostCorrectionDto.builder()
                .correctionOldIds(correctionOldIds)
                .newCostList(newCostList)
                .policy(costPolicy)
                .build();
    }

    /**
     * 根据历史佣金记录构建新的佣金记录(推荐人信息和新的保全记录，需要调用方补充该部分)(不适用于险种变更)
     * @param eventJob
     * @param eventType
     * @param channelCode
     * @param policyNo
     * @param newBusinessTime
     * @param config
     * @param costPolicy
     * @param old
     * @return
     */
    private SettlementCostInfoEntity builderNewCostInfoByOldCostInfo(SettlementEventJobEntity eventJob,
                                                                     SettlementEventTypeEnum eventType,
                                                                     String channelCode,
                                                                     String policyNo,
                                                                     Date newBusinessTime,
                                                                     CostBasicCommissionConfigDto config,
                                                                     SettlementCostPolicyInfoEntity costPolicy,SettlementCostInfoEntity old){
        SettlementCostInfoEntity bean = new SettlementCostInfoEntity();
        BeanUtils.copyProperties(old,bean);
        bean.setId(null);
        bean.setCostCode(PolicySettlementUtils.createCodeLastNumber("CC"));
        //记账时间处理
        bean.setSettlementTime(new Date());
        bean.setSettlementDate(bean.getSettlementTime());
        bean.setBusinessAccountTime(getCorrectionBusinessAccountTime(old.getBusinessAccountTime(),newBusinessTime));
        //事件编号
        bean.setEventSourceCode(eventJob.getPushEventCode());

        //事件信息
        if(Objects.nonNull(eventType)) {
            bean.setSettlementEventCode(eventType.getEventCode());
            bean.setSettlementEventDesc(eventType.getEventDesc());

        }

        bean.setSettlementGenerateType(1);
        bean.setSourceCostCode(old.getCostCode());

        //冲正信息（冲正标志需设置为未冲正）
        bean.setCorrectionFlag(0);
        bean.setCorrectionUser(null);
        bean.setCorrectionOpType(null);
        bean.setCorrectionRemark(null);

        //清除确认信息
        cleanConfirmInfo(bean);
        //清除归属人信息


        //保费
        bean.setPremium(calcCorrectionNewPremium(costPolicy,old,channelCode));
        //佣金信息
        EpProductInfoVo product = new EpProductInfoVo();
        BeanUtils.copyProperties(old,product);
        calcBasicCommission(policyNo,old.getEndorsementNo(),config,bean);
        bean.setBusinessDiscountPremium(old.getBusinessDiscountPremium());
        return bean;
    }

}
