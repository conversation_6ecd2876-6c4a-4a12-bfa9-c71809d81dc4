package com.mpolicy.settlement.core.thirdpart.request;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * 小额保险订单查询query
 *
 * <AUTHOR>
 */
@Data
@ApiModel
public class WhaleAddCommissionPushQuery {

    /**
     * 批次号
     */
    @ApiModelProperty(value = "结算批次号")
    private List<String> batchNoList;

    /**
     * 加佣记录id
     */
    @ApiModelProperty(value = "加佣记录id")
    private Integer id;

    /**
     * 活动id
     */
    @ApiModelProperty(value = "活动id")
    private Integer saId;

    /**
     * 第几页
     */
    @ApiModelProperty(value = "第几页")
    private Integer pageSize;

    /**
     * 每页记录数
     */
    @ApiModelProperty(value = "每页记录数")
    private Integer size;
}
