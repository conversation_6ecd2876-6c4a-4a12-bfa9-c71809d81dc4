package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.modules.reconcile.dao.BiSettlementIncomeRegulatorySubmitDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.BiSettlementIncomeRegulatorySubmitEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.BiSettlementIncomeRegulatorySubmitService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BiSettlementIncomeRegulatorySubmitServiceImpl
    extends ServiceImpl<BiSettlementIncomeRegulatorySubmitDao, BiSettlementIncomeRegulatorySubmitEntity>
    implements BiSettlementIncomeRegulatorySubmitService {

    /**
     * 批量插入数据
     *
     * @param listData
     */
    @Override
    public void saveList(List<BiSettlementIncomeRegulatorySubmitEntity> listData) {
        baseMapper.insertBatchSomeColumn(listData);
    }
}
