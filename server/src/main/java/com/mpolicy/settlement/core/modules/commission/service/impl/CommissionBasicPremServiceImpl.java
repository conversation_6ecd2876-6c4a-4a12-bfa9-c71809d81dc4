package com.mpolicy.settlement.core.modules.commission.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.modules.commission.dao.CommissionBasicPremDao;
import com.mpolicy.settlement.core.modules.commission.entity.CommissionBasicPremEntity;
import com.mpolicy.settlement.core.modules.commission.service.CommissionBasicPremService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("commissionBasicPremService")
public class CommissionBasicPremServiceImpl extends ServiceImpl<CommissionBasicPremDao, CommissionBasicPremEntity> implements CommissionBasicPremService {

    @Override
    public Integer batchUpdateMonitorCode(List<CommissionBasicPremEntity> list){
        if(CollectionUtils.isEmpty(list)){
            return 0;
        }
        return this.baseMapper.batchUpdateMonitorCode(list);
    }
}
