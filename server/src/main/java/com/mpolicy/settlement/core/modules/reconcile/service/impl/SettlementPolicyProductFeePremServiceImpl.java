package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementPolicyProductFeePremDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyProductFeePremEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementPolicyProductFeePremService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2025/3/7
 * @Version 1.0
 */
@Slf4j
@Service
public class SettlementPolicyProductFeePremServiceImpl extends ServiceImpl<SettlementPolicyProductFeePremDao, SettlementPolicyProductFeePremEntity>
        implements SettlementPolicyProductFeePremService {


}