package com.mpolicy.settlement.core.modules.reconcile.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcilePolicyEntity;

import java.util.List;

/**
 * 保司结算对账单关联数据
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:34
 */
public interface SettlementReconcilePolicyService extends IService<SettlementReconcilePolicyEntity> {

    /**
     * 批量保存数据
     *
     * @param listData 数据集合
     * @return void
     * <AUTHOR>
     * @since 2023/8/31
     */
    void saveList(List<SettlementReconcilePolicyEntity> listData);
}

