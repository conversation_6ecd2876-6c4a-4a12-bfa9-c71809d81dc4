package com.mpolicy.settlement.core.modules.reconcile.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/3/7 02:15
 * @Version 1.0
 */
@TableName(value = "settlement_policy_product_fee_prem")
@Data
public class SettlementPolicyProductFeePremEntity implements Serializable {
    /**
     * 主键id
     */
    @TableId
    private Integer id;

    /**
     * 费率编码
     */
    private String premCode;

    /**
     * 保单号
     */
    private String policyNo;

    /**
     * 批单号
     */
    private String batchCode;

    /**
     * 保单年期
     */
    private Integer year;

    /**
     * 缴费期数
     */
    private Integer period;

    /**
     * 结算类型0:小鲸 1:飞小鲸
     */
    private Integer reconcileType;

    /**
     * 小鲸险种编码
     */
    private String productCode;

    /**
     * 小鲸险种名称
     */
    private String productName;

    /**
     * 保司协议险种编码
     */
    private String insuranceProductCode;

    /**
     * 保司协议险种名称
     */
    private String insuranceProductName;

    /**
     * 保费
     */
    private BigDecimal premium;

    /**
     * 税后保费
     */
    private BigDecimal taxAfterPremium;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 税率
     */
    private String settlementMethod;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}