package com.mpolicy.settlement.core.modules.distribution.service.Impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.add.entity.SettlementAddCostInfoEntity;
import com.mpolicy.settlement.core.modules.autocost.enums.ConfirmStatusEnum;
import com.mpolicy.settlement.core.modules.distribution.dao.SettlementDistributionCostInfoDao;
import com.mpolicy.settlement.core.modules.distribution.entity.SettlementDistributionCostInfoEntity;
import com.mpolicy.settlement.core.modules.distribution.service.SettlementDistributionCostInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import static com.mpolicy.settlement.core.common.Constant.MAX_BATCH_INSERT_NUM;
import static com.mpolicy.settlement.core.common.Constant.MAX_BATCH_UPDATE_NUM;

@Service
@Slf4j
public class SettlementDistributionCostInfoServiceImpl extends ServiceImpl<SettlementDistributionCostInfoDao, SettlementDistributionCostInfoEntity> implements SettlementDistributionCostInfoService {

    @Override
    public int saveList(List<SettlementDistributionCostInfoEntity> list) {
        if(CollectionUtils.isEmpty(list)){
            return 0;
        }
        return batchSave(list);
    }

    public int batchSave(List<SettlementDistributionCostInfoEntity> list){
        if(CollectionUtils.isEmpty(list)){
            return 0;
        }
        int result = 0;
        // 批量写入
        if (list.size() > MAX_BATCH_INSERT_NUM) {
            List<List<SettlementDistributionCostInfoEntity>> partition = ListUtils.partition(list, MAX_BATCH_INSERT_NUM);
            for (List<SettlementDistributionCostInfoEntity> x : partition) {
                result = result + baseMapper.insertBatchSomeColumn(x);
            }
        } else {
            result = baseMapper.insertBatchSomeColumn(list);
        }
        return result;
    }


    public int batchUpdateAutoCostInfo(List<SettlementDistributionCostInfoEntity> list){
        if(CollectionUtils.isEmpty(list)){
            return 0;
        }

        int result = 0;
        // 批量写入
        if (list.size() > MAX_BATCH_UPDATE_NUM) {
            List<List<SettlementDistributionCostInfoEntity>> partition = ListUtils.partition(list, MAX_BATCH_UPDATE_NUM);
            for (List<SettlementDistributionCostInfoEntity> x : partition) {
                log.info("批量更新结果集={}",x);
                result = result + baseMapper.batchUpdateAutoCostInfo(x);
            }
        } else {
            log.info("批量更新结果集={}",list);
            result = baseMapper.batchUpdateAutoCostInfo(list);
        }
        return result;
    }

    @Override
    public boolean updateConfirmedStatus(String programmeCode, String costSettlementCycle, List<String> costSubjectCodes, String confirmUser, Date confirmTime){
        if(StringUtils.isBlank(costSettlementCycle)){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("方案{}的分销佣金确认结算需传入结算周期", programmeCode)));
        }

        return this.lambdaUpdate()
                .set(SettlementDistributionCostInfoEntity::getConfirmTime, confirmTime)
                .set(SettlementDistributionCostInfoEntity::getConfirmStatus, ConfirmStatusEnum.CONFIRMED.getCode())
                .eq(SettlementDistributionCostInfoEntity::getCostSettlementCycle,costSettlementCycle)
                .eq(SettlementDistributionCostInfoEntity::getDeleted,0)
                .update();
    }
}
