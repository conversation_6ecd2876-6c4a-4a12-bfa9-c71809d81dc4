package com.mpolicy.settlement.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.dao.PcDicCompanyDao;
import com.mpolicy.settlement.core.entity.PcDicCompanyEntity;
import com.mpolicy.settlement.core.service.PcDicCompanyService;

import com.mpolicy.settlement.core.service.SysDocumentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service("pcDicCompanyService")
@Slf4j
public class PcDicCompanyServiceImpl extends ServiceImpl<PcDicCompanyDao, PcDicCompanyEntity> implements PcDicCompanyService {

    @Autowired
    private SysDocumentService sysDocumentService;

    @Override
    public void queryOne() {
        PcDicCompanyEntity company = lambdaQuery().eq(PcDicCompanyEntity::getId, 155474492472494358L).one();
        log.info("结果={}", JSON.toJSON(company));

        sysDocumentService.queryOne();
        PcDicCompanyEntity company2 = lambdaQuery().eq(PcDicCompanyEntity::getId, 155474492472494358L).one();
        if(company2 == null){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("信息不存在"));
        }
        log.info("结果={}", JSON.toJSON(company2));
    }
}
