package com.mpolicy.settlement.core.modules.reconcile.event.project.policyplatform;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileStatusEnum;
import com.mpolicy.settlement.core.enums.ReconcileTypeEnum;
import com.mpolicy.settlement.core.enums.SettlementPolicyHandlerEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.ConfirmStatusEnum;
import com.mpolicy.settlement.core.modules.govern.dto.proejct.ChangeEndorsementNoData;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.CostCorrectionDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.factory.SettlementPolicyFactory;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.SettlementPolicyHandler;
import com.mpolicy.settlement.core.modules.reconcile.event.project.common.AbsPolicyCommonEvent;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventCheckResult;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventData;
import com.mpolicy.settlement.core.modules.reconcile.service.impl.SettlementCostProcessServiceImpl;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import com.mpolicy.settlement.core.modules.reconcile.vo.BasisSettlementPolicyInfoDomain;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.mpolicy.settlement.core.common.Constant.SYSTEM_CORRECTION_USER;

/**
 * <AUTHOR>
 * @description 批单号变更
 * @date 2024/2/25 1:44 下午
 * @Version 1.0
 */

@Slf4j
@Service
public class EndorsementNoChangeEvent extends AbsPolicyCommonEvent {
    @Override
    public HandleEventCheckResult handleIncomeEventCheck(SettlementEventJobEntity eventJob, Integer reconcileType) {
        // 1.解析报文数据
        ChangeEndorsementNoData endorsementNoData =
            JSONObject.parseObject(eventJob.getEventRequest(), ChangeEndorsementNoData.class);
        // 2.参数校验
        String sourceEndorsementNo = endorsementNoData.getEndorsementNo();
        if (StrUtil.isEmpty(sourceEndorsementNo)) {
            return HandleEventCheckResult.builder().checkStatus(false).checkMsg("原批单保单号为空,不做处理").build();
        }
        String endorsementNo = endorsementNoData.getNewEndorsementNo();
        if (StrUtil.isEmpty(endorsementNo)) {
            return HandleEventCheckResult.builder().checkStatus(false).checkMsg("新批单号为空,不做处理").build();
        }
        // 判断事件是否已经生成了明细数据,如果生成了就不在运行了
        boolean checkSettlementPolicyInfo = checkSettlementPolicyInfo(eventJob.getPushEventCode(), reconcileType);
        // 如果存在纪录
        if (checkSettlementPolicyInfo) {
            return HandleEventCheckResult.builder().checkStatus(false)
                .checkMsg(StrUtil.format("批单号变更事件已经写入明细")).build();
        }
        return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();
    }

    @Override
    public String handleIncomeEvent(SettlementEventJobEntity eventJob, HandleEventData eventData) {
        // 1.解析报文数据
        ChangeEndorsementNoData endorsementNoData =
            JSONObject.parseObject(eventJob.getEventRequest(), ChangeEndorsementNoData.class);
        // 2.参数校验
        String sourceEndorsementNo = endorsementNoData.getEndorsementNo();
        String preservationCode = endorsementNoData.getPreservationCode();
        // 根据保全编码获取需要冲正的数据
        List<SettlementPolicyInfoEntity> policyInfoList = settlementPolicyInfoService.lambdaQuery()
            .eq(SettlementPolicyInfoEntity::getPreservationCode, preservationCode)
            .eq(SettlementPolicyInfoEntity::getReconcileType, eventData.getReconcileType())
            .eq(SettlementPolicyInfoEntity::getRectificationMark, StatusEnum.INVALID.getCode()).list();
        if (!policyInfoList.isEmpty()) {
            // 判断一下 是否存在已经结算的数据,如果存在,那么提示失败
            boolean anyMatch = policyInfoList.stream()
                .anyMatch(a -> ReconcileStatusEnum.RECONCILE_FINISH.getStatusCode().equals(a.getReconcileStatus()));
            if (anyMatch) {
                return "明细存在已经结算的数据,暂不处理";
            }
            // 处理批单号变更 数据冲正
            settlementPolicyInfoService.rectification(policyInfoList);
        }
        //判断这个保全是做啥的
        List<SettlementEventJobEntity> settlementEventJobList = settlementEventJobService.lambdaQuery()
            .eq(SettlementEventJobEntity::getEventBusinessCode, eventJob.getEventBusinessCode())
            .eq(SettlementEventJobEntity::getEndorsementNo, sourceEndorsementNo)
            .eq(SettlementEventJobEntity::getEventSource, "保单中心").list();
        settlementEventJobList.forEach(action -> {
            SettlementEventTypeEnum eventType = SettlementEventTypeEnum.deCode(action.getEventType());
            log.info("处理保全事件保全编码={},事件类型={}", preservationCode, eventType.getEventDesc());
            switch (eventType) {
                // 犹豫期退保
                case HESITATE_SURRENDER: {
                    try {
                        // 从请求报文里获取期数属性
                        if (StrUtil.isBlank(preservationCode)) {
                            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                                StrUtil.format("犹豫期退保 保单号={}, 缺少保全编码", eventJob.getEventBusinessCode())));
                        }
                        ReconcileTypeEnum reconcileTypeEnum =
                            ReconcileTypeEnum.matchSearchCode(eventData.getReconcileType());
                        BasisSettlementPolicyInfoDomain domain = new BasisSettlementPolicyInfoDomain();
                        domain.setContractInfo(eventData.getPolicyInfo());
                        domain.setSettlementEventTypeEnum(eventType);
                        domain.setReconcileTypeEnum(reconcileTypeEnum);
                        domain.setPreservationCode(preservationCode);
                        domain.setEventSourceCode(eventJob.getPushEventCode());
                        // 获取处理事件
                        SettlementPolicyHandler handler =
                            SettlementPolicyFactory.getInvoke(SettlementPolicyHandlerEnum.PRESERVATION);
                        // 构建结算明细基础信息
                        List<SettlementPolicyInfoEntity> settlementPolicyInfoList =
                            handler.buildBasisSettlementPolicyInfo(domain);
                        if (CollUtil.isNotEmpty(settlementPolicyInfoList)) {
                            //匹配产品信息
                            handler.matchInsuranceProduct(reconcileTypeEnum, settlementPolicyInfoList);
                            // 构建手续费和折标保费金额
                            List<SettlementPolicyInfoEntity> resultList =
                                handler.buildSettlementSubjectAmount(settlementPolicyInfoList);
                            if (!resultList.isEmpty()) {
                                settlementPolicyInfoService.saveBatch(resultList);
                            }
                            // 犹豫期退保的保单，需要对新契约的回执、回访进行设置为0
                            settlementPolicyInfoService.lambdaUpdate()
                                .set(SettlementPolicyInfoEntity::getRevisitStatus, 0)
                                .set(SettlementPolicyInfoEntity::getReceiptStatus, 0)
                                .eq(SettlementPolicyInfoEntity::getPolicyNo, eventJob.getEventBusinessCode()).update();
                        } else {
                            log.info(StrUtil.format("没有生成结算明细，保单号={}, 事件编码={}",
                                eventJob.getEventBusinessCode(), eventJob.getPushEventCode()));
                        }
                    } catch (GlobalException e) {
                        throw e;
                    } catch (Exception e) {
                        String msg =
                            StrUtil.format("生成结算明细异常，保单号={}, 事件编码={}", eventJob.getEventBusinessCode(),
                                eventJob.getPushEventCode());
                        log.warn(msg, e);
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(msg));
                    }
                    break;
                }
                //协议解约
                case PROTOCOL_TERMINATION: {
                    try {
                        // 从请求报文里获取期数属性
                        if (StrUtil.isBlank(preservationCode)) {
                            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                                StrUtil.format("附加险解约 保单号={}, 缺少保全编码", eventJob.getEventBusinessCode())));
                        }
                        ReconcileTypeEnum reconcileTypeEnum =
                            ReconcileTypeEnum.matchSearchCode(eventData.getReconcileType());
                        BasisSettlementPolicyInfoDomain domain = new BasisSettlementPolicyInfoDomain();
                        domain.setContractInfo(eventData.getPolicyInfo());
                        domain.setSettlementEventTypeEnum(SettlementEventTypeEnum.PROTOCOL_TERMINATION);
                        domain.setReconcileTypeEnum(reconcileTypeEnum);
                        domain.setPreservationCode(preservationCode);
                        domain.setEventSourceCode(eventJob.getPushEventCode());
                        // 获取处理事件
                        SettlementPolicyHandler handler =
                            SettlementPolicyFactory.getInvoke(SettlementPolicyHandlerEnum.PRESERVATION);
                        // 构建结算明细基础信息
                        List<SettlementPolicyInfoEntity> settlementPolicyInfoList =
                            handler.buildBasisSettlementPolicyInfo(domain);
                        if (CollUtil.isNotEmpty(settlementPolicyInfoList)) {
                            //匹配产品信息
                            handler.matchInsuranceProduct(reconcileTypeEnum, settlementPolicyInfoList);
                            // 构建手续费和折标保费金额
                            List<SettlementPolicyInfoEntity> resultList =
                                handler.buildSettlementSubjectAmount(settlementPolicyInfoList);
                            if (!resultList.isEmpty()) {
                                settlementPolicyInfoService.saveBatch(resultList);
                            }
                        } else {
                            log.info(StrUtil.format("没有生成结算明细，保单号={}, 事件编码={}",
                                eventJob.getEventBusinessCode(), eventJob.getPushEventCode()));
                        }
                    } catch (GlobalException e) {
                        throw e;
                    } catch (Exception e) {
                        String msg =
                            StrUtil.format("生成结算明细异常，保单号={}, 事件编码={}", eventJob.getEventBusinessCode(),
                                eventJob.getPushEventCode());
                        log.warn(msg, e);
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(msg));
                    }
                    break;
                }
                // 标准退保
                case STANDARD_SURRENDER: {
                    // 1 获取保单详情
                    EpContractInfoVo policyInfo = eventData.getPolicyInfo();
                    if (policyInfo.getContractBaseInfo().getLongShortFlag() != null && policyInfo.getContractBaseInfo()
                        .getLongShortFlag() == 1) {
                        break;
                    }
                    try {
                        // 从请求报文里获取期数属性
                        if (StrUtil.isBlank(preservationCode)) {
                            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                                StrUtil.format("附加险解约 保单号={}, 缺少保全编码", eventJob.getEventBusinessCode())));
                        }
                        ReconcileTypeEnum reconcileTypeEnum =
                            ReconcileTypeEnum.matchSearchCode(eventData.getReconcileType());
                        BasisSettlementPolicyInfoDomain domain = new BasisSettlementPolicyInfoDomain();
                        domain.setContractInfo(eventData.getPolicyInfo());
                        domain.setSettlementEventTypeEnum(SettlementEventTypeEnum.STANDARD_SURRENDER);
                        domain.setReconcileTypeEnum(reconcileTypeEnum);
                        domain.setPreservationCode(preservationCode);
                        domain.setEventSourceCode(eventJob.getPushEventCode());
                        // 获取处理事件
                        SettlementPolicyHandler handler =
                            SettlementPolicyFactory.getInvoke(SettlementPolicyHandlerEnum.PRESERVATION);
                        // 构建结算明细基础信息
                        List<SettlementPolicyInfoEntity> settlementPolicyInfoList =
                            handler.buildBasisSettlementPolicyInfo(domain);
                        if (CollUtil.isNotEmpty(settlementPolicyInfoList)) {
                            //匹配产品信息
                            handler.matchInsuranceProduct(reconcileTypeEnum, settlementPolicyInfoList);
                            // 构建手续费和折标保费金额
                            List<SettlementPolicyInfoEntity> resultList =
                                handler.buildSettlementSubjectAmount(settlementPolicyInfoList);
                            if (!resultList.isEmpty()) {
                                settlementPolicyInfoService.saveBatch(resultList);
                            }
                        } else {
                            log.info(StrUtil.format("没有生成结算明细，保单号={}, 事件编码={}",
                                eventJob.getEventBusinessCode(), eventJob.getPushEventCode()));
                        }
                    } catch (GlobalException e) {
                        throw e;
                    } catch (Exception e) {
                        String msg =
                            StrUtil.format("生成结算明细异常，保单号={}, 事件编码={}", eventJob.getEventBusinessCode(),
                                eventJob.getPushEventCode());
                        log.warn(msg, e);
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(msg));
                    }
                    break;
                }
                // 附加险解约
                case TERMINATION_PRODUCT: {
                    // 1 获取保单详情
                    EpContractInfoVo policyInfo = eventData.getPolicyInfo();
                    Integer longShortFlag = policyInfo.getContractBaseInfo().getLongShortFlag();
                    if (longShortFlag != null && longShortFlag == 1) {
                        break;
                    }
                    try {
                        // 从请求报文里获取期数属性
                        if (StrUtil.isBlank(preservationCode)) {
                            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                                StrUtil.format("附加险解约 保单号={}, 缺少保全编码", eventJob.getEventBusinessCode())));
                        }
                        ReconcileTypeEnum reconcileTypeEnum =
                            ReconcileTypeEnum.matchSearchCode(eventData.getReconcileType());
                        BasisSettlementPolicyInfoDomain domain = new BasisSettlementPolicyInfoDomain();
                        domain.setContractInfo(eventData.getPolicyInfo());
                        domain.setSettlementEventTypeEnum(SettlementEventTypeEnum.TERMINATION_PRODUCT);
                        domain.setReconcileTypeEnum(reconcileTypeEnum);
                        domain.setPreservationCode(preservationCode);
                        domain.setEventSourceCode(eventJob.getPushEventCode());
                        // 获取处理事件
                        SettlementPolicyHandler handler =
                            SettlementPolicyFactory.getInvoke(SettlementPolicyHandlerEnum.PRESERVATION);
                        // 构建结算明细基础信息
                        List<SettlementPolicyInfoEntity> settlementPolicyInfoList =
                            handler.buildBasisSettlementPolicyInfo(domain);
                        if (CollUtil.isNotEmpty(settlementPolicyInfoList)) {
                            //匹配产品信息
                            handler.matchInsuranceProduct(reconcileTypeEnum, settlementPolicyInfoList);
                            // 构建手续费和折标保费金额
                            List<SettlementPolicyInfoEntity> resultList =
                                handler.buildSettlementSubjectAmount(settlementPolicyInfoList);
                            if (!resultList.isEmpty()) {
                                settlementPolicyInfoService.saveBatch(resultList);
                            }
                        } else {
                            log.info(StrUtil.format("没有生成结算明细，保单号={}, 事件编码={}",
                                eventJob.getEventBusinessCode(), eventJob.getPushEventCode()));
                        }
                    } catch (GlobalException e) {
                        throw e;
                    } catch (Exception e) {
                        String msg =
                            StrUtil.format("生成结算明细异常，保单号={}, 事件编码={}", eventJob.getEventBusinessCode(),
                                eventJob.getPushEventCode());
                        log.warn(msg, e);
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(msg));
                    }
                    break;
                }
                // 团险增减员
                case GROUP_ADD_OR_SUBTRACT: {
                    JSONObject eventRequest = JSONObject.parseObject(action.getEventRequest());
                    if (!eventRequest.containsKey("surrenderCash")) {
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                            "团险增减员获取保单详情，保全保费信息缺失，保单号=" + eventJob.getEventBusinessCode()));
                    }
                    if (eventRequest.getBigDecimal("surrenderCash").compareTo(BigDecimal.ZERO) == 0) {
                        break;
                    }
                    try {
                        // 从请求报文里获取期数属性
                        if (StrUtil.isBlank(preservationCode)) {
                            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                                StrUtil.format("团险增减员 保单号={}, 缺少保全编码", eventJob.getEventBusinessCode())));
                        }
                        ReconcileTypeEnum reconcileTypeEnum =
                            ReconcileTypeEnum.matchSearchCode(eventData.getReconcileType());
                        BasisSettlementPolicyInfoDomain domain = new BasisSettlementPolicyInfoDomain();
                        domain.setContractInfo(eventData.getPolicyInfo());
                        domain.setSettlementEventTypeEnum(eventType);
                        domain.setReconcileTypeEnum(reconcileTypeEnum);
                        domain.setPreservationCode(preservationCode);
                        domain.setEventSourceCode(eventJob.getPushEventCode());
                        // 获取处理事件
                        SettlementPolicyHandler handler =
                            SettlementPolicyFactory.getInvoke(SettlementPolicyHandlerEnum.PRESERVATION);
                        // 构建结算明细基础信息
                        List<SettlementPolicyInfoEntity> settlementPolicyInfoList =
                            handler.buildBasisSettlementPolicyInfo(domain);
                        if (CollUtil.isNotEmpty(settlementPolicyInfoList)) {
                            //匹配产品信息
                            handler.matchInsuranceProduct(reconcileTypeEnum, settlementPolicyInfoList);
                            // 构建手续费和折标保费金额
                            List<SettlementPolicyInfoEntity> resultList =
                                handler.buildSettlementSubjectAmount(settlementPolicyInfoList);
                            if (!resultList.isEmpty()) {
                                settlementPolicyInfoService.saveBatch(resultList);
                            }
                        } else {
                            log.info(StrUtil.format("没有生成结算明细，保单号={}, 事件编码={}",
                                eventJob.getEventBusinessCode(), eventJob.getPushEventCode()));
                        }
                    } catch (GlobalException e) {
                        throw e;
                    } catch (Exception e) {
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                            StrUtil.format("生成结算明细异常，保单号={}, 事件编码={}", eventJob.getEventBusinessCode(),
                                eventJob.getPushEventCode())));
                    }
                }
            }
        });
        return "success";
    }

    @Override
    public HandleEventCheckResult handleCostEventCheck(SettlementEventJobEntity eventJob) {

        //return super.handlerPreservationCostEventCheck(eventJob, handlerEventType());
        ChangeEndorsementNoData endorsementNoData =
                JSONObject.parseObject(eventJob.getEventRequest(), ChangeEndorsementNoData.class);
        String endorsementNo = endorsementNoData.getNewEndorsementNo();
        // 1 根据新保全编号判断是否存在支出明细纪录
        if (StringUtils.isNotBlank(endorsementNo)) {
            Integer count =
                    settlementCostInfoService.lambdaQuery().eq(SettlementCostInfoEntity::getEndorsementNo, endorsementNo)
                            .eq(SettlementCostInfoEntity::getCorrectionFlag, 0)
                            .eq(SettlementCostInfoEntity::getSettlementEventCode,handlerEventType()).count();
            //存在记录
            if (count > 0) {
                return HandleEventCheckResult.builder().checkStatus(false).checkMsg(
                        StrUtil.format("支出端-该{}保全已经存在支出明细数据，保单号={}, 批单号={}",
                                handlerEventType().getEventDesc(), eventJob.getEventBusinessCode(), endorsementNo)).build();
            }
        }


        //原批单号记录是否存在
        Integer count =
                settlementCostInfoService.lambdaQuery().eq(SettlementCostInfoEntity::getEndorsementNo, eventJob.getEndorsementNo())
                        .eq(SettlementCostInfoEntity::getCorrectionFlag, 0).count();
        // 如果存在纪录
        if (count == 0) {
            return HandleEventCheckResult.builder().checkStatus(false).checkMsg(
                    StrUtil.format("支出端-原批单号记录不存在，保单号={} 原保全批单号={}",
                            eventJob.getEventBusinessCode(), eventJob.getEndorsementNo())).build();
        }
        return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();

    }

    @Override
    public String handleCostEvent(SettlementEventJobEntity eventJob, HandleEventData eventData) {

        ChangeEndorsementNoData endorsementNoData =
            JSONObject.parseObject(eventJob.getEventRequest(), ChangeEndorsementNoData.class);

        String sourceEndorsementNo = endorsementNoData.getEndorsementNo();
        if (StrUtil.isEmpty(sourceEndorsementNo)) {
            return "原批改单号不存在-success";
        }

        String endorsementNo = endorsementNoData.getNewEndorsementNo();
        if (StrUtil.isEmpty(endorsementNo)) {
            return "新批改单号不存在-success";
        }

        List<SettlementCostInfoEntity> costInfoEntityList =
            settlementCostInfoService.lambdaQuery().eq(SettlementCostInfoEntity::getEndorsementNo, sourceEndorsementNo)
                .eq(SettlementCostInfoEntity::getCorrectionFlag, 0).list();
        if (CollectionUtils.isEmpty(costInfoEntityList)) {
            return "原批改单号结算信息不存在-success";
        }

        SettlementCostPolicyInfoEntity costPolicy =
            settlementCostPolicyInfoService.getCostPolicyInfoEntityByContractCode(eventJob.getContractCode());

        if (Objects.isNull(costPolicy)) {
            return "结算保单信息缺失-success";
        }
        //2、参数验证，验证不通过抛出异常
        settlementCostProcessService.validParam(eventJob, null);

        //冲正
        CostCorrectionDto costCorrectionDto = new CostCorrectionDto();

        List<SettlementCostInfoEntity> newCostList = costInfoEntityList.stream().map(x -> {
            //生成一条对冲信息
            SettlementCostInfoEntity fixSettlementCostInfo =
                settlementCostProcessService.builderOffsetCostInfo(eventJob, handlerEventType(), x,eventJob.getCreateTime(),
                    SYSTEM_CORRECTION_USER, "批单号变更冲正", Boolean.TRUE);
            //生成一条新结算信息
            SettlementCostInfoEntity newSettlementCostInfo = new SettlementCostInfoEntity();
            BeanUtil.copyProperties(x, newSettlementCostInfo);
            newSettlementCostInfo.setId(null);
            newSettlementCostInfo.setCostCode(PolicySettlementUtils.createCodeLastNumber("CC"));
            //记账时间处理
            newSettlementCostInfo.setSettlementTime(new Date());
            newSettlementCostInfo.setSettlementDate(newSettlementCostInfo.getSettlementTime());

            //事件编号
            newSettlementCostInfo.setEventSourceCode(eventJob.getPushEventCode());

            //事件信息
            newSettlementCostInfo.setSettlementEventCode(handlerEventType().getEventCode());
            newSettlementCostInfo.setSettlementEventDesc(handlerEventType().getEventDesc());
            newSettlementCostInfo.setCommissionType(x.getCommissionType());
            newSettlementCostInfo.setSettlementGenerateType(2);
            //是否冲正
            //业务记账时间处理
            newSettlementCostInfo.setBusinessAccountTime(
                SettlementCostProcessServiceImpl.getCorrectionBusinessAccountTime(x.getBusinessAccountTime(),eventJob.getCreateTime()));
            newSettlementCostInfo.setSourceCostCode(x.getCostCode());
            newSettlementCostInfo.setCorrectionTime(new Date());
            newSettlementCostInfo.setCorrectionFlag(0);
            newSettlementCostInfo.setEndorsementNo(endorsementNo);

            newSettlementCostInfo.setCorrectionOpType(1);
            if (StringUtil.isNotBlank(endorsementNoData.getBusinessDesc())) {
                newSettlementCostInfo.setCorrectionRemark(endorsementNoData.getBusinessDesc());
            }
            //清除确认信息
            newSettlementCostInfo.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
            newSettlementCostInfo.setConfirmUser(null);
            newSettlementCostInfo.setConfirmTime(null);
            newSettlementCostInfo.setConfirmGrantTime(null);
            newSettlementCostInfo.setDocumentCode(null);
            newSettlementCostInfo.setAutoCostCode(null);
            newSettlementCostInfo.setCostSettlementCycle(null);
            newSettlementCostInfo.setBusinessDiscountPremium(x.getBusinessDiscountPremium());

            return Lists.newArrayList(fixSettlementCostInfo, newSettlementCostInfo);
        }).flatMap(Collection::stream).collect(Collectors.toList());

        costCorrectionDto.setNewCostList(newCostList);
        costCorrectionDto.setCorrectionOldIds(
            costInfoEntityList.stream().map(SettlementCostInfoEntity::getId).collect(Collectors.toList()));
        costCorrectionDto.setPolicy(costPolicy);

        settlementCostCorrectionService.saveCostCommissionRecord(eventJob, costCorrectionDto);
        policyCenterBaseClient.governApplyConfirm(eventJob.getEventBusinessCode(), "settlement_server", "支出端");
        return "success";
    }

    @Override
    public SettlementEventTypeEnum handlerEventType() {
        return SettlementEventTypeEnum.CHANGE_ENDORSEMENT_NO;
    }
}
