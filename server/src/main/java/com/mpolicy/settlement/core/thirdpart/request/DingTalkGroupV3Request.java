package com.mpolicy.settlement.core.thirdpart.request;

import com.mpolicy.settlement.core.thirdpart.enums.DingTalkEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class DingTalkGroupV3Request {

    @ApiModelProperty("文本消息")
    private String text;
    @ApiModelProperty("钉钉消息markdown")
    private DingTalkGroupMarkDownMessageRequest dingTalkGroupMarkDownMessageRequest;
    @ApiModelProperty("钉钉枚举")
    @NotNull(
            message = "钉钉枚举不能为空"
    )
    private DingTalkEnum dingTalkEnum;
    @ApiModelProperty(
            value = "钉钉群code",
            required = true
    )
    private String dingTalkGroupCode;
    @ApiModelProperty(
            value = "钉钉群发送URL",
            required = true
    )
    private String dingTalkGroupSendUrl;
    @ApiModelProperty("手机号列表")
    private List<String> mobiles;
    @ApiModelProperty("是否@全体成员")
    private Boolean isAtAll;
}
