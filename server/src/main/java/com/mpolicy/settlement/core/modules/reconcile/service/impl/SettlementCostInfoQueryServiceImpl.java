package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import com.mpolicy.settlement.core.modules.reconcile.service.SettlementCostInfoService;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementCostPolicyInfoService;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementCostInfoQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/31 2:37 下午
 * @Version 1.0
 */
@Service
@Slf4j
public class SettlementCostInfoQueryServiceImpl implements SettlementCostInfoQueryService {
    @Autowired
    private SettlementCostInfoService settlementCostInfoService;
    @Autowired
    private SettlementCostPolicyInfoService settlementCostPolicyInfoService;


}
