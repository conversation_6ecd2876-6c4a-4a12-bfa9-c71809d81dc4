package com.mpolicy.settlement.core.modules.reconcile.service;

import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.policy.common.ep.policy.EpInsuredInfoVo;
import com.mpolicy.policy.common.ep.policy.EpProductInfoVo;
import com.mpolicy.policy.common.ep.policy.qry.details.GroupPreservationInsuredVo;
import com.mpolicy.product.common.base.ProductBase;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.PolicyInterruptDto;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.PolicyPreservationDetailDto;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.PolicyRenewalTermDto;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.PreservationCustomerChangeDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 支出端计算过程服务
 * @date 2023/8/16 2:48 下午
 * @Version 1.0
 */
public interface SettlementCostProcessService {
    /**
     * 参数验证
     * @param eventJob
     * @param policyInfo
     */
    void validParam(SettlementEventJobEntity eventJob, EpContractInfoVo policyInfo, Runnable validCustomize);

    /**
     * 参数验证，用于非保单事件或者刷数事件
     * @param eventJob
     * @param validCustomize
     */
    void validParam(SettlementEventJobEntity eventJob, Runnable validCustomize);
    /**
     * 获取险种配置信息，返回以险种编码为主键的map对象
     * @param productInfoVos
     * @return 险种编码为主键的map对象
     */
    Map<String, ProductBase> getProductBaseMap(List<EpProductInfoVo> productInfoVos);

    /**
     * 保存支出端佣金记录
     * @param eventJob
     * @param policy
     * @param costInfoList
     */
    void saveCostCommissionRecord(SettlementEventJobEntity eventJob, SettlementCostPolicyInfoEntity policy, List<SettlementCostInfoEntity> costInfoList);

    /**
     * 支出端佣金记录保存(通用)
     * @param policy
     * @param costInfoList
     */
    void saveCostRecord( SettlementCostPolicyInfoEntity policy, List<SettlementCostInfoEntity> costInfoList);

    /**
     * 支出端-创建保单基础信息
     * @param policyInfo
     * @return
     */
    SettlementCostPolicyInfoEntity buildSettlementCostPolicyInfo(EpContractInfoVo policyInfo);

    /**
     * 组装退保更新保单基础信息
     * @param policyInfo
     * @param entity
     */
    void builderUpdateSettlementCostPolicyInfo(EpContractInfoVo policyInfo,SettlementCostPolicyInfoEntity entity);

    /**
     * 支出端-根据险种列表生成佣金记录(车险、没有被保人明细的团险)
     * @param eventJob     事件信息
     * @param subjectEnum  科目
     * @param eventType    事件类型
     * @param productMap   险种信息
     * @param policyInfo   保单信息
     * @return
     */
    List<SettlementCostInfoEntity> builderSettlementCostInfoByProductList(SettlementEventJobEntity eventJob, CostSubjectEnum subjectEnum, SettlementEventTypeEnum eventType, Map<String,ProductBase> productMap, EpContractInfoVo policyInfo);
    /**
     * 支出端-团险根据被保人列表生成佣金记录
     * @param eventJob
     * @param subjectEnum
     * @param eventType
     * @param productMap
     * @param policyInfo
     * @param preservationDetail
     * @return
     */
    List<SettlementCostInfoEntity> builderGroupSettlementCostInfoByProductList(SettlementEventJobEntity eventJob,
                                                                               CostSubjectEnum subjectEnum,
                                                                               SettlementEventTypeEnum eventType,
                                                                               Map<String,ProductBase> productMap,
                                                                               EpContractInfoVo policyInfo,
                                                                               PolicyPreservationDetailDto preservationDetail);
    /**
     * 支出端-根据被保人列表生成佣金记录
     * @param eventJob     事件信息
     * @param subjectEnum  科目
     * @param eventType    事件类型
     * @param productMap   险种信息
     * @param policyInfo   保单信息
     * @return
     */
    List<SettlementCostInfoEntity> builderSettlementCostInfoByInsuredList(SettlementEventJobEntity eventJob, CostSubjectEnum subjectEnum,SettlementEventTypeEnum eventType, Map<String,ProductBase> productMap,EpContractInfoVo policyInfo,List<EpInsuredInfoVo> insuredInfoVos);

    /**
     * 支出端-根据被保人列表生成续期佣金记录
     * @param eventJob     事件信息
     * @param subjectEnum  科目
     * @param eventType    事件类型
     * @param productMap   险种信息
     * @param policyInfo   保单信息
     * @return
     */
    List<SettlementCostInfoEntity> builderRenewalSettlementCostInfoByInsuredList(SettlementEventJobEntity eventJob, CostSubjectEnum subjectEnum, SettlementEventTypeEnum eventType, Map<String,ProductBase> productMap, EpContractInfoVo policyInfo, PolicyRenewalTermDto renewalTermDto);

    /**
     * 支出端-根据险种列表生成续期佣金记录
     * @param eventJob
     * @param subjectEnum
     * @param eventType
     * @param productMap
     * @param policyInfo
     * @param renewalTermDto
     * @return
     */
    List<SettlementCostInfoEntity> builderRenewalSettlementCostInfoByProductList(SettlementEventJobEntity eventJob,
                                                                                     CostSubjectEnum subjectEnum,
                                                                                     SettlementEventTypeEnum eventType,
                                                                                     Map<String, ProductBase> productMap,
                                                                                     EpContractInfoVo policyInfo,
                                                                                     PolicyRenewalTermDto renewalTermDto);
    /**
     *
     * @param eventJob
     * @param subjectEnum
     * @param eventType
     * @param productMap
     * @param policyInfo
     * @param preservationDetail
     * @param insuredInfoVos
     * @return
     */
    List<SettlementCostInfoEntity> builderGroupSettlementCostInfoByInsuredList(SettlementEventJobEntity eventJob,
                                                                               CostSubjectEnum subjectEnum,
                                                                               SettlementEventTypeEnum eventType,
                                                                               Map<String,ProductBase> productMap,
                                                                               EpContractInfoVo policyInfo,PolicyPreservationDetailDto preservationDetail,List<GroupPreservationInsuredVo> insuredInfoVos);

    /**
     * 附加险解约
     * @param eventJob
     * @param subjectEnum
     * @param eventType
     * @param productMap
     * @param policyInfo
     * @param preservationDetail
     * @return
     */
    List<SettlementCostInfoEntity> builderPolicyTerminationProductCostInfo(SettlementEventJobEntity eventJob,
                                                                           CostSubjectEnum subjectEnum,
                                                                           SettlementEventTypeEnum eventType,
                                                                           Map<String,ProductBase> productMap,
                                                                           EpContractInfoVo policyInfo,
                                                                           PolicyPreservationDetailDto preservationDetail);

    
    /**
     * 长险保单中断
     * @param eventJob
     * @param subjectEnum
     * @param eventType
     * @param productMap
     * @param policyInfo
     * @return
     */
    List<SettlementCostInfoEntity> builderPolicyInterruptCostInfo(SettlementEventJobEntity eventJob,
                                                                  CostSubjectEnum subjectEnum,
                                                                  SettlementEventTypeEnum eventType,
                                                                  Map<String, ProductBase> productMap,
                                                                  EpContractInfoVo policyInfo,
                                                                  PolicyInterruptDto dto);

    /**
     *
     * @param policy
     */
    void updateCostPolicyRecord(SettlementCostPolicyInfoEntity policy);

    SettlementCostInfoEntity builderOffsetCostInfo(SettlementEventJobEntity eventJob,
                                                   SettlementEventTypeEnum eventType,
                                                   SettlementCostInfoEntity old,
                                                   Date newBusinessTime,
                                                   String correctionUser,
                                                   String correctionRemark, Boolean isCorrection);

    SettlementCostInfoEntity builderOffsetCostInfoBySourceCode(String sourceCode,
                                                   SettlementEventTypeEnum eventType,
                                                   SettlementCostInfoEntity old,
                                                               Date newBusinessTime,
                                                   String correctionUser,
                                                   String correctionRemark, Boolean isCorrection);



    Map<String, ProductBase> mapProductBaseByProductCodes(List<String> productCodes);

    SettlementCostInfoEntity builderOriginalChangeNewCostInfo(SettlementEventJobEntity eventJob,
                                                                            SettlementEventTypeEnum eventType,
                                                                            SettlementCostInfoEntity old,
                                                                            Date newBusinessTime);


}
