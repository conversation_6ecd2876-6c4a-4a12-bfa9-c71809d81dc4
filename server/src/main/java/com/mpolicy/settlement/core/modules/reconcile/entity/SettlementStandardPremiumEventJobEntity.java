package com.mpolicy.settlement.core.modules.reconcile.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 结算交互事件受理表
 * 
 * <AUTHOR>
 * @since  2023-05-21 22:28:33
 */
@TableName("settlement_standard_premium_event_job")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementStandardPremiumEventJobEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;

	/**
	 * 事件push编码
	 */
	private String pushEventCode;

	/**
	 * 事件类型
	 */
	private String eventType;

	/**
	 * 事件名称
	 */
	private String eventName;

	/**
	 * 事件名称
	 */
	private String eventDesc;

	/**
	 * 业务编码
	 */
	private String eventBusinessCode;

	/**
	 * 保单中心合同编号
	 */
	private String contractCode;
	/**
	 * 保全批单号
	 */
	private String endorsementNo;

	/**
	 * 事件来源 保单中心、协议管理、车险佣金
	 */
	private String eventSource;

	/**
	 * 业务请求事件报文
	 */
	private String eventRequest;

	/**
	 * 业务请求响应报文
	 */
	private String eventResponse;


	/**
	 * 事件状态;0代处理1处理中2处理完成3无需处理-1处理失败
	 */
	private Integer eventStatus;

	/**
	 * 异常编码
	 */
	private String errorCode;

	/**
	 * 请求响应消息
	 */
	private String eventMessage;

	/**
	 * 事件处理耗时
	 */
	private long eventCostMillis;

	/**
	 * 事件处理完成时间
	 */
	private Date eventFinishTime;

	private String channelCode;



	/**
	 * 是否删除;0有效-1删除
	 */
	@TableLogic
	private Integer deleted;

	/**
	 * 创建人
	 */
	private String createUser;

	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;

	/**
	 * 更新人
	 */
	private String updateUser;

	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;

	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private Integer revision;
}
