package com.mpolicy.settlement.core.modules.reconcile.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileSubjectEntity;

import java.util.List;

/**
 * 保司结算对账单科目
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:34
 */
public interface SettlementReconcileSubjectService extends IService<SettlementReconcileSubjectEntity> {

    /**
     * 批量插入
     * @param entityList
     */
    void insertBatchSomeColumn(List<SettlementReconcileSubjectEntity> entityList);

    /**
     * 更新对账单科目信息
     * @param reconcileCode
     */
    void updateSettlementReconcileSubject(String reconcileCode);
}

