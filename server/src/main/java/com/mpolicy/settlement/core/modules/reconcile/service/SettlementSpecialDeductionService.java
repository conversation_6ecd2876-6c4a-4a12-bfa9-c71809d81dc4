package com.mpolicy.settlement.core.modules.reconcile.service;

import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.product.common.base.ProductBase;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.EpPreserveSurrenderInsuredDto;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.EpPreserveSurrenderProductDto;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.PolicyPreservationDetailDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;

import java.util.List;
import java.util.Map;

public interface SettlementSpecialDeductionService {
    List<SettlementCostInfoEntity> builderSpecialDeductionByInsuredInfo(SettlementEventJobEntity eventJob,
                                                                        CostSubjectEnum subjectEnum,
                                                                        SettlementEventTypeEnum eventType,
                                                                        Map<String, ProductBase> productMap,
                                                                        EpContractInfoVo policyInfo,
                                                                        PolicyPreservationDetailDto preservationDetail,List<EpPreserveSurrenderInsuredDto> surrenderDetails);

    List<SettlementCostInfoEntity> builderSpecialDeductionByProductInfo(SettlementEventJobEntity eventJob,
                                                                        CostSubjectEnum subjectEnum,
                                                                        SettlementEventTypeEnum eventType,
                                                                        Map<String, ProductBase> productMap,
                                                                        EpContractInfoVo policyInfo,
                                                                        PolicyPreservationDetailDto preservationDetail,List<EpPreserveSurrenderProductDto> surrenderDetails);
}
