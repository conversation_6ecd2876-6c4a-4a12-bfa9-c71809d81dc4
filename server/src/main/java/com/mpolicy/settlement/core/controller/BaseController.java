package com.mpolicy.settlement.core.controller;

import com.mpolicy.settlement.core.common.Constant;
import com.mpolicy.web.common.access.UserContext;
import com.mpolicy.web.common.jwt.vo.SubjectInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;

/**
 * 基础 controller
 *
 * <AUTHOR>
 * @since 2021/2/7
 */
@Slf4j
public class BaseController {

    /**
     * 环境
     */
    @Value("${spring.profiles.active:dev}")
    private String profilesActive;

    /**
     * 构建测试租户信息
     * @return
     */
    protected SubjectInfo getSubjectInfo(){

        if(StringUtils.equals(profilesActive,"dev")){
            SubjectInfo subjectInfo = new SubjectInfo();
            // 签发对象：custoemrCode
            subjectInfo.setApplyAuthNo("C20220527101753179050");
            subjectInfo.setApplyAuthName("张三");
            // 应用编码：智能保险运算定制对应关键key
            subjectInfo.setChannel(Constant.CUSTOMER_SERVER_CHANNEL);
            return subjectInfo;
        }else{
            return UserContext.getUser();
        }
    }

    /**
     * 是经纪人平台操作用户
     */
    protected boolean isAgentChannel(SubjectInfo subjectInfo){
        return StringUtils.equals(subjectInfo.getChannel(),Constant.AGENT_SERVER_CHANNEL);
    }

    /**
     * 是小程序客户系统操作用户
     */
    protected boolean isCustomerChannel(SubjectInfo subjectInfo){
        return StringUtils.equals(subjectInfo.getChannel(),Constant.CUSTOMER_SERVER_CHANNEL);
    }
}
