package com.mpolicy.settlement.core.common;

import com.mpolicy.common.redis.key.BasePrefix;

/**
 * <p>
 * 缓存keys
 * </p>
 *
 * <AUTHOR>
 * @since 2022/10/11
 */
public class SettlementCoreCenterKeys extends BasePrefix {

    //3小时
    private static int THREE_HOURS = (1 * 60 * 60 * 2);

    private SettlementCoreCenterKeys(String prefix) {
        super(prefix);
    }

    private SettlementCoreCenterKeys(int expireSeconds, String prefix) {
        super(expireSeconds, prefix);
    }

    /**
     * 测试使用的key
     */
    public static SettlementCoreCenterKeys TEST_KEY = new SettlementCoreCenterKeys(60 * 30, "testKey");

    /**
     * 员工区域分支缓存
     */
    public static SettlementCoreCenterKeys EMPLOYEE_REGION_ORG = new SettlementCoreCenterKeys(60 * 60 * 6, "EMPLOYEE_REGION_ORG");

    /**
     * 商品信息缓存
     */
    public static SettlementCoreCenterKeys SELL_PRODUCT_INFO = new SettlementCoreCenterKeys(60 * 60 * 24, "SELL_PRODUCT_INFO");


    public static SettlementCoreCenterKeys HEAD_BRANCH_INFO = new SettlementCoreCenterKeys(60 * 60 * 24, "HEAD_BRANCH_INFO");

    public static SettlementCoreCenterKeys EMPLOYEE_POST_RECORD = new SettlementCoreCenterKeys(60 * 60 * 24, "EMPLOYEE_POST_RECORD");
    /**
     * 超时1分钟重置
     */
    public static SettlementCoreCenterKeys SETTLEMENT_SUBJECT = new SettlementCoreCenterKeys(60 * 5, "SETTLEMENT_SUBJECT");
}
