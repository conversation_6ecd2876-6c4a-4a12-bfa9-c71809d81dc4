package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.oss.StorageService;
import com.mpolicy.common.oss.vo.OssBaseOut;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.policy.common.enums.*;
import com.mpolicy.service.common.service.DicCacheHelper;
import com.mpolicy.settlement.core.entity.SysDocumentEntity;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementPolicyInfoDao;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementReconcileInfoDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.PolicyInsuredPeriodTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcileInfoService;
import com.mpolicy.settlement.core.modules.reconcile.vo.SettlementReconcilePolicyXls;
import com.mpolicy.settlement.core.modules.reconcile.vo.SqlHelperVo;
import com.mpolicy.settlement.core.service.SysDocumentService;
import com.mpolicy.settlement.core.utils.SettlementCoreUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 保司结算对账单
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:39
 */
@Slf4j
@Service("settlementReconcileInfoService")
public class SettlementReconcileInfoServiceImpl
    extends ServiceImpl<SettlementReconcileInfoDao, SettlementReconcileInfoEntity>
    implements SettlementReconcileInfoService {

    @Autowired
    private SettlementPolicyInfoDao settlementPolicyInfoDao;
    /***
     * oss文件操作
     */
    @Autowired
    protected StorageService storageService;

    /***
     * 文件存储service
     */
    @Autowired
    protected SysDocumentService sysDocumentService;

    /**
     * 生成对账单明细
     *
     * @param reconcileInfo
     */
    @Override
    public void generateDetail(SettlementReconcileInfoEntity reconcileInfo) {
        List<SettlementPolicyInfoEntity> settlementPolicyInfoList =
            new LambdaQueryChainWrapper<>(settlementPolicyInfoDao).eq(SettlementPolicyInfoEntity::getReconcileCode,
                reconcileInfo.getReconcileCode()).list();
        this.settlementPolicyInfoToXls(reconcileInfo, settlementPolicyInfoList);
    }

    @Async
    @Override
    public void settlementPolicyInfoToXls(SettlementReconcileInfoEntity reconcileInfo,
        List<SettlementPolicyInfoEntity> settlementPolicyInfoList) {
        // 构建导出xls文件
        List<SettlementReconcilePolicyXls> xlsData = settlementPolicyInfoList.stream().map(x -> {
            SettlementReconcilePolicyXls bean = new SettlementReconcilePolicyXls();
            BeanUtils.copyProperties(x, bean);
            bean.setReconcileCode(reconcileInfo.getReconcileCode());
            bean.setReconcileName(reconcileInfo.getReconcileName());
            bean.setReconcileMonth(reconcileInfo.getReconcileMonth());
            // 构建导出vo信息
            buildSettlementReconcilePolicyXlsVo(x, bean);
            return bean;
        }).collect(Collectors.toList());
        // 执行xls生成、上传oss
        String fileName = reconcileInfo.getReconcileName().concat("对账保单数据.xlsx");
        try (OutputStream out = Files.newOutputStream(Paths.get(StrUtil.format("logs/{}", fileName)))) {
            // 生成保单文件
            ExcelWriter writer = new ExcelWriter(out, ExcelTypeEnum.XLSX);
            // 设置SHEET名称
            Sheet sheet = new Sheet(1, 0, SettlementReconcilePolicyXls.class);
            sheet.setSheetName(reconcileInfo.getReconcileName() + "-对账保单列表");
            writer.write(xlsData, sheet);
            writer.finish();
            out.flush();
            // 上传到oss
            OssBaseOut policy = uploadExportReport(new File(StrUtil.format("logs/{}", fileName)), "reconcile");
            log.info("上传到oss完成.....上传结果={}", JSON.toJSONString(policy));
            reconcileInfo.setPolicyCount(xlsData.size());
            reconcileInfo.setReconcilePolicyUrl(policy.getAccessDomainPath());
            baseMapper.updateById(reconcileInfo);
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    protected OssBaseOut uploadExportReport(File file, String modelCode) {
        OssBaseOut ossResult =
            storageService.uploadFileInputSteam("settlement/reconcile/".concat(FileUtil.getName(file)), file);
        SysDocumentEntity bean = new SysDocumentEntity();
        // 文件大小
        bean.setFileSize(file.length());
        bean.setFileName(FileUtil.getName(file));
        bean.setFileType(FileUtil.getType(file));
        bean.setFileExt(FileUtil.getSuffix(file));
        // 文件编码
        bean.setFileCode(CommonUtils.createCode("oss"));
        // oss访问
        bean.setFilePath(ossResult.getFilePath());
        bean.setDomainPath(ossResult.getAccessDomainPath());
        bean.setFileSystem("settlement");
        bean.setFileModule(modelCode);
        bean.setRelationCode(DateUtil.today());
        sysDocumentService.save(bean);
        return ossResult;
    }

    private void buildSettlementReconcilePolicyXlsVo(SettlementPolicyInfoEntity sourceData,
        SettlementReconcilePolicyXls xlsData) {
        // 保单类型
        if (StringUtils.isNotBlank(sourceData.getPolicyProductType())) {
            xlsData.setPolicyProductType(
                PolicyProductTypeEnum.getProdTypeEnum(sourceData.getPolicyProductType()).getPolicyProductType());
        }
        // 大类信息
        if (StringUtils.isNotBlank(sourceData.getProductGroup())) {
            xlsData.setProductGroup(DicCacheHelper.getValue(sourceData.getProductGroup()));
        }
        // 二类 + 三分类信息
        if (StringUtils.isNotBlank(sourceData.getLevel2Code())) {
            xlsData.setLevel2Code(DicCacheHelper.getValue(sourceData.getLevel2Code()));
        }
        if (StringUtils.isNotBlank(sourceData.getLevel3Code())) {
            xlsData.setLevel3Code(DicCacheHelper.getValue(sourceData.getLevel3Code()));
        }
        // 科目信息 + 长短线
        xlsData.setReconcileSubjectCode(sourceData.getSettlementSubjectCode());
        xlsData.setReconcileSubjectName(sourceData.getSettlementSubjectName());
        if (sourceData.getLongShortFlag() != null && sourceData.getLongShortFlag() != -1) {
            xlsData.setLongShortFlag(sourceData.getLongShortFlag() == 1 ? "长险" : "短险");
        } else {
            xlsData.setLongShortFlag("未知");
        }
        // 是否主险
        if (sourceData.getMainInsurance() != null) {
            xlsData.setMainInsurance(sourceData.getMainInsurance() == 1 ? "是" : "否");
        } else {
            xlsData.setMainInsurance("未知");
        }
        // 保障期间
        if (StringUtils.isNotBlank(sourceData.getInsuredPeriodType())) {
            xlsData.setInsuredPeriodType(
                PolicyInsuredPeriodTypeEnum.getInsuredTypeEnumByCode(sourceData.getInsuredPeriodType())
                    .getInsuredType());
        }
        // 缴费方式
        if (StringUtils.isNotBlank(sourceData.getPeriodType())) {
            xlsData.setPeriodType(
                PolicyPaymentTypeEnum.getPaymentTypeEnumByCode(sourceData.getPeriodType()).getPaymentDesc());
        }
        // 缴费期间类型
        if (StringUtils.isNotBlank(sourceData.getPaymentPeriodType())) {
            xlsData.setPaymentPeriodType(
                PolicyPaymentPeriodTypeEnum.getPaymentPeriodTypeEnumByCode(sourceData.getPaymentPeriodType())
                    .getPeriodUnit());
        }
        // 结算比例处理
        xlsData.setSettlementRate(StrUtil.format("{}{}", SettlementCoreUtils.getDecimalNoZero(
            new BigDecimal(xlsData.getSettlementRate()).multiply(new BigDecimal("100"))), "%"));
        // 结算比例金额四舍五入保留2位小数
        xlsData.setSettlementAmount(xlsData.getSettlementAmount().setScale(2, RoundingMode.HALF_UP));
        // 保单来源 + 销售方式 + 销售平台
        if (StringUtils.isNotBlank(xlsData.getPolicySource())) {
            xlsData.setPolicySource(PolicySourceEnum.getPolicySourceEnum(xlsData.getPolicySource()) != null
                ? PolicySourceEnum.getPolicySourceEnum(xlsData.getPolicySource()).getSourceDesc() : "未知");
        }
        if (sourceData.getSalesType() != null) {
            xlsData.setSalesType(sourceData.getSalesType() == 1 ? "线下" : "网销");
        } else {
            xlsData.setSalesType("未知");
        }
        if (StringUtils.isNotBlank(xlsData.getSalesPlatform())) {
            xlsData.setSalesPlatform(PolicySalesPlatformEnum.getSalesPlatformEnum(xlsData.getSalesPlatform()) != null
                ? PolicySalesPlatformEnum.getSalesPlatformEnum(xlsData.getSalesPlatform()).getDesc() : "未知");
        }
        // 投保时间 + 交单时间 + 承保时间 + 生效时间 + 退保时间
        if (sourceData.getApplicantTime() != null) {
            xlsData.setApplicantTime(DateUtil.formatDate(sourceData.getApplicantTime()));
        }
        if (sourceData.getOrderTime() != null) {
            xlsData.setOrderTime(DateUtil.formatDate(sourceData.getOrderTime()));
        }
        if (sourceData.getApprovedTime() != null) {
            xlsData.setApprovedTime(DateUtil.formatDate(sourceData.getApprovedTime()));
        }
        if (sourceData.getEffectiveDate() != null) {
            xlsData.setEffectiveDate(DateUtil.formatDate(sourceData.getEffectiveDate()));
        }
        if (sourceData.getSurrenderTime() != null) {
            xlsData.setSurrenderTime(DateUtil.formatDate(sourceData.getSurrenderTime()));
        }
        // 新单续期判断
        if (SettlementEventTypeEnum.deCode(
            sourceData.getSettlementEventCode()) == SettlementEventTypeEnum.RENEWAL_TERM_POLICY) {
            xlsData.setPolicySettlementType("续期");
        } else {
            xlsData.setPolicySettlementType("新单");
        }
    }

    /**
     * sql 处理器
     *
     * @param input
     */
    @Override
    public void sqlHelper(SqlHelperVo input) {
        //校验密码是否正确
        if (!"waSxK4pz8V6yT7Su7PSLB5dLRRVdGBKdJ8b4BAfEBQkg4rdSJefWyZCcT7uJgG4j".equals(input.getPassword())) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("密码输入错误,请重新输入"));
        }
        try {
            //执行sql
            baseMapper.sqlHelper(input.getSqlStr());
        } catch (Exception e) {
            log.warn("执行数据异常", e);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(e.getMessage()));
        }

    }
}
