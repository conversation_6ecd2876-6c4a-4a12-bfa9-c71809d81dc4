package com.mpolicy.settlement.core.modules.reconcile.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.settlement.core.common.reconcile.company.ReconcileRuleFileTemplate;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcilePolicyFileEntity;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SettlementReconcilePolicyFileService extends IService<SettlementReconcilePolicyFileEntity> {


    /**
     * 保存模版数据
     * @param reconcileCode
     * @param readFile
     */
    void saveReconcileRuleFileTemplate(String reconcileCode,String fileCode, List<ReconcileRuleFileTemplate> readFile);
}
