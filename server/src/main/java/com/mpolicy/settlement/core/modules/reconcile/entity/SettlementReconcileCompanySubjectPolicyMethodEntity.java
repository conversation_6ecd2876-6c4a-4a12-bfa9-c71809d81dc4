package com.mpolicy.settlement.core.modules.reconcile.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 关联险种
 * 
 * <AUTHOR>
 * @date 2023-05-22 09:21:26
 */
@TableName("settlement_reconcile_company_subject_policy_method")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementReconcileCompanySubjectPolicyMethodEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private Integer id;
	/**
	 * 结算编码
	 */
	private String subjectRuleCode;
	/**
	 * 保单方式
	 */
	private Integer policyMethod;
}
