package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.common.commission.dto.*;
import com.mpolicy.settlement.core.common.keys.SettlementCostKeys;
import com.mpolicy.settlement.core.common.reconcile.enums.PremEventEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.factory.PremEventFactory;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.PremEventHandler;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.*;
import com.mpolicy.settlement.core.modules.reconcile.service.CostBasicCommissionConfigService;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementBasicCommValidService;
import com.mpolicy.web.common.annotation.Lock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SettlementBasicCommValidServiceImpl implements SettlementBasicCommValidService {

    public static final String WHITE_LIST = "WHITE_LIST";
    /**
     *
     */
    public static Integer PER_MAX_COUNT = 500;

    @Autowired
    protected CostBasicCommissionConfigService commissionConfigService;

    public List<ValidateResultDto> validateCostConfig(List<ValidateCostConfigParam> params){
        if(CollectionUtils.isEmpty(params)){
            return Collections.emptyList();
        }
        if(params.size()>PER_MAX_COUNT){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("单次验证记录数不能超过"+PER_MAX_COUNT+"个保单"));
        }
        return params.stream().map(p->{
            ValidateResultDto dto = new ValidateResultDto();
            dto.setPolicyNo(p.getPolicyNo());
            try{
                if(CollectionUtils.isNotEmpty(p.getInsuredList())){
                    p.getInsuredList().stream().forEach(o->{
                        getCostConfig(p,o.getProductInfoList(),o);
                    });
                }else{
                    getCostConfig(p,p.getProductInfoList(),null);
                }
                dto.setCode(ValidateResultDto.SUCCESS_CODE);
            }catch (GlobalException ge){
                dto.setCode(ge.getCode()+"");
                dto.setMsg(ge.getMsg());
            }catch (Exception e){
                dto.setCode(ValidateResultDto.OTHER_ERROR);
                dto.setMsg("系统异常");
            }
            return dto;
        }).collect(Collectors.toList());

    }

    private void getCostConfig(ValidateCostConfigParam params, List<ProductInfoDto> productList, InsuredInfoDto insured){
        productList.stream().forEach(o->{
            PolicyProductPremInput input = createPolicyProductPremInput(params,o,insured);
            commissionConfigService.listCostBasicCommissionConfig(params.getPolicyNo(),params.getPolicyProductType(),input);
        });
    }

    private PolicyProductPremInput createPolicyProductPremInput(ValidateCostConfigParam params, ProductInfoDto product, InsuredInfoDto insured){
        PolicyProductPremInput input = new PolicyProductPremInput();
        input.setChannelCode(params.getChannelCode());
        input.setPolicyNo(params.getPolicyNo());
        input.setMainProductCode(params.getMainProductCode());
        input.setProductCode(product.getProductCode());
        input.setPlantCode(product.getPlanCode());
        input.setInsuredPeriodType(product.getInsuredPeriodType());
        input.setInsuredPeriod(product.getInsuredPeriod());
        if(insured!=null) {
            input.setInsuredPolicyAge(insured.getInsuredPolicyAge());
            input.setInsuredGender(insured.getInsuredGender());
            input.setInsuredBirthday(insured.getInsuredBirthday());
        }
        input.setPeriodType(product.getPeriodType());
        input.setPaymentPeriodType(product.getPaymentPeriodType());
        input.setPaymentPeriod(product.getPaymentPeriod());
        //0:新单 1:续投
        input.setInsuranceType(params.getInsuranceType());
        input.setApprovedTime(params.getApprovedTime());
        input.setRenewalYear(params.getRenewalYear());
        input.setRenewalPeriod(params.getRenewalPeriod());
        // 投保人信息
        if (params.getApplicantInfo() != null) {
            input.setApplicantGender(params.getApplicantInfo().getApplicantGender());
            input.setApplicantBirthday(params.getApplicantInfo().getApplicantBirthday());
        }
        input.setSalesType(params.getSalesType());
        // 是否自保件
        input.setSelfPreservation(params.getSelfPreservation());

        input.setOrgCode(params.getOrgCode());
//        // 代理人机构编码
//        if (policyInfo.getAgentInfoList() != null && policyInfo.getAgentInfoList().size() > 0) {
//            policyInfo.getAgentInfoList().stream().filter(a -> a.getMainFlag() == 1).findFirst().ifPresent(epAgentInfoVo -> input.setOrgCode(epAgentInfoVo.getOrgCode()));
//        }
        return input;
    }



    @Override
    public List<ValidateResultDto> validateIncomeConfig(List<ValidateCostConfigParam> params) {
        if (CollectionUtils.isEmpty(params)) {
            return Collections.emptyList();
        }

        if (params.size() > PER_MAX_COUNT) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("单次验证记录数不能超过" + PER_MAX_COUNT + "个保单"));
        }
        List<String> whiteList;
        try{
            String whaleListString = redisService.get(SettlementCostKeys.POLICY_VALID_ENTRY, WHITE_LIST, String.class);
            whiteList = JSONArray.parseArray(whaleListString, String.class);
            if (CollectionUtils.isEmpty(whiteList)) {
                whiteList = Collections.emptyList();
            }
        } catch(Exception e) {
            whiteList = Collections.emptyList();
        }

//        //查协议、查合约
//        params.stream().map(ValidateCostConfigParam::getProductInfoList).collect(Collectors.toList());
//        params.stream().flatMap(x -> x.getInsuredList().stream())
//                .flatMap(x -> x.getProductInfoList().stream())
//                .map(ProductInfoDto::getProductCode)
//                .collect(Collectors.toList());

        List<String> finalWhiteList = whiteList;
        return params.stream().map(p -> {
            ValidateResultDto dto = new ValidateResultDto();
            String policyNo = p.getPolicyNo();
            dto.setPolicyNo(policyNo);
            try{

                if (finalWhiteList.contains(p.getPolicyNo()) || finalWhiteList.contains(p.getEndorsementNo())) {
                    log.info("收入校验白名单：{}", p.getPolicyNo());
                    dto.setCode(ValidateResultDto.SUCCESS_CODE);
                    return dto;
                }

                List<PolicyProductPremInput> productPremInputList = Optional.ofNullable(p.getInsuredList())
                        .filter(CollectionUtils::isNotEmpty)
                        .map(insuredList ->
                                     insuredList.stream().flatMap(
                                             insured -> insured.getProductInfoList().stream().map(product -> createPolicyProductPremInput(p, product, insured))
                                     ).collect(Collectors.toList())
                        )
                        .orElse(
                                p.getProductInfoList().stream().map(x -> createPolicyProductPremInput(p, x, null)).collect(Collectors.toList())
                        );
                Map<Integer, Set<String>> reconcileTypeMap = Optional.ofNullable(p.getInsuredList())
                        .filter(CollectionUtils::isNotEmpty)
                        .map(insuredList ->
                                     insuredList.stream().flatMap(
                                             insured -> insured.getProductInfoList().stream()
                                     ).collect(Collectors.groupingBy( x -> Objects.isNull(x.getReconcileType()) ? -1 : x.getReconcileType(), Collectors.mapping(ProductInfoDto::getProductCode, Collectors.toSet())))
                        )
                        .orElseGet(() ->
                                p.getProductInfoList().stream().collect(
                                        Collectors.groupingBy( x -> Objects.isNull(x.getReconcileType()) ? -1 : x.getReconcileType(), Collectors.mapping(ProductInfoDto::getProductCode, Collectors.toSet()))
                                )
                        );

                if (reconcileTypeMap.containsKey(-1)) {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("存在险种协议类型不存在-{}", CollUtil.join(reconcileTypeMap.get(-1), "','") )));
                }


                for (PolicyProductPremInput premInfoInput : productPremInputList) {

                    PremEventHandler contractPremEventHandler = PremEventFactory.getInvoke(PremEventEnum.CONTRACT_PREM.getCode());
                    PremEventHandler protocolPremEventHandler = PremEventFactory.getInvoke(PremEventEnum.PROTOCOL_PREM.getCode());
                    boolean matchAny = false;
                    if (reconcileTypeMap.getOrDefault(0, Collections.emptySet()).contains(premInfoInput.getProductCode())) {
                        matchAny = true;
                        try{
                            List<PolicyProductPremResult> protocolresultList = protocolPremEventHandler.queryPolicyProductPrem(premInfoInput);
                            incomeValid(p, premInfoInput, protocolresultList, protocolPremEventHandler);
                        } catch(Exception e) {
                            log.warn("协议结算校验失败-{}", JSON.toJSON(premInfoInput));
                            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("协议结算校验失败-{}", e.getMessage())));
                        }
                    }

                    if (reconcileTypeMap.getOrDefault(1, Collections.emptySet()).contains(premInfoInput.getProductCode())) {
                        matchAny = true;
                        try{
                            List<PolicyProductPremResult> contractResultList = contractPremEventHandler.queryPolicyProductPrem(premInfoInput);
                            incomeValid(p, premInfoInput, contractResultList, contractPremEventHandler);
                        } catch(Exception e) {
                            log.warn("合约结算校验失败-{}", JSON.toJSON(premInfoInput));
                            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("合约结算校验失败-{}", e.getMessage())));
                        }
                    }
                    if (!matchAny) {
                        throw new GlobalException(
                                BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("未匹配到协议类型-{}-{}", premInfoInput.getPolicyNo(), premInfoInput.getProductCode()))
                        );
                    }
                    dto.setCode(ValidateResultDto.SUCCESS_CODE);
                }
            } catch(GlobalException ge) {
                log.warn("收入校验异常", ge);
                dto.setCode(ge.getCode() + "");
                dto.setMsg(ge.getMsg());
            } catch(Exception e) {
                log.warn("收入校验异常", e);
                dto.setCode(ValidateResultDto.OTHER_ERROR);
                dto.setMsg("系统异常");
            }
            return dto;
        }).collect(Collectors.toList());

    }

    @Autowired
    private IRedisService redisService;

    @Override
    @Lock(keys = "WHITE_LIST", attemptTimeout = 500)
    public void addFilterWhitePolicy(String policyNo, int op) {

        if (op == 1) {
            redisService.delete(SettlementCostKeys.POLICY_VALID_ENTRY, WHITE_LIST);
            return;
        }

        if (!redisService.exists(SettlementCostKeys.POLICY_VALID_ENTRY, WHITE_LIST)) {
            redisService.set(SettlementCostKeys.POLICY_VALID_ENTRY, WHITE_LIST, JSONArray.toJSON(Lists.newArrayList(policyNo)));
        } else {
            String whaleListString = redisService.get(SettlementCostKeys.POLICY_VALID_ENTRY, WHITE_LIST, String.class);
            List<String> list = JSONArray.parseArray(whaleListString, String.class);
            list.add(policyNo);
            redisService.set(SettlementCostKeys.POLICY_VALID_ENTRY, WHITE_LIST, JSONArray.toJSON(list));
        }

    }



    private void incomeValid(ValidateCostConfigParam p, PolicyProductPremInput premInfoInput, List<PolicyProductPremResult> premResultList,
                             PremEventHandler eventHandler) {
        if (premResultList.size() > 1) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("获取到多个结算科目信息，保单号={} 险种编码={}", p.getPolicyNo(), premInfoInput.getProductCode())));
        }

        PolicyProductPremResult productPremInfo = premResultList.get(0);

        //结算方式为空的时候 默认税前
        if (productPremInfo.getSettlementMethod() == null) {
            productPremInfo.setSettlementMethod(0);
        }
        // 税率为空的时候 默认为1
        if (productPremInfo.getTaxRate() == null) {
            productPremInfo.setTaxRate(new BigDecimal("1"));
        }

        if (productPremInfo.getIsCustomYearRate() == 1) {
            return;
////            // 判断是否为一单一议的佣金费率
//            PolicyPremInput build = PolicyPremInput.builder()
//                    .policyNo(premInfoInput.getPolicyNo())
//                    .productCode(premInfoInput.getProductCode())
////                                .batchCode(settlementPolicyInfo.getEndorsementNo())
//                    .year(p.getRenewalYear())
//                    .period(p.getRenewalPeriod())
//                    .insuranceProductCode(productPremInfo.getInsuranceProductCode())
//                    .build();
//            PolicyPremResult settlementPolicyPrem = eventHandler.queryPolicyPrem(build);
//
//            if (Objects.isNull(settlementPolicyPrem)) {
//                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("一单一议保单费率获取失败，保单号={} 险种编码={}", p.getPolicyNo(), premInfoInput.getProductCode())));
//            }
        }
    }

    @Override
    public List<IncomeRateOut> queryIncomeRateConfig(IncomeProfitInfoParamsDto profitInfoParamsDto) {

        List<IncomeRateOut> premResultList = Lists.newArrayList();
        PremEventHandler contractPremEventHandler = PremEventFactory.getInvoke(PremEventEnum.CONTRACT_PREM.getCode());
        PremEventHandler protocolPremEventHandler = PremEventFactory.getInvoke(PremEventEnum.PROTOCOL_PREM.getCode());
        PolicyProductPremInput premInfoInput = new PolicyProductPremInput();
        BeanUtil.copyProperties(profitInfoParamsDto, premInfoInput);
        try{
            List<PolicyProductPremResult> protocolresultList = protocolPremEventHandler.queryPolicyProductPrem(premInfoInput);
            PolicyPremResult protocolRate = queryIncomeRate(profitInfoParamsDto, premInfoInput, protocolresultList, protocolPremEventHandler);
            PolicyTaxPremResult protocolTaxRate = queryIncomeTaxRate(profitInfoParamsDto, premInfoInput, protocolresultList, protocolPremEventHandler);

            if (Objects.nonNull(protocolRate)) {
                IncomeRateOut protocolIncomeRateOut = cvIncomeCustomYearRate(profitInfoParamsDto, protocolRate);
                protocolIncomeRateOut.setReconcileType(0);
                if (Objects.nonNull(protocolTaxRate)) {
                    protocolIncomeRateOut.setTaxRate(protocolTaxRate.getTaxRate());
                }
                premResultList.add(protocolIncomeRateOut);

            } else if (CollectionUtils.isNotEmpty(protocolresultList) && protocolresultList.size() == 1) {
                IncomeRateOut protocolIncomeRateOut = cvIncomeRate(profitInfoParamsDto, premInfoInput, protocolresultList);
                if (Objects.nonNull(protocolTaxRate)) {
                    protocolIncomeRateOut.setTaxRate(protocolTaxRate.getTaxRate());
                }
                protocolIncomeRateOut.setReconcileType(0);
                premResultList.add(protocolIncomeRateOut);
            }
        } catch(Exception e) {
            log.warn("协议结算费率失败-{}", JSON.toJSON(profitInfoParamsDto), e);
        }

        try{
            List<PolicyProductPremResult> contractResultList = contractPremEventHandler.queryPolicyProductPrem(premInfoInput);
            PolicyPremResult contractRate = queryIncomeRate(profitInfoParamsDto, premInfoInput, contractResultList, contractPremEventHandler);
            PolicyTaxPremResult contractTaxRate = queryIncomeTaxRate(profitInfoParamsDto, premInfoInput, contractResultList, contractPremEventHandler);
            if (Objects.nonNull(contractRate)) {
                IncomeRateOut contractIncomeRateOut = cvIncomeCustomYearRate(profitInfoParamsDto, contractRate);
                contractIncomeRateOut.setReconcileType(1);
                if (Objects.nonNull(contractTaxRate)) {
                    contractIncomeRateOut.setTaxRate(contractTaxRate.getTaxRate());
                }

                premResultList.add(contractIncomeRateOut);
            } else if (CollectionUtils.isNotEmpty(contractResultList) && contractResultList.size() == 1) {
                IncomeRateOut contractIncomeRateOut = cvIncomeRate(profitInfoParamsDto, premInfoInput, contractResultList);
                if (Objects.nonNull(contractTaxRate)) {
                    contractIncomeRateOut.setTaxRate(contractTaxRate.getTaxRate());
                }
                contractIncomeRateOut.setReconcileType(1);
                premResultList.add(contractIncomeRateOut);
            }
        } catch(Exception e) {
            log.warn("合约结算费率失败-{}", JSON.toJSON(premInfoInput), e);
        }

        return premResultList;
    }

    private PolicyTaxPremResult queryIncomeTaxRate(IncomeProfitInfoParamsDto profitInfoParamsDto, PolicyProductPremInput premInfoInput,
                                                List<PolicyProductPremResult> premResultList, PremEventHandler eventHandler) {
        try{
            log.info("计算税率一单一议-{}", JSON.toJSONString(profitInfoParamsDto));
            if (premResultList.size() > 1) {
                log.warn(StrUtil.format("获取到多个结算科目信息，保单号={} 险种编码={}", profitInfoParamsDto.getPolicyNo(), premInfoInput.getProductCode()));
                return null;
            }

            PolicyProductPremResult productPremInfo = premResultList.get(0);

            //结算方式为空的时候 默认税前
            if (productPremInfo.getSettlementMethod() == null) {
                productPremInfo.setSettlementMethod(0);
            }
            // 税率为空的时候 默认为1
            if (productPremInfo.getTaxRate() == null) {
                productPremInfo.setTaxRate(new BigDecimal("1"));
            }

            if (productPremInfo.getIsCustomTaxRate() == 1) {
    //            // 判断是否为一单一议的佣金费率
                PolicyPremInput build = PolicyPremInput.builder()
                        .policyNo(premInfoInput.getPolicyNo())
                        .productCode(premInfoInput.getProductCode())
                        .batchCode(profitInfoParamsDto.getEndorsementNo())
                        .year(profitInfoParamsDto.getRenewalYear())
                        .period(profitInfoParamsDto.getRenewalPeriod())
                        .insuranceProductCode(productPremInfo.getInsuranceProductCode())
                        .build();
                PolicyTaxPremResult settlementTaxPolicyPrem = eventHandler.queryPolicyTaxPrem(build);

                if (Objects.isNull(settlementTaxPolicyPrem)) {
                    log.warn(StrUtil.format("一单一议保单税率获取失败，保单号={} 险种编码={}", profitInfoParamsDto.getPolicyNo(), premInfoInput.getProductCode()));
                    return null;
                }
                log.info("利润计算税率一单一议结果-{}", JSON.toJSONString(settlementTaxPolicyPrem));
                return settlementTaxPolicyPrem;
            }
        } catch(Exception e) {
            log.warn("税率一单一议查询失败", e);
        }
        return null;
    }

    private static IncomeRateOut cvIncomeCustomYearRate(IncomeProfitInfoParamsDto p, PolicyPremResult protocolRate) {
        IncomeRateOut protocolIncomeRateOut = new IncomeRateOut();
        protocolIncomeRateOut.setPolicyNo(p.getPolicyNo());
        protocolIncomeRateOut.setRenewalYear(p.getRenewalYear());
        protocolIncomeRateOut.setRenewalPeriod(p.getRenewalPeriod());
        protocolIncomeRateOut.setEndorsementNo(p.getEndorsementNo());
        protocolIncomeRateOut.setYearRate(protocolRate.getYearRate());
        protocolIncomeRateOut.setProductCode(protocolRate.getProductCode());
        protocolIncomeRateOut.setTaxRate(protocolRate.getTaxRate());
        protocolIncomeRateOut.setVehicleVesselTax(protocolRate.getVehicleVesselTax());
        protocolIncomeRateOut.setVehicleVesselTaxRate(protocolRate.getVehicleVesselTaxRate());
        protocolIncomeRateOut.setSettlementMethod(protocolRate.getSettlementMethod());
        return protocolIncomeRateOut;
    }

    private static IncomeRateOut cvIncomeRate(IncomeProfitInfoParamsDto p, PolicyProductPremInput premInfoInput, List<PolicyProductPremResult> protocolresultList) {
        IncomeRateOut protocolIncomeRateOut = new IncomeRateOut();
        PolicyProductPremResult protocolProductPremResult = protocolresultList.get(0);
        protocolIncomeRateOut.setPolicyNo(p.getPolicyNo());
        protocolIncomeRateOut.setSettlementMethod(String.valueOf(protocolProductPremResult.getSettlementMethod()));
        protocolIncomeRateOut.setRenewalYear(p.getRenewalYear());
        protocolIncomeRateOut.setRenewalPeriod(p.getRenewalPeriod());
        protocolIncomeRateOut.setEndorsementNo(p.getEndorsementNo());
        protocolIncomeRateOut.setYearRate(protocolProductPremResult.getYearRate());
        protocolIncomeRateOut.setProductCode(premInfoInput.getProductCode());
        protocolIncomeRateOut.setTaxRate(protocolProductPremResult.getTaxRate());
        return protocolIncomeRateOut;
    }

    private PolicyPremResult queryIncomeRate(IncomeProfitInfoParamsDto profitInfoParamsDto, PolicyProductPremInput premInfoInput, List<PolicyProductPremResult> premResultList,
                                             PremEventHandler eventHandler) {
        if (premResultList.size() > 1) {
            log.warn(StrUtil.format("获取到多个结算科目信息，保单号={} 险种编码={}", profitInfoParamsDto.getPolicyNo(), premInfoInput.getProductCode()));
            return null;
        }

        PolicyProductPremResult productPremInfo = premResultList.get(0);

        //结算方式为空的时候 默认税前
        if (productPremInfo.getSettlementMethod() == null) {
            productPremInfo.setSettlementMethod(0);
        }
        // 税率为空的时候 默认为1
        if (productPremInfo.getTaxRate() == null) {
            productPremInfo.setTaxRate(new BigDecimal("1"));
        }

        if (productPremInfo.getIsCustomYearRate() == 1) {
//            // 判断是否为一单一议的佣金费率
            PolicyPremInput build = PolicyPremInput.builder()
                    .policyNo(premInfoInput.getPolicyNo())
                    .productCode(premInfoInput.getProductCode())
                    .batchCode(profitInfoParamsDto.getEndorsementNo())
                    .year(profitInfoParamsDto.getRenewalYear())
                    .period(profitInfoParamsDto.getRenewalPeriod())
                    .insuranceProductCode(productPremInfo.getInsuranceProductCode())
                    .build();
            PolicyPremResult settlementPolicyPrem = eventHandler.queryPolicyPrem(build);

            if (Objects.isNull(settlementPolicyPrem)) {
                log.warn(StrUtil.format("一单一议保单费率获取失败，保单号={} 险种编码={}", profitInfoParamsDto.getPolicyNo(), premInfoInput.getProductCode()));
                return null;
            }
            return settlementPolicyPrem;
        }
        return null;
    }



}
