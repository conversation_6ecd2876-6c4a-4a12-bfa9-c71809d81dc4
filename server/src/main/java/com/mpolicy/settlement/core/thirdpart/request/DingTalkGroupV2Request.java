package com.mpolicy.settlement.core.thirdpart.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class DingTalkGroupV2Request {

    @ApiModelProperty(value="通知内容", required = true)
    @NotNull(message = "通知内容不能为空")
    private String content;

    @ApiModelProperty(value="钉钉群code", required = true)
    @NotNull(message = "钉钉群code不能为空")
    private String dingTalkGroupCode;

    @ApiModelProperty(value="手机号列表")
    @NotNull(message = "手机号列表不能为空")
    private List<String> mobiles;

}
