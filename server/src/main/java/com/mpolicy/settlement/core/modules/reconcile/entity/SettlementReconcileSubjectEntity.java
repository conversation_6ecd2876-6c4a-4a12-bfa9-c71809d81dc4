package com.mpolicy.settlement.core.modules.reconcile.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;

/**
 * 保司结算对账单科目
 * 
 * <AUTHOR>
 * @date 2023-05-21 22:28:34
 */
@TableName("settlement_reconcile_subject")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementReconcileSubjectEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 对账唯一单号
	 */
	private String reconcileCode;
	/**
	 * 科目规则编码
	 */
	private String subjectRuleCode;
	/**
	 * 科目规则数据信息
	 */
	private String subjectSettingData;
	/**
	 * 对账科目编码
	 */
	private String reconcileSubjectCode;
	/**
	 * 对账科目名称
	 */
	private String reconcileSubjectName;
	/**
	 * 科目金额
	 */
	private BigDecimal subjectTotalAmount;
	/**
	 * 科目对应保单数量
	 */
	private Integer subjectPolicyCount;

	/**
	 * 是否删除;0有效-1删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private Integer revision;
}
