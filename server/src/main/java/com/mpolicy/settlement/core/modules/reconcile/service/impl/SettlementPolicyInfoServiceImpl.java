package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.policy.common.ep.policy.*;
import com.mpolicy.policy.common.ep.policy.preserve.EpPreservationV2Vo;
import com.mpolicy.product.common.base.ProductBase;
import com.mpolicy.settlement.core.common.Constant;
import com.mpolicy.settlement.core.common.keys.AdminCommonKeys;
import com.mpolicy.settlement.core.common.reconcile.BatchIsCompletedReconcileRecord;
import com.mpolicy.settlement.core.common.reconcile.BatchIsCompletedReconcileRecordPreservation;
import com.mpolicy.settlement.core.common.reconcile.RefreshSettlementPolicyInfoCommissionVo;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileMonthEnum;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileStatusEnum;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileSubjectOnlineEnum;
import com.mpolicy.settlement.core.enums.ReconcileTypeEnum;
import com.mpolicy.settlement.core.modules.protocol.dto.ProtocolInsuranceProductInfoOut;
import com.mpolicy.settlement.core.modules.protocol.helper.ProtocolBaseHelper;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementPolicyInfoDao;
import com.mpolicy.settlement.core.modules.reconcile.dto.rule.ReconcileCompanySubjectDetail;
import com.mpolicy.settlement.core.modules.reconcile.entity.*;
import com.mpolicy.settlement.core.modules.reconcile.enums.PolicyProductTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.PreservationProjectEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementGenerateTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.helper.ReconcileBaseHelper;
import com.mpolicy.settlement.core.modules.reconcile.helper.ReconcileRuleHelper;
import com.mpolicy.settlement.core.modules.reconcile.service.*;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import com.mpolicy.settlement.core.service.common.AgentBaseService;
import com.mpolicy.settlement.core.service.common.OpenApiBaseService;
import com.mpolicy.settlement.core.service.common.PolicyCenterBaseClient;
import com.mpolicy.settlement.core.service.common.ProductBaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 结算保单明细记录表
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:39
 */
@Slf4j
@Service("settlementPolicyInfoService")
public class SettlementPolicyInfoServiceImpl extends ServiceImpl<SettlementPolicyInfoDao, SettlementPolicyInfoEntity>
    implements SettlementPolicyInfoService {

    @Autowired
    private SettlementReconcileSubjectService settlementReconcileSubjectService;

    @Autowired
    private PolicyCenterBaseClient policyCenterBaseClient;

    @Autowired
    private ProductBaseService productBaseService;

    @Autowired
    private OpenApiBaseService openApiBaseService;

    @Autowired
    private AgentBaseService agentBaseService;

    @Autowired
    private SettlementReconcileInfoService settlementReconcileInfoService;

    @Autowired
    private SettlementReconcileConfirmService settlementReconcileConfirmService;

    @Autowired
    private SettlementReconcilePolicyService settlementReconcilePolicyService;
    @Autowired
    private SettlementReconcileCompanyService settlementReconcileCompanyService;

    @Autowired
    private IRedisService redisService;

    public static void main(String[] args) {
        DateTime dateTime = DateUtil.parse("2023年01月", "yyyy年MM月");
        DateTime offset = DateUtil.offset(dateTime, DateField.MONTH, -1);
        System.out.println(dateTime);
        System.out.println(offset);
    }

    private DateTime builderDateTime(ReconcileMonthEnum reconcileMonthEnum, DateTime reconcileMonth,
        Integer timeEndDay) {
        DateTime dateTime = DateUtil.offset(reconcileMonth, DateField.MONTH, -reconcileMonthEnum.getMonth());
        // 获取日期的月最后日
        DateTime resDateTime;
        DateTime dayOfMonth = DateUtil.endOfMonth(dateTime);
        if (timeEndDay > dayOfMonth.dayOfMonth()) {
            resDateTime = dayOfMonth;
        } else {
            resDateTime = DateUtil.date(dateTime).setField(DateField.DAY_OF_MONTH, timeEndDay);
        }
        log.info("匹配时间条件对账月度[{}]规则[{}],{}日,实际匹配到的时间={}", reconcileMonth.toString("yyyy年MM月"),
            reconcileMonthEnum.getDesc(), timeEndDay, resDateTime);
        return DateUtil.endOfDay(resDateTime);
    }

    /**
     * 新契约保单明细集合【投保时间 + 承保时间 + 缴费时间 + 生效时间 + 回执时间 + 回访时间】
     *
     * @param subject 科目信息
     * @return java.util.List<com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity>
     * <AUTHOR>
     * @since 2023/6/11 22:47
     */
    private List<SettlementPolicyInfoEntity> builderNewPolicy(SettlementReconcileInfoEntity reconcileInfo,
        ReconcileCompanySubjectDetail subject) {

        boolean needData = false;
        DateTime reconcileMonth = DateUtil.parse(reconcileInfo.getReconcileMonth(), "yyyy年MM月");
        //  投保时间截止
        DateTime applicantTime = null;
        Integer applicantTimeEndDay = subject.getApplicantTimeEndDay();
        if (applicantTimeEndDay != null && applicantTimeEndDay != 0) {
            applicantTime =
                builderDateTime(ReconcileMonthEnum.matchSearchCode(subject.getApplicantTimeEndType()), reconcileMonth,
                    applicantTimeEndDay);
            needData = true;
        }
        //  承保时间截止
        DateTime underwriteTime = null;
        Integer underwriteTimeEndDay = subject.getUnderwriteTimeEndDay();
        if (underwriteTimeEndDay != null && underwriteTimeEndDay != 0) {
            underwriteTime =
                builderDateTime(ReconcileMonthEnum.matchSearchCode(subject.getUnderwriteTimeEndType()), reconcileMonth,
                    underwriteTimeEndDay);
            needData = true;
        }
        // 缴费时间
        DateTime paymentPeriodEnd = null;
        Integer paymentPeriodEndDay = subject.getPaymentPeriodEndDay();
        if (paymentPeriodEndDay != null && paymentPeriodEndDay != 0) {
            paymentPeriodEnd =
                builderDateTime(ReconcileMonthEnum.matchSearchCode(subject.getPaymentPeriodEndType()), reconcileMonth,
                    paymentPeriodEndDay);
            needData = true;
        }
        // 生效时间
        DateTime effectiveTime = null;
        Integer effectiveTimeEndDay = subject.getEffectiveTimeEndDay();
        if (effectiveTimeEndDay != null && effectiveTimeEndDay != 0) {
            effectiveTime =
                builderDateTime(ReconcileMonthEnum.matchSearchCode(subject.getEffectiveTimeEndType()), reconcileMonth,
                    effectiveTimeEndDay);
            needData = true;
        }
        // 回执时间
        DateTime receiptTime = null;
        Integer receiptTimeEndDay = subject.getReceiptTimeEndDay();
        if (receiptTimeEndDay != null && receiptTimeEndDay != 0) {
            receiptTime =
                builderDateTime(ReconcileMonthEnum.matchSearchCode(subject.getReceiptTimeEndType()), reconcileMonth,
                    receiptTimeEndDay);
            needData = true;
        }
        // 回访时间
        DateTime revisitTime = null;
        Integer callbackTimeEndDay = subject.getCallbackTimeEndDay();
        if (callbackTimeEndDay != null && callbackTimeEndDay != 0) {
            revisitTime =
                builderDateTime(ReconcileMonthEnum.matchSearchCode(subject.getCallbackTimeEndType()), reconcileMonth,
                    callbackTimeEndDay);
            needData = true;
        }

        // 判断是否有配置新契约采集
        if (!needData) {
            log.info("对账单编号={} 未配置新契约采集规则", reconcileInfo.getReconcileCode());
            return new ArrayList<>();
        }
        if (CollUtil.isEmpty(subject.getProductCodeList())) {
            log.info("对账单编号={} 未配置新契约小鲸险种编码集合", reconcileInfo.getReconcileCode());
            return new ArrayList<>();
        }

        log.info(
            "【新契约】构建结算保单信息，对账单编号={} 查询条件【applicant_time = {} approved_time={}, enforce_time={}, reality_time={}, " +
                "receipt_time={}, revisit_time={} 险种编码集合={}】",
            reconcileInfo.getReconcileCode(), applicantTime, underwriteTime, effectiveTime, paymentPeriodEnd,
            receiptTime, revisitTime, subject.getProductCodeList());
        // 新契约的事件
        List<String> eventCodeList = Arrays.asList(SettlementEventTypeEnum.PERSONAL_NEW_POLICY.getEventCode(),
            SettlementEventTypeEnum.GROUP_NEW_POLICY.getEventCode());
        DateTime finalReceiptTime = receiptTime;
        DateTime finalRevisitTime = revisitTime;
        String postponedMonth = reconcileMonth.toString("yyyy-MM");
        List<SettlementPolicyInfoEntity> resultList =
            lambdaQuery().eq(SettlementPolicyInfoEntity::getSettlementSubjectCode, subject.getReconcileSubjectCode())
                .in(SettlementPolicyInfoEntity::getSettlementEventCode, eventCodeList)
                .in(SettlementPolicyInfoEntity::getReconcileExecuteStatus, CollUtil.newArrayList(3,1))
                .eq(SettlementPolicyInfoEntity::getReconcileStatus, 0)
                .eq(SettlementPolicyInfoEntity::getHangStatus, 0)
                .eq(SettlementPolicyInfoEntity::getRectificationMark, 0)
                .eq(SettlementPolicyInfoEntity::getReconcileType, reconcileInfo.getReconcileType()).and(
                    a -> a.le(SettlementPolicyInfoEntity::getPostponedMonth, postponedMonth).or()
                        .isNull(SettlementPolicyInfoEntity::getPostponedMonth))
                .in(SettlementPolicyInfoEntity::getProductCode, subject.getProductCodeList())
                .le(applicantTime != null, SettlementPolicyInfoEntity::getApplicantTime, applicantTime)
                .le(underwriteTime != null, SettlementPolicyInfoEntity::getApprovedTime, underwriteTime)
                .le(effectiveTime != null, SettlementPolicyInfoEntity::getEnforceTime, effectiveTime)
                .le(paymentPeriodEnd != null, SettlementPolicyInfoEntity::getRealityTime, paymentPeriodEnd)
                .and(receiptTime != null, x -> x.le(SettlementPolicyInfoEntity::getReceiptTime, finalReceiptTime).or()
                    .eq(SettlementPolicyInfoEntity::getReceiptStatus, 0)).and(revisitTime != null,
                    x -> x.le(SettlementPolicyInfoEntity::getRevisitTime, finalRevisitTime).or()
                        .eq(SettlementPolicyInfoEntity::getRevisitStatus, 0)).list();
        String sql = StrUtil.format(
            "select * from settlement_policy_info where deleted = 0 " + "and settlement_subject_code = '{}' " + "and "
                + "settlement_event_code in ('{}') " + "and reconcile_execute_status = (1,3) " + " and reconcile_status = "
                + "0 " + "and reconcile_type = {} " + "and (postponed_month <= {} or postponed_month is null )" +
                "and " + "product_code in ('{}')",
            subject.getReconcileSubjectCode(), CollUtil.join(eventCodeList, "','"), reconcileInfo.getReconcileType(),
            postponedMonth, CollUtil.join(subject.getProductCodeList(), "','"));
        StringBuffer buffer = new StringBuffer(sql);
        if (applicantTime != null) {
            buffer.append("and applicant_time <= '").append(applicantTime).append("' ");
        }
        if (underwriteTime != null) {
            buffer.append("and approved_time <= '").append(underwriteTime).append("' ");
        }
        if (effectiveTime != null) {
            buffer.append("and enforce_time <= '").append(effectiveTime).append("' ");
        }
        if (paymentPeriodEnd != null) {
            buffer.append("and paymentPeriod_end <= '").append(paymentPeriodEnd).append("' ");
        }
        if (receiptTime != null) {
            buffer.append("and (receipt_time <= '").append(finalReceiptTime).append("' or receipt_status = 0) ");
        }
        if (revisitTime != null) {
            buffer.append("and (revisit_time <= '").append(finalRevisitTime).append("' or revisit_status = 0) ");
        }
        log.info("结算单编码:[{}]查询新契约数据[{}]条 SQL=[{}]", reconcileInfo.getReconcileCode(), resultList.size(),
            buffer);
        return resultList;
    }

    /**
     * 续期保单数据过滤
     *
     * @param subject subject
     * @return java.util.List<com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity>
     * <AUTHOR>
     * @since 2023/6/18 16:27
     */
    private List<SettlementPolicyInfoEntity> builderRenewalTermPolicy(SettlementReconcileInfoEntity reconcileInfo,
        ReconcileCompanySubjectDetail subject) {
        //  续期查询时间
        DateTime queryTime;
        DateTime reconcileMonth = DateUtil.parse(reconcileInfo.getReconcileMonth(), "yyyy年MM月");
        // 对账方式 0:实时 1:过宽
        Integer reconciliationMethod = subject.getReconciliationMethod();
        // 过宽期
        Integer gracePeriodDay = subject.getGracePeriodDay();
        // 过宽
        if (reconciliationMethod == 1) {
            // 过宽日期类型
            Integer wideDateEndDay = subject.getWideDateEndDay();
            if (wideDateEndDay != null && wideDateEndDay != 0) {
                queryTime =
                    builderDateTime(ReconcileMonthEnum.matchSearchCode(subject.getWideDateEndType()), reconcileMonth,
                        wideDateEndDay);
            } else {
                queryTime = null;
            }
        } else {
            // 实收
            Integer paymentReceivedDateEndDay = subject.getPaymentReceivedDateEndDay();
            if (paymentReceivedDateEndDay != null && paymentReceivedDateEndDay != 0) {
                queryTime = builderDateTime(ReconcileMonthEnum.matchSearchCode(subject.getPaymentReceivedDateEndType()),
                    reconcileMonth, paymentReceivedDateEndDay);
            } else {
                queryTime = null;
            }
        }
        if (queryTime == null) {
            log.info("对账单编号={} 未配置续期采集规则", reconcileInfo.getReconcileCode());
            return new ArrayList<>();
        }
        if (subject.getProductCodeList().isEmpty()) {
            log.info("对账单编号={} 未配置小鲸险种编码集合", reconcileInfo.getReconcileCode());
            return new ArrayList<>();
        }
        log.info("【续期】构建结算保单信息，对账单编号={} 查询条件【queryTime={} 险种编码集合={}】",
            reconcileInfo.getReconcileCode(), queryTime, subject.getProductCodeList());
        String postponedMonth = reconcileMonth.toString("yyyy-MM");
        // 续期的所有保单
        List<SettlementPolicyInfoEntity> list =
            lambdaQuery().eq(SettlementPolicyInfoEntity::getSettlementSubjectCode, subject.getReconcileSubjectCode())
                .and(a -> a.le(SettlementPolicyInfoEntity::getPostponedMonth, postponedMonth).or()
                    .isNull(SettlementPolicyInfoEntity::getPostponedMonth))
                .in(SettlementPolicyInfoEntity::getSettlementEventCode,
                        SettlementEventTypeEnum.RENEWAL_TERM_POLICY.getEventCode(),
                        SettlementEventTypeEnum.CHANGE_RENEWAL_FALLBACK.getEventCode())
                .eq(SettlementPolicyInfoEntity::getRectificationMark, 0)
                .eq(SettlementPolicyInfoEntity::getHangStatus, 0)
                .in(SettlementPolicyInfoEntity::getReconcileExecuteStatus,  CollUtil.newArrayList(3,1))
                .eq(SettlementPolicyInfoEntity::getReconcileStatus, 0)
                .eq(SettlementPolicyInfoEntity::getReconcileType, reconcileInfo.getReconcileType())
                .in(SettlementPolicyInfoEntity::getProductCode, subject.getProductCodeList()).list();
        String sql = StrUtil.format(
            "select * from settlement_policy_info where deleted = 0 "
                    + "and settlement_subject_code = '{}' "
                    + "and "
                    + "settlement_event_code in ('{}','{}') "
                    + "and reconcile_execute_status in (1,3) "
                    + "and reconcile_status = 0 "
                    + "and reconcile_type = {} "
                    + "and (postponed_month <= '{}' or postponed_month is null )"
                    + "and product_code in ('{}')",
            subject.getReconcileSubjectCode(), SettlementEventTypeEnum.RENEWAL_TERM_POLICY.getEventCode(),
                SettlementEventTypeEnum.CHANGE_RENEWAL_FALLBACK.getEventCode(),
            reconcileInfo.getReconcileType(), postponedMonth, CollUtil.join(subject.getProductCodeList(), "','"));
        log.info("结算单编码:[{}]查询续期数据[{}]条 SQL=[{}]", reconcileInfo.getReconcileCode(), list.size(), sql);
        // 实收/过宽过滤处理
        return list.stream().filter(x -> {
            if (reconciliationMethod == 1) {
                // 过宽 匹配的是应缴时间 + 过宽天数
                return queryTime.compareTo(DateUtil.offsetDay(x.getPayableTime(), gracePeriodDay)) >= 0;
            } else if(Objects.equals(x.getSettlementEventCode(), SettlementEventTypeEnum.CHANGE_RENEWAL_FALLBACK.getEventCode())) {
                return queryTime.compareTo(x.getPayableTime()) >= 0;
            } else {
                // 实时 匹配的是实缴时间
                return queryTime.compareTo(x.getRealityTime()) >= 0;
            }
        }).collect(Collectors.toList());
    }

    /**
     * 续投保单数据
     *
     * @param subject 科目
     * @return java.util.List<com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity>
     * <AUTHOR>
     * @since 2023/6/11 22:49
     */
    private List<SettlementPolicyInfoEntity> builderRenewalPolicy(SettlementReconcileInfoEntity reconcileInfo,
        ReconcileCompanySubjectDetail subject) {

        boolean needData = false;
        DateTime reconcileMonth = DateUtil.parse(reconcileInfo.getReconcileMonth(), "yyyy年MM月");
        // 续投保单获取[投保时间截止 承保时间截止 + 生效时间] 前的保单
        DateTime beforeInsureTimeEnd = null;
        if (subject.getBeforeInsureTimeEndDay() != null && subject.getBeforeInsureTimeEndDay() != 0) {
            beforeInsureTimeEnd =
                builderDateTime(ReconcileMonthEnum.matchSearchCode(subject.getBeforeInsureTimeEndType()),
                    reconcileMonth, subject.getBeforeInsureTimeEndDay());
            needData = true;
        }
        DateTime beforeUnderwriteTimeEnd = null;
        if (subject.getBeforeUnderwriteTimeEndDay() != null && subject.getBeforeUnderwriteTimeEndDay() != 0) {
            beforeUnderwriteTimeEnd =
                builderDateTime(ReconcileMonthEnum.matchSearchCode(subject.getBeforeUnderwriteTimeEndType()),
                    reconcileMonth, subject.getBeforeUnderwriteTimeEndDay());
            needData = true;
        }
        DateTime beforeEffectiveTimeEnd = null;
        if (subject.getBeforeEffectiveTimeEndDay() != null && subject.getBeforeEffectiveTimeEndDay() != 0) {
            beforeEffectiveTimeEnd =
                builderDateTime(ReconcileMonthEnum.matchSearchCode(subject.getBeforeEffectiveTimeEndType()),
                    reconcileMonth, subject.getBeforeEffectiveTimeEndDay());
            needData = true;
        }

        // 续投保单获取[投保时间截止 承保时间截止 + 生效时间] 后的保单
        DateTime afterInsureTimeEnd = null;
        if (subject.getAfterInsureTimeEndDay() != null && subject.getAfterInsureTimeEndDay() != 0) {
            afterInsureTimeEnd =
                builderDateTime(ReconcileMonthEnum.matchSearchCode(subject.getAfterInsureTimeEndType()), reconcileMonth,
                    subject.getAfterInsureTimeEndDay());
            needData = true;
        }
        DateTime afterUnderwriteTimeEnd = null;
        if (subject.getAfterUnderwriteTimeEndDay() != null && subject.getAfterUnderwriteTimeEndDay() != 0) {
            afterUnderwriteTimeEnd =
                builderDateTime(ReconcileMonthEnum.matchSearchCode(subject.getAfterUnderwriteTimeEndType()),
                    reconcileMonth, subject.getAfterUnderwriteTimeEndDay());
            needData = true;
        }
        DateTime afterEffectiveTimeEnd = null;
        if (subject.getAfterEffectiveTimeEndDay() != null && subject.getAfterEffectiveTimeEndDay() != 0) {
            afterEffectiveTimeEnd =
                builderDateTime(ReconcileMonthEnum.matchSearchCode(subject.getAfterEffectiveTimeEndType()),
                    reconcileMonth, subject.getAfterEffectiveTimeEndDay());
            needData = true;
        }

        // 判断是否有配置续投采集
        if (!needData) {
            log.info("对账单编号={} 未配置续投采集规则", reconcileInfo.getReconcileCode());
            return new ArrayList<>();
        }

        if (subject.getProductCodeList().isEmpty()) {
            log.info("对账单编号={} 未配置小鲸险种编码集合", reconcileInfo.getReconcileCode());
            return new ArrayList<>();
        }
        String postponedMonth = reconcileMonth.toString("yyyy-MM");
        // 获取规则保单
        List<SettlementPolicyInfoEntity> renewalTermList =
            lambdaQuery().eq(SettlementPolicyInfoEntity::getSettlementSubjectCode, subject.getReconcileSubjectCode())
                .eq(SettlementPolicyInfoEntity::getSettlementEventCode,
                    SettlementEventTypeEnum.RENEWAL_POLICY.getEventCode())
                .in(SettlementPolicyInfoEntity::getReconcileExecuteStatus,  CollUtil.newArrayList(3,1)).and(
                    a -> a.le(SettlementPolicyInfoEntity::getPostponedMonth, postponedMonth).or()
                        .isNull(SettlementPolicyInfoEntity::getPostponedMonth))
                .eq(SettlementPolicyInfoEntity::getReconcileStatus, 0)
                .eq(SettlementPolicyInfoEntity::getRectificationMark, 0)
                .eq(SettlementPolicyInfoEntity::getHangStatus, 0)
                .eq(SettlementPolicyInfoEntity::getReconcileType, reconcileInfo.getReconcileType())
                .in(SettlementPolicyInfoEntity::getProductCode, subject.getProductCodeList()).list();

        log.info(
            "【续投】构建结算保单信息，对账单编号={} 查询条件保单到期前续保【beforeInsureTimeEnd={}，beforeUnderwriteTimeEnd" +
                "={}，beforeEffectiveTimeEnd={}】" + "查询条件保单到期后但在宽限期内续投【afterInsureTimeEnd={}，afterUnderwriteTimeEnd" + "={}，afterEffectiveTimeEnd={} 险种编码集合={}】",
            reconcileInfo.getReconcileCode(), beforeInsureTimeEnd, beforeUnderwriteTimeEnd, beforeEffectiveTimeEnd,
            afterInsureTimeEnd, afterUnderwriteTimeEnd, afterEffectiveTimeEnd, subject.getProductCodeList());

        List<SettlementPolicyInfoEntity> result = new ArrayList<>();
        // 根据续投的明细进行过滤
        for (SettlementPolicyInfoEntity settlementPolicyInfo : renewalTermList) {
            // 保单到期前续投规则[投保时间 <= 保单生效时间]
            if (settlementPolicyInfo.getApplicantTime().compareTo(settlementPolicyInfo.getEnforceTime()) < 1) {
                // 投保时间
                if (beforeInsureTimeEnd != null) {
                    if (settlementPolicyInfo.getApplicantTime().compareTo(beforeInsureTimeEnd) > 0) {
                        continue;
                    }
                }
                // 承保时间
                if (beforeUnderwriteTimeEnd != null) {
                    if (settlementPolicyInfo.getApprovedTime().compareTo(beforeUnderwriteTimeEnd) > 0) {
                        continue;
                    }
                }
                // 生效时间
                if (beforeEffectiveTimeEnd != null) {
                    if (settlementPolicyInfo.getEnforceTime().compareTo(beforeEffectiveTimeEnd) > 0) {
                        continue;
                    }
                }
            } else {
                // 保单到期后续投
                // 投保时间截止
                if (afterInsureTimeEnd != null) {
                    if (settlementPolicyInfo.getApplicantTime().compareTo(afterInsureTimeEnd) > 0) {
                        continue;
                    }
                }
                // 承保时间截止
                if (afterUnderwriteTimeEnd != null) {
                    if (settlementPolicyInfo.getApprovedTime().compareTo(afterUnderwriteTimeEnd) > 0) {
                        continue;
                    }
                }
                // 生效时间截止
                if (afterEffectiveTimeEnd != null) {
                    if (settlementPolicyInfo.getEnforceTime().compareTo(afterEffectiveTimeEnd) > 0) {
                        continue;
                    }
                }
            }
            result.add(settlementPolicyInfo);
        }

        String sql = StrUtil.format(
            "select * from settlement_policy_info where deleted = 0 " + "and settlement_subject_code = '{}' " + "and "
                + "settlement_event_code = '{}' " + "and reconcile_execute_status = (1,3) " + "and reconcile_status = 0 " + "and reconcile_type = {} " + "and (postponed_month <= '{}' or postponed_month is null )" + "and product_code in ('{}') ",
            subject.getReconcileSubjectCode(), SettlementEventTypeEnum.RENEWAL_POLICY.getEventCode(),
            reconcileInfo.getReconcileType(), postponedMonth, CollUtil.join(subject.getProductCodeList(), "','"));
        StringBuffer buffer = new StringBuffer(sql);
        buffer.append("and ((grace_period_status = 1 and ");
        // 投保时间
        if (beforeInsureTimeEnd != null) {
            buffer.append("applicant_time < '").append(beforeInsureTimeEnd).append("'");
        }
        // 承保时间
        if (beforeUnderwriteTimeEnd != null) {
            buffer.append("approved_time < '").append(beforeUnderwriteTimeEnd).append("'");
        }
        // 生效时间
        if (beforeEffectiveTimeEnd != null) {
            buffer.append("enforce_time < '").append(beforeEffectiveTimeEnd).append("'");
        }
        buffer.append(") or (grace_period_status != 1 and ");
        // 投保时间
        if (afterInsureTimeEnd != null) {
            buffer.append("applicant_time < '").append(afterInsureTimeEnd).append("'");
        }
        // 承保时间
        if (afterUnderwriteTimeEnd != null) {
            buffer.append("approved_time < '").append(afterUnderwriteTimeEnd).append("'");
        }
        // 生效时间
        if (afterEffectiveTimeEnd != null) {
            buffer.append("enforce_time < '").append(afterEffectiveTimeEnd).append("'");
        }
        buffer.append("))");
        log.info("结算单编码:[{}]查询续投数据[{}]条最终满足条件的数量[{}] SQL=[{}]", reconcileInfo.getReconcileCode(),
            renewalTermList.size(), result.size(), buffer);
        return result;
    }

    /**
     * 保全保单数据
     *
     * @param subject 保全保单
     * @return java.util.List<com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity>
     * <AUTHOR>
     * @since 2023/6/11 23:06
     */
    private List<SettlementPolicyInfoEntity> builderPreservationPolicy(SettlementReconcileInfoEntity reconcileInfo,
        ReconcileCompanySubjectDetail subject) {
        if (CollUtil.isEmpty(subject.getPreservationPolicyMethodList())) {
            log.info("对账单编号={} 没有配置保全保单方式", reconcileInfo.getReconcileCode());
            return Collections.emptyList();
        }
        boolean needData = false;
        DateTime reconcileMonth = DateUtil.parse(reconcileInfo.getReconcileMonth(), "yyyy年MM月");
        // 保全生效日期
        DateTime preservationEffectiveTime = null;
        Integer preservationEffectiveTimeEndDay = subject.getPreservationEffectiveTimeEndDay();
        if (preservationEffectiveTimeEndDay != null && preservationEffectiveTimeEndDay != 0) {
            preservationEffectiveTime =
                builderDateTime(ReconcileMonthEnum.matchSearchCode(subject.getPreservationEffectiveTimeEndType()),
                    reconcileMonth, preservationEffectiveTimeEndDay);
            needData = true;
        }
        // 批改时间截止每月几日
        DateTime preservationTime = null;
        Integer correctTimeEndDay = subject.getCorrectTimeEndDay();
        if (correctTimeEndDay != null && correctTimeEndDay != 0) {
            preservationTime =
                builderDateTime(ReconcileMonthEnum.matchSearchCode(subject.getCorrectTimeEndType()), reconcileMonth,
                    correctTimeEndDay);
            needData = true;
        }
        // 判断是否有配置续投采集
        if (!needData) {
            log.info("对账单编号={} 未配置保全采集规则", reconcileInfo.getReconcileCode());
            return new ArrayList<>();
        }

        if (subject.getProductCodeList().isEmpty()) {
            log.info("对账单编号={} 未配置小鲸险种编码集合", reconcileInfo.getReconcileCode());
            return new ArrayList<>();
        }
        log.info(
            "【保全】保单构建结算保单信息，对账单编号={} 查询条件【preservation_effective_time={}，endorsement_time={} 险种编码集合={}】",
            reconcileInfo.getReconcileCode(), preservationEffectiveTime, preservationTime,
            subject.getProductCodeList());
        String postponedMonth = reconcileMonth.toString("yyyy-MM");
        // 保全保单科目事件
        List<String> eventCodeList = Arrays.asList(
                SettlementEventTypeEnum.STANDARD_SURRENDER.getEventCode(),
            SettlementEventTypeEnum.HESITATE_SURRENDER.getEventCode(),
            SettlementEventTypeEnum.PROTOCOL_TERMINATION.getEventCode(),
            SettlementEventTypeEnum.TERMINATION_PRODUCT.getEventCode(),
            SettlementEventTypeEnum.VEHICLE_PREMIUM_INFO_CHANGE.getEventCode(),
            SettlementEventTypeEnum.POLICY_REFUND.getEventCode(),
            SettlementEventTypeEnum.POLICY_REPAY.getEventCode(),
            SettlementEventTypeEnum.POLICY_PRODUCT_CHANGE.getEventCode(),
            SettlementEventTypeEnum.POLICY_SUPPLEMENT_PREMIUM.getEventCode(),
            SettlementEventTypeEnum.CHANGE_RENEWAL_FALLBACK.getEventCode(),
            SettlementEventTypeEnum.GROUP_ADD_OR_SUBTRACT.getEventCode());
        List<SettlementPolicyInfoEntity> resultList = lambdaQuery().and(
                a -> a.le(SettlementPolicyInfoEntity::getPostponedMonth, postponedMonth).or()
                    .isNull(SettlementPolicyInfoEntity::getPostponedMonth))
            .eq(SettlementPolicyInfoEntity::getSettlementSubjectCode, subject.getReconcileSubjectCode())
            .in(SettlementPolicyInfoEntity::getSettlementEventCode, eventCodeList)
            .in(SettlementPolicyInfoEntity::getReconcileExecuteStatus,  CollUtil.newArrayList(3,1))
            .eq(SettlementPolicyInfoEntity::getReconcileStatus, 0)
            .eq(SettlementPolicyInfoEntity::getRectificationMark, 0)
            .eq(SettlementPolicyInfoEntity::getHangStatus, 0)
            .eq(SettlementPolicyInfoEntity::getReconcileType, reconcileInfo.getReconcileType())
            .in(SettlementPolicyInfoEntity::getProductCode, subject.getProductCodeList())
            .le(preservationEffectiveTime != null, SettlementPolicyInfoEntity::getPreservationEffectTime,
                preservationEffectiveTime)
            // .le(preservationTime != null, SettlementPolicyInfoEntity::getEndorsementTime, preservationTime)
            .list();
        String sql = StrUtil.format(
            "select * from settlement_policy_info where deleted = 0 " + "and settlement_subject_code = '{}' " + "and "
                + "settlement_event_code in ('{}') " + "and reconcile_execute_status = (1,3) " + "and reconcile_status = "
                + "0 " + "and reconcile_type = {} " + "and (postponed_month <= '{}' or  postponed_month IS NULL )" +
                "and " + "product_code in ('{}') ",
            subject.getReconcileSubjectCode(), CollUtil.join(eventCodeList, "','"), reconcileInfo.getReconcileType(),
            postponedMonth, CollUtil.join(subject.getProductCodeList(), "','"));
        StringBuffer buffer = new StringBuffer(sql);
        if (preservationEffectiveTime != null) {
            buffer.append("and preservation_effect_time <= '").append(preservationEffectiveTime).append("' ");
        }
        // 过滤保单方式
        log.info("结算单编码:[{}]查询保全数据[{}]条 SQL=[{}]", reconcileInfo.getReconcileCode(), resultList.size(),
            buffer);
        // 批改时间暂不支持，需要保单中心扩展
        return resultList;
    }

    /**
     * 根据对账单所配置的规则科目获取保单详细数据 【新契约保单 + 退保保全 + 续期】 1：获取对账单所配置的规则科目编码集合 2：解析科目编码集合的规则 3：根据规则获取保单明细数据
     */
    @Override
    public List<SettlementPolicyInfoEntity> querySettlementPolicyInfoList(SettlementReconcileInfoEntity reconcileInfo) {
        List<SettlementReconcileSubjectEntity> subjectRuleList = settlementReconcileSubjectService.lambdaQuery()
            .eq(SettlementReconcileSubjectEntity::getReconcileCode, reconcileInfo.getReconcileCode()).list();
        if (CollUtil.isEmpty(subjectRuleList)) {
            log.info("对账单={}没有匹配到科目信息,不查询保单数据",reconcileInfo.getReconcileCode());
            return Collections.emptyList();
        }
        List<String> subjectRuleCodeList = subjectRuleList.stream()
            .map(SettlementReconcileSubjectEntity::getSubjectRuleCode).distinct().collect(Collectors.toList());

        // 根据结算单科目编码集合获取配置规则
        List<ReconcileCompanySubjectDetail> subjectDetails =
            ReconcileRuleHelper.queryReconcileCompanySubjectDetailList(reconcileInfo.getReconcileCompanyCode(),
                reconcileInfo.getReconcileType(), subjectRuleCodeList);
        log.info("结算账单号=[{}]获取规则详情数据信息 = 【{}】", reconcileInfo.getReconcileCode(),
            JSON.toJSONString(subjectDetails));
        // 构建保单详情数据【新契约 + 续期 + 续投 + 保全】
        List<SettlementPolicyInfoEntity> result = new ArrayList<>();
        subjectDetails.forEach(x -> {
            if (CollUtil.isEmpty(x.getProductCodeList())) {
                log.info("规则编码={}没有匹配到险种信息,不查询保单数据", x.getSubjectRuleCode());
                return;
            }
            // 1【新契约】
            result.addAll(builderNewPolicy(reconcileInfo, x));
            // 2【续期】
            result.addAll(builderRenewalTermPolicy(reconcileInfo, x));
            // 3【续投】
            result.addAll(builderRenewalPolicy(reconcileInfo, x));
            // 4【保全】
            result.addAll(builderPreservationPolicy(reconcileInfo, x));
        });
        log.info("结算编码[{}]根据对账单所配置的规则科目获取保单详细数据总数={}", reconcileInfo.getReconcileCode(),
            result.size());
        return result;
    }

    @Override
    public void settlementPolicyFinish(String reconcileCode, String userName,String settlementMonth) {
        baseMapper.updateSettlementPolicyFinish(reconcileCode, userName,settlementMonth);
    }

    @Override
    public void saveList(List<SettlementPolicyInfoEntity> listData) {
        // 批量写入
        if (listData.size() > 600) {
            List<List<SettlementPolicyInfoEntity>> partition = ListUtils.partition(listData, 600);
            partition.forEach(x -> baseMapper.insertBatchSomeColumn(x));
        } else {
            baseMapper.insertBatchSomeColumn(listData);
        }
    }

    /**
     * @param params
     */
    @Override
    public void refreshSettlementPolicyInfoCommission(RefreshSettlementPolicyInfoCommissionVo params) {
        // 已经结算的不整,只处理可执行对账的
        List<SettlementPolicyInfoEntity> settlementPolicyInfoList = this.lambdaQuery()
            .in(CollUtil.isNotEmpty(params.getIds()), SettlementPolicyInfoEntity::getId, params.getIds())
            .in(CollUtil.isNotEmpty(params.getEventSourceCodeList()), SettlementPolicyInfoEntity::getEventSourceCode,
                params.getEventSourceCodeList())
            .eq(SettlementPolicyInfoEntity::getRectificationMark, StatusEnum.INVALID.getCode())
            .eq(SettlementPolicyInfoEntity::getReconcileStatus, ReconcileStatusEnum.TO_BE_RECONCILE.getStatusCode())
            .isNull(SettlementPolicyInfoEntity::getReconcileCode).list();
        log.info("需要处理任务数:{}", settlementPolicyInfoList.size());
        if (!settlementPolicyInfoList.isEmpty()) {
            // 批量写入
            if (settlementPolicyInfoList.size() > 600) {
                List<List<SettlementPolicyInfoEntity>> partition = ListUtils.partition(settlementPolicyInfoList, 600);
                partition.forEach(this::updateBatchSettlementPolicyInfo);
            } else {
                updateBatchSettlementPolicyInfo(settlementPolicyInfoList);
            }
        }
    }

    /**
     * 更新结算清单明细
     *
     * @param batchList 结算清单
     */
    private void updateBatchSettlementPolicyInfo(List<SettlementPolicyInfoEntity> batchList) {
        try {
            Map<Integer, List<SettlementPolicyInfoEntity>> settlementPolicyInfoMap =
                batchList.stream().collect(Collectors.groupingBy(SettlementPolicyInfoEntity::getReconcileType));
            settlementPolicyInfoMap.forEach((reconcileType, settlementPolicyInfoList) -> {
                List<String> productCodes =
                    settlementPolicyInfoList.stream().map(SettlementPolicyInfoEntity::getProductCode).distinct()
                        .collect(Collectors.toList());
                List<ProtocolInsuranceProductInfoOut> insuranceProductList =
                    ProtocolBaseHelper.queryProtocolInsuranceProductList(productCodes, reconcileType);
                log.info("获取险种与产品的关系={}", JSONUtil.toJsonStr(insuranceProductList));
                Map<String, ProtocolInsuranceProductInfoOut> insuranceProductMap = insuranceProductList.stream()
                    .collect(Collectors.toMap(ProtocolInsuranceProductInfoOut::getProductCode, v -> v));
                settlementPolicyInfoList.forEach(p -> {
                    p.setReconcileExecuteStatus(0);
                    p.setReconcileExecuteTime(null);
                    p.setReconcileExecuteDesc(null);
                    // 协议险种信息 + 是否可对账
                    try {
                        if (insuranceProductMap.containsKey(p.getProductCode())) {
                            // 协议产品信息
                            p.setProtocolProductCode(
                                insuranceProductMap.get(p.getProductCode()).getInsuranceProductCode());
                            p.setProtocolProductName(
                                insuranceProductMap.get(p.getProductCode()).getInsuranceProductName());
                            // 设置结算费率表信息
                            ReconcileBaseHelper.buildSettlementCommission(p);
                        } else {
                            p.setReconcileExecuteDesc("未配置到协议产品信息");
                            log.info("为配置到协议产品信息，小鲸险种编码={}", p.getProductCode());
                        }

                    } catch (Exception e) {
                        p.setReconcileExecuteDesc(StrUtil.isBlank(e.getMessage()) ? "未知异常" : e.getMessage());
                        log.warn("获取产品险种费率表信息异常，异常信息={}",
                            StrUtil.isBlank(e.getMessage()) ? "未知异常" : e.getMessage(), e);
                    } finally {
                        this.lambdaUpdate().eq(SettlementPolicyInfoEntity::getId, p.getId())
                            .set(SettlementPolicyInfoEntity::getReconcileExecuteStatus, p.getReconcileExecuteStatus())
                            .set(SettlementPolicyInfoEntity::getReconcileExecuteDesc, p.getReconcileExecuteDesc())
                            .set(SettlementPolicyInfoEntity::getReconcileExecuteTime, p.getReconcileExecuteTime())
                            .set(SettlementPolicyInfoEntity::getProtocolProductCode, p.getProtocolProductCode())
                            .set(SettlementPolicyInfoEntity::getProtocolProductName, p.getProtocolProductName())
                            .set(SettlementPolicyInfoEntity::getPremCode, p.getPremCode())
                            .set(SettlementPolicyInfoEntity::getSettlementSubjectName, p.getSettlementSubjectName())
                            .set(SettlementPolicyInfoEntity::getSettlementSubjectCode, p.getSettlementSubjectCode())
                            .set(SettlementPolicyInfoEntity::getIsCustomRate, p.getIsCustomRate())
                            .set(SettlementPolicyInfoEntity::getProtocolCode, p.getProtocolCode())
                            .set(SettlementPolicyInfoEntity::getSettlementRateMethod, p.getSettlementRateMethod())
                            .set(SettlementPolicyInfoEntity::getSettlementRateTax, p.getSettlementRateTax())
                            .set(SettlementPolicyInfoEntity::getSettlementRate, p.getSettlementRate())
                            .set(SettlementPolicyInfoEntity::getSettlementAmount, p.getSettlementAmount())
                            .set(SettlementPolicyInfoEntity::getExternalSignatoryCode, p.getExternalSignatoryCode())
                            .set(SettlementPolicyInfoEntity::getExternalSignatoryName, p.getExternalSignatoryName())
                            .set(SettlementPolicyInfoEntity::getExternalSignatoryType, p.getExternalSignatoryType())
                            .set(SettlementPolicyInfoEntity::getInnerSignatoryCode, p.getInnerSignatoryCode())
                            .set(SettlementPolicyInfoEntity::getInnerSignatoryName, p.getInnerSignatoryName()).update();
                    }
                });
            });
        } catch (Exception e) {
            log.warn("更新结算清单异常", e);
            log.info("更新结算清单异常数据={}", JSONUtil.toJsonStr(batchList));
        }

    }

    /**
     * 批量刷新结算单费率信息(差异)
     *
     * @param reconcileCode 对账单编码
     */
    @Override
    public void batchRefreshRate(String reconcileCode, String uuid) {
        // 判断如果是已完成对账的就不需要刷新
        SettlementReconcileInfoEntity settlementReconcileInfo = settlementReconcileInfoService.lambdaQuery()
            .eq(SettlementReconcileInfoEntity::getReconcileCode, reconcileCode).one();
        if (settlementReconcileInfo == null) {
            redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, "2");
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("结算清单信息不存在"));
        }
        if (!settlementReconcileInfo.getReconcileStatus().equals(ReconcileStatusEnum.RECONCILE_ING.getStatusCode())) {
            redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, "2");
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("结算状态不是对账中,不允许操作"));
        }
        int id = 0;
        int limit = 200;
        int count = 0;
        try {
            while (true) {
                //获取确认单中的差异数据
                List<SettlementReconcileConfirmEntity> reconcileConfirmList =
                    settlementReconcileConfirmService.lambdaQuery()
                        .eq(SettlementReconcileConfirmEntity::getReconcileCode, reconcileCode)
                        .eq(SettlementReconcileConfirmEntity::getDiffFlag, StatusEnum.NORMAL.getCode())
                        .gt(SettlementReconcileConfirmEntity::getId, id)
                        .orderByAsc(SettlementReconcileConfirmEntity::getId).last("limit " + limit).list();
                if (reconcileConfirmList.isEmpty()) {
                    break;
                } else {
                    SettlementReconcileConfirmEntity settlementReconcileConfirm =
                        CollUtil.getLast(reconcileConfirmList);
                    log.info("开始处理SettlementReconcileConfirm差异数据ID:[{}]-[{}]", id,
                        settlementReconcileConfirm.getId());
                    id = settlementReconcileConfirm.getId();
                }

                List<String> billCodeList =
                    reconcileConfirmList.stream().map(SettlementReconcileConfirmEntity::getBillCode).distinct()
                        .collect(Collectors.toList());

                //记录标记
                settlementReconcileConfirmService.lambdaUpdate().set(SettlementReconcileConfirmEntity::getMarkStatus, 1)
                    .in(SettlementReconcileConfirmEntity::getBillCode, billCodeList).update();

                List<SettlementPolicyInfoEntity> settlementPolicyInfoList = new ArrayList<>();
                // 获取小鲸临时表数据
                List<SettlementReconcilePolicyEntity> settlementReconcilePolicyList =
                    settlementReconcilePolicyService.lambdaQuery()
                        .eq(SettlementReconcilePolicyEntity::getReconcileGenerateType,
                            SettlementGenerateTypeEnum.BUSINESS_EVENTS.getCode())
                        .in(SettlementReconcilePolicyEntity::getBillCode, billCodeList).list();
                if (!settlementReconcilePolicyList.isEmpty()) {
                    List<String> settlementCodeList =
                        settlementReconcilePolicyList.stream().map(SettlementReconcilePolicyEntity::getSettlementCode)
                            .distinct().collect(Collectors.toList());
                    // 已经结算的不整,只处理可执行对账的
                    settlementPolicyInfoList.addAll(
                        this.lambdaQuery().in(SettlementPolicyInfoEntity::getSettlementCode, settlementCodeList)
                            .eq(SettlementPolicyInfoEntity::getReconcileStatus,
                                ReconcileStatusEnum.TO_BE_RECONCILE.getStatusCode())
                            .eq(SettlementPolicyInfoEntity::getRectificationMark, StatusEnum.INVALID.getCode())
                            .isNull(SettlementPolicyInfoEntity::getReconcileCode).list());
                    billCodeList.removeAll(
                        settlementReconcilePolicyList.stream().map(SettlementReconcilePolicyEntity::getBillCode)
                            .distinct().collect(Collectors.toList()));
                }
                if (!billCodeList.isEmpty()) {
                    // 获取保司临时表数据
                    List<SettlementReconcileCompanyEntity> settlementReconcileCompanyList =
                        settlementReconcileCompanyService.lambdaQuery()
                            .eq(SettlementReconcileCompanyEntity::getSettlementSubjectCode,
                                ReconcileSubjectOnlineEnum.FIRST_YR_COMM.getCode())
                            .in(SettlementReconcileCompanyEntity::getBillCode, billCodeList).list();
                    if (!settlementReconcileCompanyList.isEmpty()) {
                        //新契约数据
                        List<String> policyNoList =
                            settlementReconcileCompanyList.stream().filter(f -> StrUtil.isBlank(f.getEndorsementNo()))
                                .map(SettlementReconcileCompanyEntity::getPolicyNo).distinct()
                                .collect(Collectors.toList());
                        if (!policyNoList.isEmpty()) {
                            // 已经结算的不整,只处理可执行对账的
                            settlementPolicyInfoList.addAll(
                                this.lambdaQuery().in(SettlementPolicyInfoEntity::getPolicyNo, policyNoList)
                                    .apply("LENGTH(IFNULL(endorsement_no,'')) = 0")
                                    .eq(SettlementPolicyInfoEntity::getReconcileStatus,
                                        ReconcileStatusEnum.TO_BE_RECONCILE.getStatusCode())
                                    .eq(SettlementPolicyInfoEntity::getRectificationMark, StatusEnum.INVALID.getCode())
                                    .isNull(SettlementPolicyInfoEntity::getReconcileCode).list());
                        }
                        //保全数据
                        Map<String, String> policyMap = settlementReconcileCompanyList.stream()
                            .filter(f -> StrUtil.isNotBlank(f.getEndorsementNo())).collect(
                                Collectors.toMap(SettlementReconcileCompanyEntity::getEndorsementNo,
                                    SettlementReconcileCompanyEntity::getPolicyNo, (v1, v2) -> {
                                        // 这里处理自己的逻辑
                                        return v2;
                                    }));
                        if (!policyMap.isEmpty()) {
                            // 已经结算的不整,只处理可执行对账的
                            settlementPolicyInfoList.addAll(this.lambdaQuery()
                                .in(SettlementPolicyInfoEntity::getPolicyNo, new ArrayList<>(policyMap.values()))
                                .in(SettlementPolicyInfoEntity::getEndorsementNo, new ArrayList<>(policyMap.keySet()))
                                .eq(SettlementPolicyInfoEntity::getRectificationMark, StatusEnum.INVALID.getCode())
                                .eq(SettlementPolicyInfoEntity::getReconcileStatus,
                                    ReconcileStatusEnum.TO_BE_RECONCILE.getStatusCode())
                                .isNull(SettlementPolicyInfoEntity::getReconcileCode).list());
                        }
                    }
                }
                // 如果数据为空那么久跳出当前循环

                if (!settlementPolicyInfoList.isEmpty()) {
                    try {
                        //处理数据前 去个重
                        settlementPolicyInfoList =
                            settlementPolicyInfoList.stream().distinct().collect(Collectors.toList());
                        updateBatchSettlementPolicyInfo(settlementPolicyInfoList);
                    } catch (Exception e) {
                        log.warn("处理费率数据异常", e);
                    } finally {
                        log.info("完成处理数据[{}]条", settlementPolicyInfoList.size());
                        count += settlementPolicyInfoList.size();
                    }
                }
            }
        } catch (Exception e) {
            log.warn("刷新结算单费率信息异常", e);
        } finally {
            redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, "2");
        }
        log.info("合计处理数据条数={}条", count);
    }

    /**
     * 处理明细变更保全批单号
     *
     * @param settlementPolicyInfoList 需要变更的明细数据
     * @param newEndorsementNo         新批单号
     * @param pushEventCode            事件源编码
     */
    @Override
    public void handleEndorsementNoChange(List<SettlementPolicyInfoEntity> settlementPolicyInfoList,
        String pushEventCode, String newEndorsementNo) {
        List<SettlementPolicyInfoEntity> rectificationList = new ArrayList<>(settlementPolicyInfoList);
        // 1.将现有数据设置为冲正数据,并设置无需对账
        rectificationList.forEach(action -> {
            // 生成新的编码 原来的编码还要继续使用
            String settlementCode = PolicySettlementUtils.createCodeLastNumber("SC");
            action.setSettlementCode(settlementCode);
            action.setRectificationMark(StatusEnum.NORMAL.getCode());
            action.setReconcileExecuteStatus(2);
            action.setReconcileExecuteDesc("业务冲正数据");
        });
        //将本身数据变成冲正数据状态
        this.updateBatchById(rectificationList);
        // 封装真正的冲正的数据  将保费/手续费 取反
        List<SettlementPolicyInfoEntity> saveRectificationList = settlementPolicyInfoList.stream().map(m -> {
            SettlementPolicyInfoEntity settlementPolicyInfo =
                BeanUtil.copyProperties(m, SettlementPolicyInfoEntity.class);
            String settlementCode = PolicySettlementUtils.createCodeLastNumber("SC");
            settlementPolicyInfo.setId(null);
            settlementPolicyInfo.setSettlementCode(settlementCode);
            settlementPolicyInfo.setPremium(m.getPremium().negate());
            settlementPolicyInfo.setProductPremiumTotal(m.getProductPremiumTotal().negate());
            settlementPolicyInfo.setSettlementAmount(m.getSettlementAmount().negate());
            settlementPolicyInfo.setRectificationMark(StatusEnum.NORMAL.getCode());
            settlementPolicyInfo.setSettlementGenerateType(SettlementGenerateTypeEnum.AUTO_CORRECTION.getCode());
            return settlementPolicyInfo;
        }).collect(Collectors.toList());
        // 插入冲正数据
        this.saveList(saveRectificationList);

        // 封装修正数据
        List<SettlementPolicyInfoEntity> updateList = settlementPolicyInfoList.stream().map(m -> {
            SettlementPolicyInfoEntity settlementPolicyInfo =
                BeanUtil.copyProperties(m, SettlementPolicyInfoEntity.class);
            settlementPolicyInfo.setId(null);
            settlementPolicyInfo.setEventSourceCode(pushEventCode);
            settlementPolicyInfo.setEndorsementNo(newEndorsementNo);
            return settlementPolicyInfo;
        }).collect(Collectors.toList());
        // 更新数据
        this.saveList(updateList);
    }

    @Override
    public void handlePolicyNoChange(List<SettlementPolicyInfoEntity> settlementPolicyInfoList, String pushEventCode,
        String policyNo) {
        List<SettlementPolicyInfoEntity> rectificationList = new ArrayList<>(settlementPolicyInfoList);
        // 1.将现有数据设置为冲正数据,并设置无需对账
        rectificationList.forEach(action -> {
            // 生成新的编码 原来的编码还要继续使用
            String settlementCode = PolicySettlementUtils.createCodeLastNumber("SC");
            action.setSettlementCode(settlementCode);
            action.setRectificationMark(StatusEnum.NORMAL.getCode());
            action.setReconcileExecuteStatus(2);
            action.setReconcileExecuteDesc("业务冲正数据");
        });
        //将本身数据变成冲正数据状态
        this.updateBatchById(rectificationList);
        // 封装真正的冲正的数据  将保费/手续费 取反
        List<SettlementPolicyInfoEntity> saveRectificationList = settlementPolicyInfoList.stream().map(m -> {
            SettlementPolicyInfoEntity settlementPolicyInfo =
                BeanUtil.copyProperties(m, SettlementPolicyInfoEntity.class);
            String settlementCode = PolicySettlementUtils.createCodeLastNumber("SC");
            settlementPolicyInfo.setId(null);
            settlementPolicyInfo.setSettlementCode(settlementCode);
            settlementPolicyInfo.setPremium(m.getPremium().negate());
            settlementPolicyInfo.setProductPremiumTotal(m.getProductPremiumTotal().negate());
            settlementPolicyInfo.setSettlementAmount(m.getSettlementAmount().negate());
            settlementPolicyInfo.setRectificationMark(StatusEnum.NORMAL.getCode());
            settlementPolicyInfo.setSettlementGenerateType(SettlementGenerateTypeEnum.AUTO_CORRECTION.getCode());
            return settlementPolicyInfo;
        }).collect(Collectors.toList());
        // 插入冲正数据
        this.saveList(saveRectificationList);
    }

    /**
     * 保全生成结算明细
     *
     * @param policyInfo         保单信息
     * @param preservationDetail 保全信息
     * @param reconcileType      事件类型
     * @return
     */
    @Override
    public List<SettlementPolicyInfoEntity> createSettlementPolicyInfo(SettlementEventTypeEnum settlementEventTypeEnum,
        EpContractInfoVo policyInfo, EpPreservationV2Vo preservationDetail, Integer reconcileType) {
        if (policyInfo == null) {
            return Collections.emptyList();
        }
        log.info("获取保单详情，保单详情=【{}】", JSON.toJSONString(policyInfo));
        ReconcileTypeEnum reconcileTypeEnum = ReconcileTypeEnum.matchSearchCode(reconcileType);
        if (reconcileTypeEnum == null) {
            return Collections.emptyList();
        }
        //获取所有的险种编码 (当前保全不支持增加险种 但是可以减少险种信息)
        List<String> productCodes =
            policyInfo.getProductInfoList().stream().map(EpProductInfoVo::getProductCode).distinct()
                .collect(Collectors.toList());
        // 获取所有小鲸险种详情集合
        List<ProductBase> productList = productBaseService.getProductBaseList(productCodes);
        Map<String, ProductBase> productMap = productList.stream()
            .collect(Collectors.toMap(ProductBase::getProductCode, Function.identity(), (x, y) -> y));
        List<String> subtractList = CollUtil.subtractToList(productCodes, new ArrayList<>(productMap.keySet()));
        if (CollUtil.isNotEmpty(subtractList)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                StrUtil.format("险种编码={}没有获取到详情,请检查", JSONUtil.toJsonStr(subtractList))));
        }
        // 3 获取主险信息
        String mainEpProductCode = policyInfo.getContractBaseInfo().getMainProductCode();
        if (StrUtil.isBlank(mainEpProductCode)) {
            EpProductInfoVo epProductInfoVo =
                policyInfo.getProductInfoList().stream().filter(x -> x.getMainInsurance() == 1).findFirst()
                    .orElse(null);
            if (epProductInfoVo != null) {
                mainEpProductCode = epProductInfoVo.getProductCode();
                policyInfo.getContractBaseInfo().setMainProductCode(mainEpProductCode);
            }
        }
        if (mainEpProductCode == null || !productMap.containsKey(mainEpProductCode)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                StrUtil.format("保单号={}, 缺少主险信息", policyInfo.getContractBaseInfo().getPolicyNo())));
        }
        // 对账基础信息
        SettlementPolicyInfoEntity bean =
            createSettlementPolicyInfoBasis(policyInfo, preservationDetail, reconcileType);
        List<SettlementPolicyInfoEntity> resultList = new ArrayList<>();
        // 判断不同的类型进行不同的处理
        if (settlementEventTypeEnum == SettlementEventTypeEnum.STANDARD_SURRENDER) {
            // 主险 如果是保单是长险的话，退保是不需要做结算处理
            ProductBase mainProductBase = productMap.get(mainEpProductCode);
            if (mainProductBase.getLongShortFlag() != null && mainProductBase.getLongShortFlag() == 1) {
                return Collections.emptyList();
            }
            resultList = buildStandardSurrender(policyInfo, bean, productMap);
        } else if (settlementEventTypeEnum == SettlementEventTypeEnum.HESITATE_SURRENDER) {
            // 犹豫期退保
            resultList = buildHesitateSurrender(policyInfo, bean, productMap);
        } else if (settlementEventTypeEnum == SettlementEventTypeEnum.PROTOCOL_TERMINATION) {
            // 协议解约
            resultList = buildProtocolTermination(policyInfo, bean, productMap);
        } else if (settlementEventTypeEnum == SettlementEventTypeEnum.TERMINATION_PRODUCT) {
            // 附加险解约
            // 主险 如果是保单是长险的话，退保是不需要做结算处理
            ProductBase mainProductBase = productMap.get(mainEpProductCode);
            if (mainProductBase.getLongShortFlag() != null && mainProductBase.getLongShortFlag() == 1) {
                return Collections.emptyList();
            }
            resultList = buildTerminationProduct(policyInfo, bean, productMap, preservationDetail);
        } else if (settlementEventTypeEnum == SettlementEventTypeEnum.GROUP_ADD_OR_SUBTRACT) {
            // 团险增减员 保费为0 不处理
            if (bean.getPremium().compareTo(BigDecimal.ZERO) == 0) {
                return Collections.emptyList();
            }
            resultList = buildGroupAddOrSubtract(policyInfo, bean, productMap);
        }else if (settlementEventTypeEnum == SettlementEventTypeEnum.PERSONAL_NEW_POLICY) {
            // 处理个险新契约
            resultList = buildPersonalNewPolicy(policyInfo, bean, productMap);
        }else if (settlementEventTypeEnum == SettlementEventTypeEnum.GROUP_NEW_POLICY) {
            // 团险增减员 保费为0 不处理
            if (bean.getPremium().compareTo(BigDecimal.ZERO) == 0) {
                return Collections.emptyList();
            }
            resultList = buildGroupNewPolicy(policyInfo, bean, productMap);
        }
        return resultList;
    }
    /**
     * 团险新契约
     * @param policyInfo
     * @param sourceBean
     * @param productMap
     * @return
     */
    private List<SettlementPolicyInfoEntity> buildGroupNewPolicy(EpContractInfoVo policyInfo,
        SettlementPolicyInfoEntity sourceBean, Map<String, ProductBase> productMap){
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        List<SettlementPolicyInfoEntity> resultList = new ArrayList<>();
        // 2 获取所有小鲸险种集合
        // 4 遍历险种
        policyInfo.getProductInfoList().forEach(x -> {
            // 基础构建
            SettlementPolicyInfoEntity bean = BeanUtil.copyProperties(sourceBean,SettlementPolicyInfoEntity.class);
            // 执行写入结算保单详情
            // 险种信息
            bean.setProductCode(x.getProductCode());
            bean.setProductName(x.getProductName());
            bean.setPlanCode(x.getPlanCode());
            bean.setPlanName(x.getPlanName());
            bean.setPremium(x.getPremium());
            bean.setMainInsurance(x.getMainInsurance());
            bean.setAdditionalRisksType(x.getAdditionalRisksType());
            bean.setEffectiveDate(policyInfo.getContractExtendInfo().getEnforceTime());
            bean.setEndDate(PolicySettlementUtils.getInsuredEndDate(bean.getEffectiveDate(), x.getInsuredPeriod(), x.getInsuredPeriodType()));
            // 团险续期年期 + 续期期数默认用1
            bean.setRenewalYear(1);
            bean.setRenewalPeriod(1);
            bean.setCoverage(x.getCoverage());
            bean.setCoverageUnit(x.getCoverageUnit());
            bean.setCoverageUnitName(x.getCoverageUnitName());
            bean.setInsuredPeriodType(x.getInsuredPeriodType());
            bean.setInsuredPeriod(x.getInsuredPeriod());
            bean.setPeriodType(x.getPeriodType());
            bean.setPaymentPeriodType(x.getPaymentPeriodType());
            bean.setPaymentPeriod(x.getPaymentPeriod());
            bean.setDrawAge(x.getAnnDrawAge());
            // 险种总保费为正常保费
            bean.setProductPremiumTotal(x.getPremium());
            bean.setCopies(x.getCopies());
            // 险种分类信息
            if (productMap.containsKey(x.getProductCode())) {
                ProductBase productBase = productMap.get(x.getProductCode());
                bean.setProductGroup(productBase.getProductGroup());
                if (StrUtil.isBlank(bean.getProductName())){
                    bean.setProductName(productBase.getProductName());
                }
                bean.setProductType(productBase.getProductType());
                bean.setLevel2Code(productBase.getLevel2Code());
                bean.setLevel3Code(productBase.getLevel3Code());
                if (StrUtil.isBlank(productBase.getLevel2Code())){
                    throw new GlobalException(
                        BasicCodeMsg.SERVER_ERROR.setMsg("险种编码=" + productBase.getProductCode()+"不存在二级分类"));
                }
                // [团险]折算保费运算：无需被保人年龄 + 长短线默认短线
                ReconcileBaseHelper.buildDiscountPremium(policyNo, x.getProductCode(), null, PolicyProductTypeEnum.GROUP, 0,
                    x.getPremium(), x.getPeriodType(), x.getPaymentPeriodType(), x.getPaymentPeriod(), bean);
            } else {
                bean.setReconcileExecuteDesc("产品信息不存在，产品编码=" + x.getProductCode());
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(bean.getReconcileExecuteDesc()));
            }
            boolean b = ReconcileBaseHelper.buildSettlementCommission(bean);
            if (!b) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(bean.getReconcileExecuteDesc()));
            }
            resultList.add(bean);
        });
        return resultList;
    }

    /**
     * 个险新契约
     * @param policyInfo
     * @param sourceBean
     * @param productMap
     * @return
     */
    private List<SettlementPolicyInfoEntity> buildPersonalNewPolicy(EpContractInfoVo policyInfo,
        SettlementPolicyInfoEntity sourceBean, Map<String, ProductBase> productMap){
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        List<SettlementPolicyInfoEntity> resultList = new ArrayList<>();
        PolicyProductTypeEnum policyProductTypeEnum =
            PolicyProductTypeEnum.getProdTypeEnum(policyInfo.getPolicyProductType());
        // 非车险
        if (policyProductTypeEnum != PolicyProductTypeEnum.VEHICLE) {
            // 获取所有小鲸险种集合
            log.info("新契约保单为【个财险】保单号={}", policyNo);
            // 开始处理遍历被保人信息
            List<EpInsuredInfoVo> insuredInfoList = policyInfo.getInsuredInfoList();
            for (EpInsuredInfoVo insured : insuredInfoList) {
                insured.getProductInfoList().forEach(x -> {
                    SettlementPolicyInfoEntity bean =BeanUtil.copyProperties(sourceBean,SettlementPolicyInfoEntity.class);
                    // 构建保单明细 + 明细纪录集合
                    // 被保人信息
                    bean.setInsuredName(insured.getInsuredName());
                    bean.setInsuredMobile(insured.getInsuredMobile());
                    bean.setInsuredIdCard(insured.getInsuredIdCard());
                    bean.setInsuredGender(insured.getInsuredGender());
                    bean.setInsuredBirthday(insured.getInsuredBirthday());
                    bean.setInsuredPolicyAge(insured.getInsuredPolicyAge());
                    // 险种信息
                    bean.setProductCode(x.getProductCode());
                    bean.setProductName(x.getProductName());
                    bean.setPlanCode(x.getPlanCode());
                    bean.setPlanName(x.getPlanName());
                    bean.setMainInsurance(x.getMainInsurance());
                    bean.setAdditionalRisksType(x.getAdditionalRisksType());
                    bean.setEffectiveDate(policyInfo.getContractExtendInfo().getEnforceTime());
                    bean.setEndDate(PolicySettlementUtils.getInsuredEndDate(bean.getEffectiveDate(), x.getInsuredPeriod(), x.getInsuredPeriodType()));
                    // 新契约续期年期 + 续期期数默认用1
                    bean.setRenewalYear(1);
                    bean.setRenewalPeriod(1);
                    bean.setCoverage(x.getCoverage());
                    bean.setCoverageUnit(x.getCoverageUnit());
                    bean.setCoverageUnitName(x.getCoverageUnitName());
                    bean.setInsuredPeriodType(x.getInsuredPeriodType());
                    bean.setInsuredPeriod(x.getInsuredPeriod());
                    bean.setPeriodType(x.getPeriodType());
                    bean.setPaymentPeriodType(x.getPaymentPeriodType());
                    bean.setPaymentPeriod(x.getPaymentPeriod());
                    bean.setDrawAge(x.getAnnDrawAge());
                    bean.setPremium(x.getPremium());
                    // 新契约个险，险种总保费为正常保费
                    bean.setProductPremiumTotal(x.getPremium());
                    bean.setCopies(x.getCopies());
                    // 其他信息
                    if (productMap.containsKey(x.getProductCode())) {
                        ProductBase productBase = productMap.get(x.getProductCode());
                        bean.setProductGroup(productBase.getProductGroup());
                        bean.setLevel2Code(productBase.getLevel2Code());
                        bean.setLevel3Code(productBase.getLevel3Code());
                        if (StrUtil.isBlank(productBase.getLevel2Code())){
                            throw new GlobalException(
                                BasicCodeMsg.SERVER_ERROR.setMsg("险种编码=" + productBase.getProductCode()+"不存在二级分类"));
                        }
                        // [个险]折算保费运算
                        ReconcileBaseHelper.buildDiscountPremium(policyNo, x.getProductCode(), insured.getInsuredPolicyAge()
                            , policyProductTypeEnum, productMap.get(x.getProductCode()).getLongShortFlag(), x.getPremium(), x.getPeriodType(), x.getPaymentPeriodType(), x.getPaymentPeriod(), bean);
                    } else {
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + x.getProductCode()));
                    }
                    // 设置结算费率表信息[优先标准费率 > 农保佣金]
                    boolean b = ReconcileBaseHelper.buildSettlementCommission(bean);
                    if (!b) {
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(bean.getReconcileExecuteDesc()));
                    }
                    resultList.add(bean);
                });
            }
        } else {
            log.info("新契约保单为【车险】保单号={}",policyNo);
            // 遍历险种
            policyInfo.getProductInfoList().forEach(x -> {
                // 基础构建
                SettlementPolicyInfoEntity bean = BeanUtil.copyProperties(sourceBean,SettlementPolicyInfoEntity.class);
                // 险种信息
                bean.setProductCode(x.getProductCode());
                bean.setProductName(x.getProductName());
                bean.setPlanCode(x.getPlanCode());
                bean.setPlanName(x.getPlanName());
                bean.setProductType(x.getPeriodType());
                bean.setMainInsurance(x.getMainInsurance());
                bean.setAdditionalRisksType(x.getAdditionalRisksType());
                bean.setEffectiveDate(policyInfo.getContractExtendInfo().getEnforceTime());
                bean.setEndDate(PolicySettlementUtils.getInsuredEndDate(bean.getEffectiveDate(), x.getInsuredPeriod(), x.getInsuredPeriodType()));
                // 团险续期年期 + 续期期数默认用1
                bean.setRenewalYear(1);
                bean.setRenewalPeriod(1);
                bean.setCoverage(x.getCoverage());
                bean.setCoverageUnit(x.getCoverageUnit());
                bean.setCoverageUnitName(x.getCoverageUnitName());
                bean.setInsuredPeriodType(x.getInsuredPeriodType());
                bean.setInsuredPeriod(x.getInsuredPeriod());
                bean.setPeriodType(x.getPeriodType());
                bean.setPaymentPeriodType(x.getPaymentPeriodType());
                bean.setPaymentPeriod(x.getPaymentPeriod());
                bean.setDrawAge(x.getAnnDrawAge());
                bean.setPremium(x.getPremium());
                // 险种总保费为正常保费
                bean.setProductPremiumTotal(x.getPremium());
                bean.setCopies(x.getCopies());
                // 险种分类信息
                if (productMap.containsKey(x.getProductCode())) {
                    ProductBase productBase = productMap.get(x.getProductCode());
                    if (StrUtil.isBlank(bean.getProductName())){
                        bean.setProductName(productBase.getProductName());
                    }
                    bean.setProductGroup(productBase.getProductGroup());
                    bean.setLevel2Code(productBase.getLevel2Code());
                    bean.setLevel3Code(productBase.getLevel3Code());
                    // [车险]折算保费运算：无需被保人年龄 + 长短线默认短线
                    ReconcileBaseHelper.buildDiscountPremium(policyNo, x.getProductCode(), null,
                        PolicyProductTypeEnum.VEHICLE, 0, x.getPremium(), x.getPeriodType(), x.getPaymentPeriodType(), x.getPaymentPeriod(), bean);
                } else {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + x.getProductCode()));
                }
                // 设置结算费率表信息[优先标准费率 > 农保佣金]
                boolean b = ReconcileBaseHelper.buildSettlementCommission(bean);
                if (!b) {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(bean.getReconcileExecuteDesc()));
                }
                resultList.add(bean);
            });
        }
        return resultList;
    }
    /**
     * 团险增减员
     *
     * @param policyInfo
     * @param sourceBean
     * @param productMap
     * @return
     */
    private List<SettlementPolicyInfoEntity> buildGroupAddOrSubtract(EpContractInfoVo policyInfo,
        SettlementPolicyInfoEntity sourceBean, Map<String, ProductBase> productMap) {
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        List<SettlementPolicyInfoEntity> resultList = new ArrayList<>();
        // 1 获取保单详情
        // 2 获取主险信息，团险读取任意一个险种信息即可
        EpProductInfoVo mainEpProductInfo =
            policyInfo.getProductInfoList().stream().filter(x -> x.getMainInsurance() == 1).findFirst().orElse(null);
        if (mainEpProductInfo == null) {
            log.warn("团险增减员获取保单详情，主险信息缺失，保单详情=【{}】", JSON.toJSONString(policyInfo));
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                "团险新契约获取保单详情，主险数量不为1，保单详情=【" + JSON.toJSONString(policyInfo) + "】"));
        }
        // 3 获取所有小鲸险种集合
        ProductBase product = productMap.get(mainEpProductInfo.getProductCode());
        if (product == null) {
            throw new GlobalException(
                BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + mainEpProductInfo.getProductCode()));
        }
        // 4 构建写入基本信息
        SettlementPolicyInfoEntity bean = BeanUtil.copyProperties(sourceBean, SettlementPolicyInfoEntity.class);
        // 5 获取小鲸险种信息
        bean.setProductCode(product.getProductCode());
        bean.setProductName(product.getProductName());
        // todo 计划也取主险的 可能会存在两个 但是只生成一个对账单,以保司为准
        bean.setPlanCode(mainEpProductInfo.getPlanCode());
        bean.setPlanName(mainEpProductInfo.getPlanName());
        bean.setProductGroup(product.getProductGroup());
        bean.setLevel2Code(product.getLevel2Code());
        bean.setLevel3Code(product.getLevel3Code());
        bean.setProductType(mainEpProductInfo.getPeriodType());
        bean.setMainInsurance(mainEpProductInfo.getMainInsurance());
        bean.setAdditionalRisksType(mainEpProductInfo.getAdditionalRisksType());
        bean.setEffectiveDate(policyInfo.getContractExtendInfo().getEnforceTime());
        bean.setEndDate(
            PolicySettlementUtils.getInsuredEndDate(bean.getEffectiveDate(), mainEpProductInfo.getInsuredPeriod(),
                mainEpProductInfo.getInsuredPeriodType()));
        // 团险续期年期 + 续期期数默认用1
        bean.setRenewalYear(1);
        bean.setRenewalPeriod(1);
        bean.setCoverage(mainEpProductInfo.getCoverage());
        bean.setCoverageUnit(mainEpProductInfo.getCoverageUnit());
        bean.setCoverageUnitName(mainEpProductInfo.getCoverageUnitName());
        bean.setInsuredPeriodType(mainEpProductInfo.getInsuredPeriodType());
        bean.setInsuredPeriod(mainEpProductInfo.getInsuredPeriod());
        bean.setPeriodType(mainEpProductInfo.getPeriodType());
        bean.setPaymentPeriodType(mainEpProductInfo.getPaymentPeriodType());
        bean.setPaymentPeriod(mainEpProductInfo.getPaymentPeriod());
        bean.setDrawAge(mainEpProductInfo.getAnnDrawAge());
        // 险种总保费为正常保费
        bean.setProductPremiumTotal(mainEpProductInfo.getPremium());
        bean.setCopies(mainEpProductInfo.getCopies());
        // 折算保费运算
        ReconcileBaseHelper.buildDiscountPremium(policyNo, product.getProductCode(), null, PolicyProductTypeEnum.GROUP,
            0, bean.getPremium(), mainEpProductInfo.getPeriodType(), mainEpProductInfo.getPaymentPeriodType(),
            mainEpProductInfo.getPaymentPeriod(), bean);
        // 设置结算费率表信息[优先标准费率 > 农保佣金]
        boolean b = ReconcileBaseHelper.buildSettlementCommission(bean);
        if (!b) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(bean.getReconcileExecuteDesc()));
        }
        resultList.add(bean);
        return resultList;
    }

    /**
     * 附加险解约
     *
     * @param policyInfo
     * @param sourceBean
     * @param productMap
     * @return
     */
    private List<SettlementPolicyInfoEntity> buildTerminationProduct(EpContractInfoVo policyInfo,
        SettlementPolicyInfoEntity sourceBean, Map<String, ProductBase> productMap,
        EpPreservationV2Vo preservationDetail) {
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        String mainProductCode = policyInfo.getContractBaseInfo().getMainProductCode();
        PolicyProductTypeEnum policyProductTypeEnum =
            PolicyProductTypeEnum.getProdTypeEnum(policyInfo.getPolicyProductType());
        List<SettlementPolicyInfoEntity> resultList = new ArrayList<>();
        switch (policyProductTypeEnum) {
            case PERSONAL:
            case PROPERTY: {
                log.info("接受保单中心结算事件(个财)");
                // 需要操作的数据
                preservationDetail.getTerminationProductList().forEach(product -> {
                    SettlementPolicyInfoEntity bean =
                        BeanUtil.copyProperties(sourceBean, SettlementPolicyInfoEntity.class);
                    // 取第一个被保人的险种集合取获取险种
                    if (policyInfo.getInsuredInfoList().isEmpty()) {
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                            StrUtil.format("保单信息不存在被保人信息，保单号={}", policyNo)));
                    }
                    EpInsuredInfoVo epInsuredInfoVo = policyInfo.getInsuredInfoList().get(0);
                    EpPersonalProductInfoVo epPersonalProductInfoVo = epInsuredInfoVo.getProductInfoList().stream()
                        .filter(cp -> StringUtils.equals(cp.getProductCode(), product.getProductCode())).findFirst()
                        .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                            StrUtil.format("险种编码={} 在保单信息不存在", product.getProductCode()))));
                    ProductBase productBase = productMap.get(product.getProductCode());
                    if (productBase == null) {
                        throw new GlobalException(
                            BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + product.getProductCode()));
                    }
                    // 险种信息
                    bean.setProductType(productBase.getProductType());
                    bean.setProductCode(productBase.getProductCode());
                    bean.setProductName(productBase.getProductName());
                    bean.setProductGroup(productBase.getProductGroup());
                    bean.setLevel2Code(productBase.getLevel2Code());
                    bean.setLevel3Code(productBase.getLevel3Code());
                    // 设置结算金额相关
                    bean.setPremium(product.getSurrenderCash().abs().negate());
                    bean.setProductPremiumTotal(epPersonalProductInfoVo.getPremium());
                    bean.setSurrenderAmount(bean.getPremium());
                    // 折算保费运算
                    ReconcileBaseHelper.buildDiscountPremium(policyNo, productBase.getProductCode(),
                        epInsuredInfoVo.getInsuredPolicyAge(), policyProductTypeEnum, productBase.getLongShortFlag(),
                        bean.getPremium(), epPersonalProductInfoVo.getPeriodType(),
                        epPersonalProductInfoVo.getPaymentPeriodType(), epPersonalProductInfoVo.getPaymentPeriod(),
                        bean);
                    // 其他信息
                    bean.setProductType(productBase.getProductType());
                    bean.setMainInsurance(productBase.getMainProductFlag());
                    bean.setAdditionalRisksType(productBase.getAdditionalRisksType());
                    bean.setEffectiveDate(policyInfo.getContractExtendInfo().getEnforceTime());
                    // 设置结算费率表信息[优先标准费率 > 农保佣金]
                    boolean b = ReconcileBaseHelper.buildSettlementCommission(bean);
                    if (!b) {
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(bean.getReconcileExecuteDesc()));
                    }
                    resultList.add(bean);
                });
                break;
            }
            case VEHICLE:
            case GROUP: {
                log.info("接受保单中心结算事件(车团)");
                // 需要操作的数据
                preservationDetail.getTerminationProductList().forEach(product -> {
                    // 执行写入结算保单详情
                    SettlementPolicyInfoEntity bean =
                        BeanUtil.copyProperties(sourceBean, SettlementPolicyInfoEntity.class);
                    // 获取团单的险种信息
                    EpProductInfoVo epProductInfoVo = policyInfo.getProductInfoList().stream()
                        .filter(cp -> StringUtils.equals(cp.getProductCode(), product.getProductCode())).findFirst()
                        .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                            StrUtil.format("险种编码={} 在保单信息不存在", product.getProductCode()))));

                    ProductBase productBase = productMap.get(product.getProductCode());
                    if (productBase == null) {
                        throw new GlobalException(
                            BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + product.getProductCode()));
                    }
                    // 险种信息
                    bean.setProductCode(productBase.getProductCode());
                    bean.setProductName(productBase.getProductName());
                    bean.setProductGroup(productBase.getProductGroup());
                    bean.setLevel2Code(productBase.getLevel2Code());
                    bean.setLevel3Code(productBase.getLevel3Code());
                    // 设置结算金额相关
                    bean.setPremium(product.getSurrenderCash().abs().negate());
                    bean.setProductPremiumTotal(epProductInfoVo.getPremium());
                    bean.setSurrenderAmount(bean.getPremium());
                    // 产品组信息
                    bean.setProductType(productBase.getProductType());
                    bean.setMainInsurance(productBase.getMainProductFlag());
                    bean.setAdditionalRisksType(productBase.getAdditionalRisksType());
                    bean.setEffectiveDate(policyInfo.getContractExtendInfo().getEnforceTime());
                    // 设置结算费率表信息[优先标准费率 > 农保佣金]
                    boolean b = ReconcileBaseHelper.buildSettlementCommission(bean);
                    if (!b) {
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(bean.getReconcileExecuteDesc()));
                    }
                    resultList.add(bean);
                });
                break;
            }
            default: {
                log.warn("保单类型暂不处理.");
                return resultList;
            }
        }
        return resultList;
    }

    private List<SettlementPolicyInfoEntity> buildProtocolTermination(EpContractInfoVo policyInfo,
        SettlementPolicyInfoEntity sourceBean, Map<String, ProductBase> productMap) {
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        PolicyProductTypeEnum policyProductTypeEnum =
            PolicyProductTypeEnum.getProdTypeEnum(policyInfo.getPolicyProductType());
        List<SettlementPolicyInfoEntity> resultList = new ArrayList<>();
        switch (policyProductTypeEnum) {
            case PERSONAL:
            case PROPERTY: {
                log.info("接受保单中心结算事件(个财)");
                // 获取所有小鲸险种集合
                List<EpInsuredInfoVo> insuredInfoList = policyInfo.getInsuredInfoList();
                // 需要操作的数据
                for (EpInsuredInfoVo insured : insuredInfoList) {
                    insured.getProductInfoList().forEach(x -> {
                        // 基础构建
                        SettlementPolicyInfoEntity bean =
                            BeanUtil.copyProperties(sourceBean, SettlementPolicyInfoEntity.class);
                        // 被保人信息
                        bean.setInsuredName(insured.getInsuredName());
                        bean.setInsuredMobile(insured.getInsuredMobile());
                        bean.setInsuredIdCard(insured.getInsuredIdCard());
                        bean.setInsuredGender(insured.getInsuredGender());
                        bean.setInsuredBirthday(insured.getInsuredBirthday());
                        bean.setInsuredPolicyAge(insured.getInsuredPolicyAge());
                        // 险种信息
                        bean.setProductCode(x.getProductCode());
                        bean.setProductName(x.getProductName());
                        bean.setPlanCode(x.getPlanCode());
                        bean.setPlanName(x.getPlanName());
                        bean.setMainInsurance(x.getMainInsurance());
                        bean.setAdditionalRisksType(x.getAdditionalRisksType());
                        bean.setEffectiveDate(policyInfo.getContractExtendInfo().getEnforceTime());
                        bean.setEndDate(
                            PolicySettlementUtils.getInsuredEndDate(bean.getEffectiveDate(), x.getInsuredPeriod(),
                                x.getInsuredPeriodType()));
                        // 新契约用 续期年期 + 续期期数
                        bean.setCoverage(x.getCoverage());
                        bean.setCoverageUnit(x.getCoverageUnit());
                        bean.setCoverageUnitName(x.getCoverageUnitName());
                        bean.setInsuredPeriodType(x.getInsuredPeriodType());
                        bean.setInsuredPeriod(x.getInsuredPeriod());
                        bean.setPeriodType(x.getPeriodType());
                        bean.setPaymentPeriodType(x.getPaymentPeriodType());
                        bean.setPaymentPeriod(x.getPaymentPeriod());
                        bean.setDrawAge(x.getAnnDrawAge());
                        // 协议解约险种总保费为正常保费
                        bean.setPremium(x.getPremium().abs().negate());
                        bean.setProductPremiumTotal(bean.getPremium());
                        bean.setCopies(x.getCopies());
                        // 产品组信息
                        if (productMap.containsKey(x.getProductCode())) {
                            ProductBase productBase = productMap.get(x.getProductCode());
                            if (StrUtil.isBlank(bean.getProductName())){
                                bean.setProductName(productBase.getProductName());
                            }
                            bean.setProductGroup(productBase.getProductGroup());
                            bean.setProductType(productBase.getProductType());
                            bean.setLevel2Code(productBase.getLevel2Code());
                            bean.setLevel3Code(productBase.getLevel3Code());
                            // 折算保费运算
                            ReconcileBaseHelper.buildDiscountPremium(policyNo, x.getProductCode(),
                                insured.getInsuredPolicyAge(), policyProductTypeEnum,
                                productMap.get(x.getProductCode()).getLongShortFlag(), bean.getPremium(),
                                x.getPeriodType(), x.getPaymentPeriodType(), x.getPaymentPeriod(), bean);
                        } else {
                            throw new GlobalException(
                                BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + x.getProductCode()));
                        }
                        // 设置结算费率表信息[优先标准费率 > 农保佣金]
                        boolean b = ReconcileBaseHelper.buildSettlementCommission(bean);
                        if (!b) {
                            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(bean.getReconcileExecuteDesc()));
                        }
                        resultList.add(bean);
                    });
                }
                break;
            }
            case VEHICLE:
            case GROUP: {
                log.info("接受保单中心结算事件(车团)");
                EpProductInfoVo mainEpProductInfo =
                    policyInfo.getProductInfoList().stream().filter(x -> x.getMainInsurance() == 1).findFirst()
                        .orElse(null);
                if (mainEpProductInfo == null) {
                    log.warn("团险增减员获取保单详情，主险信息缺失，保单详情=【{}】", JSON.toJSONString(policyInfo));
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                        "团险新契约获取保单详情，主险数量不为1，保单详情=【" + JSON.toJSONString(policyInfo) + "】"));
                }
                // 需要操作的数据
                policyInfo.getProductInfoList().forEach(x -> {
                    // 基础构建
                    SettlementPolicyInfoEntity bean =
                        BeanUtil.copyProperties(sourceBean, SettlementPolicyInfoEntity.class);
                    // 险种信息
                    bean.setProductCode(x.getProductCode());
                    bean.setProductName(x.getProductName());
                    bean.setPlanCode(x.getPlanCode());
                    bean.setPlanName(x.getPlanName());
                    bean.setMainInsurance(x.getMainInsurance());
                    bean.setAdditionalRisksType(x.getAdditionalRisksType());
                    bean.setEffectiveDate(policyInfo.getContractExtendInfo().getEnforceTime());
                    bean.setEndDate(
                        PolicySettlementUtils.getInsuredEndDate(bean.getEffectiveDate(), x.getInsuredPeriod(),
                            x.getInsuredPeriodType()));
                    // 团险续期年期 + 续期期数默认用1
                    bean.setRenewalYear(1);
                    bean.setRenewalPeriod(1);
                    bean.setCoverage(x.getCoverage());
                    bean.setCoverageUnit(x.getCoverageUnit());
                    bean.setCoverageUnitName(x.getCoverageUnitName());
                    bean.setInsuredPeriodType(x.getInsuredPeriodType());
                    bean.setInsuredPeriod(x.getInsuredPeriod());
                    bean.setPeriodType(x.getPeriodType());
                    bean.setPaymentPeriodType(x.getPaymentPeriodType());
                    bean.setPaymentPeriod(x.getPaymentPeriod());
                    bean.setDrawAge(x.getAnnDrawAge());
                    // 险种总保费为正常保费
                    bean.setPremium(x.getPremium().abs().negate());
                    bean.setProductPremiumTotal(bean.getPremium());
                    bean.setCopies(x.getCopies());
                    // 产品组信息
                    if (productMap.containsKey(x.getProductCode())) {
                        ProductBase productBase = productMap.get(x.getProductCode());
                        if (StrUtil.isBlank(bean.getProductName())){
                            bean.setProductName(productBase.getProductName());
                        }
                        bean.setProductGroup(productBase.getProductGroup());
                        bean.setProductType(productBase.getProductType());
                        bean.setLevel2Code(productBase.getLevel2Code());
                        bean.setLevel3Code(productBase.getLevel3Code());
                        // 折算保费运算
                        ReconcileBaseHelper.buildDiscountPremium(policyNo, x.getProductCode(), null,
                            policyProductTypeEnum, 0, bean.getPremium(), x.getPeriodType(),
                            x.getPaymentPeriodType(), x.getPaymentPeriod(), bean);
                    } else {
                        throw new GlobalException(
                            BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + x.getProductCode()));
                    }
                    // 设置结算费率表信息[优先标准费率 > 农保佣金]
                    boolean b = ReconcileBaseHelper.buildSettlementCommission(bean);
                    if (!b) {
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(bean.getReconcileExecuteDesc()));
                    }
                    resultList.add(bean);
                });
                break;
            }
            default: {
                log.warn("保单类型暂不处理.");
            }
        }

        return resultList;
    }

    /**
     * 处理犹豫期退保
     *
     * @param policyInfo
     * @param sourceBean
     * @param productMap
     * @return
     */
    private List<SettlementPolicyInfoEntity> buildHesitateSurrender(EpContractInfoVo policyInfo,
        SettlementPolicyInfoEntity sourceBean, Map<String, ProductBase> productMap) {
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        PolicyProductTypeEnum policyProductTypeEnum =
            PolicyProductTypeEnum.getProdTypeEnum(policyInfo.getPolicyProductType());
        List<SettlementPolicyInfoEntity> resultList = new ArrayList<>();
        switch (policyProductTypeEnum) {
            case PERSONAL:
            case PROPERTY: {
                // 需要操作的数据
                List<EpInsuredInfoVo> insuredInfoList = policyInfo.getInsuredInfoList();
                for (EpInsuredInfoVo insured : insuredInfoList) {
                    insured.getProductInfoList().forEach(x -> {
                        SettlementPolicyInfoEntity bean =
                            BeanUtil.copyProperties(sourceBean, SettlementPolicyInfoEntity.class);
                        bean.setHesitateSurrender(1);
                        // 被保人信息
                        bean.setInsuredName(insured.getInsuredName());
                        bean.setInsuredMobile(insured.getInsuredMobile());
                        bean.setInsuredIdCard(insured.getInsuredIdCard());
                        bean.setInsuredGender(insured.getInsuredGender());
                        bean.setInsuredBirthday(insured.getInsuredBirthday());
                        bean.setInsuredPolicyAge(insured.getInsuredPolicyAge());
                        // 险种信息
                        bean.setProductCode(x.getProductCode());
                        bean.setProductName(x.getProductName());
                        bean.setPlanCode(x.getPlanCode());
                        bean.setPlanName(x.getPlanName());
                        bean.setMainInsurance(x.getMainInsurance());
                        bean.setAdditionalRisksType(x.getAdditionalRisksType());
                        bean.setEffectiveDate(policyInfo.getContractExtendInfo().getEnforceTime());
                        bean.setEndDate(
                            PolicySettlementUtils.getInsuredEndDate(bean.getEffectiveDate(), x.getInsuredPeriod(),
                                x.getInsuredPeriodType()));
                        // 新契约用 续期年期 + 续期期数
                        bean.setCoverage(x.getCoverage());
                        bean.setCoverageUnit(x.getCoverageUnit());
                        bean.setCoverageUnitName(x.getCoverageUnitName());
                        bean.setInsuredPeriodType(x.getInsuredPeriodType());
                        bean.setInsuredPeriod(x.getInsuredPeriod());
                        bean.setPeriodType(x.getPeriodType());
                        bean.setPaymentPeriodType(x.getPaymentPeriodType());
                        bean.setPaymentPeriod(x.getPaymentPeriod());
                        bean.setDrawAge(x.getAnnDrawAge());
                        // 新契约个险，险种总保费为正常保费
                        bean.setPremium(x.getPremium().abs().negate());
                        bean.setProductPremiumTotal(bean.getPremium());
                        bean.setCopies(x.getCopies());
                        if (productMap.containsKey(x.getProductCode())) {
                            ProductBase productBase = productMap.get(x.getProductCode());
                            if (StrUtil.isBlank(bean.getProductName())){
                                bean.setProductName(productBase.getProductName());
                            }
                            bean.setProductGroup(productBase.getProductGroup());
                            bean.setProductType(productBase.getProductType());
                            bean.setLevel2Code(productBase.getLevel2Code());
                            bean.setLevel3Code(productBase.getLevel3Code());
                            // 折算保费运算
                            ReconcileBaseHelper.buildDiscountPremium(policyNo, x.getProductCode(),
                                insured.getInsuredPolicyAge(), policyProductTypeEnum,
                                productMap.get(x.getProductCode()).getLongShortFlag(), bean.getPremium(),
                                x.getPeriodType(), x.getPaymentPeriodType(), x.getPaymentPeriod(), bean);
                        } else {
                            throw new GlobalException(
                                BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + x.getProductCode()));
                        }
                        // 设置结算费率表信息[优先标准费率 > 农保佣金]
                        boolean b = ReconcileBaseHelper.buildSettlementCommission(bean);
                        if (!b) {
                            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(bean.getReconcileExecuteDesc()));
                        }
                        resultList.add(bean);
                    });
                }
                break;
            }
            case VEHICLE:
            case GROUP: {
                log.info("接受保单中心结算事件(车团)");
                policyInfo.getProductInfoList().forEach(x -> {
                    SettlementPolicyInfoEntity bean =
                        BeanUtil.copyProperties(sourceBean, SettlementPolicyInfoEntity.class);
                    bean.setHesitateSurrender(1);
                    // 险种信息
                    bean.setProductCode(x.getProductCode());
                    bean.setProductName(x.getProductName());
                    bean.setPlanCode(x.getPlanCode());
                    bean.setPlanName(x.getPlanName());
                    bean.setMainInsurance(x.getMainInsurance());
                    bean.setAdditionalRisksType(x.getAdditionalRisksType());
                    bean.setEffectiveDate(policyInfo.getContractExtendInfo().getEnforceTime());
                    bean.setEndDate(
                        PolicySettlementUtils.getInsuredEndDate(bean.getEffectiveDate(), x.getInsuredPeriod(),
                            x.getInsuredPeriodType()));
                    // 团险续期年期 + 续期期数默认用1
                    bean.setRenewalYear(1);
                    bean.setRenewalPeriod(1);
                    bean.setCoverage(x.getCoverage());
                    bean.setCoverageUnit(x.getCoverageUnit());
                    bean.setCoverageUnitName(x.getCoverageUnitName());
                    bean.setInsuredPeriodType(x.getInsuredPeriodType());
                    bean.setInsuredPeriod(x.getInsuredPeriod());
                    bean.setPeriodType(x.getPeriodType());
                    bean.setPaymentPeriodType(x.getPaymentPeriodType());
                    bean.setPaymentPeriod(x.getPaymentPeriod());
                    bean.setDrawAge(x.getAnnDrawAge());
                    // 险种总保费为正常保费
                    bean.setPremium(x.getPremium().abs().negate());
                    bean.setProductPremiumTotal(bean.getPremium());
                    bean.setCopies(x.getCopies());
                    // 产品组信息
                    if (productMap.containsKey(x.getProductCode())) {
                        ProductBase productBase = productMap.get(x.getProductCode());
                        if (StrUtil.isBlank(bean.getProductName())){
                            bean.setProductName(productBase.getProductName());
                        }
                        bean.setProductGroup(productBase.getProductGroup());
                        bean.setProductType(productBase.getProductType());
                        bean.setLevel2Code(productBase.getLevel2Code());
                        bean.setLevel3Code(productBase.getLevel3Code());
                        // 折算保费运算
                        ReconcileBaseHelper.buildDiscountPremium(policyNo, x.getProductCode(), null,
                            policyProductTypeEnum, 0, bean.getPremium(), x.getPeriodType(),
                            x.getPaymentPeriodType(), x.getPaymentPeriod(), bean);
                    } else {
                        throw new GlobalException(
                            BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + x.getProductCode()));
                    }
                    // 设置结算费率表信息[优先标准费率 > 农保佣金]
                    boolean b = ReconcileBaseHelper.buildSettlementCommission(bean);
                    if (!b) {
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(bean.getReconcileExecuteDesc()));
                    }
                    resultList.add(bean);
                });
                break;
            }
            default: {
                log.warn("保单类型暂不处理.");
                return resultList;
            }
        }
        // 犹豫期退保的保单，需要对新契约的回执、回访进行设置为0
        this.lambdaUpdate().set(SettlementPolicyInfoEntity::getRevisitStatus, 0)
            .set(SettlementPolicyInfoEntity::getReceiptStatus, 0).eq(SettlementPolicyInfoEntity::getPolicyNo, policyNo)
            .update();
        return resultList;
    }

    /**
     * 处理退保
     *
     * @param policyInfo
     * @param bean
     */
    private List<SettlementPolicyInfoEntity> buildStandardSurrender(EpContractInfoVo policyInfo,
        SettlementPolicyInfoEntity bean, Map<String, ProductBase> productMap) {
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        PolicyProductTypeEnum policyProductTypeEnum =
            PolicyProductTypeEnum.getProdTypeEnum(policyInfo.getPolicyProductType());
        List<SettlementPolicyInfoEntity> resultList = new ArrayList<>();
        switch (policyProductTypeEnum) {
            case PERSONAL:
            case PROPERTY: {
                log.info("接受保单中心结算事件(个财)");
                // 主被保人 + 主险种
                EpInsuredInfoVo epInsuredInfoVo = policyInfo.getInsuredInfoList().get(0);
                EpPersonalProductInfoVo x = policyInfo.getInsuredInfoList().get(0).getProductInfoList().get(0);
                // 险种信息
                ProductBase productBase = productMap.get(x.getProductCode());
                if (productBase == null) {
                    throw new GlobalException(
                        BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + productBase.getProductCode()));
                }
                bean.setProductCode(x.getProductCode());
                bean.setProductName(x.getProductName());
                bean.setPlanCode(x.getPlanCode());
                bean.setPlanName(x.getPlanName());
                if (StrUtil.isBlank(bean.getProductName())){
                    bean.setProductName(productBase.getProductName());
                }
                bean.setProductGroup(productBase.getProductGroup());
                bean.setProductType(productBase.getProductType());
                bean.setLevel2Code(productBase.getLevel2Code());
                bean.setLevel3Code(productBase.getLevel3Code());
                // 折算保费运算
                ReconcileBaseHelper.buildDiscountPremium(policyNo, x.getProductCode(),
                    epInsuredInfoVo.getInsuredPolicyAge(), policyProductTypeEnum, productBase.getLongShortFlag(),
                    bean.getPremium(), x.getPeriodType(), x.getPaymentPeriodType(), x.getPaymentPeriod(), bean);
                bean.setProductType(productBase.getProductType());
                bean.setMainInsurance(x.getMainInsurance());
                bean.setAdditionalRisksType(x.getAdditionalRisksType());
                bean.setEffectiveDate(policyInfo.getContractExtendInfo().getEnforceTime());
                bean.setEndDate(PolicySettlementUtils.getInsuredEndDate(bean.getEffectiveDate(), x.getInsuredPeriod(),
                    x.getInsuredPeriodType()));
                bean.setCoverage(x.getCoverage());
                bean.setCoverageUnit(x.getCoverageUnit());
                bean.setCoverageUnitName(x.getCoverageUnitName());
                bean.setInsuredPeriodType(x.getInsuredPeriodType());
                bean.setInsuredPeriod(x.getInsuredPeriod());
                bean.setPeriodType(x.getPeriodType());
                bean.setPaymentPeriodType(x.getPaymentPeriodType());
                bean.setPaymentPeriod(x.getPaymentPeriod());
                bean.setDrawAge(x.getAnnDrawAge());
                // 险种总保费为正常保费
                bean.setProductPremiumTotal(x.getPremium());
                bean.setCopies(x.getCopies());
                break;
            }
            case VEHICLE:
            case GROUP: {
                log.info("接受保单中心结算事件(车团)");
                EpProductInfoVo x = policyInfo.getProductInfoList().get(0);
                // 险种信息
                bean.setProductCode(x.getProductCode());
                bean.setProductName(x.getProductName());
                bean.setPlanCode(x.getPlanCode());
                bean.setPlanName(x.getPlanName());
                ProductBase productBase = productBaseService.getProductInfo(x.getProductCode());
                // 产品组信息
                if (StrUtil.isBlank(bean.getProductName())){
                    bean.setProductName(productBase.getProductName());
                }
                bean.setProductGroup(productBase.getProductGroup());
                bean.setLevel2Code(productBase.getLevel2Code());
                bean.setLevel3Code(productBase.getLevel3Code());
                // 折算保费运算
                ReconcileBaseHelper.buildDiscountPremium(policyNo, x.getProductCode(), null,
                    PolicyProductTypeEnum.GROUP, 0, bean.getPremium(), x.getPeriodType(), x.getPaymentPeriodType(),
                    x.getPaymentPeriod(), bean);
                bean.setProductType(productBase.getProductType());
                bean.setMainInsurance(x.getMainInsurance());
                bean.setAdditionalRisksType(x.getAdditionalRisksType());
                bean.setEffectiveDate(policyInfo.getContractExtendInfo().getEnforceTime());
                bean.setEndDate(PolicySettlementUtils.getInsuredEndDate(bean.getEffectiveDate(), x.getInsuredPeriod(),
                    x.getInsuredPeriodType()));
                bean.setCoverage(x.getCoverage());
                bean.setCoverageUnit(x.getCoverageUnit());
                bean.setCoverageUnitName(x.getCoverageUnitName());
                bean.setInsuredPeriodType(x.getInsuredPeriodType());
                bean.setInsuredPeriod(x.getInsuredPeriod());
                bean.setPeriodType(x.getPeriodType());
                bean.setPaymentPeriodType(x.getPaymentPeriodType());
                bean.setPaymentPeriod(x.getPaymentPeriod());
                bean.setDrawAge(x.getAnnDrawAge());
                // 险种总保费为正常保费
                bean.setProductPremiumTotal(x.getPremium());
                bean.setCopies(x.getCopies());
                break;
            }
            default: {
                log.warn("保单类型暂不处理.");
                return resultList;
            }
        }
        // 设置结算费率表信息[优先标准费率 > 农保佣金]
        boolean b = ReconcileBaseHelper.buildSettlementCommission(bean);
        if (!b) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(bean.getReconcileExecuteDesc()));
        }
        resultList.add(bean);
        return resultList;
    }


    private SettlementPolicyInfoEntity createSettlementPolicyInfoBasis(EpContractInfoVo policyInfo,
        EpPreservationV2Vo preservationDetail, Integer reconcileType) {
        SettlementPolicyInfoEntity bean = new SettlementPolicyInfoEntity();
        bean.setReconcileType(reconcileType);
        // 判断是否为保全
        if (preservationDetail != null) {
            //bean.setPreservationCode(preservationCode);
            bean.setEndorsementNo(preservationDetail.getEndorsementNo());
            bean.setPreservationType(preservationDetail.getPreservationType());
            bean.setPreservationProject(preservationDetail.getPreservationProject());
            bean.setPreservationWhy(preservationDetail.getPreservationWhy());
            // 设置保全生效时间 + 退保时间
            bean.setPreservationEffectTime(preservationDetail.getPreservationEffectTime());
            bean.setPreservationPeriod(preservationDetail.getRenewalTermPeriod());
            // 判断保全类型是不是退保 如果是退保的话 保全生效时间=退保时间
            if (preservationDetail.getPreservationProject().equals(PreservationProjectEnum.SURRENDER.getCode())) {
                bean.setSurrenderTime(preservationDetail.getPreservationEffectTime());
                // 退保是负数
                preservationDetail.setSurrenderCash(preservationDetail.getSurrenderCash().abs().negate());
            }
            bean.setPremium(preservationDetail.getSurrenderCash());
        }
        Date date = new Date();
        String settlementCode = PolicySettlementUtils.createCodeLastNumber("SC");
        // 1 基本信息
        bean.setCreateUser("settlement_event");
        bean.setPolicyProductType(policyInfo.getPolicyProductType());
        bean.setPolicySource(policyInfo.getPolicySource());
        bean.setSelfPreservation(policyInfo.getContractBaseInfo().getSelfPreservation());
        bean.setCustomerCode(policyInfo.getChannelInfo().getMiniAppCustomerCode());
        // 3 保单基本信息
        EpContractBaseInfoVo contractBaseInfo = policyInfo.getContractBaseInfo();
        EpContractExtendInfoVo contractExtendInfo = policyInfo.getContractExtendInfo();
        bean.setContractCode(policyInfo.getContractCode());
        BeanUtils.copyProperties(contractBaseInfo, bean);
        BeanUtils.copyProperties(contractExtendInfo, bean);
        // 4 回执/回访日志
        bean.setReceiptStatus(contractExtendInfo.getIsNeedReceipt());
        bean.setReceiptTime(contractExtendInfo.getReceiptSignTime());
        bean.setRevisitStatus(contractExtendInfo.getIsNeedRevisit());
        bean.setRevisitTime(contractExtendInfo.getRevisitTime());
        // 5 投保人信息
        bean.setApplicantName(policyInfo.getApplicantInfo().getApplicantName());
        bean.setApplicantMobile(policyInfo.getApplicantInfo().getApplicantMobile());
        bean.setApplicantIdCard(policyInfo.getApplicantInfo().getApplicantIdCard());
        bean.setApplicantGender(policyInfo.getApplicantInfo().getApplicantGender());
        bean.setApplicantBirthday(policyInfo.getApplicantInfo().getApplicantBirthday());
        bean.setApplicantAge(policyInfo.getApplicantInfo().getApplicantAge());
        // 6 主代理人信息
        policyInfo.getAgentInfoList().stream().filter(x -> x.getMainFlag() == 1).findFirst().ifPresent(p -> {
            bean.setMainAgentCode(p.getAgentCode());
            bean.setOrgCode(p.getOrgCode());
        });
        if (StringUtils.isBlank(bean.getMainAgentCode())) {
            bean.setMainAgentCode(policyInfo.getContractBaseInfo().getAgentCode());
            bean.setOrgCode(policyInfo.getContractBaseInfo().getOrgCode());
        }
        // 7 渠道推荐人类型
        EpPolicyChannelInfoVo channelInfo = policyInfo.getChannelInfo();
        BeanUtils.copyProperties(channelInfo, bean);
        // 8 业务类型 zhnx渠道就为农保渠道
        if (StringUtils.equalsIgnoreCase(Constant.DEFAULT_CS_CHANNEL, policyInfo.getChannelInfo().getChannelCode())) {
            bean.setBusinessType(2);
        } else {
            bean.setBusinessType(1);
        }
        // 9 获取推荐人人姓名、渠道名称
        if (StringUtils.isNotBlank(bean.getReferrerCode())) {
            if (bean.getReferrerType() != null && bean.getReferrerType() == 1) {
                // 如果为代理人形态，获取代理人姓名
                if (StringUtils.isNotBlank(bean.getReferrerCode())) {
                    Optional.ofNullable(agentBaseService.getAgentInfoByAgentCode(bean.getReferrerCode(), false))
                        .ifPresent(x -> {
                            bean.setReferrerName(x.getAgentName());
                            bean.setReferrerWno(x.getBusinessCode());
                            bean.setChannelBranchCode(x.getOrgCode());
                            bean.setChannelBranchName(x.getOrgName());
                        });
                }
            } else {
                // 如果为渠道推荐人获取推到人信息
                if (StringUtils.isNotBlank(bean.getReferrerCode())) {
                    Optional.ofNullable(openApiBaseService.getReferrerInfo(bean.getReferrerCode(), false))
                        .ifPresent(x -> {
                            bean.setReferrerName(x.getReferrerName());
                            bean.setReferrerWno(x.getReferrerWno());
                            bean.setReferrerOgrCode(x.getReferrerOgrCode());
                            bean.setReferrerOgrName(x.getReferrerOgrName());
                        });
                }
            }
        }
        // 10 渠道信息
        if (StringUtils.isNotBlank(bean.getChannelCode())) {
            if (StringUtils.equals("gdyw", bean.getChannelCode())) {
                bean.setChannelName("代理人业务");
            } else {
                Optional.ofNullable(openApiBaseService.getChannelInfo(bean.getChannelCode(), false)).ifPresent(x -> {
                    bean.setChannelName(x.getChannelName());
                });
            }
        }
        if (StringUtils.isBlank(bean.getChannelName())) {
            bean.setChannelName("未知渠道");
        }
        // 结算保单详情 结算保单详情编号 + 记账日期 + 记账时间 + 签约类型
        bean.setSettlementCode(settlementCode);
        bean.setSettlementDate(date);
        bean.setSettlementTime(date);
        // 生成方式 + 设置为未对账状态
        bean.setSettlementGenerateType(1);
        bean.setReconcileStatus(ReconcileStatusEnum.TO_BE_RECONCILE.getStatusCode());
        return bean;
    }

    /**
     * 结算明细冲正
     * @param settlementPolicyInfoList 需要冲正的明细数据
     */
    @Override
    public void rectification(List<SettlementPolicyInfoEntity> settlementPolicyInfoList) {
        List<SettlementPolicyInfoEntity> rectificationList = new ArrayList<>(settlementPolicyInfoList);
        // 1.将现有数据设置为冲正数据,并设置无需对账
        rectificationList.forEach(action -> {
            action.setRectificationMark(StatusEnum.NORMAL.getCode());
            action.setReconcileExecuteStatus(2);
            action.setSettlementAmount(action.getSettlementAmount()==null? BigDecimal.ONE:action.getSettlementAmount());
            action.setReconcileExecuteDesc("冲正数据无需对账");

        });
        //将本身数据变成冲正数据状态
        this.updateBatchById(rectificationList);
        // 封装真正的冲正的数据  将保费/手续费 取反
        List<SettlementPolicyInfoEntity> saveRectificationList = settlementPolicyInfoList.stream().map(m -> {
            SettlementPolicyInfoEntity settlementPolicyInfo =
                BeanUtil.copyProperties(m, SettlementPolicyInfoEntity.class);
            String settlementCode = PolicySettlementUtils.createCodeLastNumber("SC");
            settlementPolicyInfo.setId(null);
            settlementPolicyInfo.setSettlementCode(settlementCode);
            settlementPolicyInfo.setPremium(m.getPremium().negate());
            settlementPolicyInfo.setProductPremiumTotal(m.getProductPremiumTotal().negate());
            settlementPolicyInfo.setSettlementAmount(m.getSettlementAmount().negate());
            settlementPolicyInfo.setRectificationMark(StatusEnum.NORMAL.getCode());
            settlementPolicyInfo.setSettlementGenerateType(SettlementGenerateTypeEnum.AUTO_CORRECTION.getCode());
            return settlementPolicyInfo;
        }).collect(Collectors.toList());
        // 插入冲正数据
        this.saveList(saveRectificationList);
    }

    @Override
    public List<BatchIsCompletedReconcileRecord> batchIsCompletedReconcileRecord(List<String> policyNoList) {
        if (CollUtil.isEmpty(policyNoList)) {
            return Collections.emptyList();
        }
        List<String> dbList = lambdaQuery()
                .in(SettlementPolicyInfoEntity::getPolicyNo, policyNoList)
                .eq(SettlementPolicyInfoEntity::getReconcileStatus, ReconcileStatusEnum.RECONCILE_FINISH.getStatusCode())
                .list().stream().map(SettlementPolicyInfoEntity::getPolicyNo).collect(Collectors.toList());
        return policyNoList.stream().map(policyNo->{
            BatchIsCompletedReconcileRecord batchIsCompletedReconcileRecord = new BatchIsCompletedReconcileRecord();
            batchIsCompletedReconcileRecord.setPolicyNo(policyNo);
            batchIsCompletedReconcileRecord.setIsCompleted(dbList.contains(policyNo));
            return batchIsCompletedReconcileRecord;
        }).collect(Collectors.toList());
    }

    /**
     * 批量查询保全是否存在完成对账的记录
     * @param preservationCodeList 保全编码集合
     * @return 结果集
     */
    @Override
    public List<BatchIsCompletedReconcileRecordPreservation> batchIsCompletedReconcileRecordPreservation(List<String> preservationCodeList) {
        if (CollUtil.isEmpty(preservationCodeList)) {
            return Collections.emptyList();
        }
        // 根据保全编码分组
        List<String> dbList = lambdaQuery()
                .in(SettlementPolicyInfoEntity::getPreservationCode, preservationCodeList)
                .eq(SettlementPolicyInfoEntity::getReconcileStatus, ReconcileStatusEnum.RECONCILE_FINISH.getStatusCode())
                .list().stream().map(SettlementPolicyInfoEntity::getPreservationCode)
                .collect(Collectors.toList());
        return preservationCodeList.stream().map(preservationCode->{
            BatchIsCompletedReconcileRecordPreservation batchIsCompletedReconcileRecord = new BatchIsCompletedReconcileRecordPreservation();
            batchIsCompletedReconcileRecord.setPreservationCode(preservationCode);
            batchIsCompletedReconcileRecord.setIsCompleted(dbList.contains(preservationCode));
            batchIsCompletedReconcileRecord.setPolicyNo(batchIsCompletedReconcileRecord.getPolicyNo());
            return batchIsCompletedReconcileRecord;
        }).collect(Collectors.toList());
    }
}
