package com.mpolicy.settlement.core.modules.reconcile.service;

import com.mpolicy.settlement.core.common.reconcile.diff.DiffBacklogInput;
import com.mpolicy.settlement.core.common.reconcile.diff.ReconcileAmountAccuracyInput;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileTemplateEnum;
import com.mpolicy.settlement.core.modules.reconcile.dto.rule.ReconcileCompanyInfo;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileInfoEntity;
import com.mpolicy.settlement.core.common.reconcile.CreateReconcileVo;

import java.util.List;

/**
 * 保司结算对账单服务接口
 *
 * <AUTHOR>
 * @since 2023-05-22 22:28:39
 */
public interface SettlementReconcileService {

    /**
     * 生成保司结算对账单
     *
     * @param reconcileCompanyInfo 保司对账单配置规则信息
     * @return java.lang.String
     * <AUTHOR>
     * @since 2023/5/22 11:03
     */
    String createReconcile(ReconcileCompanyInfo reconcileCompanyInfo);

    /**
     * 强制生成对账单
     *
     * @param vo 生成规则
     * @return java.lang.String
     * <AUTHOR>
     * @since 2023/9/25 18:47
     */
    List<String> forceCreateReconcile(CreateReconcileVo vo);

    /**
     * 上传对账单文件
     *
     * @param reconcileCode     对账单唯一编号
     * @param reconcileTemplate 对账单模版类型
     * @param fileCode          文件编码
     * @param userName          操作人
     * @return com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileInfoEntity
     * <AUTHOR>
     * @since 2023/5/22 20:43
     */
    SettlementReconcileInfoEntity uploadCompanyReconcileFile(String reconcileCode, ReconcileTemplateEnum reconcileTemplate, String fileCode, String userName);

    /**
     * 重新上传对账单文件
     *
     * @param reconcileCode     对账单唯一编号
     * @param reconcileTemplate 对账单模版类型
     * @param sourceFileCode    原文件编码
     * @param fileCode          文件编码
     * @param userName          操作人
     * @return com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileInfoEntity
     * <AUTHOR>
     * @since 2023/5/22 21:11
     */
    SettlementReconcileInfoEntity retryUploadCompanyReconcileFile(String reconcileCode, ReconcileTemplateEnum reconcileTemplate, String sourceFileCode, String fileCode, String userName);

    /**
     * 删除对账单规则文件
     *
     * @param reconcileCode     对账单唯一编号
     * @param reconcileFileCode 文件编码
     * @param userName          操作人
     * @return com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileInfoEntity
     * <AUTHOR>
     * @since 2023/5/22 21:13
     */
    SettlementReconcileInfoEntity removeReconcileFile(String reconcileCode, String reconcileFileCode, String userName);

    /**
     * 开始对账
     *
     * @param reconcileCode 对账单唯一编号
     * @param userName      操作人
     * <AUTHOR>
     * @since 2023/5/22 20:11
     */
    void startReconcile(String reconcileCode, String userName,String uuid);

    /**
     * 批量处理精度
     *
     * @param reconcileAmountAccuracyInput 批量处理对账单精度信息
     * <AUTHOR>
     * @since 2023/5/25 16:22
     */
    void reconcileAmountAccuracy(ReconcileAmountAccuracyInput reconcileAmountAccuracyInput,String uuid);

    /**
     * 完成对账
     *
     * @param reconcileCode 对账单唯一编号
     * @param userName      操作人
     * <AUTHOR>
     * @since 2023/5/23 09:21
     */
    void finishReconcile(String reconcileCode, String userName,String uuid);

    /**
     * 差异申请
     *
     * @param diffBacklogInput 差异处理请求对象
     * <AUTHOR>
     * @since 2023/5/24 22:04
     */
    void diffBacklog(DiffBacklogInput diffBacklogInput);

    /**
     * 完成对账-补偿
     *
     * @param reconcileCode 对账单唯一编号
     * @param userName      操作人
     * <AUTHOR>
     * @since 2023/5/23 09:21
     */
    void finishReconcileHelp(String reconcileCode, String userName);

    /**
     * 处理对账单监管报送
     * @param reconcileCode 对账单编码
     * @param uuid 唯一标识
     */
    void reconcileRegulatorySubmit(String reconcileCode,String  uuid);
    void handleSettlementMonthReport(String reconcileCode,String  uuid);
}

