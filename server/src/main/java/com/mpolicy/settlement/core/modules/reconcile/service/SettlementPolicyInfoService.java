package com.mpolicy.settlement.core.modules.reconcile.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.policy.common.ep.policy.preserve.EpPreservationV2Vo;
import com.mpolicy.settlement.core.common.reconcile.BatchIsCompletedReconcileRecord;
import com.mpolicy.settlement.core.common.reconcile.BatchIsCompletedReconcileRecordPreservation;
import com.mpolicy.settlement.core.common.reconcile.RefreshSettlementPolicyInfoCommissionVo;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

/**
 * 结算保单明细记录表
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:39
 */
public interface SettlementPolicyInfoService extends IService<SettlementPolicyInfoEntity> {

    /**
     * 根据规则明细获取保单详细数据
     *
     * @param reconcileInfo 对账单信息
     * @return java.util.List<com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity>
     * <AUTHOR>
     * @since 2023/5/24 10:35
     */
    List<SettlementPolicyInfoEntity> querySettlementPolicyInfoList(SettlementReconcileInfoEntity reconcileInfo);

    /**
     * 设置对账完成
     * settlement_reconcile_policy 里面为reconcileGenerateType = 小鲸线上对账单的数据 关联settlement_policy_info为已对账完成
     *
     * @param reconcileCode 保司对账单号
     * @param userName      操作员
     * @param settlementMonth      结算月份
     * <AUTHOR>
     * @since 2023/5/24 09:11
     */
    void settlementPolicyFinish(String reconcileCode, String userName,String settlementMonth);

    /**
     * 批量保存数据
     *
     * @param listData 数据集合
     * @return void
     * <AUTHOR>
     * @since 2023/8/31
     */
    void saveList(List<SettlementPolicyInfoEntity> listData);

    /**
     * 手动刷新结算明细费费率信息和关联的产品名称
     *
     * @param params
     */
    void refreshSettlementPolicyInfoCommission(RefreshSettlementPolicyInfoCommissionVo params);
    /**
     * 批量刷新结算单费率信息
     * @param reconcileCode 对账单编码
     * @param reconcileCode uuid 查询唯一标识
     */
    @Async
    void batchRefreshRate(String reconcileCode,String uuid);

    /**
     * 处理保全编码变更
     * @param settlementPolicyInfoList 需要变更的明细数据
     * @param pushEventCode 事件源编码
     * @param newEndorsementNo 新批单号
     */
    void handleEndorsementNoChange(List<SettlementPolicyInfoEntity> settlementPolicyInfoList,String pushEventCode,
        String newEndorsementNo);

    /**
     * 生成结算明细
     * @param policyInfo 保单信息
     * @param reconcileType 事件类型
     * @return
     */
    List<SettlementPolicyInfoEntity> createSettlementPolicyInfo(SettlementEventTypeEnum settlementEventTypeEnum ,
        EpContractInfoVo policyInfo,
        EpPreservationV2Vo preservationDetail,
        Integer reconcileType);

    /**
     * 处理保单号变更
     * @param settlementPolicyInfoList 需要变更的明细数据
     * @param pushEventCode 事件源编码
     * @param policyNo 新保单号
     */
    void handlePolicyNoChange(List<SettlementPolicyInfoEntity> settlementPolicyInfoList, String pushEventCode, String policyNo);
    /**
     * 结算明细冲正
     * @param settlementPolicyInfoList 需要冲正的明细数据
     */
     void rectification(List<SettlementPolicyInfoEntity> settlementPolicyInfoList);

    /**
     * 批量查询是否已经完成对账
     * @param policyNoList 保单集合
     */
    List<BatchIsCompletedReconcileRecord> batchIsCompletedReconcileRecord(List<String> policyNoList);

    /**
     * 批量查询保全是否存在完成对账的记录
     * @param preservationCodeList 保全编码集合
     * @return 结果集
     */
    List<BatchIsCompletedReconcileRecordPreservation> batchIsCompletedReconcileRecordPreservation(List<String> preservationCodeList);
}

