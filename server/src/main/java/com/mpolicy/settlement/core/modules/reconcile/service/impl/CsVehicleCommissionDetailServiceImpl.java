package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import com.mpolicy.settlement.core.modules.reconcile.dao.CsVehicleCommissionDetailDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.CsVehicleCommissionDetailEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.CsVehicleCommissionDetailService;

import java.util.Collections;
import java.util.List;

/**
 * 长沙农保车险佣金明细表
 *
 * <AUTHOR>
 * @date 2023-06-02 10:31:20
 */
@Slf4j
@Service("csVehicleCommissionDetailService")
public class CsVehicleCommissionDetailServiceImpl extends ServiceImpl<CsVehicleCommissionDetailDao, CsVehicleCommissionDetailEntity> implements CsVehicleCommissionDetailService {
    /**
     *
     * @param thPolicyNo
     * @param thEndorsementNo
     * @return
     */
    public List<CsVehicleCommissionDetailEntity> listByPolicyNoAndEndorsementNo(String thPolicyNo,String thEndorsementNo){
        if(StringUtils.isBlank(thPolicyNo) && StringUtil.isBlank(thEndorsementNo)){
            return Collections.emptyList();
        }
        return lambdaQuery().eq(CsVehicleCommissionDetailEntity::getThPolicyNo,thPolicyNo)
                            .eq(!StringUtils.isBlank(thEndorsementNo),CsVehicleCommissionDetailEntity::getThEndorsementNo,thEndorsementNo)
                            .list();
    }


}
