package com.mpolicy.settlement.core.modules.reconcile.service;

import com.mpolicy.settlement.core.common.reconcile.RectificationOneVo;
import com.mpolicy.settlement.core.modules.reconcile.vo.RectificationVo;
import com.mpolicy.settlement.core.modules.reconcile.vo.RefreshPolicyPremiumVo;

import java.util.List;

/**
 * 业财一体化帮助服务接口
 *
 * <AUTHOR>
 * @since 2023/9/14
 */
public interface ReconcileHelpService {

    /**
     * 刷新线下单代理人+机构信息
     *
     * <AUTHOR>
     * @since 2023/9/14
     */
    void refreshPolicyInfo(int startPage, int testStatus);

    /**
     * 刷新线下单代理人+机构信息
     *
     * <AUTHOR>
     * @since 2023/9/14
     */
    void refreshPolicyReconcileSuccess(int startPage, int testStatus);

    /**
     * 刷新线下单代理人+机构信息
     *
     * <AUTHOR>
     * @since 2023/9/14
     */
    void refreshOfflinePolicyOrgInfo();

    /**
     * 根据保单号集合刷新保单信息
     *
     * @param policyCodes 保单号集合
     * <AUTHOR>
     * @since 2023/9/24 15:38
     */
    void refreshPolicyInfoByCodeList(List<String> policyCodes);

    /**
     * 刷新保费信息
     * @param policyList
     */
    void refreshPolicyPremium(List<RefreshPolicyPremiumVo> policyList);

    /**
     * 冲正数据
     * @param policyList
     */
    void rectification(List<RectificationVo> policyList);

    /**
     * 单一数据冲正处理
     * @param vo
     */
    void rectificationOne(RectificationOneVo vo);
}

