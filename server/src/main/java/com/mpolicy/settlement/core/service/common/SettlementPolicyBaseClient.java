package com.mpolicy.settlement.core.service.common;

import com.alibaba.fastjson.JSON;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.Result;
import com.mpolicy.policy.client.SettlementPolicyClient;
import com.mpolicy.policy.common.settlement.base.SettlementPolicyInfo;
import com.mpolicy.policy.common.settlement.request.SettlementPolicyRequest;
import com.mpolicy.policy.common.settlement.response.SettlementPolicyCheckRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 结算保单服务支持
 *
 * <AUTHOR>
 * @since 2023/11/30 09:54
 */
@Component
@Slf4j
public class SettlementPolicyBaseClient {

    @Autowired
    private SettlementPolicyClient settlementPolicyClient;


    /**
     * 根据保单号获取结算保单信息
     *
     * @param policyCode 保单号
     * @param throwEx    不存在是否抛出异常
     * @return 结算保单信息
     * <AUTHOR>
     * @since 2023/11/30 09:56
     */
    public SettlementPolicyInfo getSettlementPolicyByPolicyCode(String policyCode, boolean throwEx) {
        Result<SettlementPolicyInfo> result = settlementPolicyClient.querySettlementPolicyInfo(policyCode);
        if (!result.isSuccess()) {
            log.warn("结算保单服务获取保单详情失败，保单号={} msg={}", policyCode, JSON.toJSONString(result));
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        // 保单不存在
        if (result.getData() == null) {
            return null;
        }
        return result.getData();
    }

    /**
     * 获取结算保单信息集合
     *
     * @param policyCodeList 保单号集合
     * @param throwEx        不存在是否抛出异常
     * @return 结算保单信息
     * <AUTHOR>
     * @since 2023/11/30 09:58
     */
    public List<SettlementPolicyInfo> listSettlementPolicy(List<String> policyCodeList, boolean throwEx) {
        SettlementPolicyRequest request = new SettlementPolicyRequest();
        request.setPolicyCodeList(policyCodeList);
        Result<List<SettlementPolicyInfo>> result = settlementPolicyClient.querySettlementPolicyList(request);
        if (!result.isSuccess()) {
            log.warn("结算保单服务获取保单详情集合失败，保单号={} msg={}", policyCodeList, JSON.toJSONString(result));
            if (throwEx) {
                throw new GlobalException(result);
            }
            return Collections.emptyList();
        }
        // 保单不存在
        if (result.getData() == null) {
            return Collections.emptyList();
        }
        return result.getData();
    }

    /**
     * 根据保单号进行存在校验结果
     *
     * @param policyCodeList 保单号集合
     * @param throwEx        不存在是否抛出异常
     * @return 结算保单信息
     * <AUTHOR>
     * @since 2023/12/21
     */
    public SettlementPolicyCheckRes checkSettlementPolicy(List<String> policyCodeList, boolean throwEx) {
        SettlementPolicyRequest request = new SettlementPolicyRequest();
        request.setPolicyCodeList(policyCodeList);
        Result<SettlementPolicyCheckRes> result = settlementPolicyClient.checkSettlementPolicyList(request);
        if (!result.isSuccess()) {
            log.warn("根据保单号进行存在校验结果失败，保单号={} msg={}", policyCodeList, JSON.toJSONString(result));
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        // 保单不存在
        if (result.getData() == null) {
            return null;
        }
        return result.getData();
    }

    /**
     * 获取结算保单信息集合 map
     * 温馨提示：key=保单号 value=结算保单信息
     *
     * @param policyCodeList 保单号集合
     * @param throwEx        不存在是否抛出异常
     * @return 结算保单信息
     * <AUTHOR>
     * @since 2023/11/30 09:58
     */
    public Map<String, SettlementPolicyInfo> mapSettlementPolicy(List<String> policyCodeList, boolean throwEx) {
        SettlementPolicyRequest request = new SettlementPolicyRequest();
        request.setPolicyCodeList(policyCodeList);
        Result<List<SettlementPolicyInfo>> result = settlementPolicyClient.querySettlementPolicyList(request);
        if (!result.isSuccess()) {
            log.warn("结算保单服务获取保单详情集合失败，保单号={} msg={}", policyCodeList, JSON.toJSONString(result));
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        // 不存在
        if (result.getData() == null) {
            return null;
        }
        return result.getData().stream().collect(Collectors.toMap(SettlementPolicyInfo::getPolicyNo, Function.identity(), (k1, k2) -> k1));
    }
}

