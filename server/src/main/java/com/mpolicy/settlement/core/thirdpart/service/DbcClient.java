package com.mpolicy.settlement.core.thirdpart.service;

import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.thirdpart.client.DbcFeign;
import com.mpolicy.settlement.core.thirdpart.request.DbcBusinessIncentiveCalculateDataAddDTO;
import com.mpolicy.settlement.core.thirdpart.request.WhaleAddCommissionPushQuery;
import com.mpolicy.settlement.core.thirdpart.response.AddCommissionSettlementPushDto;
import com.mpolicy.settlement.core.thirdpart.response.ChongHoResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class DbcClient {
    @Autowired
    private DbcFeign dbcFeign;


    public Boolean saveBatch(List<DbcBusinessIncentiveCalculateDataAddDTO> addRecordList){
        ChongHoResult<Boolean> result = null;
        try {
            log.info("同步的记录数{}",addRecordList.size());
            result = dbcFeign.saveBatch(addRecordList);
        } catch (Exception e) {
            log.warn("调用分销系统批量保存督导或主任绩效异常,请开发人员及时排查问题", e);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("调用分销系统批量保存督导或主任绩效异常,请开发人员及时排查问题"));
        }
        if (!result.isSuccess()) {
            log.warn("调用分销系统批量保存督导或主任绩效不成功,请开发人员及时排查问题", result);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("调用分销系统批量保存督导或主任绩效不成功，请联系开发人员"));
        }
        return result.getData();
    }
}
