package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementPolicyProductPremDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyProductPremEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementPolicyProductPremService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 */
@Slf4j
@Service("settlementPolicyProductPremService")
public class SettlementPolicyProductPremServiceImpl extends ServiceImpl<SettlementPolicyProductPremDao, SettlementPolicyProductPremEntity> implements SettlementPolicyProductPremService {


}
