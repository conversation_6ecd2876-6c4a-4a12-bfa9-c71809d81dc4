package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementReconcileConfirmDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileConfirmEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcileConfirmService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 保司结算对账单汇总表
 *
 * <AUTHOR>
 * @date 2023-05-23 14:32:21
 */
@Slf4j
@Service("settlementReconcileConfirmService")
public class SettlementReconcileConfirmServiceImpl extends ServiceImpl<SettlementReconcileConfirmDao, SettlementReconcileConfirmEntity> implements SettlementReconcileConfirmService {

    @Override
    public void saveList(List<SettlementReconcileConfirmEntity> listData) {
        // 批量写入
        if (listData.size() > 3000) {
            List<List<SettlementReconcileConfirmEntity>> partition = ListUtils.partition(listData, 3000);
            partition.forEach(this::insertBatchSomeColumn);
        } else {
            this.insertBatchSomeColumn(listData);
        }


    }

    @Override
    public void insertBatchSomeColumn(List<SettlementReconcileConfirmEntity> batchList) {
        try {
            baseMapper.insertBatchSomeColumn(batchList);
        } catch (Exception e) {
            log.info("生成保司结算对账单汇总表异常原因[{}]数据={}", e.getMessage(), JSONUtil.toJsonStr(batchList));
            throw e;
        }
    }
}
