package com.mpolicy.settlement.core.thirdpart.dto.omsbiz;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 工商信息同步请求参数
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LicenseSyncRequest {

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 页码
     */
    private Integer pageIndex;

    /**
     * 页大小
     */
    private Integer pageSize;

    /**
     * 开始时间
     */
    private String startTime;
}
