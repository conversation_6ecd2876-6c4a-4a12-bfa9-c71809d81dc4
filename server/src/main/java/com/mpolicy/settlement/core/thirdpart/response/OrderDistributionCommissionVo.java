package com.mpolicy.settlement.core.thirdpart.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class OrderDistributionCommissionVo implements Serializable {
    @ApiModelProperty("佣金归属人编码证件号")
    private String manageCode;

    @ApiModelProperty("归属人姓名")
    private String manageName;

    @ApiModelProperty("保司保单号")
    private String policyNo;

    @ApiModelProperty("保司批单号")
    private String endorsementNo;

    @ApiModelProperty("农保订单号")
    private String orderId;

    @ApiModelProperty("发放金额")
    private BigDecimal commissionAmt;

    @ApiModelProperty(value = "月批次号")
    private String monthlyBatchNo;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty("保单状态 1承保成功 4退保成功")
    private String policyStatus;

    @ApiModelProperty("分销订单id")
    private Long orderDistributionId;
}
