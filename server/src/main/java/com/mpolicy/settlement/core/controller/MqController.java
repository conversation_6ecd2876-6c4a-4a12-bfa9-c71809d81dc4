package com.mpolicy.settlement.core.controller;

import com.alibaba.fastjson.JSONObject;
import com.mpolicy.common.result.Result;
import com.mpolicy.settlement.core.enums.QueueEnum;
import com.mpolicy.settlement.core.helper.SettlementMqHelper;
import com.mpolicy.web.common.behavior.annotation.Behavior;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 消息服务
 *
 * <AUTHOR>
 * @date 2022-10-11 11:30
 */
@RestController
@RequestMapping("/mq")
@Api(tags = "消息服务")
@Slf4j
public class MqController {

    /**
     * <p>
     * 消息发送
     * </p>
     */
    @ApiOperation(value = "指定队列消息", notes = "指定队列消息")
    @PostMapping("/send/queue/{code}")
    @Behavior(modelCode = "sendQueueMsg", modelName = "mq消息发送", descp = "mq消息发送记录")
    public Result<String> sendQueueMsg(@PathVariable @ApiParam(name = "code", value = "消息业务编码") String code,
                                        @RequestParam @ApiParam(name = "content", value = "消息内容") String content) {
        JSONObject data = new JSONObject();
        data.put("content", content);
        SettlementMqHelper.sendPolicyCoreMq(code, QueueEnum.ORDER_ACCEPT,data);
        return Result.success();
    }
}
