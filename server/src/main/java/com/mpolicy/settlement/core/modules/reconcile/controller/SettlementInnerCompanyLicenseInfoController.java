package com.mpolicy.settlement.core.modules.reconcile.controller;

import com.mpolicy.common.result.Result;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementInnerCompanyLicenseInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementInnerCompanyLicenseInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 结算内部公司工商信息控制器
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Api(tags = "结算内部公司工商信息管理")
@RestController
@RequestMapping("/settlement/license")
@Slf4j
public class SettlementInnerCompanyLicenseInfoController {

    @Autowired
    private SettlementInnerCompanyLicenseInfoService settlementInnerCompanyLicenseInfoService;

    /**
     * 根据统一社会信用代码查询工商信息
     */
    @ApiOperation(value = "根据统一社会信用代码查询工商信息")
    @GetMapping("/getBySocialCreditCode")
    public Result<SettlementInnerCompanyLicenseInfoEntity> getBySocialCreditCode(
            @ApiParam(value = "统一社会信用代码", required = true) @RequestParam String socialCreditCode) {
        try {
            SettlementInnerCompanyLicenseInfoEntity licenseInfo = 
                    settlementInnerCompanyLicenseInfoService.getBySocialCreditCodeFromCache(socialCreditCode);
            return Result.success(licenseInfo);
        } catch (Exception e) {
            log.error("查询工商信息失败，统一社会信用代码：{}", socialCreditCode, e);
            return Result.error("查询工商信息失败：" + e.getMessage());
        }
    }

    /**
     * 手动刷新工商信息缓存
     */
    @ApiOperation(value = "手动刷新工商信息缓存")
    @PostMapping("/refreshCache")
    public Result<String> refreshCache() {
        try {
            settlementInnerCompanyLicenseInfoService.refreshCache();
            return Result.success("缓存刷新成功");
        } catch (Exception e) {
            log.error("刷新工商信息缓存失败", e);
            return Result.error("刷新缓存失败：" + e.getMessage());
        }
    }

    /**
     * 获取缓存统计信息
     */
    @ApiOperation(value = "获取缓存统计信息")
    @GetMapping("/cacheStatistics")
    public Result<String> getCacheStatistics() {
        try {
            String statistics = settlementInnerCompanyLicenseInfoService.getCacheStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取缓存统计信息失败", e);
            return Result.error("获取缓存统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取缓存中所有工商信息
     */
    @ApiOperation(value = "获取缓存中所有工商信息")
    @GetMapping("/getAllFromCache")
    public Result<List<SettlementInnerCompanyLicenseInfoEntity>> getAllFromCache() {
        try {
            List<SettlementInnerCompanyLicenseInfoEntity> allLicenseInfo =
                    settlementInnerCompanyLicenseInfoService.getAllFromCache();
            return Result.success(allLicenseInfo);
        } catch (Exception e) {
            log.error("获取缓存中所有工商信息失败", e);
            return Result.error("获取缓存中所有工商信息失败：" + e.getMessage());
        }
    }
}
