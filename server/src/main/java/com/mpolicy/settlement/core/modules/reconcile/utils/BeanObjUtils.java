package com.mpolicy.settlement.core.modules.reconcile.utils;

import cn.hutool.core.date.DateUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import java.beans.PropertyDescriptor;
import java.util.*;

import static org.apache.commons.lang.StringEscapeUtils.escapeSql;

public class BeanObjUtils {
    public static <T> T copyPropertiesIgnoreNull(Object src, T dst) {
        final BeanWrapper beanWrapper = new BeanWrapperImpl(src);
        BeanUtils.copyProperties(src, dst, Arrays
                .stream(beanWrapper.getPropertyDescriptors())
                .map(PropertyDescriptor::getName)
                .filter(name -> beanWrapper.getPropertyValue(name) == null)
                .toArray(String[]::new)
        );
        return dst;
    }

    public static void main(String[] args){

        Date thisMonth = DateUtil.beginOfMonth(DateUtil.parseDateTime("2024-12-01 12:00:00"));

         Date settlementDate = DateUtil.offsetDay(thisMonth,4);

         System.out.println("settlementDate = "+settlementDate);

    }
}
