package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementReconcileDiffBacklogDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileDiffBacklogEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcileDiffBacklogService;

/**
 * 保司结算对账单差异待办
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:34
 */
@Slf4j
@Service("settlementReconcileDiffBacklogService")
public class SettlementReconcileDiffBacklogServiceImpl extends ServiceImpl<SettlementReconcileDiffBacklogDao, SettlementReconcileDiffBacklogEntity> implements SettlementReconcileDiffBacklogService {

}
