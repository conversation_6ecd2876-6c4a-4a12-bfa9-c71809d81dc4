package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementReconcilePolicyDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcilePolicyEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcilePolicyService;

import java.util.List;

/**
 * 保司结算对账单关联数据
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:34
 */
@Slf4j
@Service("settlementReconcilePolicyService")
public class SettlementReconcilePolicyServiceImpl extends ServiceImpl<SettlementReconcilePolicyDao, SettlementReconcilePolicyEntity> implements SettlementReconcilePolicyService {

    @Override
    public void saveList(List<SettlementReconcilePolicyEntity> listData) {
        // 批量写入
        if (listData.size() > 3000) {
            List<List<SettlementReconcilePolicyEntity>> partition = ListUtils.partition(listData, 3000);
            partition.forEach(x -> baseMapper.insertBatchSomeColumn(x));
        }else{
            baseMapper.insertBatchSomeColumn(listData);
        }
    }
}
