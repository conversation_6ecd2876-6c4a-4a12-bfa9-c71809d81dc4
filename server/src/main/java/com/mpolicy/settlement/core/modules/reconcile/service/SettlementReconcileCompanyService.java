package com.mpolicy.settlement.core.modules.reconcile.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileCompanyEntity;

import java.util.List;

/**
 * 保司结算对账单关联保司对账单数据
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:34
 */
public interface SettlementReconcileCompanyService extends IService<SettlementReconcileCompanyEntity> {

    /**
     * 批量保存数据
     *
     * @param listData 数据集合
     * @return void
     * <AUTHOR>
     * @since 2023/8/31
     */
    void saveList(List<SettlementReconcileCompanyEntity> listData);
}

