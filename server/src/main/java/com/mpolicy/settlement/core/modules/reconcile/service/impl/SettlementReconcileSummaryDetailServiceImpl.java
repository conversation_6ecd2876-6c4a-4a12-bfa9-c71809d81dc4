package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementReconcileSummaryDetailDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileSummaryDetailEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcileSummaryDetailService;

/**
 * 保司结算对账单汇总明细
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:34
 */
@Slf4j
@Service("settlementReconcileSummaryDetailService")
public class SettlementReconcileSummaryDetailServiceImpl extends ServiceImpl<SettlementReconcileSummaryDetailDao, SettlementReconcileSummaryDetailEntity> implements SettlementReconcileSummaryDetailService {

}
