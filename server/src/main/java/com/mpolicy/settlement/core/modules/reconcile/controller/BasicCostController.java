package com.mpolicy.settlement.core.modules.reconcile.controller;

import com.alibaba.fastjson.JSON;
import com.mpolicy.common.result.Result;
import com.mpolicy.settlement.core.common.commission.dto.ValidateCostConfigParam;
import com.mpolicy.settlement.core.common.commission.dto.ValidateResultDto;
import com.mpolicy.settlement.core.common.reconcile.policy.*;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.ManualCostCodeDto;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.ManualCostInfoDto;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.ManualCustomerManagerChangeDto;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.ManualProductChange;
import com.mpolicy.settlement.core.modules.reconcile.service.ManualOperationCostService;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementBasicCommValidService;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementCostCorrectionService;
import com.mpolicy.web.common.annotation.PassToken;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/settlement/basicCost")
@Api(tags = "保险公司支出业务")
@Slf4j
public class BasicCostController {

    @Autowired
    private SettlementCostCorrectionService settlementCostCorrectionService;
    @Autowired
    private ManualOperationCostService manualOperationCostService;

    @Autowired
    private SettlementBasicCommValidService settlementBasicCommValidService;

    @ApiOperation(value = "手工冲正", notes = "手工冲正")
    @PostMapping("/manualCorrection")
    @PassToken
    public Result<String> manualCorrection(@RequestBody @Valid ManualCorrectionCost correction) throws Exception {
        log.info("手工冲正入参:{}",correction);
        manualOperationCostService.manualCorrection(correction);
        return Result.success();
    }

    @ApiOperation(value = "手工冲正推荐人", notes = "手工冲正推荐人")
    @PostMapping("/manualCorrectionCustomerManager")
    @PassToken
    public Result<String> manualCorrectionCustomerManager(@RequestBody @Valid ManualCustomerManagerChangeDto correction) throws Exception {
        log.info("手工冲正推荐人:{}", JSON.toJSONString(correction));
        manualOperationCostService.manualCorrectionCustomerManager(correction);
        return Result.success();
    }

    @ApiOperation(value = "手工处理无需确认", notes = "手工处理无需确认")
    @PostMapping("/manualNoNeedConfirmStatus")
    @PassToken
    public Result<String> manualNoNeedConfirmStatus(@RequestBody @Valid ManualCostCodeDto correction) throws Exception {
        log.info("手工处理无需确认:{}", JSON.toJSONString(correction));
        manualOperationCostService.manualNoNeedConfirmStatus(correction);
        return Result.success();
    }

    @ApiOperation(value = "手工处理待确认", notes = "手工处理待确认")
    @PostMapping("/manualWaitConfirmStatus")
    @PassToken
    public Result<String> manualWaitConfirmStatus(@RequestBody @Valid ManualCostCodeDto correction) throws Exception {
        log.info("手工处理待确认:{}", JSON.toJSONString(correction));
        manualOperationCostService.manualWaitConfirmStatus(correction);
        return Result.success();
    }

    @ApiOperation(value = "手工推荐人或者记账时间(未结算)", notes = "手工推荐人或者记账时间(未结算)")
    @PostMapping("/manualModifyCostInfo")
    @PassToken
    public Result<String> manualModifyCostInfo(@RequestBody @Valid List<ManualCostInfoDto> dtos) throws Exception {
        log.info("手工推荐人或者记账时间入参:{}",dtos);
        manualOperationCostService.manualModifyCostInfo(dtos);
        return Result.success();
    }
    @ApiOperation(value = "支出端佣金配置是否配置验证接口(保单中心调用)", notes = "支出端佣金配置是否配置验证接口(保单中心调用)")
    @PostMapping("/validateCostConfig")
    @PassToken
    public Result<List<ValidateResultDto>> validateCostConfig(@RequestBody List<ValidateCostConfigParam> params){
        return Result.success(settlementBasicCommValidService.validateCostConfig(params));
    }




    @ApiOperation(value = "手工处理险种变更", notes = "手工处理险种变更")
    @PostMapping("/manualProductChange")
    @PassToken
    public Result<String> manualProductChange(@RequestBody @Valid ManualProductChange change) throws Exception {
        log.info("手工处理险种变更:{}",change);
        manualOperationCostService.manualProductChange(change);
        return Result.success();
    }




}
