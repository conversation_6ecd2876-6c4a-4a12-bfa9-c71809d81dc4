package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementReconcileCompanySubjectDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileCompanySubjectEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcileCompanySubjectService;

/**
 * 结算保司配置对应规则科目表
 *
 * <AUTHOR>
 * @date 2023-05-22 09:19:53
 */
@Slf4j
@Service("settlementReconcileCompanySubjectService")
public class SettlementReconcileCompanySubjectServiceImpl extends ServiceImpl<SettlementReconcileCompanySubjectDao, SettlementReconcileCompanySubjectEntity> implements SettlementReconcileCompanySubjectService {

}
