package com.mpolicy.settlement.core.thirdpart.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum IncentiveCalcBusinessTypeEnum {
    INSURANCE_DIRECTOR_PROMOTION_FEE("INSURANCE_DIRECTOR_PROMOTION_FEE","保险主任推广费", IncentiveCalcBusinessSystemEnum.INSURANCE),
    INSURANCE_DIRECTOR_INCENTIVE_FEE("INSURANCE_DIRECTOR_INCENTIVE_FEE","保险主任绩效", IncentiveCalcBusinessSystemEnum.INSURANCE),
    INSURANCE_SUPERVISION_PROMOTION_FEE("INSURANCE_SUPERVISION_PROMOTION_FEE","保险督导推广费", IncentiveCalcBusinessSystemEnum.INSURANCE),
    INSURANCE_SUPERVISION_INCENTIVE_FEE("INSURANCE_SUPERVISION_INCENTIVE_FEE","保险督导绩效", IncentiveCalcBusinessSystemEnum.INSURANCE),
    ERP_OVERDUE_FEE("ERP_OVERDUE_FEE", "erp系统逾期费", IncentiveCalcBusinessSystemEnum.ERP);

    private final String code;

    private final String desc;

    private final IncentiveCalcBusinessSystemEnum businessSystemEnum;
}
