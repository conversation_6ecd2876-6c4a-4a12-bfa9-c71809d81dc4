package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.policy.common.enums.PolicyPaymentTypeEnum;
import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.policy.common.ep.policy.EpInsuredInfoVo;
import com.mpolicy.policy.common.ep.policy.EpPersonalProductInfoVo;
import com.mpolicy.policy.common.ep.policy.EpProductInfoVo;
import com.mpolicy.product.common.base.ProductBase;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.enums.SettlementExceptionEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.ConfirmStatusEnum;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.*;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.CostBasicCommissionConfigDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.CostTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.PolicyProductTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.ProductStatusEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyProductPremInput;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyProductPremResult;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementCostProcessService;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementCostSurrenderProcessService;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import com.mpolicy.settlement.core.utils.LambdaUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import javax.swing.plaf.basic.BasicIconFactory;
import java.math.BigDecimal;
import java.util.*;

import static com.mpolicy.settlement.core.common.Constant.ZHNX_CHANNEL_CODE;
import static com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum.PERSONAL_NEW_POLICY;
import static com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum.STANDARD_SURRENDER;

@Service("settlementCostSurrenderProcessService")
@Slf4j
public class SettlementCostSurrenderProcessServiceImpl extends SettlementCostProcessServiceImpl implements SettlementCostSurrenderProcessService {

    /**
     * 犹豫期退保
     *
     * @param eventJob
     * @param subjectEnum
     * @param eventType
     * @param productMap
     * @param policyInfo
     * @param preservationDetail
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> builderPolicyHesitateSurrenderCostInfo(SettlementEventJobEntity eventJob,
                                                                                 CostSubjectEnum subjectEnum,
                                                                                 SettlementEventTypeEnum eventType,
                                                                                 Map<String, ProductBase> productMap,
                                                                                 EpContractInfoVo policyInfo,
                                                                                 PolicyPreservationDetailDto preservationDetail) {
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();
        EpPreserveSurrenderDetailDto detail = preservationDetail.getSurrenderDetail();
        PolicyProductTypeEnum policyProductTypeEnum = PolicyProductTypeEnum.getProdTypeEnum(policyInfo.getPolicyProductType());
        Integer insuranceType = getInsuranceType(eventType, policyInfo.getSourcePolicyNo());
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        switch (policyProductTypeEnum) {
            case PERSONAL:
            case PROPERTY: {
                //BigDecimal totalPremium = surrenderDetails.stream().map(EpPreserveSurrenderDetailVo::getPremium).reduce(BigDecimal.ZERO, BigDecimal::add);
                //boolean haveSurrenderPremium = surrenderDetails.get(0).getSurrenderPremium()==null?true:false;
                //BigDecimal dynamicPremium = BigDecimal.ZERO;
                List<EpPreserveSurrenderInsuredDto> surrenderDetails = detail.getInsuredList();
                if(CollectionUtils.isEmpty(surrenderDetails)){
                    throw new GlobalException(SettlementExceptionEnum.C_B_SURRENDER_DETAIL_NOT_EXIST.getException(StrUtil.format("支出端-基础佣金-该保单{}退保明细记录不存在,退保信息：{}", policyNo,detail)));
                }
                for (int i = 0; i < surrenderDetails.size(); i++) {
                    EpPreserveSurrenderInsuredDto dto = surrenderDetails.get(i);
                    Optional<EpInsuredInfoVo> opt = policyInfo.getInsuredInfoList().stream().filter(o -> Objects.equals(o.getInsuredCode(), dto.getInsuredCode())).findFirst();
                    if (!opt.isPresent()) {
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-犹豫期退保人员明细明细在保单详情明细中不存在-保单号={},被保人：{}", eventJob.getEventBusinessCode(), dto.getInsuredCode())));
                    }
                    List<EpPreserveProductDto> surrenderProductDtos = dto.getInsuredProductList();
                    for (int j = 0; j < surrenderProductDtos.size(); j++) {
                        EpPreserveProductDto productDto = surrenderProductDtos.get(j);
                        Optional<EpPersonalProductInfoVo> productOpt = opt.get().getProductInfoList().stream().filter(p -> Objects.equals(p.getProductCode(), productDto.getProductCode())).findFirst();
                        EpProductInfoVo product = productOpt.get();
                        EpInsuredInfoVo insured = opt.get();
                        Integer renewalPeriod =preservationDetail.getRenewalPeriod()!=null?preservationDetail.getRenewalPeriod():preservationDetail.getRenewalTermPeriod();
                        PolicyProductPremInput input = buildBasicCommissionConfigParam(policyInfo, insuranceType, product,insured.getInsuredPolicyAge(), renewalPeriod, renewalPeriod);
                        List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(policyNo, input);
                        for (PolicyProductPremResult config : configList) {
                            //初始化，生成科目信息
                            SettlementCostInfoEntity bean = initSettlementCostInfo(eventJob, subjectEnum, eventType, policyInfo,insuranceType);
                            //记录佣金匹配时间，便于冲正流程中获取
                            bean.setCostConfigMatchTime(input.getApprovedTime());
                            //记账时间处理
                            bean.setSettlementTime(getBasicSettlementTime(eventType, policyInfo));
                            bean.setSettlementDate(bean.getSettlementTime());
                            //设置险种保费和实际用于计算的保费
                            bean.setProductPremium(product.getPremium());
                            /* 保单中心险种拆分去除
                            bean.setPremium(product.getPremium().negate());
                            bean.setBusinessPremium(product.getPremium().negate());
                            */
                            bean.setPremium(productDto.getSurrenderPremium());
                            bean.setBusinessPremium(productDto.getSurrenderPremium());
                            bean.setBusinessAccountTime(getBusinessAccountTime(eventType,policyInfo,preservationDetail));
                            //佣金信息
                            calcBasicCommission(policyNo, null, product, config, bean);
                            //初始化确认信息
                            bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                            //被保人信息
                            setInsuredInfo(insured, bean);

                            //险种信息
                            builderCostProductInfo(policyInfo, productMap, product, bean, renewalPeriod, renewalPeriod);
                            //保全信息
                            builderPreservationInfo(preservationDetail, productDto.getSurrenderPremium(), bean);

                            //折算保费
                            builderDiscountPremium(policyNo,policyInfo.getPolicyProductType(),bean);
                            costInfoEntities.add(bean);
                        }
                    }
                }
                break;
            }
            case VEHICLE: {
                List<EpPreserveSurrenderProductDto> surrenderDetails = detail.getProductList();
                for (int i = 0; i < surrenderDetails.size(); i++) {
                    EpPreserveSurrenderProductDto dto = surrenderDetails.get(i);
                    Optional<EpProductInfoVo> opt = policyInfo.getProductInfoList().stream().filter(o -> Objects.equals(o.getProductCode(), dto.getProductCode())).findFirst();
                    if (!opt.isPresent()) {
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-犹豫期退保险种明细在保单详情明细中不存在-保单号={},险种编码：{}", eventJob.getEventBusinessCode(), dto.getProductCode())));
                    }

                    EpProductInfoVo product = opt.get();
                    Integer renewalPeriod =preservationDetail.getRenewalPeriod()!=null?preservationDetail.getRenewalPeriod():preservationDetail.getRenewalTermPeriod();
                    PolicyProductPremInput input = commissionConfigService.buildBasicCommissionConfigParam(policyInfo, insuranceType, product,null, renewalPeriod, renewalPeriod);
                    List<CostBasicCommissionConfigDto> configList = commissionConfigService.getCostBasicCommissionConfig(policyNo,policyProductTypeEnum.getCode(), input);

                    for (CostBasicCommissionConfigDto config : configList) {
                        //初始化，生成科目信息
                        SettlementCostInfoEntity bean = initSettlementCostInfo(eventJob, subjectEnum, eventType, policyInfo,insuranceType);
                        //记录佣金匹配时间，便于冲正流程中获取
                        bean.setCostConfigMatchTime(input.getApprovedTime());
                        //记账时间处理
                        bean.setSettlementTime(getBasicSettlementTime(eventType, policyInfo));
                        bean.setSettlementDate(bean.getSettlementTime());
                        //设置险种保费和实际用于计算的保费
                        bean.setProductPremium(product.getPremium());
                        /* 因保单中心险种层拆分
                        bean.setPremium(product.getPremium().negate());
                        bean.setBusinessPremium(product.getPremium().negate());
                        */
                        bean.setPremium(dto.getSurrenderPremium());
                        bean.setBusinessPremium(dto.getSurrenderPremium());
                        bean.setBusinessAccountTime(getBusinessAccountTime(eventType,policyInfo,preservationDetail));
                        //佣金信息
                        calcBasicCommission(policyNo,  config, bean);
                        //初始化确认信息
                        bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                        //险种信息
                        builderCostProductInfo(policyInfo, productMap, product, bean, renewalPeriod, renewalPeriod);
                        //保全信息
                        builderPreservationInfo(preservationDetail, dto.getSurrenderPremium(), bean);

                        //折算保费
                        builderDiscountPremium(policyNo,policyInfo.getPolicyProductType(),bean);
                        costInfoEntities.add(bean);

                        //是否存在车船税
                        if(config.isVehicleInsurance() && Objects.equals(config.getExistVehicleVesselTax(),Boolean.TRUE)){
                            //是否交强险
                            if(isCompulsoryInsurance(policyInfo.getPolicyProductType(),productMap.get(product.getProductCode()))){
                                costInfoEntities.add(builderCompulsoryInsuranceCostInfo(eventType,bean,config));
                            }
                        }

                    }
                }
            }
            case GROUP: {
                log.warn("团险没有犹豫期退保.");
                break;
            }
            default: {
                log.warn("保单类型暂不处理.");
            }
        }
        //设置佣金归属人信息并返回
        return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);
    }
    /**
     * 协议解约
     *
     * @param eventJob
     * @param subjectEnum
     * @param eventType
     * @param productMap
     * @param policyInfo
     * @param preservationDetail
     * @return
     */
    @Override
    @Deprecated
    public List<SettlementCostInfoEntity> builderProtocolTerminationCostInfo(SettlementEventJobEntity eventJob,
                                                                             CostSubjectEnum subjectEnum,
                                                                             SettlementEventTypeEnum eventType,
                                                                             Map<String, ProductBase> productMap,
                                                                             EpContractInfoVo policyInfo,
                                                                             PolicyPreservationDetailDto preservationDetail) {
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();
        EpPreserveSurrenderDetailDto detail = preservationDetail.getSurrenderDetail();
        PolicyProductTypeEnum policyProductTypeEnum = PolicyProductTypeEnum.getProdTypeEnum(policyInfo.getPolicyProductType());
        Integer insuranceType = getInsuranceType(eventType, policyInfo.getSourcePolicyNo());
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        Integer longShortFlag = policyInfo.getContractBaseInfo().getLongShortFlag();
        BaseSurrenderDto param = BaseSurrenderDto.builder()
                .eventJob(eventJob)
                .subjectEnum(subjectEnum)
                .eventType(eventType)
                .productMap(productMap)
                .policyInfo(policyInfo)
                .preservationDetail(preservationDetail)

                .insuranceType(insuranceType)
                .policyNo(policyNo)
                .longShortFlag(longShortFlag)
                .build();

        switch (policyProductTypeEnum) {
            case PERSONAL:
            case PROPERTY: {
                if (longShortFlag == 1) {
                    builderLongPolicySurrenderCostInfo(eventJob, subjectEnum, eventType, productMap, policyInfo, preservationDetail, costInfoEntities);
                    return costInfoEntities;
                } else {
                    List<EpPreserveSurrenderInsuredDto> surrenderDetails = detail.getInsuredList();
                    for (int i = 0; i < surrenderDetails.size(); i++) {
                        EpPreserveSurrenderInsuredDto dto = surrenderDetails.get(i);
                        Optional<EpInsuredInfoVo> opt = policyInfo.getInsuredInfoList().stream().filter(o -> Objects.equals(o.getInsuredCode(), dto.getInsuredCode())).findFirst();
                        if (!opt.isPresent()) {
                            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-协议解约人员明细明细在保单详情明细中不存在-保单号={},被保人：{}", eventJob.getEventBusinessCode(), dto.getInsuredCode())));
                        }
                        EpInsuredInfoVo insured = opt.get();
                        List<EpPreserveProductDto> surrenderProductDtos = dto.getInsuredProductList();
                        for (int j = 0; j < surrenderProductDtos.size(); j++) {
                            EpPreserveProductDto productDto = surrenderProductDtos.get(j);

                            Optional<EpPersonalProductInfoVo> productOpt = insured.getProductInfoList().stream().filter(p -> Objects.equals(p.getProductCode(), productDto.getProductCode())).findFirst();
                            EpProductInfoVo product = productOpt.get();
                            Integer renewalPeriod =preservationDetail.getRenewalPeriod()!=null?preservationDetail.getRenewalPeriod():preservationDetail.getRenewalTermPeriod();
                            PolicyProductPremInput input = buildBasicCommissionConfigParam(policyInfo, insuranceType, product,insured.getInsuredPolicyAge(), renewalPeriod, renewalPeriod);
                            List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(policyNo, input);
                            BigDecimal premium = productDto.getSurrenderPremium();
                            for (PolicyProductPremResult config : configList) {
                                //初始化，生成科目信息
                                SettlementCostInfoEntity bean = initSettlementCostInfo(eventJob, subjectEnum, eventType, policyInfo,insuranceType);
                                //记录佣金匹配时间，便于冲正流程中获取
                                bean.setCostConfigMatchTime(input.getApprovedTime());
                                //记账时间处理
                                bean.setSettlementTime(getBasicSettlementTime(eventType, policyInfo));
                                bean.setSettlementDate(bean.getSettlementTime());
                                //设置险种保费和实际用于计算的保费
                                bean.setProductPremium(product.getPremium());
                                bean.setSurrenderAmount(premium);
                                bean.setPremium(premium);
                                bean.setBusinessPremium(premium);
                                bean.setBusinessAccountTime(getBusinessAccountTime(eventType,policyInfo,preservationDetail));
                                //佣金信息
                                calcBasicCommission(policyNo, null, product, config, bean);
                                //初始化确认信息
                                bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                                //被保人信息
                                setInsuredInfo(insured, bean);
                                //险种信息
                                builderCostProductInfo(policyInfo, productMap, product, bean, renewalPeriod, renewalPeriod);
                                //保全信息
                                builderPreservationInfo(preservationDetail, premium, bean);

                                //折算保费
                                builderDiscountPremium(policyNo,policyInfo.getPolicyProductType(),bean);
                                costInfoEntities.add(bean);
                            }
                        }


                    }
                    return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);

                }
            }
            case VEHICLE: {
                List<EpPreserveSurrenderProductDto> surrenderDetails = detail.getProductList();
                for (int i = 0; i < surrenderDetails.size(); i++) {
                    EpPreserveSurrenderProductDto dto = surrenderDetails.get(i);
                    Optional<EpProductInfoVo> opt = policyInfo.getProductInfoList().stream().filter(o -> Objects.equals(o.getProductCode(), dto.getProductCode())).findFirst();
                    if (!opt.isPresent()) {
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-协议解约险种明细在保单详情明细中不存在-保单号={},险种编码：{}", eventJob.getEventBusinessCode(), dto.getProductCode())));
                    }
                    EpProductInfoVo product = opt.get();
                    Integer renewalPeriod =preservationDetail.getRenewalPeriod()!=null?preservationDetail.getRenewalPeriod():preservationDetail.getRenewalTermPeriod();
                    PolicyProductPremInput input = commissionConfigService.buildBasicCommissionConfigParam(policyInfo, insuranceType, product,null, renewalPeriod, renewalPeriod);
                    List<CostBasicCommissionConfigDto> configList = commissionConfigService.getCostBasicCommissionConfig(policyNo,policyProductTypeEnum.getCode(), input);
                    //长险全额退,即退保保费为源保费
                    BigDecimal premium = dto.getSurrenderPremium();
                    for (CostBasicCommissionConfigDto config : configList) {
                        //初始化，生成科目信息
                        SettlementCostInfoEntity bean = initSettlementCostInfo(eventJob, subjectEnum, eventType, policyInfo,insuranceType);
                        //记录佣金匹配时间，便于冲正流程中获取
                        bean.setCostConfigMatchTime(input.getApprovedTime());
                        //记账时间处理
                        bean.setSettlementTime(getBasicSettlementTime(eventType, policyInfo));
                        bean.setSettlementDate(bean.getSettlementTime());
                        //设置险种保费和实际用于计算的保费
                        bean.setProductPremium(product.getPremium());
                        bean.setPremium(premium.abs().negate());
                        bean.setSurrenderAmount(premium.abs().negate());
                        bean.setBusinessPremium(premium.abs().negate());
                        bean.setBusinessAccountTime(getBusinessAccountTime(eventType,policyInfo,preservationDetail));
                        //佣金信息
                        calcBasicCommission(policyNo, config, bean);
                        //初始化确认信息
                        bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                        //险种信息
                        builderCostProductInfo(policyInfo, productMap, product, bean,renewalPeriod, renewalPeriod);
                        //保全信息
                        builderPreservationInfo(preservationDetail, premium.abs().negate(), bean);
                        //折算保费
                        builderDiscountPremium(policyNo,policyInfo.getPolicyProductType(),bean);
                        costInfoEntities.add(bean);

                        //是否存在车船税
                        if(config.isVehicleInsurance() && Objects.equals(config.getExistVehicleVesselTax(),Boolean.TRUE)){
                            //是否交强险
                            if(isCompulsoryInsurance(policyInfo.getPolicyProductType(),productMap.get(product.getProductCode()))){
                                costInfoEntities.add(builderCompulsoryInsuranceCostInfo(eventType,bean,config));
                            }
                        }
                    }
                }
                return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);
            }

            case GROUP: {
                break;
            }
            default: {
                log.warn("保单类型暂不处理.");
            }
        }


        return costInfoEntities;
    }



    /**
     * 标准退保
     *
     * @param eventJob
     * @param subjectEnum
     * @param eventType
     * @param productMap
     * @param policyInfo
     * @param preservationDetail
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> builderStandardSurrenderCostInfo(SettlementEventJobEntity eventJob,
                                                                           CostSubjectEnum subjectEnum,
                                                                           SettlementEventTypeEnum eventType,
                                                                           Map<String, ProductBase> productMap,
                                                                           EpContractInfoVo policyInfo,
                                                                           PolicyPreservationDetailDto preservationDetail) {
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();
        PolicyProductTypeEnum policyProductTypeEnum = PolicyProductTypeEnum.getProdTypeEnum(policyInfo.getPolicyProductType());
        Integer insuranceType = getInsuranceType(eventType, policyInfo.getSourcePolicyNo());
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();


        //是否长险
        Integer longShortFlag = policyInfo.getContractBaseInfo().getLongShortFlag();
        BaseSurrenderDto param = BaseSurrenderDto.builder()
                .eventJob(eventJob)
                .subjectEnum(subjectEnum)
                .eventType(eventType)
                .productMap(productMap)
                .policyInfo(policyInfo)
                .preservationDetail(preservationDetail)

                .insuranceType(insuranceType)
                .policyNo(policyNo)
                //.totalPremium(totalPremium)
                //.haveDetailSurrenderPremium(haveDetailSurrenderPremium)
                //.dynamicPremium(dynamicPremium)
                .longShortFlag(longShortFlag)
                .build();


        switch (policyProductTypeEnum) {
            case PERSONAL:
            case PROPERTY: {
                log.info("合同{}长短险标志：{}",longShortFlag);
                if (Objects.equals(longShortFlag,1)) {

                    builderLongPolicySurrenderCostInfo(eventJob,subjectEnum,eventType,productMap,policyInfo,preservationDetail,costInfoEntities);
                    return costInfoEntities;
                }else{
                    builderStandardSurrenderByInsuredInfo(param, costInfoEntities);
                    return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);
                }

            }
            case VEHICLE: {
                if(CollectionUtils.isNotEmpty(param.getPreservationDetail().getSurrenderDetail().getInsuredList())){
                    builderStandardSurrenderByInsuredInfo(param, costInfoEntities);
                }else {
                    builderStandardSurrenderByProductInfo(param, costInfoEntities,policyProductTypeEnum);
                }
                return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);
            }
            case GROUP: {
                //农保渠道，或则有被保人明细
                if(Objects.equals(param.getPolicyInfo().getChannelInfo().getChannelCode(),ZHNX_CHANNEL_CODE)
                   || CollectionUtils.isNotEmpty(preservationDetail.getSurrenderDetail().getInsuredList() )){
                    builderGroupStandardSurrenderByInsuredInfo(param,costInfoEntities);
                    return costInfoEntities;
                }else{
                    builderStandardSurrenderByProductInfo(param,costInfoEntities,policyProductTypeEnum);
                    return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);
                }

            }
            default: {
                log.warn("保单类型暂不处理.");
            }
        }
        return costInfoEntities;
    }


    private void builderStandardSurrenderByProductInfo(BaseSurrenderDto param, List<SettlementCostInfoEntity> costInfoEntities,PolicyProductTypeEnum policyProductTypeEnum) {

        log.info("支出端-基础佣金--根据险种纬度计算退保佣金");
        //List<SettlementCostInfoEntity> oldList = listPolicyUnCorrectionCostInfoByContractCode(param.getEventType().getEventName(),param.getPolicyInfo().getContractCode(),param.getPolicyInfo().getContractBaseInfo().getPolicyNo());
        List<SettlementCostInfoEntity> oldList = settlementCostInfoService.listLongPolicyUnCorrectionCostInfoByContractCode(param.getPolicyInfo().getContractCode());
        //过滤所有险种编码、被保人编码、期数等于退保记录的险种编码、被保人编码、期数（增加期数是可能存在某期退了附加险，后面又新增了附加险）
        oldList = filterOldSurrenderCostInfo(oldList);
        //安合同号+险种编码+被保人编码+期数
        Map<String,List<SettlementCostInfoEntity>> oldMap = LambdaUtils.groupBy(oldList,o-> keyCostInfo(o.getContractCode() , o.getProductCode() , StringUtils.isBlank(o.getInsuredCode())?"":o.getInsuredCode() , o.getRenewalPeriod()));

        List<EpPreserveSurrenderProductDto> surrenderDetails = param.getPreservationDetail().getSurrenderDetail().getProductList();

        /* 因保单中心险种拆分去除
        boolean haveSurrenderPremium = surrenderDetails.get(0).getSurrenderPremium() == null ? false : true;
        BigDecimal totalPremium = surrenderDetails.stream().map(EpPreserveSurrenderProductDto::getPremium).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal dynamicPremium = BigDecimal.ZERO;
        */
        Boolean isWhaleLong = isWhaleLongPolicy(param.getPolicyInfo(), param.getProductMap());

        for (int i = 0; i < surrenderDetails.size(); i++) {
            EpPreserveSurrenderProductDto dto = surrenderDetails.get(i);
            Optional<EpProductInfoVo> opt = param.getPolicyInfo().getProductInfoList().stream().filter(o -> Objects.equals(o.getProductCode(), dto.getProductCode())).findFirst();
            if (!opt.isPresent()) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-退保险种明细在保单详情明细中不存在-保单号={},险种编码：{}", param.getEventJob().getEventBusinessCode(), dto.getProductCode())));
            }
            EpProductInfoVo product = opt.get();
            Integer renewalPeriod =param.getPreservationDetail().getRenewalPeriod()!=null?param.getPreservationDetail().getRenewalPeriod():param.getPreservationDetail().getRenewalTermPeriod();
            /*PolicyProductPremInput input = commissionConfigService.buildBasicCommissionConfigParam(param.getPolicyInfo(), param.getInsuranceType(), product,null, renewalPeriod, renewalPeriod);
            List<CostBasicCommissionConfigDto> configList = commissionConfigService.getCostBasicCommissionConfig(param.getPolicyNo(),param.getPolicyInfo().getPolicyProductType(), input);
*/
            //长险全额退,即退保保费为源保费
            BigDecimal surrenderPremium;
            if (param.getLongShortFlag() == 1) {
                surrenderPremium = dto.getPremium();
            } else {
                surrenderPremium = dto.getSurrenderPremium();
                /* 因保单中心险种拆分去除
                if (!haveSurrenderPremium) {
                    surrenderPremium = param.getPreservationDetail().getSurrenderCash().multiply(dto.getPremium()).divide(totalPremium, 2, BigDecimal.ROUND_HALF_UP);
                    if (dynamicPremium.abs().add(surrenderPremium.abs()).compareTo(param.getPreservationDetail().getSurrenderCash().abs()) > 0 || i == surrenderDetails.size() - 1) {
                        surrenderPremium = param.getPreservationDetail().getSurrenderCash().subtract(dynamicPremium);
                    }
                    dynamicPremium = dynamicPremium.add(surrenderPremium);
                }*/
            }
            BigDecimal calcPremium = isWhaleLong?BigDecimal.ZERO:surrenderPremium;

            String key = keyCostInfo(param.getPolicyInfo().getContractCode(),dto.getProductCode(),"",renewalPeriod);
            log.info("支出端-基础佣金-退保事件查询历史无被保人佣金记录key为:{}",key);
            if(oldMap.containsKey(key)){
                //按历史佣金记录进行退保
                log.info("支出端-基础佣金-历史佣金记录存在，按历史佣金记录进行退保");
                List<SettlementCostInfoEntity> olds = oldMap.get(key);

                List<SettlementCostInfoEntity> costs = builderInsuredCostInfoByOldCostInfo(param,null,product,olds,calcPremium,surrenderPremium,renewalPeriod);
                if(olds.size() != costs.size()){
                    throw new GlobalException(SettlementExceptionEnum.C_B_SURRENDER_DETAIL_NOT_EQUALS_OLD.getException(StrUtil.format("支出端-基础佣金-根据历史记录计算出的退保记录数与历史记录数不一致-保单号={},险种编码={},key={}", param.getEventJob().getEventBusinessCode(), dto.getProductCode(),key)));
                }
                costInfoEntities.addAll(costs);
            }else{
                //按佣金配置进行退保
                log.info("支出端-基础佣金-历史佣金记录不存在，按佣金配置进行退保计算");
                PolicyProductPremInput input = commissionConfigService.buildBasicCommissionConfigParam(param.getPolicyInfo(), param.getInsuranceType(), product,null, renewalPeriod, renewalPeriod);
                List<CostBasicCommissionConfigDto> configList = commissionConfigService.getCostBasicCommissionConfig(param.getPolicyNo(),param.getPolicyInfo().getPolicyProductType(), input);

                builderInsuredCostInfoByCostRateConfig(param, null,product,input,configList,calcPremium,surrenderPremium,renewalPeriod,costInfoEntities,Objects.equals(PolicyProductTypeEnum.GROUP,policyProductTypeEnum)?true:false);
            }


        }
    }

    private void builderGroupStandardSurrenderByInsuredInfo(BaseSurrenderDto param, List<SettlementCostInfoEntity> costInfoEntities) {
        log.info("支出端-基础佣金--根据被保人、险种纬度计算【团险】退保佣金");

        List<SettlementCostInfoEntity> oldList = listPolicyUnCorrectionCostInfoByContractCode(param.getEventType().getEventName(),param.getPolicyInfo().getContractCode(),param.getPolicyInfo().getContractBaseInfo().getPolicyNo());

        //过滤所有险种编码、被保人编码、期数等于退保记录的险种编码、被保人编码、期数（增加期数是可能存在某期退了附加险，后面又新增了附加险）
        oldList = filterOldSurrenderCostInfo(oldList);
        //安合同号+险种编码+被保人编码+期数
        Map<String,List<SettlementCostInfoEntity>> oldMap = LambdaUtils.groupBy(oldList,o-> keyCostInfo(o.getContractCode() , o.getProductCode() , o.getInsuredCode() , o.getRenewalPeriod()));

        List<EpPreserveSurrenderInsuredDto> surrenderDetails = param.getPreservationDetail().getSurrenderDetail().getInsuredList();
        boolean haveSurrenderPremium = surrenderDetails.get(0).getInsuredSurrenderPremium() == null ? false : true;
        //中和农信渠道，如果没有被保人退保保费则抛出异常
        if (Objects.equals(param.getPolicyInfo().getChannelInfo().getChannelCode(), ZHNX_CHANNEL_CODE) && !haveSurrenderPremium) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-团险整单退保被保人退保明细金额不存在-保单号={}", param.getEventJob().getEventBusinessCode())));
        }

        for (int i = 0; i < surrenderDetails.size(); i++) {
            EpPreserveSurrenderInsuredDto insuredDto = surrenderDetails.get(i);
            List<EpPreserveProductDto> surrenderProductDtos = insuredDto.getInsuredProductList();

            BigDecimal dynamicPremium = BigDecimal.ZERO;
            BigDecimal totalPremium = surrenderProductDtos.stream().map(EpPreserveProductDto::getPremium).reduce(BigDecimal.ZERO, BigDecimal::add);

            for (int j = 0; j < surrenderProductDtos.size(); j++) {
                EpPreserveProductDto productDto = surrenderProductDtos.get(j);

                //计算退保保费
                BigDecimal premium = insuredDto.getInsuredSurrenderPremium().multiply(productDto.getPremium()).divide(totalPremium, 2, BigDecimal.ROUND_HALF_UP);
                if (dynamicPremium.abs().add(premium.abs()).compareTo(insuredDto.getInsuredSurrenderPremium().abs()) > 0 || j == surrenderProductDtos.size() - 1) {
                    premium = insuredDto.getInsuredSurrenderPremium().subtract(dynamicPremium);
                }
                dynamicPremium = dynamicPremium.add(premium);

                EpProductInfoVo product = new EpProductInfoVo();
                BeanUtils.copyProperties(productDto, product);
                Integer renewalPeriod =param.getPreservationDetail().getRenewalPeriod()!=null?param.getPreservationDetail().getRenewalPeriod():param.getPreservationDetail().getRenewalTermPeriod();

                String key = keyCostInfo(param.getPolicyInfo().getContractCode(),productDto.getProductCode(),insuredDto.getInsuredCode(),renewalPeriod);
                log.info("支出端-基础佣金-退保事件查询历史团险佣金记录key为:{}",key);
                if(oldMap.containsKey(key)){
                    //按历史佣金记录进行退保
                    log.info("支出端-基础佣金-历史佣金记录存在，按历史佣金记录进行退保");
                    List<SettlementCostInfoEntity> olds = oldMap.get(key);
                    List<SettlementCostInfoEntity>  costs = builderInsuredCostInfoByOldCostInfo(param,insuredDto,product,olds,premium,premium,renewalPeriod);
                    if(olds.size() != costs.size()){

                    }
                    costInfoEntities.addAll(costs);

                }else{
                    //按佣金配置进行退保
                    log.info("支出端-基础佣金-历史佣金记录不存在，按佣金配置进行退保计算");
                    PolicyProductPremInput input = commissionConfigService.buildBasicCommissionConfigParam(param.getPolicyInfo(), param.getInsuranceType(), product,insuredDto.getInsuredPolicyAge(), renewalPeriod, renewalPeriod);
                    List<CostBasicCommissionConfigDto> configList = commissionConfigService.getCostBasicCommissionConfig(param.getPolicyNo(),param.getPolicyInfo().getPolicyProductType(), input);

                    builderInsuredCostInfoByCostRateConfig(param, insuredDto,product,input,configList,premium,premium,renewalPeriod,costInfoEntities,true);
                }
            }
        }
    }



    /*************** 长险退保 begin*****************************/
    private void builderLongPolicySurrenderCostInfo(SettlementEventJobEntity eventJob,
                                                    CostSubjectEnum subjectEnum,
                                                    SettlementEventTypeEnum eventType,
                                                    Map<String, ProductBase> productMap,
                                                    EpContractInfoVo policyInfo,
                                                    PolicyPreservationDetailDto preservationDetail,
                                                    List<SettlementCostInfoEntity> costInfoEntities) {
        //长险退保查询历史的未确认未冲正的且为基础佣金的记录
        List<SettlementCostInfoEntity> oldList = listPolicyUnCorrectionCostInfoByContractCode(eventType.getEventName(),policyInfo.getContractCode(),policyInfo.getContractBaseInfo().getPolicyNo());

        //查询是否有首期记录，协议解约的情况下需要抛异常
        Optional<SettlementCostInfoEntity> first = oldList.stream().filter(o -> Objects.equals(o.getInitialEventCode(), PERSONAL_NEW_POLICY.getEventCode())).findFirst();
        if (!first.isPresent()) {
            if(Objects.equals(eventJob.getEventType(), SettlementEventTypeEnum.PROTOCOL_TERMINATION.getEventCode())){
                throw new GlobalException(SettlementExceptionEnum.C_B_LONG_SURROUND_OLD_NOT_EXIST.getException(StrUtil.format("支出端-基础佣金-{}事件-历史新契约佣金记录不存在-保单号={}",eventType.getEventName(), policyInfo.getContractBaseInfo().getPolicyNo())));
            }
        }
        //过滤所有险种编码、被保人编码、期数等于退保记录的险种编码、被保人编码、期数（增加期数是可能存在某期退了附加险，后面又新增了附加险）
        oldList = filterOldSurrenderCostInfo(oldList);

        for (SettlementCostInfoEntity old : oldList) {
            costInfoEntities.add(builderLongSurrenderCostInfoByOldCost(eventJob, eventType, policyInfo, old, preservationDetail));
        }

    }




    protected SettlementCostInfoEntity builderLongSurrenderCostInfoByOldCost(SettlementEventJobEntity eventJob,
                                                                             SettlementEventTypeEnum eventType, EpContractInfoVo policyInfo,
                                                                             SettlementCostInfoEntity old, PolicyPreservationDetailDto preservationDetail) {
        log.info("支出端-基础佣金--根据历史佣金记录计算【长险】退保佣金");

        SettlementCostInfoEntity bean = new SettlementCostInfoEntity();
        BeanUtils.copyProperties(old, bean);
        bean.setId(null);
        bean.setCostCode(PolicySettlementUtils.createCodeLastNumber("CC"));
        //记账时间处理
        bean.setSettlementTime(new Date());
        bean.setSettlementDate(bean.getSettlementTime());
        //事件编号
        bean.setEventSourceCode(eventJob.getPushEventCode());
        //事件信息
        if (Objects.nonNull(eventType)) {
            bean.setSettlementEventCode(eventType.getEventCode());
            bean.setSettlementEventDesc(eventType.getEventDesc());
            bean.setInitialEventCode(eventType.getEventCode());
            setCommissionTypeByEventType(eventType, bean);
        }

        bean.setSettlementGenerateType(1);

        //险种状态处理
        bean.setProductStatus(getProductStatusByEventCode(eventType.getEventCode()));

        //清除确认信息
        cleanConfirmInfo(bean);

        bean.setBusinessPremium(old.getBusinessPremium().negate());
        if(old.getDiscountPremium()!=null) {
            bean.setDiscountPremium(old.getDiscountPremium().negate());
        }
        bean.setBusinessAccountTime(getBusinessAccountTime(eventType,policyInfo,preservationDetail));
        //退保保费
        log.info("合同{}长险渠道:{}",old.getContractCode(),preservationDetail.getSellChannelCode());
        if (Objects.equals(preservationDetail.getSellChannelCode(), ZHNX_CHANNEL_CODE)) {
            //标准退保、当前期数大于1，回访成功的情况下就不扣钱
            boolean renewal2Surrender= Objects.equals(eventType.getEventCode(), STANDARD_SURRENDER.getEventCode())
                    && preservationDetail.getRenewalTermPeriod() > 1
                    && Objects.equals(policyInfo.getContractExtendInfo().getRevisitResult(), 1)
                    ;
            log.info("合同{}当前退保类型：{},当前期数：{},回访结果：{},是否扣费：{}",old.getContractCode(),eventType.getEventCode(),preservationDetail.getRenewalTermPeriod(),policyInfo.getContractExtendInfo().getRevisitResult(),renewal2Surrender);
            //标准退保、缴费类型为
            Optional<EpPersonalProductInfoVo> opt =  policyInfo.getInsuredInfoList().stream().flatMap(x -> x.getProductInfoList().stream()).filter(o->Objects.equals(o.getMainInsurance(),1)).findFirst();
            //长险趸交退保规则-过来犹豫期且回访成功退保，不追推广费。（2025-02-17去除）
            //2025-02-17 规则调整为：趸交保单：2023年4月1日0点之前交单，不追回推广费（一正，不追手续费）；2023年4月 1 日 0 点起交单，过犹且回访成功后退保，不追；不满足以上全追。
            boolean onlyOneTime = Objects.equals(eventType.getEventCode(), STANDARD_SURRENDER.getEventCode())
                    && (opt.isPresent() && Objects.equals(opt.get().getPeriodType(), PolicyPaymentTypeEnum.DJ.getCode()))
                    && (policyInfo.getContractBaseInfo().getOrderTime().before(DateUtil.parseDateTime(SINGLE_PAYMENT_SPLIT_TIME))
                       ||  (!policyInfo.getContractBaseInfo().getOrderTime().before(DateUtil.parseDateTime(SINGLE_PAYMENT_SPLIT_TIME))
                            && Objects.equals(policyInfo.getContractExtendInfo().getRevisitResult(), 1)
                            //且 交单时间满 12 月后退保
                            && DateUtil.offsetMonth(DateUtil.beginOfDay(policyInfo.getContractBaseInfo().getOrderTime()),12).isBefore(bean.getBusinessAccountTime())
                            )
                    );
            log.info("合同{}当前退保类型：{},缴费方式：{},回访结果：{},是否不追推广费：{}",old.getContractCode(),eventType.getEventCode(),opt.get().getPeriodType(),policyInfo.getContractExtendInfo().getRevisitResult(),onlyOneTime);
            if (renewal2Surrender || onlyOneTime) {
                bean.setPremium(BigDecimal.ZERO);
                bean.setCostAmount(BigDecimal.ZERO);
                bean.setGrantAmount(BigDecimal.ZERO);
            } else {
                bean.setPremium(old.getPremium().negate());
                bean.setCostAmount(old.getCostAmount().negate());
                bean.setGrantAmount(old.getGrantAmount().negate());

            }
        } else {
            if (LIST_CITY_ZERO_PREMIUM_EVENT.contains(eventType.getEventCode())) {
                bean.setPremium(BigDecimal.ZERO);
                bean.setCostAmount(BigDecimal.ZERO);
                bean.setGrantAmount(BigDecimal.ZERO);
            } else {
                bean.setPremium(old.getPremium().negate());
                bean.setCostAmount(old.getCostAmount().negate());
                bean.setGrantAmount(old.getGrantAmount().negate());
            }
        }

        //佣金配置是否可变(已确认 或者 被退保记录就是不可变（手工冲正）)
        if(Objects.equals(old.getConfirmStatus(),ConfirmStatusEnum.CONFIRMED.getCode())
                || Objects.equals(old.getImmutableFlag(),1)){
            bean.setImmutableFlag(1);
        }
        //生成的长险暂发、补发的记录
        bean.setAutoCostCode(null);
        bean.setDocumentCode(null);
        bean.setCostSettlementCycle(null);
        bean.setGrantMonth(null);

        return bean;
    }
    /*************** 长险退保 end*****************************/

    /*************** 短险退保 begin*****************************/


    private void builderStandardSurrenderByInsuredInfo(BaseSurrenderDto param, List<SettlementCostInfoEntity> costInfoEntities) {
        log.info("支出端-基础佣金--根据被保人、险种计算【短险】退保佣金");
        //退保查询历史的未确认未冲正的且为基础佣金的记录
        //List<SettlementCostInfoEntity> oldList = listPolicyUnCorrectionCostInfoByContractCode(param.getEventType().getEventName(),param.getPolicyInfo().getContractCode(),param.getPolicyInfo().getContractBaseInfo().getPolicyNo());
        List<SettlementCostInfoEntity> oldList = settlementCostInfoService.listLongPolicyUnCorrectionCostInfoByContractCode(param.getPolicyInfo().getContractCode());
        //过滤所有险种编码、被保人编码、期数等于退保记录的险种编码、被保人编码、期数（增加期数是可能存在某期退了附加险，后面又新增了附加险）
        oldList = filterOldSurrenderCostInfo(oldList);
        //安合同号+险种编码+被保人编码+期数
        Map<String,List<SettlementCostInfoEntity>> oldMap = LambdaUtils.groupBy(oldList,o-> keyCostInfo(o.getContractCode() , o.getProductCode() , o.getInsuredCode() , o.getRenewalPeriod()));

        List<EpPreserveSurrenderInsuredDto> surrenderDetails = param.getPreservationDetail().getSurrenderDetail().getInsuredList();
        //是否有被保人退保明细金额
        if(CollectionUtils.isEmpty(surrenderDetails)){
            throw new GlobalException(SettlementExceptionEnum.C_B_SURRENDER_DETAIL_NOT_EXIST.getException(StrUtil.format("支出端-基础佣金-该保单{}退保明细记录不存在,退保信息：{}", param.getPolicyNo(),param.getPreservationDetail())));
        }
        /*   因保单中险种拆分去除
        boolean haveSurrenderPremium = surrenderDetails.get(0).getInsuredSurrenderPremium() == null ? false : true;
        BigDecimal totalPremium = BigDecimal.ZERO;
        BigDecimal dynamicPremium = BigDecimal.ZERO;
        if (!haveSurrenderPremium) {
            for (EpPreserveSurrenderInsuredDto dto : surrenderDetails) {
                totalPremium = totalPremium.add(dto.getInsuredProductList().stream().map(EpPreserveProductDto::getPremium).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
        }
         */
        for (int i = 0; i < surrenderDetails.size(); i++) {
            EpPreserveSurrenderInsuredDto dto = surrenderDetails.get(i);
            Optional<EpInsuredInfoVo> opt = param.getPolicyInfo().getInsuredInfoList().stream().filter(o -> Objects.equals(o.getInsuredCode(), dto.getInsuredCode())).findFirst();
            if (!opt.isPresent()) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-标准退保人员明细明细在保单详情明细中不存在-保单号={},被保人：{}", param.getEventJob().getEventBusinessCode(), dto.getInsuredCode())));
            }
            EpInsuredInfoVo insured = opt.get();

            List<EpPreserveProductDto> surrenderProductDtos = dto.getInsuredProductList();
            /*  因保单中险种拆分去除
                if (haveSurrenderPremium) {
                dynamicPremium = BigDecimal.ZERO;
                totalPremium = surrenderProductDtos.stream().map(EpPreserveProductDto::getPremium).reduce(BigDecimal.ZERO, BigDecimal::add);
            }*/
            for (int j = 0; j < surrenderProductDtos.size(); j++) {
                EpPreserveProductDto productDto = surrenderProductDtos.get(j);

                Optional<EpPersonalProductInfoVo> productOpt = insured.getProductInfoList().stream().filter(p -> Objects.equals(p.getProductCode(), productDto.getProductCode())).findFirst();
                EpProductInfoVo product = productOpt.get();
                //计算退保保费
                BigDecimal surrenderPremium = productDto.getSurrenderPremium();
                /*  因保单中险种拆分去除
                BigDecimal surrenderPremium;
                if (haveSurrenderPremium) {
                    surrenderPremium = dto.getInsuredSurrenderPremium().multiply(productDto.getPremium()).divide(totalPremium, 2, BigDecimal.ROUND_HALF_UP);
                    if (dynamicPremium.abs().add(surrenderPremium.abs()).compareTo(dto.getInsuredSurrenderPremium().abs()) > 0 || j == surrenderProductDtos.size() - 1) {
                        surrenderPremium = dto.getInsuredSurrenderPremium().subtract(dynamicPremium);
                    }
                    dynamicPremium = dynamicPremium.add(surrenderPremium);
                } else {
                    surrenderPremium = param.getPreservationDetail().getSurrenderCash().multiply(productDto.getPremium()).divide(totalPremium, 2, BigDecimal.ROUND_HALF_UP);
                    if (dynamicPremium.abs().add(surrenderPremium.abs()).compareTo(param.getPreservationDetail().getSurrenderCash().abs()) > 0 || (i == surrenderDetails.size() - 1 && j == surrenderProductDtos.size() - 1)) {
                        surrenderPremium = param.getPreservationDetail().getSurrenderCash().subtract(dynamicPremium);
                    }
                    dynamicPremium = dynamicPremium.add(surrenderPremium);
                }*/
                Integer renewalPeriod = param.getPreservationDetail().getRenewalPeriod()!=null?param.getPreservationDetail().getRenewalPeriod():param.getPreservationDetail().getRenewalTermPeriod();
                String key = keyCostInfo(param.getPolicyInfo().getContractCode(),productDto.getProductCode(),dto.getInsuredCode(),renewalPeriod);
                log.info("支出端-基础佣金-退保事件查询历史佣金记录key为:{}",key);
                if(oldMap.containsKey(key)){
                    //按历史佣金记录进行退保
                    log.info("支出端-基础佣金-历史佣金记录存在，按历史佣金记录进行退保");
                    List<SettlementCostInfoEntity> olds = oldMap.get(key);
                    List<SettlementCostInfoEntity> costs = builderInsuredCostInfoByOldCostInfo(param,dto,product,olds,surrenderPremium,surrenderPremium,renewalPeriod);
                    if(olds.size() != costs.size()){
                        throw new GlobalException(SettlementExceptionEnum.C_B_SURRENDER_DETAIL_NOT_EQUALS_OLD.getException(StrUtil.format("支出端-基础佣金-根据历史记录计算出的退保记录数与历史记录数不一致-保单号={},被保人={},key={}", param.getEventJob().getEventBusinessCode(), dto.getInsuredCode(),key)));
                    }
                    costInfoEntities.addAll(costs);
                }else{
                    //按佣金配置进行退保
                    log.info("支出端-基础佣金-历史佣金记录不存在，按佣金配置进行退保计算");
                    PolicyProductPremInput input = commissionConfigService.buildBasicCommissionConfigParam(param.getPolicyInfo(), param.getInsuranceType(), product,insured.getInsuredPolicyAge(), renewalPeriod, renewalPeriod);
                    List<CostBasicCommissionConfigDto> configList = commissionConfigService.getCostBasicCommissionConfig(param.getPolicyNo(),dto.getAddEndorsementNo(),param.getPolicyInfo().getPolicyProductType(), input);

                    builderInsuredCostInfoByCostRateConfig(param, dto,product,input,configList,surrenderPremium,surrenderPremium,renewalPeriod,costInfoEntities,false);
                }
            }
        }
    }

    /**
     * 退保金额 保单中心拆分至被保人险种纬度
     * @param param
     * @param costInfoEntities
     */
    private void builderNewStandardSurrenderByInsuredInfo(BaseSurrenderDto param, List<SettlementCostInfoEntity> costInfoEntities) {
        log.info("支出端-基础佣金--根据被保人、险种计算【短险】退保佣金");
        //长险退保查询历史的未确认未冲正的且为基础佣金的记录
        List<SettlementCostInfoEntity> oldList = listPolicyUnCorrectionCostInfoByContractCode(param.getEventType().getEventName(),param.getPolicyInfo().getContractCode(),param.getPolicyInfo().getContractBaseInfo().getPolicyNo());

        //过滤所有险种编码、被保人编码、期数等于退保记录的险种编码、被保人编码、期数（增加期数是可能存在某期退了附加险，后面又新增了附加险）
        oldList = filterOldSurrenderCostInfo(oldList);
        //安合同号+险种编码+被保人编码+期数
        Map<String,List<SettlementCostInfoEntity>> oldMap = LambdaUtils.groupBy(oldList,o-> keyCostInfo(o.getContractCode() , o.getProductCode() , o.getInsuredCode() , o.getRenewalPeriod()));

        List<EpPreserveSurrenderInsuredDto> surrenderDetails = param.getPreservationDetail().getSurrenderDetail().getInsuredList();
        //是否有被保人退保明细金额
        if(CollectionUtils.isEmpty(surrenderDetails)){
            throw new GlobalException(SettlementExceptionEnum.C_B_SURRENDER_DETAIL_NOT_EXIST.getException(StrUtil.format("支出端-基础佣金-该保单{}退保明细记录不存在,退保信息：{}", param.getPolicyNo(),param.getPreservationDetail())));
        }
        boolean haveSurrenderPremium = surrenderDetails.get(0).getInsuredProductList() != null
                && surrenderDetails.get(0).getInsuredProductList().get(0).getSurrenderPremium()!=null? true : false;
        BigDecimal totalPremium = BigDecimal.ZERO;
        BigDecimal dynamicPremium = BigDecimal.ZERO;
        if (!haveSurrenderPremium) {
            for (EpPreserveSurrenderInsuredDto dto : surrenderDetails) {
                totalPremium = totalPremium.add(dto.getInsuredProductList().stream().map(EpPreserveProductDto::getPremium).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
        }

        for (int i = 0; i < surrenderDetails.size(); i++) {
            EpPreserveSurrenderInsuredDto dto = surrenderDetails.get(i);
            Optional<EpInsuredInfoVo> opt = param.getPolicyInfo().getInsuredInfoList().stream().filter(o -> Objects.equals(o.getInsuredCode(), dto.getInsuredCode())).findFirst();
            if (!opt.isPresent()) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-标准退保人员明细明细在保单详情明细中不存在-保单号={},被保人：{}", param.getEventJob().getEventBusinessCode(), dto.getInsuredCode())));
            }
            EpInsuredInfoVo insured = opt.get();

            List<EpPreserveProductDto> surrenderProductDtos = dto.getInsuredProductList();

            for (int j = 0; j < surrenderProductDtos.size(); j++) {
                EpPreserveProductDto productDto = surrenderProductDtos.get(j);

                Optional<EpPersonalProductInfoVo> productOpt = insured.getProductInfoList().stream().filter(p -> Objects.equals(p.getProductCode(), productDto.getProductCode())).findFirst();
                EpProductInfoVo product = productOpt.get();
                //计算退保保费
                BigDecimal surrenderPremium;
                if (haveSurrenderPremium) {
                    surrenderPremium = productDto.getSurrenderPremium();
                } else {
                    surrenderPremium = param.getPreservationDetail().getSurrenderCash().multiply(productDto.getPremium()).divide(totalPremium, 2, BigDecimal.ROUND_HALF_UP);
                    if (dynamicPremium.abs().add(surrenderPremium.abs()).compareTo(param.getPreservationDetail().getSurrenderCash().abs()) > 0 || (i == surrenderDetails.size() - 1 && j == surrenderProductDtos.size() - 1)) {
                        surrenderPremium = param.getPreservationDetail().getSurrenderCash().subtract(dynamicPremium);
                    }
                    dynamicPremium = dynamicPremium.add(surrenderPremium);
                }
                Integer renewalPeriod = param.getPreservationDetail().getRenewalPeriod()!=null?param.getPreservationDetail().getRenewalPeriod():param.getPreservationDetail().getRenewalTermPeriod();
                String key = keyCostInfo(param.getPolicyInfo().getContractCode(),productDto.getProductCode(),dto.getInsuredCode(),renewalPeriod);
                log.info("支出端-基础佣金-退保事件查询历史佣金记录key为:{}",key);
                if(oldMap.containsKey(key)){
                    //按历史佣金记录进行退保
                    log.info("支出端-基础佣金-历史佣金记录存在，按历史佣金记录进行退保");
                    List<SettlementCostInfoEntity> olds = oldMap.get(key);
                    List<SettlementCostInfoEntity> costs = builderInsuredCostInfoByOldCostInfo(param,dto,product,olds,surrenderPremium,surrenderPremium,renewalPeriod);
                    if(olds.size() != costs.size()){
                        throw new GlobalException(SettlementExceptionEnum.C_B_SURRENDER_DETAIL_NOT_EQUALS_OLD.getException(StrUtil.format("支出端-基础佣金-根据历史记录计算出的退保记录数与历史记录数不一致-保单号={},被保人={},key={}", param.getEventJob().getEventBusinessCode(), dto.getInsuredCode(),key)));
                    }
                    costInfoEntities.addAll(costs);
                }else{
                    //按佣金配置进行退保
                    log.info("支出端-基础佣金-历史佣金记录不存在，按佣金配置进行退保计算");
                    PolicyProductPremInput input = commissionConfigService.buildBasicCommissionConfigParam(param.getPolicyInfo(), param.getInsuranceType(), product,insured.getInsuredPolicyAge(), renewalPeriod, renewalPeriod);
                    List<CostBasicCommissionConfigDto> configList = commissionConfigService.getCostBasicCommissionConfig(param.getPolicyNo(),dto.getAddEndorsementNo(),param.getPolicyInfo().getPolicyProductType(), input);

                    builderInsuredCostInfoByCostRateConfig(param, dto,product,input,configList,surrenderPremium,surrenderPremium,renewalPeriod,costInfoEntities,false);
                }
            }
        }
    }
    private String keyCostInfo(String contractCode,String productCode,String insuredCode,Integer renewalPeriod){
        return StrUtil.format("{}_{}_{}_{}",contractCode,productCode,insuredCode,renewalPeriod);
    }

    private String getProductStatusByEventCode(String eventCode){
        if(Objects.equals(eventCode,SettlementEventTypeEnum.HESITATE_SURRENDER.getEventCode())){
            return ProductStatusEnum.HESITATION_CANCEL.getCode();
        }else if(Objects.equals(eventCode,SettlementEventTypeEnum.STANDARD_SURRENDER.getEventCode())){
            return ProductStatusEnum.CANCELLATION.getCode();
        }else if (Objects.equals(eventCode,SettlementEventTypeEnum.PROTOCOL_TERMINATION.getEventCode())){
            return ProductStatusEnum.AGREEMENT_TERMINATION.getCode();
        }else{
            return "";
        }
    }




    /*************** 短险退保 end*****************************/
}
