package com.mpolicy.settlement.core.modules.commission.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.settlement.core.common.commission.dto.CommissionBasicPolicyPrem;
import com.mpolicy.settlement.core.enums.EventSendStatusEnum;
import com.mpolicy.settlement.core.enums.PremChangeTypeEnum;
import com.mpolicy.settlement.core.enums.SettlementProtocolEventEnum;
import com.mpolicy.settlement.core.helper.SettlementProtocolHelper;
import com.mpolicy.settlement.core.modules.commission.entity.CommissionBasicPolicyPremEntity;
import com.mpolicy.settlement.core.modules.commission.service.CommissionBasicPolicyPremService;
import com.mpolicy.settlement.core.modules.commission.service.CommissionManageService;
import com.mpolicy.settlement.core.modules.commission.utils.ProductPremUtil;
import com.mpolicy.settlement.core.modules.common.service.SettlementPremChangeLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/08/05
 */
@Slf4j
@Service("commissionManageService")
public class CommissionManageServiceImpl implements CommissionManageService {

    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private CommissionBasicPolicyPremService commissionBasicPolicyPremService;
    @Autowired
    private SettlementPremChangeLogService settlementPremChangeLogService;

    public void doPolicyPremHandler(List<CommissionBasicPolicyPrem> policyPremList, String opUser){
        if(CollectionUtils.isEmpty(policyPremList)){
            return ;
        }
        log.info("开始处理一单一议费率信息，对象转换");
        List<CommissionBasicPolicyPremEntity> entityList = getCommissionBasicPolicyPremEntityList(policyPremList);

        log.info("根据保单号从数据库中获取已存在的费率信息");
        List<String> policyNos = entityList.stream().map(CommissionBasicPolicyPremEntity::getPolicyNo).distinct()
                .collect(Collectors.toList());
        Map<String, CommissionBasicPolicyPremEntity> policyPremMap =
                commissionBasicPolicyPremService.lambdaQuery().in(CommissionBasicPolicyPremEntity::getPolicyNo, policyNos).list().stream()
                        .collect(Collectors.toMap(ProductPremUtil::getCommissionPolicyFactor, v -> v, (v1, v2) -> {
                            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                                    StrUtil.format("数据库中存在相同premCode他们的ID是[{}-{}]的数据,请联系开发人员检查", v1.getId(),
                                            v2.getId())));
                        }));

        log.info("导入的费率信息分组处理");
        List<CommissionBasicPolicyPremEntity> insertPolicyPremList = new ArrayList<>();
        List<CommissionBasicPolicyPremEntity> updatePolicyPremList = new ArrayList<>();
        List<String> removePolicyPremList = new ArrayList<>();
        entityList.forEach(action -> {
            // 费率的匹配因子一样
            if (policyPremMap.containsKey(action.getPremCode())) {
                log.info("费率的匹配因子一样");
                CommissionBasicPolicyPremEntity commissionBasicPolicyPrem = policyPremMap.get(action.getPremCode());
                log.info("判断他们的费率信息是否一样{}/{}",ProductPremUtil.getCommissionPolicyRateCode(action),ProductPremUtil.getCommissionPolicyRateCode(commissionBasicPolicyPrem));
                //判断他们的费率信息是否一样,如果不一样才处理
                if (!ProductPremUtil.getCommissionPolicyRateCode(action)
                        .equals(ProductPremUtil.getCommissionPolicyRateCode(commissionBasicPolicyPrem))) {
                    action.setId(commissionBasicPolicyPrem.getId());
                    String premCode = ProductPremUtil.getCommissionPolicyPremCode(action);
                    action.setPremCode(premCode);
                    updatePolicyPremList.add(action);
                    removePolicyPremList.add(commissionBasicPolicyPrem.getPremCode());
                }
            } else {
                // 匹配因子不一样直接进行追加
                log.info("匹配因子不一样直接进行追加");
                String premCode = ProductPremUtil.getCommissionPolicyPremCode(action);
                action.setPremCode(premCode);
                insertPolicyPremList.add(action);
            }
        });
        log.info("分组结果：新增{},变更{},删除{}",insertPolicyPremList.size(),updatePolicyPremList.size(),removePolicyPremList.size());
        //存储一单一议费率信息
        savePolicyPrem(opUser,insertPolicyPremList,updatePolicyPremList,removePolicyPremList);
    }

    public List<CommissionBasicPolicyPremEntity> getCommissionBasicPolicyPremEntityList(List<CommissionBasicPolicyPrem> policyPremList){
        Set<String> premCodeSet = new HashSet<>();
        return  policyPremList.stream().map(c->{
            CommissionBasicPolicyPremEntity entity = new CommissionBasicPolicyPremEntity();
            BeanUtils.copyProperties(c,entity);

            // 匹配因子唯一值,当前只左存储,后面还会更新这个值.
            String premCode = ProductPremUtil.getCommissionPolicyFactor(entity);
            if (premCodeSet.contains(premCode)) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                        StrUtil.format("保单号{}存在重复的佣金记录key:{},请检查",entity.getPolicyNo(), premCode)));
            }else{
                premCodeSet.add(premCode);
            }
            entity.setPremCode(premCode);
            return entity;
        }).collect(Collectors.toList());
    }

    public void savePolicyPrem(String opUser,List<CommissionBasicPolicyPremEntity> insertPolicyPremList,
                               List<CommissionBasicPolicyPremEntity> updatePolicyPremList,
                               List<String> removePolicyPremList){

        if (removePolicyPremList.isEmpty() && updatePolicyPremList.isEmpty() && insertPolicyPremList.isEmpty()) {
            log.info("数据没有变更不处理.....");
            return;
        }
        String pushEventCode = CommonUtils.createCodeLastNumber("PE");
        log.info("开始存储一单一议费率信息，生成的变更事件编码：{}",pushEventCode);
        JSONObject msgData = createPolicyPremMsgJSONObject(pushEventCode,opUser,insertPolicyPremList,updatePolicyPremList,removePolicyPremList);
        Boolean result = transactionTemplate.execute(new TransactionCallback<Boolean>() {
            @Override
            public Boolean doInTransaction(TransactionStatus transactionStatus) {
                try {
                    log.info("事件编码{}开始批量插入一单一议费率信息",pushEventCode);
                    //批量操作插入记录
                    commissionBasicPolicyPremService.batchInsert(insertPolicyPremList);
                    //批量操作更新记录
                    commissionBasicPolicyPremService.batchUpdateById(updatePolicyPremList);
                    log.info("事件编码{}开始记录变更日志",pushEventCode);
                    //操作日志保存
                    settlementPremChangeLogService.saveNeedSendSettlementPremChangeLog(pushEventCode,
                            PremChangeTypeEnum.COMMISSION_POLICY.getCode(), pushEventCode, msgData.toJSONString(), EventSendStatusEnum.DOING.getCode());
                    return Boolean.TRUE;
                } catch (Exception e){
                    //回滚
                    transactionStatus.setRollbackOnly();
                    return Boolean.FALSE;
                }
            }
        });
        log.info("事件编码{}一单一议变更记录插入结果:{},",pushEventCode,result);
        if(result){
            // 推送事件
            try{
                log.info("事件编码{}开始发送MQ消息",pushEventCode);
                SettlementProtocolHelper.pushSettlementProtocolEvent(pushEventCode,
                        SettlementProtocolEventEnum.COMMISSION_POLICY_PREM_CHANGE, msgData);
                log.info("事件编码{}的MQ消息发送完成",pushEventCode);
                settlementPremChangeLogService.updateSendStatus(pushEventCode,EventSendStatusEnum.DOING.getCode(),EventSendStatusEnum.SUCCESS.getCode());
            }catch (Exception e){
                log.info("事件编码{}的MQ消息发送失败");
                settlementPremChangeLogService.updateSendStatus(pushEventCode,EventSendStatusEnum.DOING.getCode(),EventSendStatusEnum.FAIL.getCode());
            }
        }
        log.info("事件编码{}处理完成",pushEventCode);
    }
    private JSONObject createPolicyPremMsgJSONObject(String pushEventCode,String opUser,List<CommissionBasicPolicyPremEntity> insertPolicyPremList,
                                     List<CommissionBasicPolicyPremEntity> updatePolicyPremList,
                                     List<String> removePolicyPremList){
        //MQ通知
        JSONObject msgData = new JSONObject();
        //操作时间"
        msgData.put("opeTime", DateUtil.date().toString());
        //操作人
        msgData.put("opeName", opUser);
        //事件编码
        msgData.put("pushEventCode", pushEventCode);
        // 移除的保单数据
        msgData.put("removePolicyPremList", removePolicyPremList);
        // 修改的保单数据
        msgData.put("updatePolicyPremList", JSONObject.toJSONString(
                updatePolicyPremList.stream().map(CommissionBasicPolicyPremEntity::getPremCode)
                        .collect(Collectors.toList())));
        // 新增的保单号数据
        msgData.put("insertPolicyPremList", JSONObject.toJSONString(
                insertPolicyPremList.stream().map(CommissionBasicPolicyPremEntity::getPremCode)
                        .collect(Collectors.toList())));
        return msgData;
    }
}
