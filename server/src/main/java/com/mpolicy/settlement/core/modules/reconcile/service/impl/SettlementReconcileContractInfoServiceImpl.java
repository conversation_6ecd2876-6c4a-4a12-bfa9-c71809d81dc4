package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementReconcileContractInfoDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileContractInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcileContractInfoService;
import org.springframework.stereotype.Service;

@Service("settlementReconcileContractInfoService")
public class SettlementReconcileContractInfoServiceImpl extends ServiceImpl<SettlementReconcileContractInfoDao, SettlementReconcileContractInfoEntity> implements SettlementReconcileContractInfoService {


}
