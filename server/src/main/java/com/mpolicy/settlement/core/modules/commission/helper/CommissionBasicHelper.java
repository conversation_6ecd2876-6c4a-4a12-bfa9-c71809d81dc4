package com.mpolicy.settlement.core.modules.commission.helper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.common.Constant;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileSubjectOnlineEnum;
import com.mpolicy.settlement.core.enums.SettlementExceptionEnum;
import com.mpolicy.settlement.core.modules.commission.entity.CommissionBasicPolicyPremEntity;
import com.mpolicy.settlement.core.modules.commission.entity.CommissionBasicPremEntity;
import com.mpolicy.settlement.core.modules.commission.helper.InnerCompanyHelper;
import com.mpolicy.settlement.core.modules.commission.service.CommissionBasicInfoService;
import com.mpolicy.settlement.core.modules.commission.service.CommissionBasicPolicyPremService;
import com.mpolicy.settlement.core.modules.commission.service.CommissionBasicPremService;
import com.mpolicy.settlement.core.modules.protocol.service.OrgInfoService;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyPremInput;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyPremResult;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyProductPrem;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyProductPremResult;
import com.mpolicy.settlement.core.modules.reconcile.helper.ReconcileSubjectHelper;
import com.mpolicy.settlement.core.utils.PremUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 基础佣金处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CommissionBasicHelper {

    private static CommissionBasicHelper commissionBasicHelper;
    @Autowired
    private CommissionBasicInfoService commissionBasicInfoService;
    @Autowired
    private CommissionBasicPremService commissionBasicPremService;
    @Autowired
    private CommissionBasicPolicyPremService commissionBasicPolicyPremService;
    @Autowired
    private OrgInfoService orgInfoService;

    @PostConstruct
    public void init() {
        commissionBasicHelper = this;
        commissionBasicHelper.commissionBasicInfoService = this.commissionBasicInfoService;
        commissionBasicHelper.commissionBasicPremService = this.commissionBasicPremService;
        commissionBasicHelper.orgInfoService = this.orgInfoService;
        commissionBasicHelper.commissionBasicPolicyPremService = this.commissionBasicPolicyPremService;
    }

    /**
     * 获取基础佣金费率 一个渠道仅仅可以匹配到一条数据
     * 支出端需要提供基础佣金（加佣除外）配置接口有2个：
     * 1、基础佣金一单一议的查询接口；
     * 2、根据险种信息(险种编码、缴费期、保障期、续投/新报、续期标志)获取支出端的佣金比例列表
     *
     * @param input
     * @return
     */
    public static List<PolicyProductPremResult> queryPolicyProductPrem(PolicyProductPrem input) {
        if (!StrUtil.isAllNotBlank(input.getProductCode(), input.getEffectiveDate(), input.getChannelCode())) {
            log.warn("[基础佣金]匹配费率参数:{} 请求参数存在必填项为空", JSONUtil.toJsonStr(input));
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("请求参数存在必填项为空,请检查."));
        }
        //获取基础佣金费率表
        List<CommissionBasicPremEntity> productPremList = commissionBasicHelper.commissionBasicPremService.lambdaQuery()
                .eq(CommissionBasicPremEntity::getProductCode, input.getProductCode())
                .list();
        if (productPremList.isEmpty()) {
            log.warn("[基础佣金]匹配费率参数:{} 根据险种编码没有匹配到响应的费率信息", JSONUtil.toJsonStr(input));
            throw new GlobalException(SettlementExceptionEnum.CONFIG_PARAM_PRODUCT_NOT_MATCH.getException(StrUtil.format("支出端-获取基础佣金配置, 根据险种编码没有匹配到响应的费率信息,入参：{}",input)));
            //return Collections.emptyList();
        }
        //匹配科目类型
        List<String> costList = CollUtil.newArrayList(ReconcileSubjectOnlineEnum.FIRST_YR_COMM.getName());
        productPremList = productPremList.stream().filter(f -> costList.contains(f.getCostType())).collect(Collectors.toList());
        if (productPremList.isEmpty()) {
            log.warn("[基础佣金]匹配费率参数:{} 根据费用类型没有匹配到响应的费率信息", JSONUtil.toJsonStr(input ));
            throw new GlobalException(SettlementExceptionEnum.CONFIG_PARAM_COST_TYPE_NOT_MATCH.getException(StrUtil.format("支出端-获取基础佣金配置, 根据费用类型没有匹配到响应的费率信息,入参：{}",input)));
            //return Collections.emptyList();
        }
        // 匹配适用分公司
        List<String> orgList = CollUtil.newArrayList(input.getOrgCode(), Constant.NATIONWIDE_ORG_CODE);
        productPremList = productPremList.stream().filter(f -> orgList.contains(f.getOrgCode())).collect(Collectors.toList());
        if (productPremList.isEmpty()) {
            log.warn("[基础佣金]匹配费率参数:{} 根据适用分公司没有匹配到响应的费率信息", JSONUtil.toJsonStr(input));
            throw new GlobalException(SettlementExceptionEnum.CONFIG_PARAM_ORG_CODE_NOT_MATCH.getException(StrUtil.format("支出端-获取基础佣金配置, 根据适用分公司没有匹配到响应的费率信息,入参：{}",input)));
            //return Collections.emptyList();
        }

        // 匹配时间
        productPremList = productPremList.stream()
                .filter(f -> DateUtil.isIn(DateUtil.parseDate(input.getEffectiveDate()), DateUtil.parseDate(f.getEffectiveStartDate()),DateUtil.endOfDay(DateUtil.parseDate(f.getEffectiveEndDate())) ))
                .collect(Collectors.toList());
        if (productPremList.isEmpty()) {
            List<String> timeList = productPremList.stream()
                .map(m -> m.getEffectiveStartDate() + "~" + DateUtil.endOfDay(DateUtil.parseDate(m.getEffectiveEndDate())))
                .distinct().collect(Collectors.toList());
            log.warn("[基础佣金]匹配费率参数:{} 根据时间区间没有匹配到响应的费率信息[{}]", JSONUtil.toJsonStr(input),timeList);
            throw new GlobalException(SettlementExceptionEnum.CONFIG_PARAM_TIME_LIST_NOT_MATCH.getException(StrUtil.format("支出端-获取基础佣金配置, 根据时间区间{}没有匹配到响应的费率信息,入参：{}",timeList,input)));
            //return Collections.emptyList();
        }

        // 先判断有没有配置指定渠道,如果配置了那么就不要通用渠道的数据了,如果没有配置,那么只取默认渠道的数据
        List<CommissionBasicPremEntity> commissionBasicPremList = productPremList.stream().filter(f -> f.getChannelCode().equals(input.getChannelCode())).collect(Collectors.toList());
        if (commissionBasicPremList.isEmpty()) {
            log.warn("[基础佣金]指定渠道没有获取到费率信息,开始匹配通用渠道数据");
            commissionBasicPremList = productPremList.stream().filter(f -> f.getChannelCode().equals(Constant.NATIONWIDE_CHANNEL_CODE)).collect(Collectors.toList());
        }
        if (commissionBasicPremList.isEmpty()) {
            log.warn(StrUtil.format("[基础佣金]保单={},小鲸险种={}没有匹配到相应的费率信息", input.getPolicyNo(), input.getProductCode()));
            return Collections.emptyList();
        }
        List<CommissionBasicPremEntity> resultProductPremList = CommissionBasicHelper.mateCommissionBasicPrem(commissionBasicPremList, input);
        if (resultProductPremList.isEmpty()) {
            log.warn(StrUtil.format("[基础佣金]保单={},小鲸险种={}没有匹配到相应的费率信息", input.getPolicyNo(), input.getProductCode()));
            return Collections.emptyList();
        }
        List<PolicyProductPremResult> resultList = new ArrayList<>();
        for (CommissionBasicPremEntity commissionBasicPrem : resultProductPremList) {
            PolicyProductPremResult result = CommissionBasicHelper.extractProductPrem(commissionBasicPrem, input.getRenewalYear());
            if (result != null) {
                String subjectCode = ReconcileSubjectHelper.matchSearchName(commissionBasicPrem.getCostType());
                if (StrUtil.isBlank(subjectCode)) {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                            StrUtil.format("科目={}科目信息未配置", commissionBasicPrem.getCostType())));
                }
                result.setSettlementSubjectCode(subjectCode);
                result.setSettlementSubjectName(commissionBasicPrem.getCostType());
                result.setReportOrgCode(input.getReportOrgCode());
                result.setReportOrgName(input.getReportOrgName());
                try {
                    result.setTaxRate(new BigDecimal(commissionBasicPrem.getTaxRate()));
                    result.setSettlementMethod(commissionBasicPrem.getSettlementMethod());
                } catch (Exception e) {
                    result.setTaxRate(BigDecimal.ZERO);
                    result.setSettlementMethod(StatusEnum.INVALID.getCode());
                }
                result.setSettlementCompanyCode(commissionBasicPrem.getSettlementCompanyCode());
                result.setSettlementCompanyName(commissionBasicPrem.getSettlementCompanyName());
                result.setBusinessCode(commissionBasicPrem.getCommissionCode());
                result.setPremCode(commissionBasicPrem.getPremCode());
                resultList.add(result);
            }
        }
        resultList.stream().collect(Collectors.groupingBy(PolicyProductPremResult::getSettlementSubjectCode)).forEach((key, m) -> {
            //判断命中的多条数据是不是一个结算机构的 如果是,那么报错处理
            boolean isSettlementCompanyDuplicate = m.stream()
                    .map(PolicyProductPremResult::getSettlementCompanyCode)
                    .collect(Collectors.groupingBy(name -> name, Collectors.counting()))
                    .values().stream()
                    .anyMatch(count -> count > 1);
            if (isSettlementCompanyDuplicate) {
                log.warn("[基础佣金]科目:{} 命中多条费率信息:{}", JSONUtil.toJsonStr(key), JSONUtil.toJsonStr(m));
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("保单={},产品={}科目={}配到费率多条费率信息,请检查", input.getPolicyNo(), input.getProductCode(), key)));
            }
        });
        log.info("[基础佣金]成功获取费率信息={}", JSONUtil.toJsonStr(resultList));
        return resultList;
    }

    /**
     * 查询一单一议
     *
     * @param input
     * @return
     */
    public static PolicyPremResult queryPolicyPrem(PolicyPremInput input) {
        log.info("[基础佣金]查询一单一议入参={}", JSONUtil.toJsonStr(input));
        List<CommissionBasicPolicyPremEntity> premList = commissionBasicHelper.commissionBasicPolicyPremService.lambdaQuery()
                .eq(CommissionBasicPolicyPremEntity::getPolicyNo, input.getPolicyNo())
                .eq(CommissionBasicPolicyPremEntity::getSettlementCompanyCode, input.getSettlementCompanyCode())
                .list();
        if (premList.isEmpty()) {
            log.info("[基础佣金]查询一单一议入参={},没有匹配到一单一议数据", JSONUtil.toJsonStr(input));
            return null;
        }
        // 判断有没有满足险种的数据,没有的话取 险种编码为空的数据
        if (StrUtil.isBlank(input.getProductCode())) {
            premList = premList.stream().filter(f -> StrUtil.isBlank(f.getProductCode())).collect(Collectors.toList());
        } else {
            List<CommissionBasicPolicyPremEntity> resultList = premList.stream().filter(f -> StrUtil.isNotBlank(f.getProductCode()) && input.getProductCode().equals(f.getProductCode()))
                    .collect(Collectors.toList());
            if (resultList.isEmpty()) {
                resultList = premList.stream().filter(f -> StrUtil.isBlank(f.getProductCode())).collect(Collectors.toList());
            }
            premList = resultList;
        }
        if (premList.isEmpty()) {
            return null;
        }
        // 批单号同险种编码
        if (StrUtil.isBlank(input.getBatchCode())) {
            premList = premList.stream().filter(f -> StrUtil.isBlank(f.getBatchCode())).collect(Collectors.toList());
        } else {
            List<CommissionBasicPolicyPremEntity> resultList = premList.stream().filter(f -> StrUtil.isNotBlank(f.getBatchCode()) && input.getBatchCode().equals(f.getBatchCode()))
                    .collect(Collectors.toList());
            if (resultList.isEmpty()) {
                resultList = premList.stream().filter(f -> StrUtil.isBlank(f.getBatchCode())).collect(Collectors.toList());
            }
            premList = resultList;
        }
        if (premList.isEmpty()) {
            return null;
        }

        // 保单年期
        if (input.getYear() == null) {
            premList = premList.stream().filter(f -> f.getYear() == null).collect(Collectors.toList());
        } else {
            List<CommissionBasicPolicyPremEntity> resultList =
                    premList.stream().filter(f -> f.getYear() != null && input.getYear().equals(f.getYear())).collect(Collectors.toList());
            if (resultList.isEmpty()) {
                resultList = premList.stream().filter(f -> f.getYear() == null).collect(Collectors.toList());
            }
            premList = resultList;
        }
        if (premList.isEmpty()) {
            return null;
        }

        // 缴费期数
        if (input.getPeriod() == null) {
            premList = premList.stream().filter(f -> f.getPeriod() == null).collect(Collectors.toList());
        } else {
            List<CommissionBasicPolicyPremEntity> resultList =
                    premList.stream().filter(f -> f.getPeriod() != null && input.getPeriod().equals(f.getPeriod())).collect(Collectors.toList());
            if (resultList.isEmpty()) {
                resultList = premList.stream().filter(f -> f.getPeriod() == null).collect(Collectors.toList());
            }
            premList = resultList;
        }
        if (premList.isEmpty()) {
            return null;
        }


        if (premList.size() > 1) {
            throw new GlobalException(SettlementExceptionEnum.CONFIG_A_SINGLE_PROPOSAL_EXCEPTION.getException(StrUtil.format("获取一单一议请求参数={},匹配到了多个费率信息", JSONUtil.toJsonStr(input))));
        }
        log.info("[基础佣金]查询一单一议入参={},正常匹配到费率信息={}", JSONUtil.toJsonStr(input), JSONUtil.toJsonStr(premList));
        PolicyPremResult policyPremResult = premList.stream().map(m -> {
            PolicyPremResult result = new PolicyPremResult();
            result.setProductName(m.getProductName());
            result.setPremCode(m.getPremCode());
            result.setYearRate(m.getCommissionRate());
            result.setVehicleVesselTax(m.getVehicleVesselTax());
            result.setVehicleVesselTaxRate(m.getVehicleVesselTaxRate());
            return result;
        }).findFirst().get();
        log.info("[基础佣金]匹配到的一单一议数据={}", JSONUtil.toJsonStr(policyPremResult));
        return policyPremResult;
    }


    /**
     * 匹配费率数据
     *
     * @param commissionBasicPremList 佣金配置
     * @param vo                      查询条件
     * @return
     */
    private static List<CommissionBasicPremEntity> mateCommissionBasicPrem(List<CommissionBasicPremEntity> commissionBasicPremList, PolicyProductPrem vo) {
        log.info("[基础佣金]费率匹配请求参数:{},开始匹配费率表={}", JSONUtil.toJsonStr(vo), JSONUtil.toJsonStr(commissionBasicPremList));
        List<CommissionBasicPremEntity> resultList = commissionBasicPremList.stream().filter(f -> {
            //主险险种编码
            if (!PremUtil.verifyParam(vo.getMainProductCode(), f.getMainProductCode())) {
                log.info("[基础佣金]保单={},premCode={}主险险种编码不匹配请求参数={},费率表参数={}", vo.getPolicyNo(), f.getPremCode(), vo.getMainProductCode(), f.getMainProductCode());
                return false;
            }
            //投保计划
            if (!PremUtil.verifyParam(vo.getProductPlan(), f.getProductPlan())) {
                log.info("[基础佣金]保单={},premCode={}投保计划不匹配请求参数={},费率表参数={}", vo.getPolicyNo(), f.getPremCode(), vo.getProductPlan(), f.getProductPlan());
                return false;
            }
            //满期年龄
            if (!PremUtil.verifyParam(vo.getExpireAge(), f.getExpireAge())) {
                log.info("[基础佣金]保单={},premCode={}满期年龄不匹配请求参数={},费率表参数={}", vo.getPolicyNo(), f.getPremCode(), vo.getExpireAge(), f.getExpireAge());
                return false;
            }
            //被保人性别
            if (!PremUtil.verifyParam(vo.getInsuredGender(), f.getInsuredGender())) {
                log.info("[基础佣金]保单={},premCode={}被保人性别不匹配请求参数={},费率表参数={}", vo.getPolicyNo(), f.getPremCode(), vo.getInsuredGender(), f.getInsuredGender());
                return false;
            }
            //投保人性别
            if (!PremUtil.verifyParam(vo.getApplicantGender(), f.getApplicantGender())) {
                log.info("[基础佣金]保单={},premCode={}投保人性别不匹配请求参数={},费率表参数={}", vo.getPolicyNo(), f.getPremCode(), vo.getApplicantGender(), f.getApplicantGender());
                return false;
            }
            //投保人年龄
            if (!PremUtil.verifyAge(vo.getApplicantAge(), f.getApplicantAge())) {
                log.info("[基础佣金]保单={},premCode={}投保计划不匹配请求参数={},费率表参数={}", vo.getPolicyNo(), f.getPremCode(), vo.getApplicantAge(), f.getApplicantAge());
                return false;
            }
            //被保人年龄
            if (!PremUtil.verifyAge(vo.getInsuredAge(), f.getInsuredAge())) {
                log.info("[基础佣金]保单={},premCode={}被保人年龄不匹配请求参数={},费率表参数={}", vo.getPolicyNo(), f.getPremCode(), vo.getInsuredAge(), f.getInsuredAge());
                return false;
            }
            //保障期间
            if (!PremUtil.verifyParam(vo.getCoveragePeriod(), f.getCoveragePeriod())) {
                log.info("[基础佣金]保单={},premCode={}保障期间不匹配请求参数={},费率表参数={}", vo.getPolicyNo(), f.getPremCode(), vo.getCoveragePeriod(), f.getCoveragePeriod());
                return false;
            }
            //缴费期间类型
            if (!PremUtil.verifyParam(vo.getPaymentPeriodType(), f.getPaymentPeriodType())) {
                log.info("[基础佣金]缴费间期不匹配请求参数={},费率表参数={}", vo.getPaymentPeriodType(), f.getPaymentPeriodType());
                return false;
            }
            //缴费方式
            if (!PremUtil.verifyParam(vo.getPaymentType(), f.getPaymentType())) {
                log.info("[基础佣金]保单={},premCode={}缴费方式不匹配请求参数={},费率表参数={}", vo.getPolicyNo(), f.getPremCode(), vo.getPaymentType(), f.getPaymentType());
                return false;
            }
            //缴费期
            if (!PremUtil.verifyParam(vo.getPaymentPeriod(), f.getPaymentPeriod())) {
                log.info("[基础佣金]保单={},premCode={}缴费期不匹配请求参数={},费率表参数={}", vo.getPolicyNo(), f.getPremCode(), vo.getPaymentPeriod(), f.getPaymentPeriod());
                return false;
            }
            //是否为自保件
            if (!PremUtil.verifyParam(vo.getSelfPreservation(), f.getSelfPreservation())) {
                log.info("[基础佣金]保单={},premCode={}是否为自保件不匹配请求参数={},费率表参数={}", vo.getPolicyNo(), f.getPremCode(), vo.getSelfPreservation(), f.getSelfPreservation());
                return false;
            }
            return true;
        }).collect(Collectors.toList());
        //开始处理数据
        if (resultList.isEmpty()) {
            log.info("[基础佣金]保单号{}未能匹配到费率信息", vo.getPolicyNo());
        } else {
            log.info("[基础佣金]保单号{}成功匹配到的费率信息={}", vo.getPolicyNo(), JSONUtil.toJsonStr(resultList));
        }
        return resultList;
    }


    /**
     * 获取费率信息
     *
     * @param productPrem 费率文件
     * @param renewalYear 续投,续期 期次
     * @return
     */
    private static PolicyProductPremResult extractProductPrem(CommissionBasicPremEntity productPrem, Integer renewalYear) {
        //匹配费率信息
        String yearRate = "";
        switch (renewalYear) {
            case 1: {
                yearRate = productPrem.getFirstYearBrokage();
                break;
            }
            case 2: {
                yearRate = productPrem.getRenewalBrokage2year();
                break;
            }
            case 3: {
                yearRate = productPrem.getRenewalBrokage3year();
                break;
            }
            case 4: {
                yearRate = productPrem.getRenewalBrokage4year();
                break;
            }
            case 5: {
                yearRate = productPrem.getRenewalBrokage5year();
                break;
            }
            case 6: {
                yearRate = productPrem.getRenewalBrokage6year();
                break;
            }
            default: {
                if (StrUtil.isNotBlank(productPrem.getRenewalAutoExpand())) {
                    String[] ranges = productPrem.getRenewalAutoExpand().split("\\|");
                    for (String range : ranges) {
                        String[] parts = range.split("_");
                        String[] nums = parts[0].split("-");
                        int start = Integer.parseInt(nums[0]);
                        int end = Integer.parseInt(nums[1]);
                        double percentage = Double.parseDouble(parts[1].substring(0, parts[1].length() - 1)) / 100;
                        if (renewalYear >= start && renewalYear <= end) {
                            yearRate = String.valueOf(percentage);
                            break;
                        }
                    }
                }
                break;
            }
        }
        if (StrUtil.isBlank(yearRate)) {
            return null;
        }
        PolicyProductPremResult result = new PolicyProductPremResult();
        try {
            // 如果解析失败了,那么就提示去查一单一议的数据
            result.setYearRate(new BigDecimal(yearRate));
            result.setIsCustomYearRate(StatusEnum.INVALID.getCode());
        } catch (Exception e) {
            if (!"一单一议".equals(yearRate)) {
                log.warn("[基础佣金]匹配到的年费率就是数字也不是一单一议请检查:{}", yearRate);
            }
            result.setYearRate(BigDecimal.ZERO);
            result.setIsCustomYearRate(StatusEnum.NORMAL.getCode());
        }
        return result;
    }


}
