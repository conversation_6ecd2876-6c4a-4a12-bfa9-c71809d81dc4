package com.mpolicy.settlement.core.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.dao.SysDocumentDao;
import com.mpolicy.settlement.core.entity.SysDocumentEntity;
import com.mpolicy.settlement.core.service.SysDocumentService;
import com.mpolicy.service.common.datasources.annotation.DataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;


/**
 * <p>
 * oss文件存储读取
 * </p>
 *
 * <AUTHOR>
 * @since 2022/4/22
 */
@Service("sysDocumentService")
@Slf4j
@DataSource(value = "xiaowhale")
public class SysDocumentServiceImpl extends ServiceImpl<SysDocumentDao, SysDocumentEntity> implements SysDocumentService {

    @Override
    public void queryOne() {
        SysDocumentEntity document = Optional.ofNullable(
                lambdaQuery()
                        .eq(SysDocumentEntity::getId, 29631)
                        .one()
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("文档id=[{}]信息不存在", 29631))));
        log.info("结果1={}", JSON.toJSON(document));
    }
}
