package com.mpolicy.settlement.core.modules.reconcile.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 保司结算对账单操作纪录表
 * 
 * <AUTHOR>
 * @date 2023-05-21 22:28:33
 */
@TableName("settlement_reconcile_ope_log")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementReconcileOpeLogEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 对账唯一单号
	 */
	private String reconcileCode;
	/**
	 * 操作类型
	 */
	private String opeType;
	/**
	 * 操作名称
	 */
	private String opeName;
	/**
	 * 操作描述
	 */
	private String opeDesc;
	/**
	 * 操作报文
	 */
	private String opeRequest;
	/**
	 * 操作响应报文
	 */
	private String opeResponse;
	/**
	 * 操作信息
	 */
	private String opeMessage;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
}
