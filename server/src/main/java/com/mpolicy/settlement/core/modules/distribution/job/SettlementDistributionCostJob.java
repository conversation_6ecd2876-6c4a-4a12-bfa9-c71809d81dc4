package com.mpolicy.settlement.core.modules.distribution.job;

import cn.hutool.core.date.DateUtil;
import com.mpolicy.settlement.core.modules.add.entity.SettlementCommissionSyncRecordEntity;
import com.mpolicy.settlement.core.modules.add.enums.SyncRecordStatusEnum;
import com.mpolicy.settlement.core.modules.add.service.SettlementAddCostManageService;
import com.mpolicy.settlement.core.modules.add.service.SettlementCommissionSyncRecordService;
import com.mpolicy.settlement.core.modules.distribution.service.SettlementDistributionCostManageService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

import static cn.hutool.core.date.DatePattern.PURE_DATE_PATTERN;

@Component
@Slf4j
public class SettlementDistributionCostJob {

    @Autowired
    private SettlementDistributionCostManageService settlementDistributionCostManageService;
    @XxlJob("distCommissionSyncRecordHandler")
    public void distCommissionSyncRecordHandler() {
        XxlJobHelper.log("开始处理分销佣金批量同步记录");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("distCommissionSyncRecordHandler入参：{}",jobParam);
        settlementDistributionCostManageService.batchAddSyncDistRecord(StringUtils.isNotBlank(jobParam)?jobParam:DateUtil.format(DateUtil.date(),"yyyyMM"));
        XxlJobHelper.log("分销佣金批量同步记录处理完成");
    }

    @XxlJob("syncConfirmStatusToDiscHandler")
    public void syncConfirmStatusToDiscHandler() {
        XxlJobHelper.log("开始同步确认状态给上游分销");
        settlementDistributionCostManageService.syncConfirmStatusToDist();
        XxlJobHelper.log("完成同步确认状态给上游分销");
    }

}
