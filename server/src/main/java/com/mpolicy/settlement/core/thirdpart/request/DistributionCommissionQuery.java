package com.mpolicy.settlement.core.thirdpart.request;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 小额保险分销查询query
 *
 * <AUTHOR>
 */
@Data
@ApiModel
public class DistributionCommissionQuery {

    /**
     * 批次号
     */
    @ApiModelProperty(value = "月结算批次号")
    private String monthBatchNo;

    /**
     * 第几页
     */
    @ApiModelProperty(value = "第几页")
    private Integer pageSize;

    /**
     * 每页记录数
     */
    @ApiModelProperty(value = "每页记录数")
    private Integer size;
}
