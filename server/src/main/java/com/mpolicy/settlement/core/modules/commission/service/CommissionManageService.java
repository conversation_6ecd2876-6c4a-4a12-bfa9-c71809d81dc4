package com.mpolicy.settlement.core.modules.commission.service;

import com.mpolicy.settlement.core.common.commission.dto.CommissionBasicPolicyPrem;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/08/05
 */
public interface CommissionManageService {
    /**
     * 一单一议费率处理
     * @param policyPremList
     * @param opUser
     */
    void doPolicyPremHandler(List<CommissionBasicPolicyPrem> policyPremList, String opUser);
}
