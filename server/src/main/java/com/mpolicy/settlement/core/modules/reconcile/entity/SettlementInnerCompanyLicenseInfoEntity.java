package com.mpolicy.settlement.core.modules.reconcile.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 结算内部公司工商信息
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@TableName("settlement_inner_company_license_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementInnerCompanyLicenseInfoEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId
    private Integer id;

    /**
     * 经营状态 1、经营中 2 已注销 3注销中 4待注销
     */
    private Integer businessLicenseStatus;

    /**
     * 工商信息性质(0：总子公司 1：分支机构)
     */
    private Integer businessNature;

    /**
     * 营业执照名称
     */
    private String licenseName;

    /**
     * 营业执照类型
     */
    private Integer licenseType;

    /**
     * 经营类型 1-业务主体；2-合规主体
     */
    private Integer managementType;

    /**
     * 修改时间
     */
    private Date modifyDate;

    /**
     * 统一社会信用代码/税务登记证号码
     */
    private String socialCreditCode;

    /**
     * 主体类型 1-小贷公司；2-项目公司；3-咨询公司
     */
    private Integer subjectType;

    /**
     * 是否删除;0有效-1删除
     */
    @TableLogic
    private Integer deleted;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 乐观锁
     */
    @Version
    @TableField(fill = FieldFill.INSERT)
    private Integer revision;
}
