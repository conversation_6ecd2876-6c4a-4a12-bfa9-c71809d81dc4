package com.mpolicy.settlement.core.modules.reconcile.utils;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.date.DateUtils;
import com.mpolicy.common.utils.other.RandomUtil;
import com.mpolicy.policy.common.enums.PolicyInsuredPeriodTypeEnum;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 结算保单工具类
 *
 * <AUTHOR>
 * @since 2023-05-23 10:30
 */
@Slf4j
public class PolicySettlementUtils {

    static final Pattern P = Pattern.compile("\\d+");

    /**
     * 获取保单截至日期
     *
     * @param startDate 生效日期
     * @param num       时长
     * @param code      保障期间类型
     * @return 截至日期
     */
    public static String getInsuredEndDate(Date startDate, Integer num, String code) {

        log.info("U****1******{} {} {}", startDate, num, code);
        if(startDate == null){

        }

        PolicyInsuredPeriodTypeEnum insuredPeriodTypeEnum = PolicyInsuredPeriodTypeEnum.getInsuredTypeEnumByCode(code);
        log.info("U****2******");
        if (insuredPeriodTypeEnum == null) {
            return "";
        } else {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            switch (insuredPeriodTypeEnum) {
                case LIFE:
                    return PolicyInsuredPeriodTypeEnum.LIFE.getInsuredUnit();
                case AGE:
                    return num + insuredPeriodTypeEnum.getInsuredUnit();
                case DAY:
                case MONTH:
                case YEAR: {
                    log.info("U****3******");
                    Date date = queryEffPeriodEnd(startDate, num + insuredPeriodTypeEnum.getInsuredType(), null);
                    if (date == null) {
                        throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("获取保障截止日期失败，startDate={},num={},code={}", startDate, num, code)));
                    }
                    return sdf.format(date);
                }
                default:
                    return "";
            }
        }
    }

    /**
     * 获取保单保险止期
     *
     * @param effectiveDate    生效日期
     * @param coverageYear     保障期间
     * @param customerBirthday 生日
     * @return 终止日期
     */
    public static Date queryEffPeriodEnd(Date effectiveDate, String coverageYear, String customerBirthday) {
        String regex = "\\d+";
        //若只是数字,没有单位,增加单位"天"
        if (coverageYear.matches(regex)) {
            return DateUtils.addDateDays(effectiveDate, Integer.parseInt(coverageYear));
        }
        // 如果为指定到多少岁的
        if (coverageYear.contains("岁")) {
            Date birthday = DateUtils.convertString2Date(customerBirthday, "yyyy-MM-dd");
            int number = getStrNumber(coverageYear);
            // 计算投保年龄
            int age = getAge(effectiveDate, birthday);
            // 生效日期减一天
            Date res = DateUtils.addDateYears(effectiveDate, number - age);
            return DateUtils.addDateDays(res, -1);
        } else {
            return DateUtils.convertString2Date(addPeriod(effectiveDate, coverageYear), "yyyy-MM-dd");
        }
    }

    /**
     * XX年-XX
     *
     * @param period XX年
     */
    public static int getStrNumber(String period) {
        Matcher m = P.matcher(period);
        if (m.find()) {
            return Integer.parseInt(m.group());
        } else {
            return 0;
        }
    }

    /**
     * 订单详情页，保障期间的展示需为具体时间，如：2017-5-17至2019-5-16
     * 根据生效时间及period，算出保障截止时间
     */
    public static String addPeriod(Date startDate, String period) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String result = "";
        Calendar date = Calendar.getInstance();
        date.setTime(startDate);
        Matcher m = P.matcher(period);
        if (m.find()) {
            int num = Integer.parseInt(m.group());
            if (period.contains("天") || period.contains("日")) {
                date.add(Calendar.DAY_OF_MONTH, num);
                result = sdf.format(date.getTime());
            } else if (period.contains("月")) {
                date.add(Calendar.MONTH, num);
                date.add(Calendar.DAY_OF_MONTH, -1);
                result = sdf.format(date.getTime());
            } else if (period.contains("年")) {
                // 如果是闰年 + 月份=2月 + 日=29日的话，不做-1天处理
                boolean isLeapYear = DateUtil.isLeapYear(DateUtil.year(startDate));
                if(isLeapYear && DateUtil.month(startDate)+1 == 2 && DateUtil.dayOfMonth(startDate) == 29){
                    date.add(Calendar.YEAR, num);
                }else {
                    date.add(Calendar.YEAR, num);
                    date.add(Calendar.DAY_OF_MONTH, -1);
                }
                result = sdf.format(date.getTime());
            } else {
                result = period;
            }
        } else {
            result = period;
        }
        return result;
    }

    /**
     * 计算客户投保年龄
     *
     * @param effectiveDate 保单生效日期
     * @param birthDay      客户生日
     * @return
     */
    public static int getAge(Date effectiveDate, Date birthDay) {
        //获取当前系统时间
        Calendar cal = Calendar.getInstance();
        cal.setTime(effectiveDate);

        //如果出生日期大于当前时间，则抛出异常
        if (cal.before(birthDay)) {
            throw new IllegalArgumentException(
                    "The birthDay is before Now.It's unbelievable!");
        }
        //取出系统当前时间的年、月、日部分
        int yearNow = cal.get(Calendar.YEAR);
        int monthNow = cal.get(Calendar.MONTH);
        int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH);

        //将日期设置为出生日期
        cal.setTime(birthDay);
        //取出出生日期的年、月、日部分
        int yearBirth = cal.get(Calendar.YEAR);
        int monthBirth = cal.get(Calendar.MONTH);
        int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);
        //当前年份与出生年份相减，初步计算年龄
        int age = yearNow - yearBirth;
        //当前月份与出生日期的月份相比，如果月份小于出生月份，则年龄上减1，表示不满多少周岁
        if (monthNow <= monthBirth) {
            //如果月份相等，在比较日期，如果当前日，小于出生日，也减1，表示不满多少周岁
            if (monthNow == monthBirth) {
                if (dayOfMonthNow < dayOfMonthBirth) {
                    age--;
                }
            } else {
                age--;
            }
        }
        return age;
    }

    /**
     * 编码支持 前缀+系统时间+随机数10位置数据
     *
     * @return
     */
    public static String createCodeLastNumber(String beforeStr) {
        return beforeStr + System.currentTimeMillis() + RandomUtil.generateNumber(10);
    }

    public static String createCodeLastUuid(String beforeStr) {
        return beforeStr + IdUtil.fastSimpleUUID()+ RandomUtil.generateNumber(5);
    }


    /**
     *
     * @param costAmount
     * @param grantRate
     * @return
     */
    public static BigDecimal calcGrantAmount(BigDecimal costAmount,BigDecimal grantRate){
        return calcAmtByPercent(costAmount,grantRate,2);
    }

    /**
     * 按百分比计算金额
     * @param amt
     * @param rate
     * @return
     */
    public static BigDecimal calcAmtByPercent(BigDecimal amt,BigDecimal rate,Integer scale){
        if(amt == null || rate == null){
            return null;
        }
        return amt.multiply(rate.divide(new BigDecimal("100"),8,BigDecimal.ROUND_HALF_UP)).setScale(scale,BigDecimal.ROUND_HALF_UP);

    }
    /**
     * 按计算金额
     * @param amt
     * @param rate
     * @return
     */
    public static BigDecimal calcAmt(BigDecimal amt,BigDecimal rate,Integer scale){
        if(amt == null || rate == null){
            return null;
        }
        return amt.multiply(rate).setScale(scale,BigDecimal.ROUND_HALF_UP);
    }

    public static void main(String[] arg){
        DateTime date1 =DateUtil.parseDateTime("2023-12-31 23:59:59");
        DateTime date2 =DateUtil.parseDateTime("2024-12-31 23:59:59");
        DateTime date3 =DateUtil.parseDateTime("2025-01-01 00:00:00");
        DateTime date4 =DateUtil.parseDateTime("2023-12-31 15:06:28");



        System.out.println(DateUtil.offsetMonth(DateUtil.beginOfDay(date4),12));
        System.out.println(DateUtil.offsetMonth(DateUtil.beginOfDay(date4),12).isAfter(DateUtil.date()));

    }



}