package com.mpolicy.settlement.core.modules.reconcile.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.settlement.core.common.commission.dto.IncomeProfitInfoParamsDto;
import com.mpolicy.settlement.core.common.commission.dto.IncomeRateOut;
import com.mpolicy.settlement.core.common.commission.dto.ValidateCostConfigParam;
import com.mpolicy.settlement.core.common.keys.AdminCommonKeys;
import com.mpolicy.settlement.core.common.reconcile.*;
import com.mpolicy.settlement.core.common.reconcile.diff.DiffBacklogInput;
import com.mpolicy.settlement.core.common.reconcile.diff.ReconcileAmountAccuracyInput;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileStatusEnum;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileTemplateEnum;
import com.mpolicy.settlement.core.enums.SettlementInvoiceStatusEnum;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.event.factory.SettlementEventFactory;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.SettlementEventHandler;
import com.mpolicy.settlement.core.modules.reconcile.service.*;
import com.mpolicy.web.common.annotation.Lock;
import com.mpolicy.web.common.annotation.PassToken;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 保险公司对账业务 controller
 *
 * <AUTHOR>
 * @since 2023-05-20 22:30
 */
@RestController
@RequestMapping("/settlement/reconcile")
@Api(tags = "保险公司对账业务")
@Slf4j
@Validated
public class ReconcileController {

    @Autowired
    @Qualifier("simpleTaskExecutor")
    private AsyncTaskExecutor taskExecutor;

    @Autowired
    private SettlementReconcileService settlementReconcileService;

    @Autowired
    private ReconcileHelpService reconcileHelpService;

    @Autowired
    private SettlementReconcileInfoService settlementReconcileInfoService;

    @Autowired
    private SettlementReconcileHelperService settlementReconcileHelperService;

    @Autowired
    private SettlementPolicyInfoService settlementPolicyInfoService;

    @Autowired
    private SettlementEventJobService settlementEventJobService;

    @Autowired
    private IRedisService redisService;

    @ApiOperation(value = "上传对账单文件", notes = "上传保司对账单文件")
    @PostMapping("/file/upload/{reconcileCode}")
    @PassToken
    @Lock(keys = "#reconcileCode", attemptTimeout = 500)
    public Result<String> uploadReconcileFile(
            @PathVariable @ApiParam(name = "reconcileCode", value = "RC202305230101010101") String reconcileCode,
            @RequestParam(value = "reconcileTemplateCode") @ApiParam(name = "reconcileTemplateCode",
                    value = "文件规则类型编码", example = "F001") String reconcileTemplateCode,
            @RequestParam(value = "fileCode") @ApiParam(name = "fileCode", value = "保司对账单文件编码",
                    example = "F001") String fileCode,
            @RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员",
                    example = "张三") String userName) {

        ReconcileTemplateEnum reconcileTemplateEnum = ReconcileTemplateEnum.matchSearchCode(reconcileTemplateCode);
        settlementReconcileService.uploadCompanyReconcileFile(reconcileCode, reconcileTemplateEnum, fileCode, userName);
        return Result.success("success");
    }

    @ApiOperation(value = "重新上传对账单文件", notes = "重新上传保司对账单文件")
    @PostMapping("/file/retry_upload/{reconcileCode}")
    @PassToken
    @Lock(keys = "#reconcileCode", attemptTimeout = 500)
    public Result<String> retryUploadReconcileFile(
            @PathVariable @ApiParam(name = "reconcileCode", value = "RC202305230101010101") String reconcileCode,
            @RequestParam(value = "reconcileTemplateCode") @ApiParam(name = "reconcileTemplateCode",
                    value = "文件规则类型编码", example = "F001") String reconcileTemplateCode,
            @RequestParam(value = "sourceFileCode") @ApiParam(name = "sourceFileCode", value = "保司对账单文件编码",
                    example = "F001") String sourceFileCode,
            @RequestParam(value = "fileCode") @ApiParam(name = "fileCode", value = "保司对账单文件编码",
                    example = "F001") String fileCode,
            @RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员",
                    example = "张三") String userName) {

        ReconcileTemplateEnum reconcileTemplateEnum = ReconcileTemplateEnum.matchSearchCode(reconcileTemplateCode);
        settlementReconcileService.retryUploadCompanyReconcileFile(reconcileCode, reconcileTemplateEnum, sourceFileCode,
                fileCode, userName);
        return Result.success("success");
    }

    @ApiOperation(value = "删除对账关联对账文件", notes = "删除对账关联对账文件")
    @PostMapping("/file/remove/{reconcileCode}/{reconcileFileCode}")
    @PassToken
    @Lock(keys = "#reconcileCode", attemptTimeout = 500)
    public Result<String> removeReconcileFile(
            @PathVariable @ApiParam(name = "reconcileCode", value = "RC202305230101010101") String reconcileCode,
            @PathVariable @ApiParam(name = "reconcileFileCode", value = "oss202305023123466") String reconcileFileCode,
            @RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员",
                    example = "张三") String userName) {
        settlementReconcileService.removeReconcileFile(reconcileCode, reconcileFileCode, userName);
        return Result.success("success");
    }

    /**
     * 开始对账
     *
     * @param reconcileCode 生成对账单唯一编号
     * @return com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @since 2023/5/22 20:16
     */
    @ApiOperation(value = "开始对账", notes = "开始对账")
    @PostMapping("/start/{reconcileCode}")
    @PassToken
    @Lock(keys = "#reconcileCode", attemptTimeout = 500)
    public Result<String> startReconcile(
            @PathVariable @ApiParam(name = "reconcileCode", value = "RC202305230101010101") String reconcileCode,
            @RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员",
                    example = "张三") String userName) {
        // 获取对账单
        SettlementReconcileInfoEntity reconcileInfo = Optional.ofNullable(settlementReconcileInfoService.lambdaQuery()
                        .eq(SettlementReconcileInfoEntity::getReconcileCode, reconcileCode).one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("对账单纪录不存在")));
        ReconcileStatusEnum reconcileStatusEnum = ReconcileStatusEnum.decode(reconcileInfo.getReconcileStatus());
        if (reconcileStatusEnum == ReconcileStatusEnum.GENERATING_GENERATE_ING) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("对账单生成中，无法开启对账")));
        }
        if (reconcileStatusEnum == ReconcileStatusEnum.RECONCILE_FINISH || reconcileStatusEnum == ReconcileStatusEnum.RECONCILE_CLOSE) {
            throw new GlobalException(
                    BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("对账单已经完成对账或已关闭，无法再次对账")));
        }

        String uuid = IdUtil.fastSimpleUUID();
        redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, "1");

        // 异步执行申请开始对账
        taskExecutor.submit(() -> {
            try {
                settlementReconcileService.startReconcile(reconcileCode, userName, uuid);
            } catch (Exception e) {
                log.warn("对账单生成失败：", e);
                reconcileInfo.setReconcileStatus(ReconcileStatusEnum.GENERATING_RECONCILED_ERROR.getStatusCode());
                settlementReconcileInfoService.updateById(reconcileInfo);
            }
        });
        return Result.success(uuid);
    }

    /**
     * 批量处理精度
     *
     * @param reconcileAmountAccuracyInput 批量处理对账单精度信息
     * @return com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @since 2023/5/25 16:21
     */
    @PassToken
    @ApiOperation(value = "批量处理精度", notes = "批量处理精度")
    @PostMapping("/reconcile_amount_accuracy/action")
    public Result<String> reconcileAmountAccuracy(@RequestBody @ApiParam(name = "reconcileAmountAccuracyInput",
            value = "批量处理对账单精度信息") ReconcileAmountAccuracyInput reconcileAmountAccuracyInput) {
        // 批量处理精度
        String uuid = IdUtil.fastSimpleUUID();
        redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, "1");
        settlementReconcileService.reconcileAmountAccuracy(reconcileAmountAccuracyInput, uuid);
        return Result.success(uuid);
    }

    @ApiOperation(value = "重新对账", notes = "重新对账")
    @PostMapping("/retry_start/{reconcileCode}")
    @PassToken
    @Lock(keys = "#reconcileCode", attemptTimeout = 500)
    public Result<String> retryStartReconcile(
            @PathVariable @ApiParam(name = "reconcileCode", value = "RC202305230101010101") String reconcileCode,
            @RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员",
                    example = "张三") String userName) {
        // 1 获取对账单
        SettlementReconcileInfoEntity reconcileInfo = Optional.ofNullable(settlementReconcileInfoService.lambdaQuery()
                        .eq(SettlementReconcileInfoEntity::getReconcileCode, reconcileCode).one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("对账单纪录不存在")));
        ReconcileStatusEnum reconcileStatusEnum = ReconcileStatusEnum.decode(reconcileInfo.getReconcileStatus());
        if (reconcileStatusEnum == ReconcileStatusEnum.GENERATING_GENERATE_ING) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("对账单生成中，无法开启对账")));
        }
        if (reconcileStatusEnum == ReconcileStatusEnum.RECONCILE_FINISH || reconcileStatusEnum == ReconcileStatusEnum.RECONCILE_CLOSE) {
            throw new GlobalException(
                    BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("对账单已经完成对账或已关闭，无法再次对账")));
        }
        String uuid = IdUtil.fastSimpleUUID();
        redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, "1");

        // 异步执行申请重新对账
        taskExecutor.submit(() -> {
            try {
                // 3 生成账单处理，出现异常设置为对账单生成失败
                settlementReconcileService.startReconcile(reconcileCode, userName, uuid);
            } catch (Exception e) {
                log.warn("对账单重新对账生成失败：", e);
                reconcileInfo.setReconcileStatus(ReconcileStatusEnum.GENERATING_RECONCILED_ERROR.getStatusCode());
                settlementReconcileInfoService.updateById(reconcileInfo);
            }
        });
        return Result.success(uuid);
    }

    @PassToken
    @ApiOperation(value = "完成对账", notes = "完成对账")
    @PostMapping("/finish/{reconcileCode}")
    public Result<String> finishReconcile(
            @PathVariable @ApiParam(name = "reconcileCode", value = "RC202305230101010101") String reconcileCode,
            @RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员",
                    example = "张三") String userName) {
        // 加一个锁 防止有进行中的冲正事件
        String uuid = IdUtil.fastSimpleUUID();
        redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, "1");
        // 完成对账
        settlementReconcileService.finishReconcile(reconcileCode, userName, uuid);
        return Result.success(uuid);
    }

    /**
     * 关闭对账
     *
     * @param reconcileCode 生成对账单唯一编号
     * @return com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @since 2023/6/27 20:16
     */
    @ApiOperation(value = "关闭对账", notes = "关闭对账")
    @PostMapping("/close/{reconcileCode}")
    @PassToken
    @Lock(keys = "#reconcileCode", attemptTimeout = 500)
    public Result<String> closeReconcile(
            @PathVariable @ApiParam(name = "reconcileCode", value = "RC202305230101010101") String reconcileCode,
            @RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员",
                    example = "张三") String userName) {
        // 获取对账单
        SettlementReconcileInfoEntity reconcileInfo = Optional.ofNullable(settlementReconcileInfoService.lambdaQuery()
                        .eq(SettlementReconcileInfoEntity::getReconcileCode, reconcileCode).one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("对账单纪录不存在")));

        ReconcileStatusEnum reconcileStatus = ReconcileStatusEnum.decode(reconcileInfo.getReconcileStatus());
        if (reconcileStatus == ReconcileStatusEnum.TO_BE_RECONCILE || reconcileStatus == ReconcileStatusEnum.GENERATING_GENERATE_ERROR || reconcileStatus == ReconcileStatusEnum.GENERATING_RECONCILED_ERROR || reconcileStatus == ReconcileStatusEnum.GENERATING_GENERATE_ING) {
            log.info("允许关闭对账单，对账单号={}，当前对账状态={}", reconcileCode, reconcileStatus.getStatusDesc());
            // 关闭对账
            reconcileInfo.setReconcileStatus(ReconcileStatusEnum.RECONCILE_CLOSE.getStatusCode());
            reconcileInfo.setCreateUser(userName);
            // 设置开票状态为不可开票 金额设置为0
            reconcileInfo.setInvoiceStatus(SettlementInvoiceStatusEnum.CANNOT_BE_INVOICED.getCode());
            reconcileInfo.setInvoiceAmount(BigDecimal.ZERO);//开票中金额
            reconcileInfo.setInvoicableAmount(BigDecimal.ZERO);//可开票金额
            reconcileInfo.setInvoicedAmount(BigDecimal.ZERO);// 已开票金额
            settlementReconcileInfoService.updateById(reconcileInfo);
        } else {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                    StrUtil.format("无法关闭对账单，当前对账单状态为={}", reconcileStatus.getStatusDesc())));
        }
        return Result.success("success");
    }

    /**
     * 刷新对账单保单数据
     *
     * @param reconcileCode 生成对账单唯一编号
     * @return com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @since 2023/6/27 20:16
     */
    @ApiOperation(value = "刷新对账单保单数据", notes = "刷新对账单保单数据")
    @PostMapping("/refresh_policy/{reconcileCode}")
    @PassToken
    @Lock(keys = "#reconcileCode", attemptTimeout = 500)
    public Result<String> reconcileRefreshPolicy(
            @PathVariable @ApiParam(name = "reconcileCode", value = "RC202305230101010101") String reconcileCode,
            @RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员",
                    example = "张三") String userName) {

        String uuid = IdUtil.fastSimpleUUID();
        redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, "1");
        // 清空之前的明细纪录
        settlementReconcileInfoService.lambdaUpdate().set(SettlementReconcileInfoEntity::getReconcilePolicyUrl, null)
                .eq(SettlementReconcileInfoEntity::getReconcileCode, reconcileCode).update();
        settlementReconcileHelperService.createPolicySummary(reconcileCode, uuid);
        return Result.success(uuid);
    }

    @ApiOperation(value = "差异处理申请", notes = "差异处理申请")
    @PostMapping("/diff_backlog")
    @PassToken
    @Lock(keys = "#diffBacklogInput.reconcileCode", attemptTimeout = 500)
    public Result<String> diffBacklog(@RequestBody @ApiParam(name = "diffBacklogInput",
            value = "差异处理申请信息") DiffBacklogInput diffBacklogInput) {
        // 差异处理申请
        settlementReconcileService.diffBacklog(diffBacklogInput);
        return Result.success("success");
    }

    /**
     * 重置结算单状态 已经存在 对账编码的不处理
     *
     * @param params 刷新条件
     * @return
     */
    @PassToken
    @PostMapping("handleSettlementPolicyInfo")
    @ApiOperation(value = "[手动]重置结算单状态", notes = "[手动]重置结算单状态")
    public Result<String> handleSettlementPolicyInfo(@RequestBody @Valid HandleSettlementPolicyInfoVo params) {
        if (CollUtil.isEmpty(params.getIds()) && CollUtil.isEmpty(params.getEventSourceCodeList())) {
            return Result.error(BasicCodeMsg.PARAMETER_ERROR.setMsg("ids和pushEventCodeList最少满足一个条件"));
        }
        List<SettlementPolicyInfoEntity> settlementPolicyInfoList = settlementPolicyInfoService.lambdaQuery()
                .in(CollUtil.isNotEmpty(params.getIds()), SettlementPolicyInfoEntity::getId, params.getIds())
                .in(CollUtil.isNotEmpty(params.getEventSourceCodeList()), SettlementPolicyInfoEntity::getEventSourceCode,
                        params.getEventSourceCodeList())
                .eq(SettlementPolicyInfoEntity::getReconcileStatus, ReconcileStatusEnum.TO_BE_RECONCILE.getStatusCode())
                .eq(SettlementPolicyInfoEntity::getRectificationMark, StatusEnum.INVALID.getCode())
                .eq(params.getReconcileType() != null, SettlementPolicyInfoEntity::getReconcileType,
                        params.getReconcileType()).isNull(SettlementPolicyInfoEntity::getReconcileCode).list();
        log.info("需要处理任务数:{}", settlementPolicyInfoList.size());
        if (settlementPolicyInfoList.isEmpty()) {
            return Result.success("success");
        }
        settlementPolicyInfoList.forEach(action -> {
            action.setUpdateUser(params.getUpdateUser());
            action.setReconcileExecuteStatus(params.getReconcileExecuteStatus());
            action.setReconcileExecuteTime(null);
            action.setReconcileExecuteDesc(params.getReconcileExecuteDesc());
        });
        settlementPolicyInfoService.updateBatchById(settlementPolicyInfoList);
        //获取状态是0的数据,重新计算一下费率信息

        List<Integer> ids = settlementPolicyInfoList.stream().filter(f -> f.getReconcileExecuteStatus() == 0)
                .map(SettlementPolicyInfoEntity::getId).collect(Collectors.toList());
        if (!ids.isEmpty()) {
            RefreshSettlementPolicyInfoCommissionVo refreshSettlementPolicyInfoCommission =
                    new RefreshSettlementPolicyInfoCommissionVo();
            refreshSettlementPolicyInfoCommission.setIds(ids);
            settlementPolicyInfoService.refreshSettlementPolicyInfoCommission(refreshSettlementPolicyInfoCommission);
        }
        return Result.success("success");
    }

    @PassToken
    @PostMapping("refreshSettlementPolicyInfoCommission")
    @ApiOperation(value = "[手动]重置结算单费率信息", notes = "[手动]重置结算单状态")
    public Result<String> refreshSettlementPolicyInfoCommission(
            @RequestBody @Valid RefreshSettlementPolicyInfoCommissionVo params) {
        if (CollUtil.isEmpty(params.getIds()) && CollUtil.isEmpty(params.getEventSourceCodeList())) {
            return Result.error(BasicCodeMsg.PARAMETER_ERROR.setMsg("ids和pushEventCodeList最少满足一个条件"));
        }
        settlementPolicyInfoService.refreshSettlementPolicyInfoCommission(params);
        return Result.success("success");
    }

    @PassToken
    @PostMapping("refreshSettlementEventJob")
    @ApiOperation(value = "[手动]重新刷新对账job-保单", notes = "[手动]重新刷新对账job-保单")
    public Result<String> refreshSettlementEventJob(@RequestBody @Valid RefreshSettlementEventJobVo params) {
        if (CollUtil.isEmpty(params.getIds()) && CollUtil.isEmpty(params.getPushEventCodeList())) {
            return Result.error(BasicCodeMsg.PARAMETER_ERROR.setMsg("ids和pushEventCodeList最少满足一个条件"));
        }
        String uuid = IdUtil.fastSimpleUUID();
        redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, "1");
        // 异步执行申请开始对账
        taskExecutor.submit(() -> {
            try {
                settlementEventJobService.refreshSettlementEventJob(params);
                redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, "2");
            } catch (Exception e) {
                log.warn("重新刷新对账job失败：", e);
                redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, e.getMessage());
            }
        });
        return Result.success(uuid);
    }

    /**
     * 批量刷新结算单费率信息
     *
     * @param reconcileCode 小鲸对账单号
     * @return
     */
    @PassToken
    @PostMapping("batchRefreshRate/{reconcileCode}")
    Result<String> batchRefreshRate(
            @PathVariable @ApiParam(name = "reconcileCode", value = "小鲸对账单号") String reconcileCode) {
        String uuid = IdUtil.fastSimpleUUID();
        redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, "1");
        settlementPolicyInfoService.batchRefreshRate(reconcileCode, uuid);
        return Result.success(uuid);
    }

    @ApiOperation(value = "[结算配置]手动触发生成账单 会覆盖之前生成的对账单",
            notes = "[结算配置]手动触发生成账单 会覆盖之前生成的对账单")
    @PostMapping("forceCreateReconcile")
    @PassToken
    public Result<List<String>> forceCreateReconcile(@RequestBody @Valid CreateReconcileVo vo) {
        List<String> resultList = settlementReconcileService.forceCreateReconcile(vo);
        return Result.success(resultList);
    }

    @PassToken
    @ApiOperation(value = "处理监管报送", notes = "处理监管报送")
    @PostMapping("regulatory/submit/{reconcileCode}")
    Result<String> reconcileRegulatorySubmit(@PathVariable("reconcileCode") String reconcileCode) {
        String uuid = IdUtil.fastSimpleUUID();
        redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, "1");
        taskExecutor.submit(() -> {
            try {
                settlementReconcileService.reconcileRegulatorySubmit(reconcileCode, uuid);
            } catch (Exception e) {
                log.warn("处理监管报送异常,对账单编码={}", reconcileCode, e);
            }
        });
        return Result.success(uuid);
    }

    @PassToken
    @ApiOperation(value = "查询保单是否存在完成对账的记录", notes = "查询保单是否存在完成对账的记录")
    @GetMapping("isCompletedReconcileRecord/{policyNo}")
    Result<Boolean> isCompletedReconcileRecord(@PathVariable("policyNo") String policyNo) {
        boolean res = Optional.ofNullable(settlementPolicyInfoService.lambdaQuery()
                .eq(SettlementPolicyInfoEntity::getPolicyNo, policyNo)
                .eq(SettlementPolicyInfoEntity::getReconcileStatus, ReconcileStatusEnum.RECONCILE_FINISH.getStatusCode())
                .count()).orElse(0) > 0;
        return Result.success(res);
    }

    @PassToken
    @ApiOperation(value = "批量查询保单是否存在完成对账的记录", notes = "批量查询保单是否存在完成对账的记录")
    @PostMapping("batchIsCompletedReconcileRecord")
    Result<List<BatchIsCompletedReconcileRecord>> batchIsCompletedReconcileRecord(@RequestBody List<String> policyNoList) {
        List<BatchIsCompletedReconcileRecord> result = settlementPolicyInfoService.batchIsCompletedReconcileRecord(policyNoList);
        return Result.success(result);
    }

    @PassToken
    @ApiOperation(value = "批量查询保全是否存在完成对账的记录", notes = "批量查询保全是否存在完成对账的记录")
    @PostMapping("batchIsCompletedReconcileRecordPreservation")
    Result<List<BatchIsCompletedReconcileRecordPreservation>> batchIsCompletedReconcileRecordPreservation(@RequestBody List<String> preservationCodeList) {
        List<BatchIsCompletedReconcileRecordPreservation> result = settlementPolicyInfoService.batchIsCompletedReconcileRecordPreservation(preservationCodeList);
        return Result.success(result);
    }

    @PassToken
    @ApiOperation(value = "批量处理Job任务", notes = "批量处理Job任务")
    @PostMapping("handleSettlementEventJob")
    Result<String> handleSettlementEventJob(@RequestBody @NotEmpty(message = "事件Code不能为空") List<String> codeList) {
        if (CollUtil.isNotEmpty(codeList)) {
            settlementEventJobService.lambdaQuery()
                    .eq(SettlementEventJobEntity::getEventStatus, 0)
                    .in(SettlementEventJobEntity::getPushEventCode, codeList)
                    .list().forEach(eventJob -> {
                        SettlementEventHandler settlementEventHandler = SettlementEventFactory.getSettlementEventHandler(eventJob.getEventType());
                        if (settlementEventHandler != null) {
                            log.info("执行获取需要处理的事件任务,事件类型={}", eventJob.getEventType());
                            // 事件执行
                            settlementEventHandler.handle(eventJob);
                        } else {
                            log.warn(StrUtil.format("事件类型类型不支持，事件类型={}", eventJob.getEventType()));
                        }
                        log.info("执行获取需要处理的事件任务完成,事件id={}", eventJob.getId());
                    });
        }
        return Result.success();
    }

    @PassToken
    @ApiOperation(value = "[手动]单一数据冲正", notes = "[手动]单一数据冲正")
    @PostMapping("rectificationOne")
    public Result<String> rectificationOne(@RequestBody @Valid RectificationOneVo vo) {
        reconcileHelpService.rectificationOne(vo);
        return Result.success("success");
    }


    @Autowired
    private SettlementBasicCommValidService commValidService;

    @PassToken
    @ApiOperation(value = "查询收入端费率", notes = "查询收入端费率")
    @PostMapping("query/rate")
    public Result<List<IncomeRateOut>> queryIncomeRateConfig(@RequestBody IncomeProfitInfoParamsDto profitInfoParamsDto) {
        return Result.success(commValidService.queryIncomeRateConfig(profitInfoParamsDto));
    }

}
