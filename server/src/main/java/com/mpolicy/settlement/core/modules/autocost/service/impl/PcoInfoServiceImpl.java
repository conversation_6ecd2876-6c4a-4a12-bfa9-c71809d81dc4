package com.mpolicy.settlement.core.modules.autocost.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.oss.StorageService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.excel.ExcelUtil;
import com.mpolicy.settlement.core.common.Constant;
import com.mpolicy.settlement.core.modules.autocost.dto.CommissionGrantInfoExcelDto;
import com.mpolicy.settlement.core.modules.autocost.dto.PcoBaseInfoExcelDto;
import com.mpolicy.settlement.core.modules.autocost.dto.cache.OrganizationCache;
import com.mpolicy.settlement.core.modules.autocost.dto.dynamic.SubjectDataDynamicErrorBase;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostSubjectDataDynamicApplyEntity;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostSubjectDataDynamicInfoEntity;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostUploadPcoInfoEntity;
import com.mpolicy.settlement.core.modules.autocost.helper.SettlementEmployeeHelper;
import com.mpolicy.settlement.core.modules.autocost.service.PcoInfoService;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementCostDynamicSubjectErrorDataService;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementCostSubjectDataDynamicApplyService;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementCostUploadPcoInfoService;
import com.mpolicy.settlement.core.modules.referrer.entity.ChannelApplicationReferrerEntity;
import com.mpolicy.settlement.core.service.SysDocumentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


@Service
@Slf4j
public class PcoInfoServiceImpl extends AbstractSettlementCostImportService implements PcoInfoService {
    @Value("${insure.temp-file-path:/logs}")
    private String tempPath;
    @Autowired
    private SysDocumentService sysDocumentService;
    @Autowired
    private StorageService storageService;
    @Autowired
    protected SettlementCostUploadPcoInfoService settlementCostUploadPcoInfoService;

    @Autowired
    private SettlementCostSubjectDataDynamicApplyService settlementCostSubjectDataDynamicApplyService;
    @Autowired
    private  SettlementCostDynamicSubjectErrorDataService settlementCostDynamicSubjectErrorDataService;

    @Autowired
    private TransactionTemplate transactionTemplate;
    public Boolean loadPcoInfo(SettlementCostSubjectDataDynamicApplyEntity apply, String userName){
        log.info("开始导入PCO基础信息");
        String localFilePath = tempPath.concat(apply.getFileName());
        storageService.downloadFileToLocal(apply.getFilePath(), localFilePath);
        List<PcoBaseInfoExcelDto> excelDtos = ExcelUtil.readExcel(localFilePath, PcoBaseInfoExcelDto.class);

        if(CollectionUtils.isEmpty(excelDtos)){
            return Boolean.TRUE;
        }
        log.info("获取导入文件记录数：{}", excelDtos.size());
        Map<String, OrganizationCache> orgMap = mapOrg();
        List<SettlementCostUploadPcoInfoEntity> pcoList =  Lists.newArrayListWithCapacity(excelDtos.size());
        Map<Integer,SubjectDataDynamicErrorBase> errorData = Maps.newHashMap();
        List<String> employeeCodes = excelDtos.stream().map(PcoBaseInfoExcelDto::getEmployeeCode).collect(Collectors.toList());
        Map<String, ChannelApplicationReferrerEntity>   employeeMap =  SettlementEmployeeHelper.mapByEmployeeCodes(employeeCodes);
        Set<String> orgNameSets = Sets.newHashSet();
        for(PcoBaseInfoExcelDto p : excelDtos){
            try {
                if(employeeMap.get(p.getEmployeeCode()) == null){
                    addErrorData(errorData,apply.getApplyCode(),apply.getCostSettlementCycle(),p.getNum(), StrUtil.format("工号{}对应的信息不存在",p.getEmployeeCode()),userName);
                }else if(!Objects.equals(employeeMap.get(p.getEmployeeCode()).getReferrerName(),p.getEmployeeName())){
                    addErrorData(errorData,apply.getApplyCode(),apply.getCostSettlementCycle(),p.getNum(), StrUtil.format("工号{}对应的姓名{}与导入的姓名{}不一致",p.getEmployeeCode(),employeeMap.get(p.getEmployeeCode()).getReferrerName(),p.getEmployeeName()),userName);
                }

                OrganizationCache org = orgMap.get(p.getOrgName());
                if(Objects.isNull(org)){
                    log.warn("PCO{}所属机构{}不存在",p.getEmployeeCode(),p.getOrgName());
                    addErrorData(errorData,apply.getApplyCode(),apply.getCostSettlementCycle(),p.getNum(), StrUtil.format("PCO{}所属机构{}不存在",p.getEmployeeCode(),p.getOrgName()),userName);
                    //throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("【异常】督导" + p.getEmployeeCode() + "所属机构" + p.getOrgName() + "不存在"));
                }
                if(orgNameSets.contains(p.getOrgName())){
                    addErrorData(errorData,apply.getApplyCode(),apply.getCostSettlementCycle(),p.getNum(), StrUtil.format("一个机构只能存在一行数据,重复机构名称{}",p.getOrgName()),userName);
                }else{
                    orgNameSets.add(p.getOrgName());
                }
                SettlementCostUploadPcoInfoEntity pcoInfo = new SettlementCostUploadPcoInfoEntity();
                BeanUtils.copyProperties(p, pcoInfo);
                pcoInfo.setOpMonth(apply.getCostSettlementCycle());
                if(Objects.nonNull(org)){
                    pcoInfo.setOrgCode(org.getBranchCode());
                }
                pcoInfo.setApplyCode(apply.getApplyCode());
                pcoList.add(pcoInfo);
            }catch (GlobalException e){
                log.warn("pco{}基础信息导入处理异常:{}",p.getEmployeeCode(),e.getMsg(),e);
                addErrorData(errorData,apply.getApplyCode(),apply.getCostSettlementCycle(),p.getNum(), StrUtil.format("pco{}基础信息导入处理异常:{}",p.getEmployeeCode(),e.getMsg()),userName);
                //throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("pco{}基础信息导入处理异常:{}",p.getEmployeeCode(),e.getMsg())));
            }catch (Exception e){
                addErrorData(errorData,apply.getApplyCode(),apply.getCostSettlementCycle(),p.getNum(), StrUtil.format("pco{}基础信息导入处理异常:{}",p.getEmployeeCode(),e.getMessage()),userName);
                //throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("pco{}基础信息导入处理异常:{}",p.getEmployeeCode(),e.getMessage())));

            }
        }

        Boolean addResult = true;
        //入库
        if(CollectionUtils.isEmpty(errorData.values())) {
            addResult = transactionTemplate.execute(new TransactionCallback<Boolean>() {
                @Override
                public Boolean doInTransaction(TransactionStatus transactionStatus) {
                    try {

                        settlementCostSubjectDataDynamicApplyService.lambdaUpdate().set(SettlementCostSubjectDataDynamicApplyEntity::getApplyStatus, 1)
                                .set(SettlementCostSubjectDataDynamicApplyEntity::getApplyEnable, 1)
                                .set(SettlementCostSubjectDataDynamicApplyEntity::getApplyMessage, Constant.DEFAULT_SUCCESS)
                                .eq(SettlementCostSubjectDataDynamicApplyEntity::getApplyCode, apply.getApplyCode())
                                .update();
                        settlementCostSubjectDataDynamicApplyService.lambdaUpdate().set(SettlementCostSubjectDataDynamicApplyEntity::getApplyStatus, -2)
                                .set(SettlementCostSubjectDataDynamicApplyEntity::getApplyEnable, 0)
                                .ne(SettlementCostSubjectDataDynamicApplyEntity::getApplyCode, apply.getApplyCode())
                                .eq(SettlementCostSubjectDataDynamicApplyEntity::getCostSettlementCycle, apply.getCostSettlementCycle())
                                .update();
                        settlementCostUploadPcoInfoService.remove(Wrappers.<SettlementCostUploadPcoInfoEntity>lambdaQuery()
                                .ne(SettlementCostUploadPcoInfoEntity::getApplyCode, apply.getApplyCode())
                                .eq(SettlementCostUploadPcoInfoEntity::getOpMonth,apply.getCostSettlementCycle()));
                        settlementCostUploadPcoInfoService.saveList(pcoList);
                        return Boolean.TRUE;
                    } catch (Exception e){
                        //回滚
                        log.warn("设置有效 + 清空之前的申请数据(包括抽数记录)失败",e);
                        transactionStatus.setRollbackOnly();
                        return Boolean.FALSE;
                    }
                }
            });

        }
        if(CollectionUtils.isNotEmpty(errorData.values()) || !addResult){

            if(CollectionUtils.isNotEmpty(errorData.values())){
                settlementCostDynamicSubjectErrorDataService.saveList(errorData.values().stream().collect(Collectors.toList()));
            }

            // 主表状态设置为失败
            settlementCostSubjectDataDynamicApplyService.lambdaUpdate().set(SettlementCostSubjectDataDynamicApplyEntity::getApplyStatus, -1)
                    .set(SettlementCostSubjectDataDynamicApplyEntity::getApplyMessage, "PCO基础信息导入导入失败")
                    .set(SettlementCostSubjectDataDynamicApplyEntity::getErrorNum,errorData.size())
                    .eq(SettlementCostSubjectDataDynamicApplyEntity::getApplyCode, apply.getApplyCode())

                    .update();
            log.warn("PCO基础信息导入导入失败!!!");
            return Boolean.FALSE;
        }
        return Boolean.TRUE;

    }

    private void addErrorData(Map<Integer,SubjectDataDynamicErrorBase> errorData,String applyCode,String costSettlementCycle,
                                Integer serialNumber,String errorMsg,String userName){
        Integer rowNum = serialNumber+1;
        if(errorData.containsKey(rowNum)){
            errorData.get(rowNum).addErrorMessage(errorMsg);
        }else {
            SubjectDataDynamicErrorBase errorBase = new SubjectDataDynamicErrorBase();
            errorBase.setSheetIndex(1);
            errorBase.setSheetName("sheet1");
            errorBase.setRowNum(rowNum);
            errorBase.setSerialNumber(serialNumber);
            errorBase.setApplyCode(applyCode);
            errorBase.setUserName(userName);
            errorBase.setCostSettlementCycle(costSettlementCycle);
            errorBase.addErrorMessage(errorMsg);
            errorData.put(rowNum,errorBase);
        }
    }

    public void deletePcoInfo(String applyCode,String costSettlementCycle){
        settlementCostUploadPcoInfoService.remove(Wrappers.<SettlementCostUploadPcoInfoEntity>lambdaQuery()
                .eq(SettlementCostUploadPcoInfoEntity::getApplyCode, applyCode)
                .eq(SettlementCostUploadPcoInfoEntity::getOpMonth,costSettlementCycle));
    }


}
