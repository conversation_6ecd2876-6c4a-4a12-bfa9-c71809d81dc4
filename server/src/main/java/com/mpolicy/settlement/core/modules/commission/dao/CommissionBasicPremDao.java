package com.mpolicy.settlement.core.modules.commission.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mpolicy.settlement.core.modules.commission.entity.CommissionBasicPremEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 基础佣金费率
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-31 18:39:39
 */
public interface CommissionBasicPremDao extends BaseMapper<CommissionBasicPremEntity> {

    @Update("<script>" +
            " update commission_basic_prem " +
            " <trim prefix='set' suffixOverrides=','> " +
            "   <trim prefix='monitor_code =case' suffix='end,'>" +
            "      <foreach collection='list' item='item' index='index'>" +
            "          when id=#{item.id} then #{item.monitorCode}" +
            "      </foreach>" +
            "   </trim> "+
            " </trim> "+
            "  where id in "+
            " <foreach collection='list' index='index' item='item' open='(' separator=',' close=')'> " +
            "     #{item.id}" +
            " </foreach> " +
            " </script>")
    Integer batchUpdateMonitorCode(@Param("list") List<CommissionBasicPremEntity> list);

}
