package com.mpolicy.settlement.core.modules.autocost.service.impl;

import com.mpolicy.settlement.core.helper.HeadBranchHelper;
import com.mpolicy.settlement.core.modules.autocost.dto.cache.OrganizationCache;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

public abstract class AbstractSettlementCostImportService {

    private static Map<String,OrganizationCache> regionOrgMap ;

    @Autowired
    protected HeadBranchHelper headBranchHelper;

    public Map<String, OrganizationCache> mapOrg(){
        Map<String,OrganizationCache> orgMap = new HashMap<>();
        Map<String,OrganizationCache> branchMap = headBranchHelper.mapBranchInfoByBranchName();
        if(branchMap!=null && !branchMap.isEmpty()){
            orgMap.putAll(branchMap);
        }
        orgMap.putAll(regionOrgMap);
        return orgMap;
    }

    public static OrganizationCache genOrganizationCache(String name,String code){
        OrganizationCache o = new OrganizationCache();
        o.setBranchCode(code);
        o.setBranchName(name);
        return o;
    }
    static{
        regionOrgMap = new HashMap<>();
        regionOrgMap.put("河北区域管理部",genOrganizationCache("河北区域管理部","HBQYGLB"));
        regionOrgMap.put("湖北区域管理部",genOrganizationCache("湖北区域管理部","Z6762126"));
        regionOrgMap.put("湖南区域管理部",genOrganizationCache("湖南区域管理部","124"));
        regionOrgMap.put("海南区域管理部", genOrganizationCache("海南区域管理部", "131"));
        regionOrgMap.put("内蒙区域管理部", genOrganizationCache("内蒙区域管理部", "NMQYGLB"));
        regionOrgMap.put("河南区域管理部", genOrganizationCache("河南区域管理部", "130"));
        regionOrgMap.put("山东区域管理部", genOrganizationCache("山东区域管理部", "120"));
        regionOrgMap.put("甘肃区域管理部", genOrganizationCache("甘肃区域管理部", "119"));
        regionOrgMap.put("山西区域管理部", genOrganizationCache("山西区域管理部", "139"));
        regionOrgMap.put("四川区域管理部", genOrganizationCache("四川区域管理部", "118"));
        regionOrgMap.put("云南区域管理部", genOrganizationCache("云南区域管理部", "141"));
        regionOrgMap.put("江西区域管理部", genOrganizationCache("江西区域管理部", "Z0072634"));
        regionOrgMap.put("辽宁区域管理部", genOrganizationCache("辽宁区域管理部", "123"));
        regionOrgMap.put("广东区域管理部", genOrganizationCache("广东区域管理部", "142"));
        regionOrgMap.put("江苏区域管理部", genOrganizationCache("江苏区域管理部", "Z7560282"));
        regionOrgMap.put("全面风险管理部", genOrganizationCache("全面风险管理部", "Z2673807"));
        regionOrgMap.put("综合管理部", genOrganizationCache("综合管理部", "ZHGL"));
        regionOrgMap.put("重庆区域管理部", genOrganizationCache("重庆区域管理部", "Z8176605"));
        regionOrgMap.put("城乡融合区域管理部", genOrganizationCache("城乡融合区域管理部", "214"));
        regionOrgMap.put("风险管理部", genOrganizationCache("风险管理部", "312"));
        regionOrgMap.put("运管直辖区域管理部", genOrganizationCache("运管直辖区域管理部", "133"));
        regionOrgMap.put("保险业务管理中心", genOrganizationCache("保险业务管理中心", "Z0862702"));
        regionOrgMap.put("保险事业部", genOrganizationCache("保险事业部", "YWTZ"));
        regionOrgMap.put("保险运营支持中心", genOrganizationCache("保险运营支持中心", "Z2135151"));
        regionOrgMap.put("农产品事业部", genOrganizationCache("农产品事业部", "Z3011323"));
        regionOrgMap.put("农服技术组", genOrganizationCache("农服技术组", "YYYWZ"));
        regionOrgMap.put("合规审核组", genOrganizationCache("合规审核组", "HECSZ"));
        regionOrgMap.put("和信学苑", genOrganizationCache("和信学苑", "108"));
        regionOrgMap.put("国际合作中心", genOrganizationCache("国际合作中心", "GJHZZX"));
        regionOrgMap.put("总裁办", genOrganizationCache("总裁办", "101"));
        regionOrgMap.put("核查中心", genOrganizationCache("核查中心", "113"));
        regionOrgMap.put("生活服务拓展中心", genOrganizationCache("生活服务拓展中心", "Z3821612"));
        regionOrgMap.put("组织发展中心", genOrganizationCache("组织发展中心", "ZZFZZX"));
        regionOrgMap.put("耐用品业务中心", genOrganizationCache("耐用品业务中心", "Z3821612")); // Duplicate entry, ensure it's intended
        regionOrgMap.put("融资部", genOrganizationCache("融资部", "105"));
        regionOrgMap.put("财务智能组", genOrganizationCache("财务智能组", "Z6658114"));
        regionOrgMap.put("运营组", genOrganizationCache("运营组", "Z1440183"));
        regionOrgMap.put("业务推进组", genOrganizationCache("业务推进组", "ZHGLZ"));
        regionOrgMap.put("渠道业务组", genOrganizationCache("崇礼", "HBCL"));
        regionOrgMap.put("渠道业务组", genOrganizationCache("沽源", "HBGY"));
        regionOrgMap.put("渠道业务组", genOrganizationCache("曲江", "GDQJ"));
        regionOrgMap.put("渠道业务组", genOrganizationCache("宣化", "HBXH"));
    }
}
