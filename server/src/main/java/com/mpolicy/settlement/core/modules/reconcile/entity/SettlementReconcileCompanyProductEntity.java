package com.mpolicy.settlement.core.modules.reconcile.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 结算保司配置对应规则科目表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-10 14:29:16
 */
@TableName("settlement_reconcile_company_product")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementReconcileCompanyProductEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private Integer id;
	/**
	 * 保司编码
	 */
	private String companyCode;
	/**
	 * 保司名称
	 */
	private String companyName;
	/**
	 * 保司对账单产品名称
	 */
	private String companyProductName;
	/**
	 * 协议险种编码
	 */
	private String insuranceProductCode;
	/**
	 * 协议险种名称
	 */
	private String insuranceProductName;
	/**
	 * 险种编码
	 */
	private String productCode;
	/**
	 * 险种名称
	 */
	private String productName;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 修改人
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private String updateUser;
	/**
	 * 修改时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
}
