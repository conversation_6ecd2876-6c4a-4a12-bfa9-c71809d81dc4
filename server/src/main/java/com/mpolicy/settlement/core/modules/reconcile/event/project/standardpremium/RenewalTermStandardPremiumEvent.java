package com.mpolicy.settlement.core.modules.reconcile.event.project.standardpremium;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.policy.common.standard.PolicyStandardPremiumVo;
import com.mpolicy.policy.common.standard.RenewalTermStandardPremiumVo;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.reconcile.dto.standardpremium.PolicyProductInsuredStandardPremiumDto;
import com.mpolicy.settlement.core.modules.reconcile.dto.standardpremium.PolicyProductStandardPremiumDto;
import com.mpolicy.settlement.core.modules.reconcile.dto.standardpremium.PolicyStandardPremiumDto;
import com.mpolicy.settlement.core.modules.reconcile.dto.standardpremium.RenewalTermStandardPremiumDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementStandardPremiumEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementStandardPremiumEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.common.AbsSettlementStandardPremiumEvent;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventCheckResult;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RenewalTermStandardPremiumEvent extends AbsSettlementStandardPremiumEvent {
    @Override
    public HandleEventCheckResult handleEventCheck(SettlementStandardPremiumEventJobEntity eventJob) {
        return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();
    }

    @Override
    public String handleEvent(SettlementStandardPremiumEventJobEntity eventJob) {
        String contractCode = eventJob.getContractCode();
        JSONObject params = JSONObject.parseObject(eventJob.getEventRequest());
        Integer period = params.getInteger("period");
        if(period == null){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("合同号{}的续期标保事件，期数为空，{}", eventJob.getContractCode(),eventJob.getEventRequest())));
        }
        RenewalTermStandardPremiumDto premiumDto = builderRenewalTermStandardPremium(contractCode,period);
        //冲正标志为1的标保都设置为0，
        Boolean result = transactionTemplate.execute(new TransactionCallback<Boolean>() {
            @Override
            public Boolean doInTransaction(TransactionStatus transactionStatus) {
                try {
                    settlementCostInfoService.lambdaUpdate().set(SettlementCostInfoEntity::getBusinessDiscountPremium, BigDecimal.ZERO)
                            .eq(SettlementCostInfoEntity::getContractCode,contractCode)
                            .in(SettlementCostInfoEntity::getInitialEventCode,handlerEventType().getSettlementEventCodeList())
                            .eq(SettlementCostInfoEntity::getRenewalPeriod,period)
                            .eq(SettlementCostInfoEntity::getSettlementSubjectCode, CostSubjectEnum.FIRST_BASIC_COMM.getCode())
                            .update();
                    if(CollectionUtils.isNotEmpty(premiumDto.getProductInsuredMapList())){
                        premiumDto.getProductInsuredMapList().stream().forEach(i->{
                            UpdateWrapper<SettlementCostInfoEntity> updateWrapper1 = new UpdateWrapper<>();
                            updateWrapper1.lambda().set(SettlementCostInfoEntity::getBusinessDiscountPremium,i.getStandardPremium())
                                    .eq(SettlementCostInfoEntity::getContractCode,contractCode)
                                    .in(SettlementCostInfoEntity::getInitialEventCode,handlerEventType().getSettlementEventCodeList())
                                    .eq(SettlementCostInfoEntity::getCorrectionFlag,0)
                                    .eq(SettlementCostInfoEntity::getSettlementSubjectCode, CostSubjectEnum.FIRST_BASIC_COMM.getCode())
                                    .eq(SettlementCostInfoEntity::getInsuredCode,i.getInsuredCode())
                                    .eq(SettlementCostInfoEntity::getProductCode,i.getProductCode())
                                    .eq(SettlementCostInfoEntity::getRenewalPeriod,period);
                            int result1 = settlementCostInfoService.getBaseMapper().update(null, updateWrapper1);
                            if(result1 == 0){
                                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("合同号{}的续期记录的标保未更新到,{}", eventJob.getContractCode(), JSON.toJSONString(i))));
                            }
                        });
                    }else{
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("合同号{}的标保续期险种明细不存在", eventJob.getContractCode())));
                    }
                    return Boolean.TRUE;
                } catch (Exception e){
                    //回滚
                    log.warn("回滚续期标保事件",e);
                    transactionStatus.setRollbackOnly();
                    return Boolean.FALSE;
                }
            }
        });
        if(!result){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("合同号{}的续期标保事件处理失败", eventJob.getContractCode())));
        }
        return  "success";
    }

    @Override
    public SettlementStandardPremiumEventTypeEnum handlerEventType() {
        return SettlementStandardPremiumEventTypeEnum.STANDARD_PREMIUM_RENEWAL_TERM;
    }

    private RenewalTermStandardPremiumDto builderRenewalTermStandardPremium(String contractCode,Integer period){
        RenewalTermStandardPremiumVo vo = super.policyCenterBaseClient.queryRenewalTermStandardPremium(contractCode,period);
        RenewalTermStandardPremiumDto dto = new RenewalTermStandardPremiumDto();
        BeanUtils.copyProperties(vo,dto);
        if(CollectionUtils.isNotEmpty(vo.getProductInsuredMapList())){
            List<PolicyProductInsuredStandardPremiumDto> insuredList = vo.getProductInsuredMapList().stream()
                    .map(m->{
                        PolicyProductInsuredStandardPremiumDto insuredStandardPremiumDto = new PolicyProductInsuredStandardPremiumDto();
                        BeanUtils.copyProperties(m,insuredStandardPremiumDto);
                        return insuredStandardPremiumDto;
                    }).collect(Collectors.toList());
            dto.setProductInsuredMapList(insuredList);

        }
        return dto;
    }
}
