package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementReconcileCompanyProductDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileCompanyProductEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcileCompanyProductService;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 */
@Service("settlementReconcileCompanyProductService")
public class SettlementReconcileCompanyProductServiceImpl extends ServiceImpl<SettlementReconcileCompanyProductDao, SettlementReconcileCompanyProductEntity> implements SettlementReconcileCompanyProductService {

}
