package com.mpolicy.settlement.core.thirdpart.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NonNull;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class DbcBusinessIncentiveCalculateDataAddDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank
    @ApiModelProperty(value = "业务系统，见枚举 IncentiveCalcBusinessSystemEnum ：ERP（erp系统），INSURANCE（保险系统）")
    private String incentiveCalcBusinessSystem;

    @NotBlank
    @ApiModelProperty(value = "激励计算业务类型，见枚举 IncentiveCalcBusinessTypeEnum")
    private String incentiveCalcBusinessType;

    @ApiModelProperty(value = "业务编码：erp系统，租户ID")
    private String businessCode;

    @ApiModelProperty(value = "业务编码：erp系统，租户ID")
    private String businessCodeType;

    @NotBlank
    @ApiModelProperty(value = "统计月份 YYYY-MM")
    private String statisticsMonth;

    @ApiModelProperty(value = "督导编码/分支编码")
    @NotBlank
    private String orgEntityCode;

    @ApiModelProperty(value = "金额")
    private BigDecimal amt;

    @ApiModelProperty(value = "批次号")
    @NotBlank
    private String batchNo;
}
