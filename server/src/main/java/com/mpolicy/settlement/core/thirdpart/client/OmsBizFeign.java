package com.mpolicy.settlement.core.thirdpart.client;

import com.mpolicy.settlement.core.thirdpart.dto.omsbiz.LicenseSyncRequest;
import com.mpolicy.settlement.core.thirdpart.dto.omsbiz.LicenseSyncResponse;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * oms-biz服务Feign客户端
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@FeignClient(name = "oms-biz-service", url = "${oms-biz.api.url}")
@Configuration
public interface OmsBizFeign {

    /**
     * 工商信息同步接口
     * 
     * @param request 同步请求参数
     * @return 同步响应结果
     */
    @ApiOperation(value = "工商信息同步")
    @PostMapping("/license/new/sync")
    LicenseSyncResponse syncLicenseInfo(@RequestBody LicenseSyncRequest request);
}
