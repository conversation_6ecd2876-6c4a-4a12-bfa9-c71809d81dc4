package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.other.BigDecimalUtils;
import com.mpolicy.policy.common.ep.policy.EpAgentInfoVo;
import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.policy.common.ep.policy.EpPolicyChannelInfoVo;
import com.mpolicy.policy.common.ep.policy.qry.details.GroupPreservationInsuredVo;
import com.mpolicy.settlement.core.common.Constant;
import com.mpolicy.settlement.core.modules.reconcile.dto.cache.EmployeePostRecordCache;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.*;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.OwnerTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.helper.EmployeePostRecordHelper;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementCostOwnerService;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import com.mpolicy.settlement.core.utils.LambdaUtils;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.mpolicy.settlement.core.common.Constant.GDYW_CHANNEL_CODE;
import static com.mpolicy.settlement.core.common.Constant.ZHNX_CHANNEL_CODE;

/**
 * <AUTHOR>
 * @description
 * @date 2023/9/17 10:34 上午
 * @Version 1.0
 */
@Slf4j
@Service("settlementCostOwnerService")
public class SettlementCostOwnerServiceImpl implements SettlementCostOwnerService {
    /**
     * 根据保全记录获取佣金归属人
     * @param preservationDetail
     * @return
     */
    @Override
    public String getPreservationOwnerCodeByPreservationDetail(PolicyPreservationDetailDto preservationDetail){
        if(Objects.equals(preservationDetail.getSellChannelCode(), Constant.ZHNX_CHANNEL_CODE)){
            if(Objects.nonNull(preservationDetail.getCustomerManagerDto()) ){
                return preservationDetail.getCustomerManagerDto().getCustomerManagerCode();
            }else{
                /*if(StringUtils.isNotBlank(preservationDetail.getChannelReferrerCode())){
                    return preservationDetail.getChannelReferrerCode();
                }else{
                    return preservationDetail.getPolicyAgentCode();
                }*/
                return "";
            }
        }else{
            if(StringUtils.isNotBlank(preservationDetail.getChannelReferrerCode())){
                return preservationDetail.getChannelReferrerCode();
            }else{
                return preservationDetail.getPolicyAgentCode();
            }
        }
    }

    @Override
    public String getSplitPolicyOwnerCodeByPreservationDetail(PreserveChannelReferrerChangeDto dto){
        log.info("getSplitPolicyOwnerCodeByPreservationDetail.params={}", JSON.toJSONString(dto));
        if(Objects.equals(dto.getChannelCode(), Constant.ZHNX_CHANNEL_CODE)){
            if(StringUtils.isNotBlank(dto.getCustomerManagerCode())){
                return dto.getCustomerManagerCode();
            }else{
                return "";
            }
        }else{
            if(StringUtils.isNotBlank(dto.getChannelReferrerCode())){
                return dto.getChannelReferrerCode();
            }else{
                return dto.getReferrerCode();
            }
        }
    }
    /**
     * 个险保单佣金归属人处理
     * @param policyInfo
     * @param costInfoEntities
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> builderBasePolicyCostOwnerInfo(EpContractInfoVo policyInfo, List<SettlementCostInfoEntity> costInfoEntities){
        if(Objects.equals(policyInfo.getChannelInfo().getChannelCode(), Constant.ZHNX_CHANNEL_CODE)){
            return builderCostOwnerInfoByCustomerManager(policyInfo.getChannelInfo(),costInfoEntities);
        }else{
            //渠道推荐人 在保单详情表中渠道推荐人用的referrerCode，而在团险增减员中用channelReferrerCode
            if(StringUtils.isNotBlank(policyInfo.getChannelInfo().getReferrerCode())){
                return builderCostOwnerInfoByReferrer(policyInfo.getChannelInfo(),costInfoEntities);
            }else {
                return builderCostOwnerInfoByAgentInfo(policyInfo.getChannelInfo(), policyInfo.getAgentInfoList(), costInfoEntities);
            }
        }
    }

    /**
     * 个险保单佣金归属人处理
     * @param policyInfo
     * @param costInfoEntities
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> builderRenewalPolicyCostOwnerInfo(EpContractInfoVo policyInfo,PolicyRenewalTermDto renewalTermDto, List<SettlementCostInfoEntity> costInfoEntities){


        if(Objects.equals(policyInfo.getChannelInfo().getChannelCode(), Constant.ZHNX_CHANNEL_CODE)){
            if(renewalTermDto.getCustomerManagerChannelCode() == null){
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-佣金归属信息-农保渠道推荐人为空")));
            }
            for(SettlementCostInfoEntity costInfoEntity : costInfoEntities){
                costInfoEntity.setOwnerChannelCode(Constant.ZHNX_CHANNEL_CODE);
                costInfoEntity.setOwnerCode(renewalTermDto.getCustomerManagerCode());
                costInfoEntity.setOwnerThirdCode(renewalTermDto.getCustomerManagerChannelCode());
                //costInfoEntity.setOwnerName(renewalTermDto.getReferrerName());
                costInfoEntity.setOwnerOrgCode(renewalTermDto.getCustomerManagerOrgCode());
                costInfoEntity.setOwnerThirdOrg(renewalTermDto.getCustomerManagerChannelOrgCode());
                //costInfoEntity.setOwnerOrgName(renewalTermDto.get());
                costInfoEntity.setOwnerType(OwnerTypeEnum.RECOMMEND.getCode());
            }
            return costInfoEntities;
        }else{
            for(SettlementCostInfoEntity costInfoEntity : costInfoEntities){
                costInfoEntity.setOwnerChannelCode(policyInfo.getChannelInfo().getChannelCode());
                costInfoEntity.setOwnerCode(renewalTermDto.getCustomerManagerCode());
                costInfoEntity.setOwnerThirdCode(renewalTermDto.getCustomerManagerChannelCode());
                //costInfoEntity.setOwnerName(renewalTermDto.getReferrerName());
                costInfoEntity.setOwnerOrgCode(renewalTermDto.getCustomerManagerOrgCode());
                costInfoEntity.setOwnerThirdOrg(renewalTermDto.getCustomerManagerChannelOrgCode());
                //costInfoEntity.setOwnerOrgName(renewalTermDto.get());
                if(StringUtil.isBlank(renewalTermDto.getCustomerManagerChannelCode())){
                    costInfoEntity.setOwnerType(OwnerTypeEnum.REFERRER.getCode());
                }else{
                    costInfoEntity.setOwnerType(OwnerTypeEnum.CHANNEL_REFERRER.getCode());
                }

            }
            return costInfoEntities;
        }

    }


    @Override
    public void builderGroupPolicySurrenderOwnerInfoByInsured(String channelCode, EpPreserveSurrenderInsuredDto insured, SettlementCostInfoEntity costInfoEntity) {
        if (Objects.equals(channelCode, Constant.ZHNX_CHANNEL_CODE)) {
            costInfoEntity.setOwnerChannelCode(channelCode);
            costInfoEntity.setOwnerCode(insured.getCustomerManagerCode());
            //costInfoEntity.setOwnerName();
            costInfoEntity.setOwnerOrgCode(insured.getCustomerManagerOrgCode());
            //costInfoEntity.setOwnerOrgName();
            costInfoEntity.setOwnerThirdCode(insured.getCustomerManagerChannelCode());
            costInfoEntity.setOwnerThirdOrg(insured.getCustomerManagerChannelOrgCode());
            costInfoEntity.setOwnerThirdSuperiorCode(insured.getCustomerManagerSupervisor());
            costInfoEntity.setOwnerType(OwnerTypeEnum.RECOMMEND.getCode());
        } else {
            if (StringUtils.isNotBlank(insured.getChannelReferrerCode())) {
                costInfoEntity.setOwnerChannelCode(channelCode);
                costInfoEntity.setOwnerCode(insured.getChannelReferrerCode());
                //costInfoEntity.setOwnerName();
                costInfoEntity.setOwnerOrgCode(insured.getChannelBranchCode());
                costInfoEntity.setOwnerType(OwnerTypeEnum.CHANNEL_REFERRER.getCode());
            } else if (StringUtil.isNotBlank(insured.getReferrerCode())) {
                costInfoEntity.setOwnerChannelCode(channelCode);
                costInfoEntity.setOwnerCode(insured.getReferrerCode());
                //costInfoEntity.setOwnerName();
                costInfoEntity.setOwnerOrgCode(insured.getOrgCode());
                costInfoEntity.setOwnerType(OwnerTypeEnum.REFERRER.getCode());
            } else {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-团险退保佣金归属人信息获取为空-合同号={},保全号={}, 根据保全记录获取推荐人为空", costInfoEntity.getContractCode(), costInfoEntity.getPreservationCode())));
            }
        }
    }

    @Override
    public void builderGroupPolicyAddCostOwnerInfoByInsured(String channelCode,GroupPreservationInsuredVo insured, SettlementCostInfoEntity costInfoEntity){
        if(Objects.equals(channelCode, Constant.ZHNX_CHANNEL_CODE)) {
            costInfoEntity.setOwnerChannelCode(channelCode);
            costInfoEntity.setOwnerCode(insured.getCustomerManagerCode());
            //costInfoEntity.setOwnerName();
            costInfoEntity.setOwnerOrgCode(insured.getCustomerManagerOrgCode());
            //costInfoEntity.setOwnerOrgName();
            costInfoEntity.setOwnerThirdCode(insured.getCustomerManagerChannelCode());
            costInfoEntity.setOwnerThirdOrg(insured.getCustomerManagerChannelOrgCode());
            costInfoEntity.setOwnerThirdSuperiorCode(insured.getCustomerManagerSupervisor());
            costInfoEntity.setOwnerType(OwnerTypeEnum.RECOMMEND.getCode());
        }else {
            if(StringUtils.isNotBlank(insured.getChannelReferrerCode())){
                costInfoEntity.setOwnerChannelCode(channelCode);
                costInfoEntity.setOwnerCode(insured.getChannelReferrerCode());
                //costInfoEntity.setOwnerName();
                costInfoEntity.setOwnerOrgCode(insured.getChannelBranchCode());
                costInfoEntity.setOwnerType(OwnerTypeEnum.CHANNEL_REFERRER.getCode());
            }else if(StringUtil.isNotBlank(insured.getReferrerCode())){
                costInfoEntity.setOwnerChannelCode(channelCode);
                costInfoEntity.setOwnerCode(insured.getReferrerCode());
                //costInfoEntity.setOwnerName();
                costInfoEntity.setOwnerOrgCode(insured.getOrgCode());
                costInfoEntity.setOwnerType(OwnerTypeEnum.REFERRER.getCode());
            }else{
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-团险增减员/新契约佣金归属人信息获取为空-合同号={},保全号={}, 根据保全记录获取推荐人为空", costInfoEntity.getContractCode(),costInfoEntity.getPreservationCode())));
            }
        }

//        }else if(Objects.equals(channelCode,Constant.GDYW_CHANNEL_CODE)){
//
//            costInfoEntity.setOwnerChannelCode(channelCode);
//            costInfoEntity.setOwnerCode(insured.getReferrerCode());
//            //costInfoEntity.setOwnerName();
//            costInfoEntity.setOwnerOrgCode(insured.getOrgCode());
//            costInfoEntity.setOwnerType(OwnerTypeEnum.REFERRER.getCode());
//        }else{
//            costInfoEntity.setOwnerChannelCode(channelCode);
//            costInfoEntity.setOwnerCode(insured.getChannelReferrerCode());
//            //costInfoEntity.setOwnerName();
//            costInfoEntity.setOwnerOrgCode(insured.getChannelBranchCode());
//            costInfoEntity.setOwnerType(OwnerTypeEnum.CHANNEL_REFERRER.getCode());
//        }
    }
    @Override
    public void builderGroupPolicyNewAddCostOwnerInfoByInsured(String channelCode,EpPreserveAddSubtractDetailDto insured, SettlementCostInfoEntity costInfoEntity) {
        if (Objects.equals(channelCode, Constant.ZHNX_CHANNEL_CODE)) {
            costInfoEntity.setOwnerChannelCode(channelCode);
            costInfoEntity.setOwnerCode(insured.getCustomerManagerCode());
            //costInfoEntity.setOwnerName();
            costInfoEntity.setOwnerOrgCode(insured.getCustomerManagerOrgCode());
            //costInfoEntity.setOwnerOrgName();
            costInfoEntity.setOwnerThirdCode(insured.getCustomerManagerChannelCode());
            costInfoEntity.setOwnerThirdOrg(insured.getCustomerManagerChannelOrgCode());
            costInfoEntity.setOwnerThirdSuperiorCode(insured.getCustomerManagerSupervisor());
            costInfoEntity.setOwnerType(OwnerTypeEnum.RECOMMEND.getCode());
        } else {
            if (StringUtils.isNotBlank(insured.getChannelReferrerCode())) {
                costInfoEntity.setOwnerChannelCode(channelCode);
                costInfoEntity.setOwnerCode(insured.getChannelReferrerCode());
                //costInfoEntity.setOwnerName();
                costInfoEntity.setOwnerOrgCode(insured.getChannelBranchCode());
                costInfoEntity.setOwnerType(OwnerTypeEnum.CHANNEL_REFERRER.getCode());
            } else if (StringUtil.isNotBlank(insured.getReferrerCode())) {
                costInfoEntity.setOwnerChannelCode(channelCode);
                costInfoEntity.setOwnerCode(insured.getReferrerCode());
                //costInfoEntity.setOwnerName();
                costInfoEntity.setOwnerOrgCode(insured.getOrgCode());
                costInfoEntity.setOwnerType(OwnerTypeEnum.REFERRER.getCode());
            } else {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-团险增减员佣金归属人信息获取为空-合同号={},保全号={}, 根据保全记录获取推荐人为空", costInfoEntity.getContractCode(), costInfoEntity.getPreservationCode())));
            }
        }
    }

    /**
     * 非退保的保单维度保全佣金归属人处理
     * @param preservationDetail
     * @param costInfoEntities
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> builderPreservationCostOwnerInfo(PolicyPreservationDetailDto preservationDetail,List<SettlementCostInfoEntity> costInfoEntities){
        if(Objects.equals(preservationDetail.getSellChannelCode(), Constant.ZHNX_CHANNEL_CODE)){
            if(Objects.isNull(preservationDetail.getCustomerManagerDto()) || preservationDetail.getCustomerManagerDto().getCustomerManagerChannelCode() == null){
                //return costInfoEntities;
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-保全批改-佣金归属信息-农保渠道推荐人为空")));
            }
            CustomerManagerDto customer = preservationDetail.getCustomerManagerDto();
            for(SettlementCostInfoEntity costInfoEntity : costInfoEntities) {
                costInfoEntity.setOwnerChannelCode(preservationDetail.getSellChannelCode());
                costInfoEntity.setOwnerCode(customer.getCustomerManagerCode());
                //costInfoEntity.setOwnerName();
                costInfoEntity.setOwnerOrgCode(customer.getCustomerManagerOrgCode());
                //costInfoEntity.setOwnerOrgName();
                costInfoEntity.setOwnerThirdCode(customer.getCustomerManagerChannelCode());
                costInfoEntity.setOwnerThirdOrg(customer.getCustomerManagerChannelOrgCode());
                costInfoEntity.setOwnerThirdSuperiorCode(customer.getCustomerManagerSupervisor());
                costInfoEntity.setOwnerType(OwnerTypeEnum.RECOMMEND.getCode());
            }
        }else{
            for(SettlementCostInfoEntity costInfoEntity : costInfoEntities) {
                costInfoEntity.setOwnerChannelCode(preservationDetail.getSellChannelCode());
                if(StringUtils.isNotBlank(preservationDetail.getChannelReferrerCode())){
                    costInfoEntity.setOwnerChannelCode(preservationDetail.getSellChannelCode());
                    costInfoEntity.setOwnerCode(preservationDetail.getChannelReferrerCode());
                    costInfoEntity.setOwnerName(preservationDetail.getChannelReferrerName());
                    costInfoEntity.setOwnerOrgCode(preservationDetail.getChannelBranchCode());
                    costInfoEntity.setOwnerType(OwnerTypeEnum.CHANNEL_REFERRER.getCode());
                }else {

                    costInfoEntity.setOwnerCode(preservationDetail.getPolicyAgentCode());
                    costInfoEntity.setOwnerName(preservationDetail.getPolicyAgentName());
                    costInfoEntity.setOwnerOrgCode(preservationDetail.getManageOrgCode());
                    costInfoEntity.setOwnerName(preservationDetail.getManageOrgName());
                    costInfoEntity.setOwnerType(OwnerTypeEnum.AGENT.getCode());
                }
            }


        }
        return costInfoEntities;
    }

    /**
     * 非退保的保单维度保全佣金归属人处理
     * @param preservationDetail
     * @param costInfoEntity
     * @return
     */
    @Override
    public void builderSinglePreservationCostOwnerInfo(String channelCode,PolicyPreservationDetailDto preservationDetail,SettlementCostInfoEntity costInfoEntity){
        if(Objects.equals(channelCode, Constant.ZHNX_CHANNEL_CODE)){
            if(Objects.isNull(preservationDetail.getCustomerManagerDto()) || preservationDetail.getCustomerManagerDto().getCustomerManagerChannelCode() == null){
                //return costInfoEntities;
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-保全批改-佣金归属信息-农保渠道推荐人为空")));
            }
            CustomerManagerDto customer = preservationDetail.getCustomerManagerDto();

            costInfoEntity.setOwnerChannelCode(channelCode);
            costInfoEntity.setOwnerCode(customer.getCustomerManagerCode());
            //costInfoEntity.setOwnerName();
            costInfoEntity.setOwnerOrgCode(customer.getCustomerManagerOrgCode());
            //costInfoEntity.setOwnerOrgName();
            costInfoEntity.setOwnerThirdCode(customer.getCustomerManagerChannelCode());
            costInfoEntity.setOwnerThirdOrg(customer.getCustomerManagerChannelOrgCode());
            costInfoEntity.setOwnerThirdSuperiorCode(customer.getCustomerManagerSupervisor());
            costInfoEntity.setOwnerType(OwnerTypeEnum.RECOMMEND.getCode());

        }else{
            costInfoEntity.setOwnerChannelCode(channelCode);
            if(StringUtils.isNotBlank(preservationDetail.getChannelReferrerCode())){

                costInfoEntity.setOwnerCode(preservationDetail.getChannelReferrerCode());
                costInfoEntity.setOwnerName(preservationDetail.getChannelReferrerName());
                costInfoEntity.setOwnerOrgCode(preservationDetail.getChannelBranchCode());
                costInfoEntity.setOwnerType(OwnerTypeEnum.CHANNEL_REFERRER.getCode());
            }else {

                costInfoEntity.setOwnerCode(preservationDetail.getPolicyAgentCode());
                costInfoEntity.setOwnerName(preservationDetail.getPolicyAgentName());
                costInfoEntity.setOwnerOrgCode(preservationDetail.getManageOrgCode());
                costInfoEntity.setOwnerName(preservationDetail.getManageOrgName());
                costInfoEntity.setOwnerType(OwnerTypeEnum.AGENT.getCode());
            }
        }
    }

    /**
     * 根据渠道信息创建佣金归属人信息
     */
    public List<SettlementCostInfoEntity> builderCostOwnerInfoByCustomerManager(EpPolicyChannelInfoVo channelInfo, List<SettlementCostInfoEntity> costInfoEntities){
        //验证农保客户编号是否存在
        //中和农信渠道，推荐人不存在也要计算佣金 2024-04-29
//        if(StringUtils.isBlank(channelInfo.getCustomerManagerCode())) {
//            //return costInfoEntities;
//            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-佣金归属信息-农保渠道推荐人为空")));
//        }
        for(SettlementCostInfoEntity costInfoEntity : costInfoEntities){

            costInfoEntity.setOwnerChannelCode(channelInfo.getChannelCode());
            costInfoEntity.setOwnerCode(channelInfo.getCustomerManagerCode());
            //costInfoEntity.setOwnerName();
            costInfoEntity.setOwnerOrgCode(channelInfo.getCustomerManagerOrgCode());
            //costInfoEntity.setOwnerOrgName();
            costInfoEntity.setOwnerThirdCode(channelInfo.getCustomerManagerChannelCode());
            costInfoEntity.setOwnerThirdOrg(channelInfo.getCustomerManagerChannelOrgCode());
            costInfoEntity.setOwnerThirdSuperiorCode(channelInfo.getCustomerManagerSupervisor());

            costInfoEntity.setOwnerType(OwnerTypeEnum.RECOMMEND.getCode());
        }
        return costInfoEntities;
    }

    /**
     * 渠道推荐人
     * @param channelInfo
     * @param costInfoEntities
     * @return
     */
    public List<SettlementCostInfoEntity> builderCostOwnerInfoByReferrer(EpPolicyChannelInfoVo channelInfo,List<SettlementCostInfoEntity> costInfoEntities){
        for(SettlementCostInfoEntity costInfoEntity : costInfoEntities){
            costInfoEntity.setOwnerChannelCode(channelInfo.getChannelCode());
            costInfoEntity.setOwnerCode(channelInfo.getReferrerCode());
            costInfoEntity.setOwnerName(channelInfo.getReferrerName());
            costInfoEntity.setOwnerOrgCode(channelInfo.getChannelBranchCode());
            costInfoEntity.setOwnerOrgName(channelInfo.getBranchName());
            costInfoEntity.setOwnerType(OwnerTypeEnum.CHANNEL_REFERRER.getCode());
        }
        return costInfoEntities;
    }

    /**
     *
     * @param channelInfo
     * @param agentList
     * @param costInfoEntities
     * @return
     */
    public List<SettlementCostInfoEntity> builderCostOwnerInfoByAgentInfo(EpPolicyChannelInfoVo channelInfo, List<EpAgentInfoVo> agentList, List<SettlementCostInfoEntity> costInfoEntities){

        if(StringUtil.isNotBlank(channelInfo.getPolicyReferrerCode())){
            for(SettlementCostInfoEntity costInfoEntity : costInfoEntities){
                costInfoEntity.setOwnerChannelCode(channelInfo.getChannelCode());
                costInfoEntity.setOwnerCode(channelInfo.getPolicyReferrerCode());
                costInfoEntity.setOwnerName(channelInfo.getPolicyReferrerName());
                costInfoEntity.setOwnerOrgCode(channelInfo.getPolicyReferrerOrgCode());
                costInfoEntity.setOwnerType(OwnerTypeEnum.REFERRER.getCode());
            }
            return costInfoEntities;
        }else {
            List<SettlementCostInfoEntity> costInfoEntityList = Lists.newArrayList();
            Integer agentNum = agentList.size();
            for (SettlementCostInfoEntity costInfoEntity : costInfoEntities) {
                if (agentNum > 1) {
                    for (int i = 0; i < agentNum; i++) {
                        EpAgentInfoVo agent = agentList.get(i);
                        if (i == 0) {
                            agentInfoToOwnerInfo(channelInfo.getChannelCode(), agent, costInfoEntity, Boolean.TRUE);
                            costInfoEntityList.add(costInfoEntity);
                        } else {
                            SettlementCostInfoEntity entity = costInfoEntity.clone();
                            //需要重置单据明细唯一编号
                            entity.setCostCode(PolicySettlementUtils.createCodeLastNumber("CC"));
                            agentInfoToOwnerInfo(channelInfo.getChannelCode(), agent, entity, Boolean.TRUE);
                            costInfoEntityList.add(entity);
                        }
                    }
                } else {
                    agentInfoToOwnerInfo(channelInfo.getChannelCode(), agentList.get(0), costInfoEntity, Boolean.FALSE);
                    costInfoEntityList.add(costInfoEntity);
                }
            }
            return costInfoEntityList;
        }

    }

    /**
     * 代理人转佣金归属人
     * @param channelCode
     * @param agent
     * @param costInfoEntity
     * @param reCalc         是否需要重新计算，多个代理人的需要重算
     */
    private void agentInfoToOwnerInfo(String channelCode,EpAgentInfoVo agent, SettlementCostInfoEntity costInfoEntity,Boolean reCalc){
        costInfoEntity.setOwnerChannelCode(channelCode);
        costInfoEntity.setOwnerCode(agent.getAgentCode());
        costInfoEntity.setOwnerName(agent.getAgentName());
        costInfoEntity.setOwnerOrgCode(agent.getOrgCode());
        costInfoEntity.setOwnerName(agent.getOrgName());
        costInfoEntity.setOwnerType(OwnerTypeEnum.AGENT.getCode());
        if(reCalc) {
            //代理人分佣比例验证，分佣金比例为空、大于1，小于等于0的都是异常的分佣金比例
            if(agent.getCommissionRate() == null || agent.getCommissionRate().compareTo(BigDecimal.ONE)>0 || agent.getCommissionRate().compareTo(BigDecimal.ZERO)<=0){
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-佣金归属信息-代理人{}的分佣比例异常-合同编号={},代理人的分佣比例：{}",agent.getAgentName(),costInfoEntity.getContractCode(),agent.getCommissionRate())));
            }
            costInfoEntity.setCostDivideRate(agent.getCommissionRate());
            //佣金实际比列
            costInfoEntity.setCostActualRate(BigDecimalUtils.mul(costInfoEntity.getCostRate().doubleValue(), agent.getCommissionRate().doubleValue()));
            BigDecimal amt = BigDecimalUtils.mul(costInfoEntity.getPremium().doubleValue(),costInfoEntity.getCostActualRate().doubleValue());
            costInfoEntity.setCostAmount(amt);
            costInfoEntity.setGrantRate(costInfoEntity.getCostActualRate());
            costInfoEntity.setGrantAmount(amt);
        }
    }
    @Override
    public void builderOwnerInfoByCustomerManageChange(PreservationCustomerChangeDto afterCustomerManager,SettlementCostInfoEntity bean){
        bean.setOwnerCode(afterCustomerManager.getAfterCustomerManagerCode());
        bean.setOwnerName(null);
        //bean.setOwnerOrgName(null);
        bean.setOwnerOrgCode(afterCustomerManager.getAfterCustomerManagerOrgCode());
        bean.setOwnerChannelCode(afterCustomerManager.getAfterChannelCode());
        bean.setOwnerThirdSuperiorCode(afterCustomerManager.getAfterCustomerManagerSupervisor());
        bean.setOwnerThirdOrg(afterCustomerManager.getAfterCustomerManagerChannelOrgCode());
        bean.setOwnerThirdCode(afterCustomerManager.getAfterCustomerManagerChannelCode());
        if(Objects.equals(afterCustomerManager.getAfterChannelCode(),ZHNX_CHANNEL_CODE)) {
            bean.setOwnerType(OwnerTypeEnum.RECOMMEND.getCode());
        }else if(Objects.equals(afterCustomerManager.getAfterChannelCode(),GDYW_CHANNEL_CODE)){
            bean.setOwnerType(OwnerTypeEnum.REFERRER.getCode());
        }else{
            bean.setOwnerType(OwnerTypeEnum.CHANNEL_REFERRER.getCode());
        }
    }

    /**
     * 根据历史佣金信息设置佣金归属人
     * @param oldCostInfo
     * @param bean
     */
    @Override
    public void builderOwnerInfoByOldCostInfo(SettlementCostInfoEntity oldCostInfo ,SettlementCostInfoEntity bean){
        bean.setOwnerCode(oldCostInfo.getOwnerCode());
        bean.setOwnerName(oldCostInfo.getOwnerName());
        //bean.setOwnerOrgName(null);
        bean.setOwnerOrgCode(oldCostInfo.getOwnerOrgCode());
        bean.setOwnerChannelCode(oldCostInfo.getOwnerChannelCode());
        bean.setOwnerThirdSuperiorCode(oldCostInfo.getOwnerThirdSuperiorCode());
        bean.setOwnerThirdOrg(oldCostInfo.getOwnerThirdOrg());
        bean.setOwnerThirdCode(oldCostInfo.getOwnerThirdCode());
        bean.setOwnerType(oldCostInfo.getOwnerType());

    }

    public void handlerOwnerThirdStatus(SettlementCostPolicyInfoEntity costPolicy,List<SettlementCostInfoEntity> costInfoEntities){
        if(CollectionUtils.isEmpty(costInfoEntities)){
            return ;
        }
        //必须是中和农信渠道，且农保的工号不为空
        List<SettlementCostInfoEntity> costInfos = costInfoEntities.stream().filter(c->Objects.equals(c.getOwnerChannelCode(),ZHNX_CHANNEL_CODE)
                && StringUtils.isNotBlank(c.getOwnerThirdCode())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(costInfos)){
            return ;
        }
        Map<String,List<SettlementCostInfoEntity>> costMap = LambdaUtils.groupBy(costInfos,SettlementCostInfoEntity::getOwnerThirdCode);
        for(String key : costMap.keySet()){
            EmployeePostRecordCache postRecordCache = EmployeePostRecordHelper.getEmployeePostRecordCache(key);
            if(Objects.nonNull(postRecordCache)) {
                List<SettlementCostInfoEntity> costList = costMap.get(key);
                costList.stream().forEach(c -> {
                    doOwnerThirdStatus(postRecordCache,costPolicy,c);
                });
            }
        }
    }

    private void doOwnerThirdStatus(EmployeePostRecordCache postRecordCache,SettlementCostPolicyInfoEntity costPolicy,SettlementCostInfoEntity c){

        if(StringUtils.isBlank(postRecordCache.getLastWorkDate())){
            c.setOwnerThirdStatus(1);
        }else {
            SettlementEventTypeEnum eventTypeEnum = SettlementEventTypeEnum.deCode(c.getInitialEventCode());
            Date lastWorkDate = DateUtil.parseDate(postRecordCache.getLastWorkDate());
            c.setOwnerThirdLastWorkDate(lastWorkDate);
            switch (eventTypeEnum) {
                case PERSONAL_NEW_POLICY:
                case RENEWAL_POLICY:
                case GROUP_NEW_POLICY: {
                    try {
                        checkLastWorkDate(c, lastWorkDate, costPolicy.getOrderTime());
                        break;
                    }catch (Exception e){
                        log.warn("合同编号{}的记录编号为{}的佣金信息存在异常，交单时间为{},客户经理离职时间为{}",c.getContractCode(),c.getCostCode(),costPolicy.getOrderTime(),lastWorkDate);
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(e.getMessage()));
                    }
                }
                case RENEWAL_TERM_POLICY:{
                    try {
                        checkLastWorkDate(c,lastWorkDate,c.getRealityTime());
                        break;
                    }catch (Exception e){
                        log.warn("合同编号{}的记录编号为{}的佣金信息存在异常，实缴时间为{},客户经理离职时间为{}",c.getContractCode(),c.getCostCode(),c.getRealityTime(),lastWorkDate);
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(e.getMessage()));
                    }
                }
                case STANDARD_SURRENDER:
                case HESITATE_SURRENDER:
                case PROTOCOL_TERMINATION:
                case GROUP_ADD_OR_SUBTRACT:
                case TERMINATION_PRODUCT:{
                    try {
                        if(Objects.equals(c.getSettlementEventCode(),c.getInitialEventCode())){
                            checkLastWorkDate(c,lastWorkDate,c.getBusinessAccountTime());
                        }else{
                            checkLastWorkDate(c,lastWorkDate,c.getPreservationEffectTime());
                        }

                        break;
                    }catch (Exception e){
                        log.info("合同编号{}的记录编号为{}的佣金信息存在异常，保全事件为：{},业务记账时间为{},保全生效时间{},客户经理离职时间为{}",
                                c.getContractCode(),c.getCostCode(),c.getInitialEventCode(),c.getBusinessAccountTime(),c.getPreservationEffectTime(),lastWorkDate);
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(e.getMessage()));
                    }
                }
                default:
                    log.warn("不支持此事件类型");
            }
        }

    }
    private void checkLastWorkDate(SettlementCostInfoEntity c,Date lastWorkDate,Date transformTime){
        //交易事件不存在，按离职处理
        if(transformTime == null){
            c.setOwnerThirdStatus(0);
        }
        if (lastWorkDate.after(transformTime)) {
            c.setOwnerThirdStatus(1);
        } else {
            c.setOwnerThirdStatus(0);
        }
    }
}
