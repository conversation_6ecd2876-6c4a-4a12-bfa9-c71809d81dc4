package com.mpolicy.settlement.core.service.common;

import com.alibaba.fastjson.JSON;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.open.client.HRMdmOpenApiClient;
import com.mpolicy.open.client.HrApiClient;
import com.mpolicy.open.client.OpenApiClient;
import com.mpolicy.open.common.bms.req.UserPageReq;
import com.mpolicy.open.common.bms.resp.UserPageResp;
import com.mpolicy.open.common.cfpamf.hr.*;
import com.mpolicy.open.common.common.ChannelInfoDetail;
import com.mpolicy.open.common.hr.vo.EmployeeLdomVO;
import com.mpolicy.open.common.hr.vo.OrganizationLdomVO;
import com.mpolicy.open.common.referrer.vo.ReferrerInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * public基础服务
 *
 * <AUTHOR>
 * @since 2022-04-25 19:00
 */
@Component
@Slf4j
public class OpenApiBaseService {

    @Autowired
    private OpenApiClient openApiClient;

    //@Autowired
    //private HRMdmOpenApiClient hRMdmOpenApiClient;

    @Autowired
    private HrApiClient hrApiClient;

    /**
     * 根据渠道编码获取渠道信息
     * `
     *
     * @param channelCode 渠道编码
     * @param throwEx     不存在是否抛出异常 `
     * @return com.mpolicy.open.common.common.ChannelInfoDetail
     * <AUTHOR>
     * @since 2023/9/15 18:31
     */
    public ChannelInfoDetail getChannelInfo(String channelCode, boolean throwEx) {
        Result<ChannelInfoDetail> result = openApiClient.getChannelInfo(channelCode);
        if (!result.isSuccess()) {
            log.warn("调用公共服务获取渠道信息信息失败，渠道编码={} ，是否需要抛出异常={} msg={} ", channelCode, throwEx, result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        return result.getData();
    }

    /**
     * 根据渠道推荐人编码获取渠道推荐人信息
     *
     * @param referrerCode 渠道推荐人编码
     * @param throwEx      不存在是否抛出异常
     * @return com.mpolicy.open.common.referrer.vo.ReferrerInfoVo
     * <AUTHOR>
     * @since 2023/9/15 17:22
     */
    public ReferrerInfoVo getReferrerInfo(String referrerCode, boolean throwEx) {
        Result<ReferrerInfoVo> result = openApiClient.queryReferrerInfo(referrerCode);
        if (!result.isSuccess()) {
            log.warn("调用公共服务获取渠道推荐人信息失败，渠道推荐人编码={} ，是否需要抛出异常={} msg={} ", referrerCode, throwEx, result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        return result.getData();
    }

    /**
     * 根据渠道推荐人编码集合获取渠道推荐人信息集合
     * `
     *
     * @param referrerCodeList 渠道推荐人编码集合 `
     * @return java.util.List<com.mpolicy.open.common.referrer.vo.ReferrerInfoVo>
     * <AUTHOR>
     * @since 2023/9/15 17:23
     */
    public List<ReferrerInfoVo> getReferrerList(List<String> referrerCodeList) {
        Result<List<ReferrerInfoVo>> result = openApiClient.queryReferrerList(referrerCodeList);
        if (!result.isSuccess()) {
            log.warn("调用公共服务获取渠道推荐人信息失败，渠道推荐人编码集合={} ，msg={} ", referrerCodeList, result.getMsg());
            throw new GlobalException(result);
        }
        return result.getData();
    }

    /**
     * 根据员工编码+快照信息获取员工信息
     *
     * @param request 查询条件
     * @param throwEx 是否抛出异常
     * @return 员工信息
     * <AUTHOR>
     * @since 2023/11/17 16:04
     */
    public EmployeeLdomVO getByEmployeeCode(EmployeeByCodeRequest request, boolean throwEx) {
        //Result<EmployeeResp> result = hRMdmOpenApiClient.getByEmployeeCode(request);
        Result<EmployeeLdomVO> result = hrApiClient.getByEmployeeCode(request);
        if (!result.isSuccess()) {
            log.warn("调用公共服务获[getByEmployeeCode]失败，员工编码={} ，是否需要抛出异常={} msg={} ", JSON.toJSONString(request), throwEx, result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }

        return result.getData();
    }

    /**
     * 根据员工编号集合获取员工信息
     *
     * @param request 查询条件
     * @param throwEx throwEx
     * @return java.util.List<com.mpolicy.open.common.cfpamf.hr.EmployeeResp>
     * <AUTHOR>
     * @since 2023/11/22 18:35
     */
    public List<EmployeeLdomVO> queryByEmployeeCodes(EmployeeByCodesRequest request, boolean throwEx) {
        Result<List<EmployeeLdomVO>> result = hrApiClient.queryByEmployeeCodes(request);
        //Result<List<EmployeeResp>> result = hRMdmOpenApiClient.queryByEmployeeCodes(request);
        if (!result.isSuccess()) {
            log.warn("调用公共服务获员[queryByEmployeeCodes]失败，员工编码={} ，是否需要抛出异常={} msg={} ", JSON.toJSONString(request), throwEx, result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }

        return result.getData();
    }


    /**
     * 分页获取员工列表信息
     *
     * @param request 查询条件
     * @param throwEx 是否抛出异常
     * @return 员工信息列表
     * <AUTHOR>
     * @since 2023/11/22 18:29
     */
    public List<EmployeeLdomVO> queryAllEmployeesByPage(EmployeeTotalRequest request, boolean throwEx) {
        //Result<PageUtils<EmployeeResp>> result = hRMdmOpenApiClient.queryAllEmployees(request);
        Result<PageUtils<EmployeeLdomVO>> result = hrApiClient.queryAllEmployees(request);
        if (!result.isSuccess()) {
            log.warn("调用公共服务获员工所有失败，员工编码={} ，是否需要抛出异常={} msg={} ", JSON.toJSONString(request), throwEx, result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        return result.getData().getList();
    }

    /**
     * 获取督导管辖员工集合
     *
     * @param request 查询起
     * @param throwEx 是否抛出异常
     * @return java.util.List<com.mpolicy.open.common.cfpamf.hr.EmployeeResp>
     * <AUTHOR>
     * @since 2023/11/22 18:39
     */
    public List<EmployeeLdomVO> queryBySupervisorCode(EmployeeByCodeRequest request, boolean throwEx) {
        //Result<List<EmployeeResp>> result = hRMdmOpenApiClient.queryBySupervisorCode(request);
        Result<List<EmployeeLdomVO>> result = hrApiClient.queryBySupervisorCode(request);
        if (!result.isSuccess()) {
            log.warn("调用公共服务[queryBySupervisorCode]失败，请求信息={} ，是否需要抛出异常={} msg={} ", JSON.toJSONString(request), throwEx, result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        return result.getData();
    }


    /**
     * 根据机构请求对象信息获取员工集合信息
     *
     * @param request 请求对象信息
     * @param throwEx         是否抛出异常
     * @return 员工信息集合
     * <AUTHOR>
     * @since 2023/11/17 16:09
     */
    public List<EmployeeLdomVO> getByEmployeeListByPage(EmployeeByOrgCodeRequest request, boolean throwEx) {
        //Result<PageUtils<EmployeeResp>> result = hRMdmOpenApiClient.queryByOrgCode(request);
        Result<PageUtils<EmployeeLdomVO>> result = hrApiClient.queryByOrgCode(request);
        if (!result.isSuccess()) {
            log.warn("调用公共服务获员工信息信息失败，分支信息={} ，是否需要抛出异常={} msg={} ", JSON.toJSONString(request), throwEx, result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        return result.getData().getList();
    }

    /**
     * 根据分支岗位信息获取员工集合信息
     *
     * @param request 分支岗位信息
     * @param throwEx 是否抛出异常
     * @return 员工信息集合
     * <AUTHOR>
     * @since 2023/11/17 16:11
     */
    public List<EmployeeLdomVO> queryByPostingAndBranchCode(EmployeeByBranchAndPostingCodeRequest request, boolean throwEx) {
        //Result<List<EmployeeResp>> result = hRMdmOpenApiClient.queryByPostingAndBranchCode(request);
        Result<List<EmployeeLdomVO>> result = hrApiClient.queryByPostingAndBranchCode(request);
        if (!result.isSuccess()) {
            log.warn("调用公共服务获员工信息信息失败，分支岗位信息={} ，是否需要抛出异常={} msg={} ", JSON.toJSONString(request), throwEx, result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        return result.getData();
    }

    /**
     * 分页获取分支集合
     *
     * @param request 请求对象信息
     * @param throwEx         是否抛出异常
     * @return 分支信息
     * <AUTHOR>
     * @since 2023/11/17 16:09
     */
    public List<OrganizationLdomVO> queryBranchInfo(OrganizationRequest request, boolean throwEx) {
        //Result<PageUtils<OrganizationResp>> result = hRMdmOpenApiClient.queryBranchInfo(request);
        Result<PageUtils<OrganizationLdomVO>> result = hrApiClient.queryBranchInfo(request);
        if (!result.isSuccess()) {
            log.warn("调用公共服务获员工信息信息失败，分支信息={} ，是否需要抛出异常={} msg={} ", JSON.toJSONString(request), throwEx, result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        return result.getData().getList();
    }

    /**
     * 分页获取所有机构信息(机构编码、机构名称)集合
     *
     * @param request 请求对象信息
     * @param throwEx         是否抛出异常
     * @return 分支信息
     * <AUTHOR>
     * @since 2023/11/17 16:09
     */
    public List<OrganizationLdomVO> queryAllBranchInfo(OrganizationRequest request, boolean throwEx) {
        //Result<PageUtils<OrganizationResp>> result = hRMdmOpenApiClient.queryBranchInfo(request);
        Result<PageUtils<OrganizationLdomVO>> result = hrApiClient.pageAllBranchInfo(request);
        if (!result.isSuccess()) {
            log.warn("调用公共服务分页获取所有机构信息(机构编码、机构名称)集合失败，分支信息={} ，是否需要抛出异常={} msg={} ", JSON.toJSONString(request), throwEx, result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        return result.getData().getList();
    }

    /**
     * 根据岗位获取分组信息
     *`
     * @param request 请求对象信息
     * @param throwEx 是否抛出异常 `
     * @return 岗位对应员工信息
     * <AUTHOR>
     * @since 2023/11/24 21:07
     */
    public Map<String, List<EmployeeLdomVO>> queryByPostingCodes(EmployeeByPostingCodesRequest request, boolean throwEx) {
        //Result<Map<String, List<EmployeeResp>>> result = hRMdmOpenApiClient.queryByPostingCodes(request);
        Result<Map<String, List<EmployeeLdomVO>>> result = hrApiClient.queryByPostingCodes(request);
        if (!result.isSuccess()) {
            log.warn("调用公共服务获员工信息信息失败，请求信息={} ，是否需要抛出异常={} msg={} ", JSON.toJSONString(request), throwEx, result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        return result.getData();
    }


    /**
     * 获取员工岗位信息
     *
     * @param request 请求对象信息
     * @param throwEx 是否抛出异常
     * @return 员工岗位信息
     * <AUTHOR>
     * @since 2023/11/24 21:11
     */
    public Map<String, List<EmployeeLdomVO>> mapByPostingEmployee(PostingByEmployeeCodesRequest request, boolean throwEx) {
        //Result<Map<String, List<EmployeeResp>>> result = hRMdmOpenApiClient.queryByEmployeeCodes(request);
        Result<Map<String, List<EmployeeLdomVO>>> result = hrApiClient.getEmployeeCodesMap(request);
        if (!result.isSuccess()) {
            log.warn("调用员工岗位信息信息失败，请求信息={} ，是否需要抛出异常={} msg={} ", JSON.toJSONString(request), throwEx, result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        return result.getData();
    }

    /**
     * 根据机构请求对象信息获取员工集合信息
     *
     * @param userPageReq 请求对象信息
     * @param throwEx         是否抛出异常
     * @return 员工信息集合
     * <AUTHOR>
     * @since 2023/11/17 16:09
     */
    public List<UserPageResp> getInformalUserPage(UserPageReq userPageReq, boolean throwEx) {


        Result<List<UserPageResp>> result = openApiClient.queryInformalUserPage(userPageReq);
        if (!result.isSuccess()) {
            log.warn("调用bms服务获非正式员工信息信息失败，分支信息={} ，是否需要抛出异常={} msg={} ", JSON.toJSONString(userPageReq), throwEx, result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        return result.getData();
    }

    /**
     * 根据员工信息获取员工任职记录
     *
     * @param request 请求对象信息
     * @param throwEx         是否抛出异常
     * @return 员工岗位信息
     * <AUTHOR>
     * @since 2024/07/27 16:09
     */
    public List<PostingInfoResp> getRecordByEmployeeCodes(EmployeeByCodesRequest request, boolean throwEx) {
        Result<List<PostingInfoResp>> result = hrApiClient.recordsByEmployeeInfo(request);
        if (!result.isSuccess()) {
            log.warn("根据员工信息获取员工任职记录异常，员工信息={} ，是否需要抛出异常={} msg={} ", JSON.toJSONString(request), throwEx, result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        return result.getData();
    }

}
