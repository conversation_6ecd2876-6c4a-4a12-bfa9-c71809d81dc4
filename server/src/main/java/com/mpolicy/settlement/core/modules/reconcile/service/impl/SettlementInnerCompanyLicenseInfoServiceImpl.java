package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementInnerCompanyLicenseInfoDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementInnerCompanyLicenseInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementInnerCompanyLicenseInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 结算内部公司工商信息服务实现类
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Slf4j
@Service("settlementInnerCompanyLicenseInfoService")
public class SettlementInnerCompanyLicenseInfoServiceImpl extends ServiceImpl<SettlementInnerCompanyLicenseInfoDao, SettlementInnerCompanyLicenseInfoEntity> implements SettlementInnerCompanyLicenseInfoService {

    /**
     * 工商信息全量缓存，key为统一社会信用代码，value为工商信息实体
     * 启动时加载全部数据，后续直接从缓存查询
     */
    private final Map<String, SettlementInnerCompanyLicenseInfoEntity> licenseInfoCache = new ConcurrentHashMap<>();

    /**
     * 缓存是否已初始化标志
     */
    private volatile boolean cacheInitialized = false;

    /**
     * 读写锁，用于缓存的并发控制
     * 读操作（查询）使用读锁，写操作（刷新缓存）使用写锁
     */
    private final ReentrantReadWriteLock cacheLock = new ReentrantReadWriteLock();
    private final ReentrantReadWriteLock.ReadLock readLock = cacheLock.readLock();
    private final ReentrantReadWriteLock.WriteLock writeLock = cacheLock.writeLock();

    /**
     * 初始化缓存
     */
    @PostConstruct
    public void initCache() {
        refreshCache();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertOrUpdate(List<SettlementInnerCompanyLicenseInfoEntity> licenseInfoList) {
        if (CollectionUtils.isEmpty(licenseInfoList)) {
            return;
        }

        log.info("开始批量插入或更新工商信息，数据量：{}", licenseInfoList.size());

        try {
            // 使用自定义的批量插入或更新方法
            int affectedRows = baseMapper.batchInsertOrUpdate(licenseInfoList);
            log.info("批量插入或更新工商信息完成，影响行数：{}", affectedRows);

            // 更新缓存（如果缓存已初始化）
            if (cacheInitialized) {
                for (SettlementInnerCompanyLicenseInfoEntity entity : licenseInfoList) {
                    if (StringUtils.hasText(entity.getSocialCreditCode())) {
                        licenseInfoCache.put(entity.getSocialCreditCode(), entity);
                    }
                }
            }

        } catch (Exception e) {
            log.error("批量插入或更新工商信息失败", e);
            throw e;
        }
    }

    @Override
    public SettlementInnerCompanyLicenseInfoEntity getBySocialCreditCodeFromCache(String socialCreditCode) {
        if (!StringUtils.hasText(socialCreditCode)) {
            return null;
        }

        // 确保缓存已初始化（使用双重检查锁定模式）
        if (!cacheInitialized) {
            writeLock.lock();
            try {
                if (!cacheInitialized) {
                    refreshCacheInternal();
                }
            } finally {
                writeLock.unlock();
            }
        }

        // 使用读锁进行查询，允许多个线程并发读取
        readLock.lock();
        try {
            return licenseInfoCache.get(socialCreditCode);
        } finally {
            readLock.unlock();
        }
    }

    @Override
    public void refreshCache() {
        writeLock.lock();
        try {
            refreshCacheInternal();
        } finally {
            writeLock.unlock();
        }
    }

    /**
     * 内部缓存刷新方法，不加锁
     */
    private void refreshCacheInternal() {
        log.info("开始全量刷新工商信息缓存");

        try {
            // 清空现有缓存
            licenseInfoCache.clear();

            // 查询所有有效的工商信息
            List<SettlementInnerCompanyLicenseInfoEntity> allLicenseInfo = lambdaQuery()
                    .eq(SettlementInnerCompanyLicenseInfoEntity::getDeleted, 0)
                    .list();

            // 全量加载到缓存
            for (SettlementInnerCompanyLicenseInfoEntity entity : allLicenseInfo) {
                if (StringUtils.hasText(entity.getSocialCreditCode())) {
                    licenseInfoCache.put(entity.getSocialCreditCode(), entity);
                }
            }

            // 标记缓存已初始化
            cacheInitialized = true;

            log.info("工商信息全量缓存刷新完成，缓存数量：{}", licenseInfoCache.size());

        } catch (Exception e) {
            log.error("刷新工商信息缓存失败", e);
            cacheInitialized = false;
            throw e;
        }
    }

    @Override
    public SettlementInnerCompanyLicenseInfoEntity getBySocialCreditCode(String socialCreditCode) {
        if (!StringUtils.hasText(socialCreditCode)) {
            return null;
        }

        return lambdaQuery()
                .eq(SettlementInnerCompanyLicenseInfoEntity::getSocialCreditCode, socialCreditCode)
                .eq(SettlementInnerCompanyLicenseInfoEntity::getDeleted, 0)
                .one();
    }

    @Override
    public String getCacheStatistics() {
        return String.format("工商信息全量缓存统计 - 缓存数量: %d, 缓存状态: %s",
                licenseInfoCache.size(), cacheInitialized ? "已初始化" : "未初始化");
    }

    @Override
    public List<SettlementInnerCompanyLicenseInfoEntity> getAllFromCache() {
        // 确保缓存已初始化（使用双重检查锁定模式）
        if (!cacheInitialized) {
            writeLock.lock();
            try {
                if (!cacheInitialized) {
                    refreshCacheInternal();
                }
            } finally {
                writeLock.unlock();
            }
        }

        // 使用读锁获取所有数据
        readLock.lock();
        try {
            return new ArrayList<>(licenseInfoCache.values());
        } finally {
            readLock.unlock();
        }
    }
}
