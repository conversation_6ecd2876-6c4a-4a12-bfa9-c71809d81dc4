package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementInnerCompanyLicenseInfoDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementInnerCompanyLicenseInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementInnerCompanyLicenseInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 结算内部公司工商信息服务实现类
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Slf4j
@Service("settlementInnerCompanyLicenseInfoService")
public class SettlementInnerCompanyLicenseInfoServiceImpl extends ServiceImpl<SettlementInnerCompanyLicenseInfoDao, SettlementInnerCompanyLicenseInfoEntity> implements SettlementInnerCompanyLicenseInfoService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateBatch(List<SettlementInnerCompanyLicenseInfoEntity> licenseInfoList) {
        if (CollectionUtils.isEmpty(licenseInfoList)) {
            return;
        }
        
        for (SettlementInnerCompanyLicenseInfoEntity licenseInfo : licenseInfoList) {
            // 根据统一社会信用代码查询是否已存在
            SettlementInnerCompanyLicenseInfoEntity existingInfo = getBySocialCreditCode(licenseInfo.getSocialCreditCode());
            
            if (existingInfo != null) {
                // 更新现有记录
                licenseInfo.setId(existingInfo.getId());
                licenseInfo.setRevision(existingInfo.getRevision());
                updateById(licenseInfo);
                log.debug("更新工商信息，统一社会信用代码：{}", licenseInfo.getSocialCreditCode());
            } else {
                // 新增记录
                save(licenseInfo);
                log.debug("新增工商信息，统一社会信用代码：{}", licenseInfo.getSocialCreditCode());
            }
        }
    }

    @Override
    public SettlementInnerCompanyLicenseInfoEntity getBySocialCreditCode(String socialCreditCode) {
        return lambdaQuery()
                .eq(SettlementInnerCompanyLicenseInfoEntity::getSocialCreditCode, socialCreditCode)
                .one();
    }
}
