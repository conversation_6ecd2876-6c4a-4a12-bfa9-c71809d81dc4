package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementInnerCompanyLicenseInfoDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementInnerCompanyLicenseInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementInnerCompanyLicenseInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 结算内部公司工商信息服务实现类
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Slf4j
@Service("settlementInnerCompanyLicenseInfoService")
public class SettlementInnerCompanyLicenseInfoServiceImpl extends ServiceImpl<SettlementInnerCompanyLicenseInfoDao, SettlementInnerCompanyLicenseInfoEntity> implements SettlementInnerCompanyLicenseInfoService {

    /**
     * 工商信息缓存，key为统一社会信用代码，value为工商信息实体
     */
    private final Map<String, SettlementInnerCompanyLicenseInfoEntity> licenseInfoCache = new ConcurrentHashMap<>();

    /**
     * 不存在的统一社会信用代码缓存，避免重复查询数据库
     */
    private final Set<String> notExistSocialCreditCodeCache = ConcurrentHashMap.newKeySet();

    /**
     * 初始化缓存
     */
    @PostConstruct
    public void initCache() {
        refreshCache();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertOrUpdate(List<SettlementInnerCompanyLicenseInfoEntity> licenseInfoList) {
        if (CollectionUtils.isEmpty(licenseInfoList)) {
            return;
        }

        log.info("开始批量插入或更新工商信息，数据量：{}", licenseInfoList.size());

        try {
            // 使用自定义的批量插入或更新方法
            int affectedRows = baseMapper.batchInsertOrUpdate(licenseInfoList);
            log.info("批量插入或更新工商信息完成，影响行数：{}", affectedRows);

            // 更新缓存
            for (SettlementInnerCompanyLicenseInfoEntity entity : licenseInfoList) {
                if (StringUtils.hasText(entity.getSocialCreditCode())) {
                    licenseInfoCache.put(entity.getSocialCreditCode(), entity);
                    // 如果之前在不存在缓存中，需要移除
                    notExistSocialCreditCodeCache.remove(entity.getSocialCreditCode());
                }
            }

        } catch (Exception e) {
            log.error("批量插入或更新工商信息失败", e);
            throw e;
        }
    }

    @Override
    public SettlementInnerCompanyLicenseInfoEntity getBySocialCreditCodeFromCache(String socialCreditCode) {
        if (!StringUtils.hasText(socialCreditCode)) {
            return null;
        }

        // 先检查是否在存在缓存中
        SettlementInnerCompanyLicenseInfoEntity entity = licenseInfoCache.get(socialCreditCode);
        if (entity != null) {
            return entity;
        }

        // 检查是否在不存在缓存中，如果在则直接返回null，避免重复查询数据库
        if (notExistSocialCreditCodeCache.contains(socialCreditCode)) {
            log.debug("统一社会信用代码{}在不存在缓存中，直接返回null", socialCreditCode);
            return null;
        }

        // 缓存中都没有，查询数据库
        log.debug("缓存中未找到统一社会信用代码为{}的工商信息，尝试从数据库查询", socialCreditCode);
        entity = getBySocialCreditCode(socialCreditCode);

        if (entity != null) {
            // 存在则加入存在缓存
            licenseInfoCache.put(socialCreditCode, entity);
            log.debug("将统一社会信用代码{}的工商信息加入缓存", socialCreditCode);
        } else {
            // 不存在则加入不存在缓存
            notExistSocialCreditCodeCache.add(socialCreditCode);
            log.debug("将统一社会信用代码{}加入不存在缓存", socialCreditCode);
        }

        return entity;
    }

    @Override
    public void refreshCache() {
        log.info("开始刷新工商信息缓存");

        try {
            licenseInfoCache.clear();
            notExistSocialCreditCodeCache.clear();

            // 查询所有有效的工商信息
            List<SettlementInnerCompanyLicenseInfoEntity> allLicenseInfo = lambdaQuery()
                    .eq(SettlementInnerCompanyLicenseInfoEntity::getDeleted, 0)
                    .list();

            for (SettlementInnerCompanyLicenseInfoEntity entity : allLicenseInfo) {
                if (StringUtils.hasText(entity.getSocialCreditCode())) {
                    licenseInfoCache.put(entity.getSocialCreditCode(), entity);
                }
            }

            log.info("工商信息缓存刷新完成，缓存数量：{}", licenseInfoCache.size());

        } catch (Exception e) {
            log.error("刷新工商信息缓存失败", e);
            throw e;
        }
    }

    @Override
    public SettlementInnerCompanyLicenseInfoEntity getBySocialCreditCode(String socialCreditCode) {
        if (!StringUtils.hasText(socialCreditCode)) {
            return null;
        }

        return lambdaQuery()
                .eq(SettlementInnerCompanyLicenseInfoEntity::getSocialCreditCode, socialCreditCode)
                .eq(SettlementInnerCompanyLicenseInfoEntity::getDeleted, 0)
                .one();
    }

    @Override
    public String getCacheStatistics() {
        return String.format("工商信息缓存统计 - 存在缓存数量: %d, 不存在缓存数量: %d",
                licenseInfoCache.size(), notExistSocialCreditCodeCache.size());
    }
}
