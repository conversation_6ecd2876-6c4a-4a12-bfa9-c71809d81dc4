package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.SpringContextUtils;
import com.mpolicy.common.utils.other.BigDecimalUtils;
import com.mpolicy.policy.common.enums.EpPolicyDetailsReqOperationTypeEnum;
import com.mpolicy.policy.common.enums.PolicyPaymentTypeEnum;
import com.mpolicy.policy.common.ep.policy.*;
import com.mpolicy.policy.common.ep.policy.qry.details.GroupPreservationInsuredVo;
import com.mpolicy.product.common.base.ProductBase;
import com.mpolicy.settlement.core.common.Constant;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.common.reconcile.enums.PremEventEnum;
import com.mpolicy.settlement.core.enums.SettlementExceptionEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.ConfirmStatusEnum;
import com.mpolicy.settlement.core.modules.protocol.dto.ProtocolInsuranceProductInfoOut;
import com.mpolicy.settlement.core.modules.protocol.helper.ProtocolBaseHelper;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.*;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.CostBasicCommissionConfigDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.CsVehicleCommissionDetailEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.*;
import com.mpolicy.settlement.core.modules.reconcile.event.factory.PremEventFactory;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.PremEventHandler;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyPremInput;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyPremResult;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyProductPremInput;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyProductPremResult;
import com.mpolicy.settlement.core.modules.reconcile.helper.ReconcileBaseHelper;
import com.mpolicy.settlement.core.modules.reconcile.service.*;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import com.mpolicy.settlement.core.service.common.PolicyCenterBaseClient;
import com.mpolicy.settlement.core.service.common.ProductBaseService;
import com.mpolicy.settlement.core.utils.LambdaUtils;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mpolicy.settlement.core.common.Constant.*;
import static com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum.PERSONAL_NEW_POLICY;
import static com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum.STANDARD_SURRENDER;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/16 2:49 下午
 * @Version 1.0
 */
@Slf4j
@Service("settlementCostProcessService")
public class SettlementCostProcessServiceImpl extends AbstractSettlementCostProcessService implements SettlementCostProcessService {
    /**
     * 趸交分割时间
     * 趸交保单：2023年4月1日0点之前交单，不追回推广费（一正，不追手续费），；
     * 2023年4月 1 日 0 点起交单，过犹且回访成功后退保，不追；不满足以上全追。
     */
    public static String SINGLE_PAYMENT_SPLIT_TIME = "2023-04-01 00:00:00";
    @Autowired
    protected PolicyCenterBaseClient policyCenterBaseClient;

    @Autowired
    private ProductBaseService productBaseService;

    @Autowired
    protected SettlementCostInfoService settlementCostInfoService;

    @Autowired
    protected SettlementCostPolicyInfoService settlementCostPolicyInfoService;



    @Autowired
    protected SettlementEventJobService settlementEventJobService;

    @Autowired
    protected CostBasicCommissionConfigService commissionConfigService;

    /**
     * 自动结算每月第几天开始结算
     */


    @Value("${settlement.autoCost.settlementDay:5}")
    public void setSettlementDay(Integer settlementDay){
        autoCostSettlementDay = settlementDay;
    }


    /**
     * 支出端-获取险种配置信息
     *
     * @param productInfoVos
     * @return
     */
    @Override
    public Map<String, ProductBase> getProductBaseMap(List<EpProductInfoVo> productInfoVos) {
        if (CollectionUtils.isEmpty(productInfoVos)) {
            return Collections.EMPTY_MAP;
        }
        List<String> productCodes = productInfoVos.stream().map(EpProductInfoVo::getProductCode).distinct().collect(Collectors.toList());
        return mapProductBaseByProductCodes(productCodes);
    }

    /**
     * 根据险种编码列表获取险种配置信息
     * @param productCodes
     * @return
     */
    public Map<String, ProductBase> mapProductBaseByProductCodes(List<String> productCodes){
        List<ProductBase> productList = productBaseService.getProductBaseList(productCodes);
        if (CollectionUtils.isEmpty(productList)) {
            return Collections.EMPTY_MAP;
        }
        return productList.stream().collect(Collectors.toMap(ProductBase::getProductCode, Function.identity(), (x, y) -> y));
    }

    @Override
    public SettlementCostInfoEntity builderOriginalChangeNewCostInfo(SettlementEventJobEntity eventJob, SettlementEventTypeEnum eventType, SettlementCostInfoEntity old,
                                                                     Date newBusinessTime) {
        SettlementCostInfoEntity bean = new SettlementCostInfoEntity();
        BeanUtils.copyProperties(old, bean);
        bean.setId(null);
        bean.setCostCode(PolicySettlementUtils.createCodeLastNumber("CC"));
        //记账时间处理
        bean.setSettlementTime(new Date());
        bean.setSettlementDate(bean.getSettlementTime());


        //事件编号
        bean.setEventSourceCode(old.getCostCode());
        bean.setSourceCostCode(old.getCostCode());

        //事件信息
        if (Objects.nonNull(eventType)) {
            bean.setSettlementEventCode(eventType.getEventCode());
            bean.setSettlementEventDesc(eventType.getEventDesc());
        }
        //科目信息与被冲正记录保持一直
        //bean.setSettlementSubjectCode(subjectEnum.getCode());
        //bean.setSettlementSubjectName(subjectEnum.getName());
        bean.setSettlementGenerateType(1);
        bean.setBusinessAccountTime(getCorrectionBusinessAccountTime(old.getBusinessAccountTime(), newBusinessTime));


        //清除确认信息
        cleanConfirmInfo(bean);

        bean.setBusinessDiscountPremium(old.getBusinessDiscountPremium());
        return bean;
    }

    /**
     * 支出端-佣金计算入参验证
     *
     * @param eventJob
     */
    @Override
    public void validParam(SettlementEventJobEntity eventJob, Runnable validCustomize) {
        //检查新契约是否存在，如果存在，则必须已经完成，不存在则直接执行(可能在新契约在支出端开发前就存在)
        Integer count  = settlementEventJobService.lambdaQuery().eq(SettlementEventJobEntity::getContractCode,eventJob.getContractCode())
                .lt(SettlementEventJobEntity::getId,eventJob.getId())
                .notIn(SettlementEventJobEntity::getCostEventStatus, Arrays.asList(1,3))
                .count();
        if(count>0){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-该记录前存在未处理成功的记录，单号={} 保全申请单号={}", eventJob.getEventBusinessCode(), eventJob.getEventBusinessCode())));
        }


        if (validCustomize != null) {
            validCustomize.run();
        }
    }

    /**
     * 支出端-佣金计算入参验证
     *
     * @param eventJob
     * @param policyInfo
     */
    @Override
    public void validParam(SettlementEventJobEntity eventJob, EpContractInfoVo policyInfo, Runnable validCustomize) {
        //检查新契约是否存在，如果存在，则必须已经完成，不存在则直接执行(可能在新契约在支出端开发前就存在)
        Integer count  = settlementEventJobService.lambdaQuery().eq(SettlementEventJobEntity::getContractCode,eventJob.getContractCode())
                .lt(SettlementEventJobEntity::getId,eventJob.getId())
                .notIn(SettlementEventJobEntity::getCostEventStatus, Arrays.asList(1,3))
                .ge(SettlementEventJobEntity::getCreateTime, DateUtil.parseDateTime("2023-09-01 00:00:00"))
                .count();
        if(count>0){
            throw new GlobalException(SettlementExceptionEnum.C_B_BEFORE_EXIST_UN_SUCCESS_COST_EVENT.getException(StrUtil.format("支出端-该记录前存在未处理成功的记录，单号={} 保全申请单号={}", eventJob.getEventBusinessCode(), eventJob.getEventBusinessCode())));
        }

        String mainEpProductCode = policyInfo.getContractBaseInfo().getMainProductCode();
        if (mainEpProductCode == null) {
            throw new GlobalException(SettlementExceptionEnum.PARAM_POLICY_NOT_CONTAIN_MAIN_PRODUCT.getException(StrUtil.format("支出端-基础佣金-{}入参验证-保单缺少主险信息，保单号={}",eventJob.getEventName(), eventJob.getEventBusinessCode())));
        }

        if (validCustomize != null) {
            validCustomize.run();
        }
    }

    public Boolean isWhaleLongPolicy(EpContractInfoVo policyInfo, Map<String, ProductBase> productMap) {
        if (!Objects.equals(policyInfo.getChannelInfo().getChannelCode(), Constant.ZHNX_CHANNEL_CODE)) {
            ProductBase mainProductBase = productMap.get(policyInfo.getContractBaseInfo().getMainProductCode());
            if (mainProductBase.getLongShortFlag() != null && mainProductBase.getLongShortFlag() == 1) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }


    /**
     * 支出端-创建保单基础信息
     *
     * @param policyInfo
     * @return
     */
    @Override
    public SettlementCostPolicyInfoEntity buildSettlementCostPolicyInfo(EpContractInfoVo policyInfo) {
        SettlementCostPolicyInfoEntity entity = new SettlementCostPolicyInfoEntity();
        EpContractBaseInfoVo contractBaseInfo = policyInfo.getContractBaseInfo();
        EpContractExtendInfoVo contractExtendInfo = policyInfo.getContractExtendInfo();
        entity.setContractCode(policyInfo.getContractCode());
        BeanUtils.copyProperties(contractBaseInfo, entity);
        BeanUtils.copyProperties(contractExtendInfo, entity);

        //设置商品名称
        entity.setCommodityName(ReconcileBaseHelper.getSellProductName(entity.getCommodityCode()));
        if(StringUtils.isBlank(entity.getCommodityName())){
            entity.setCommodityName(contractBaseInfo.getMainProductName());
        }
        //回执/回访日志
        entity.setReceiptStatus(contractExtendInfo.getIsNeedReceipt());
        entity.setReceiptTime(contractExtendInfo.getReceiptSignTime());
        entity.setRevisitStatus(contractExtendInfo.getIsNeedRevisit());
        entity.setRevisitTime(contractExtendInfo.getRevisitTime());
        entity.setRevisitResult(contractExtendInfo.getRevisitResult());
        if(Objects.equals(contractExtendInfo.getRevisitResult(),1)){
            entity.setRevisitSuccessTime(new Date());
        }
        entity.setPolicyProductType(policyInfo.getPolicyProductType());
        entity.setPolicySource(policyInfo.getPolicySource());
        entity.setSerialNumber(policyInfo.getSerialNumber());
        entity.setLongShortFlag(contractBaseInfo.getLongShortFlag());


        //todo 后续小鲸保单号与保司保单号分开以后再修改回来,默认与小鲸保单号一致
        if(StringUtils.isNotBlank(contractBaseInfo.getThirdPolicyNo())) {
            entity.setCompanyPolicyNo(contractBaseInfo.getThirdPolicyNo());
        }else{
            entity.setCompanyPolicyNo(contractBaseInfo.getPolicyNo());
        }
        //投保人信息
        entity.setApplicantName(policyInfo.getApplicantInfo().getApplicantName());
        entity.setApplicantMobile(policyInfo.getApplicantInfo().getApplicantMobile());
        entity.setApplicantIdCard(policyInfo.getApplicantInfo().getApplicantIdCard());
        entity.setApplicantGender(policyInfo.getApplicantInfo().getApplicantGender());
        entity.setApplicantBirthday(policyInfo.getApplicantInfo().getApplicantBirthday());
        entity.setApplicantAge(policyInfo.getApplicantInfo().getApplicantAge());
        // 代理人机构编码
        if (policyInfo.getAgentInfoList() != null && policyInfo.getAgentInfoList().size() > 0) {
            policyInfo.getAgentInfoList().stream().filter(a -> a.getMainFlag() == 1).findFirst().ifPresent(epAgentInfoVo -> entity.setOrgCode(epAgentInfoVo.getOrgCode()));
        }
        //保单活动信息：整村推荐、四级分销单
        builderPolicyActivityInfos(policyInfo,entity);
        //车险评分等级处理
        if(Objects.equals(policyInfo.getPolicyProductType(),PolicyProductTypeEnum.VEHICLE.getCode())){
            if(policyInfo.getVehicleInfo()!=null){
                entity.setVehicleBusinessScore(policyInfo.getVehicleInfo().getVehicleBusinessScore());
            }
        }
        return entity;
    }

    /**
     * 组装退保更新保单基础信息
     * @param policyInfo
     * @param entity
     */
    @Override
    public void builderUpdateSettlementCostPolicyInfo(EpContractInfoVo policyInfo,SettlementCostPolicyInfoEntity entity){
        EpContractBaseInfoVo contractBaseInfo = policyInfo.getContractBaseInfo();
        EpContractExtendInfoVo contractExtendInfo = policyInfo.getContractExtendInfo();

        entity.setReceiptStatus(contractExtendInfo.getIsNeedReceipt());
        entity.setReceiptTime(contractExtendInfo.getReceiptSignTime());
        entity.setRevisitStatus(contractExtendInfo.getIsNeedRevisit());
        entity.setRevisitTime(contractExtendInfo.getRevisitTime());
        //回访结果未成功的情况下处理
        if(!Objects.equals(entity.getRevisitResult(),1)) {
            entity.setRevisitResult(contractExtendInfo.getRevisitResult());
            if (Objects.equals(contractExtendInfo.getRevisitResult(), 1)) {
                entity.setRevisitSuccessTime(new Date());
            }
        }
        entity.setPolicyStatus(contractBaseInfo.getPolicyStatus());
    }

    /**
     * 1.四级分销单:表中ep_policy_activity存在记录，且type为200
     * 2.整村推进保单 表中ep_policy_activity存在记录，且type为100，且activity_code有值。
     * 3.活动保单 表中ep_policy_activity存在记录，且type为100，且activity_code有值，且product_activity_code有值。(活动保单属于整村推荐保单中的一中)
     * @param policyInfo
     * @param entity
     */
    private void builderPolicyActivityInfos(EpContractInfoVo policyInfo,SettlementCostPolicyInfoEntity entity){
        entity.setDistributionFlag(0);
        entity.setRuralProxyFlag(0);
        if(CollectionUtils.isNotEmpty(policyInfo.getEpPolicyActivitys())){
            for(EpPolicyActivityVo activityVo : policyInfo.getEpPolicyActivitys()){
                //四级分销
                if(Objects.equals(EpPolicyActivityTypeEnum.FOURTH_LEVEL_DISTRIBUTION.getCode(),activityVo.getType())){
                    entity.setDistributionFlag(1);
                }
                //整村推荐
                if(Objects.equals(EpPolicyActivityTypeEnum.WHOLE_VILLAGE_PROMOTION.getCode(),activityVo.getType())
                        && StringUtils.isNotBlank(activityVo.getActivityCode())
                        && StringUtils.isNotBlank(activityVo.getProductActivityCode())){
                    entity.setRuralProxyFlag(1);
                    //活动编码
                    entity.setProductActivityCode(activityVo.getProductActivityCode());
                }

            }
        }
    }









    /**
     * 支出端-组装获取基础佣金配置的入参(被保人参数+续期参数除外)
     *
     * @param policyInfo
     * @return
     */
    public PolicyProductPremInput buildBasicCommissionConfigParam(EpContractInfoVo policyInfo,
                                                                        Integer insuranceType,
                                                                        EpProductInfoVo product,
                                                                        Integer insuredPolicyAge,
                                                                        Integer renewalYear,
                                                                        Integer renewalPeriod
    ) {
        PolicyProductPremInput input = new PolicyProductPremInput();
        input.setChannelCode(policyInfo.getChannelInfo().getChannelCode());
        input.setPolicyNo(policyInfo.getContractBaseInfo().getPolicyNo());
        input.setMainProductCode(policyInfo.getContractBaseInfo().getMainProductCode());
        input.setProductCode(product.getProductCode());
        input.setPlantCode(product.getPlanCode());
        input.setInsuredPeriodType(product.getInsuredPeriodType());
        input.setInsuredPeriod(product.getInsuredPeriod());
        input.setInsuredPolicyAge(insuredPolicyAge);
        input.setPeriodType(product.getPeriodType());
        input.setPaymentPeriodType(product.getPaymentPeriodType());
        input.setPaymentPeriod(product.getPaymentPeriod());
        input.setApprovedTime(policyInfo.getContractExtendInfo().getApprovedTime());
        //0:新单 1:续投
        input.setInsuranceType(insuranceType);
        if (POLICY_RENEWAL_FIRST_APPROVE_TIME_PRODUCTS.contains(product.getProductCode()) && StrUtil.isNotEmpty(policyInfo.getSourcePolicyNo())) {
            EpContractInfoVo firstContractInfoVo = findFirstPolicy(policyInfo.getSourcePolicyNo() , 0);
            if (Objects.isNull(firstContractInfoVo) || Objects.isNull(firstContractInfoVo.getContractExtendInfo())) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("首单信息不存在-{}", policyInfo.getSourcePolicyNo())));
            }
            input.setApprovedTime(firstContractInfoVo.getContractExtendInfo().getApprovedTime());
        }

        input.setRenewalYear(renewalYear);
        input.setRenewalPeriod(renewalPeriod);
        // 投保人信息
        if (policyInfo.getApplicantInfo() != null) {
            input.setApplicantGender(policyInfo.getApplicantInfo().getApplicantGender());
            input.setApplicantBirthday(policyInfo.getApplicantInfo().getApplicantBirthday());
        }
        input.setSalesType(policyInfo.getContractBaseInfo().getSalesType());
        // 是否自保件
        input.setSelfPreservation(policyInfo.getContractBaseInfo().getSelfPreservation());

        // 代理人机构编码
        if (policyInfo.getAgentInfoList() != null && policyInfo.getAgentInfoList().size() > 0) {
            policyInfo.getAgentInfoList().stream().filter(a -> a.getMainFlag() == 1).findFirst().ifPresent(epAgentInfoVo -> input.setOrgCode(epAgentInfoVo.getOrgCode()));
        }
        return input;

    }

    private JSONObject getRenewalProductFromEventJob(SettlementEventJobEntity eventJob, String insuredIdCard, String productCode) {
        JSONObject eventData = JSONObject.parseObject(eventJob.getEventRequest());
        JSONArray insuredInfoList = eventData.getJSONArray("insuredInfoList");
        for (int i = 0; i < insuredInfoList.size(); i++) {
            JSONObject insured = (JSONObject) insuredInfoList.get(i);
            String idCard = insured.get("insuredIdCard").toString();
            if (insuredIdCard.equals(idCard)) {
                JSONArray productInfoList = insured.getJSONArray("productInfoList");
                for (int j = 0; j < productInfoList.size(); j++) {
                    JSONObject productInfo = (JSONObject) productInfoList.get(j);
                    if (productCode.equals(productInfo.getString("productCode"))) {
                        return productInfo;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 支出端-获取基础佣金配置
     */
    public List<PolicyProductPremResult> getCostBasicCommissionConfig(String policyNo, PolicyProductPremInput input) {
        log.info("获取佣金配置比例入参：{}", input);
        PremEventHandler premEventHandler = PremEventFactory.getInvoke(PremEventEnum.COMMISSION_PREM.getCode());
        List<PolicyProductPremResult> basicConfigList = premEventHandler.queryPolicyProductPrem(input);
        if (CollectionUtils.isEmpty(basicConfigList)) {
            log.warn("支出端-获取基础佣金配置-保单号={}, 基础佣金配置信息不存在，入参：{}", policyNo, input);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-获取基础佣金配置-保单号={}, 基础佣金配置信息不存在", policyNo)));
        }
        //验证配置
        if (basicConfigList.size() > 1) {
            Set<String> set = Sets.newHashSet();
            for (PolicyProductPremResult cof : basicConfigList) {
                if (set.contains(cof.getSettlementCompanyCode())) {
                    log.warn("支出端-获取基础佣金配置-保单号={}, 结算机构编码{}存在重复配置，入参：{}", policyNo, cof.getSettlementCompanyCode(), input);
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-获取基础佣金配置-保单号={}, 结算机构编码存在重复配置", policyNo)));
                }
                String str = validBasicCommissionConfig(cof);
                if (StringUtil.isNotBlank(str)) {
                    log.warn("支出端-获取基础佣金配置-保单号={},{}，入参：{}", policyNo, str, input);
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-获取基础佣金配置-保单号={}, {}", policyNo, str)));
                }
                set.add(cof.getSettlementCompanyCode());
            }
        } else {
            String str = validBasicCommissionConfig(basicConfigList.get(0));
            if (StringUtil.isNotBlank(str)) {
                log.warn("支出端-获取基础佣金配置-保单号={},{}，入参：{}", policyNo, str, input);
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-获取基础佣金配置-保单号={}, {}", policyNo, str)));
            }
        }
        log.info("获取到的佣金配置列表：{}", basicConfigList);
        if(CollectionUtils.isNotEmpty(basicConfigList)){
            basicConfigList.stream().forEach(config->{
                if(StringUtils.isBlank(config.getSettlementCompanyCode())){
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-计算基础佣金-保单号={},佣金配置的结算机构为空", policyNo)));
                }
            });
        }
        return basicConfigList;
    }

    /**
     * 支出端-验证基础佣金配置
     *
     * @param basicConfig
     * @return
     */
    private String validBasicCommissionConfig(PolicyProductPremResult basicConfig) {
        if (StringUtil.isBlank(basicConfig.getSettlementCompanyCode())) {
            return "结算机构编码不存在";
        }
        boolean valid1 = basicConfig.getYearRate() == null && basicConfig.getIsCustomYearRate() != 0;
        boolean valid2 = basicConfig.getYearRate() != null &&
                (basicConfig.getYearRate().compareTo(BigDecimal.ZERO) < 0 || basicConfig.getYearRate().compareTo(new BigDecimal("100")) > 0);
        if (valid1 || valid2) {
            return "基础佣金配置的利率" + basicConfig.getYearRate() + "不在[0,100]范围内";
        }
        return "";
    }

    /**
     * @param policyNo
     * @param config
     * @param bean
     */
    protected void calcBasicCommission(String policyNo, String endorsementNo, PolicyProductPremResult config, SettlementCostInfoEntity bean) {
        bean.setSingleProposeFlag(config.getIsCustomYearRate());
        if (config.getIsCustomYearRate() == 1) {

            // 判断是否为一单一议的佣金费率
            PremEventHandler premEventHandler = PremEventFactory.getInvoke(PremEventEnum.COMMISSION_PREM.getCode());
            PolicyPremResult settlementPolicyPrem = premEventHandler.queryPolicyPrem(PolicyPremInput.builder()
                    .policyNo(policyNo)
                    .settlementCompanyCode(config.getSettlementCompanyCode())
                    .batchCode(endorsementNo)
                    .year(bean.getRenewalYear())
                    .period(bean.getRenewalPeriod())
                    .build());
            log.info("配置中返回一单一议保单{}/{}佣金费率信息：{}", policyNo, endorsementNo, settlementPolicyPrem);
            if (Objects.nonNull(settlementPolicyPrem)) {
                //premCode需要用一单一议的
                builderBasicCommission(config.getSettlementCompanyCode(),config.getSettlementCompanyName(), bean.getPremium(), settlementPolicyPrem.getYearRate(), settlementPolicyPrem.getPremCode(), CostTypeEnum.BASIC, bean);
            } else {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-计算基础佣金-保单号={},一单一议保单佣金费率信息未找到", policyNo)));
                //builderCsPolicyCommision(policyNo, product, bean);
            }
        } else {
            builderBasicCommission(config.getSettlementCompanyCode(),config.getSettlementCompanyName(), bean.getPremium(), config.getYearRate(), config.getPremCode(), CostTypeEnum.BASIC, bean);
        }

        if (bean.getCostAmount() == null) {
            log.warn("支出端-计算基础佣金-保单号={},计算出的支出佣金为空,佣金配置未找到", policyNo);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-计算基础佣金-保单号={},计算出的支出佣金为空,佣金配置未找到", policyNo)));
        }
    }




    /**
     * @param policyNo
     * @param product
     * @param config
     * @param bean
     */
    protected void calcBasicCommission(String policyNo, String endorsementNo, EpProductInfoVo product, PolicyProductPremResult config, SettlementCostInfoEntity bean) {
        bean.setSingleProposeFlag(config.getIsCustomYearRate());
        if (config.getIsCustomYearRate() == 1) {
            // 判断是否为一单一议的佣金费率
            PremEventHandler premEventHandler = PremEventFactory.getInvoke(PremEventEnum.COMMISSION_PREM.getCode());
            PolicyPremResult settlementPolicyPrem = premEventHandler.queryPolicyPrem(PolicyPremInput.builder()
                    .policyNo(policyNo)
                    .settlementCompanyCode(config.getSettlementCompanyCode())
                    .batchCode(endorsementNo)
                    .year(bean.getRenewalYear())
                    .period(bean.getRenewalPeriod())
                    .build());
            log.info("配置中返回一单一议保单{}/{}佣金费率信息：{}", policyNo, endorsementNo, settlementPolicyPrem);
            log.info("基础佣金配置key={},一单一议佣金配置key={}",config.getPremCode(),settlementPolicyPrem.getPremCode());
            if (Objects.nonNull(settlementPolicyPrem)) {
                builderBasicCommission(config.getSettlementCompanyCode(),config.getSettlementCompanyName(), bean.getPremium(), settlementPolicyPrem.getYearRate(), settlementPolicyPrem.getPremCode(), CostTypeEnum.BASIC, bean);
            } else {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-计算基础佣金-保单号={},一单一议保单佣金费率信息未找到", policyNo)));
                //builderCsPolicyCommision(policyNo, product, bean);
            }
        } else {
            builderBasicCommission(config.getSettlementCompanyCode(),config.getSettlementCompanyName(), bean.getPremium(), config.getYearRate(), config.getPremCode(), CostTypeEnum.BASIC, bean);
        }

        if (bean.getCostAmount() == null) {
            log.warn("支出端-计算基础佣金-保单号={},计算出的支出佣金为空,佣金配置未找到", policyNo);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-计算基础佣金-保单号={},计算出的支出佣金为空,佣金配置未找到", policyNo)));
        }
    }

    //计算【农保一单一议】佣金配置
    private void builderCsPolicyCommission(String policyNo, EpProductInfoVo product, SettlementCostInfoEntity bean) {
        //获取【农保一单一议】佣金配置
        log.info("支出端--计算基础佣金(农保一单一议)-保单号={}", policyNo);
        BigDecimal csRate = ReconcileBaseHelper.buildPolicyCostCommission(policyNo);
        if (csRate != null) {
            //农保的佣金比例为百分比，转成小鲸比例需要*0.01
            csRate = csRate.multiply(new BigDecimal("0.01"));
            builderBasicCommission("","", bean.getPremium(), csRate, null, CostTypeEnum.BASIC, bean);
        }
    }

    public static void getPolicyCommissionConfig(String policyNo, String endorsementNo, Integer year,
                                          Integer period, CostBasicCommissionConfigDto config) {
        PremEventHandler premEventHandler = PremEventFactory.getInvoke(PremEventEnum.COMMISSION_PREM.getCode());
        PolicyPremResult settlementPolicyPrem = premEventHandler.queryPolicyPrem(PolicyPremInput.builder()
                .policyNo(policyNo)
                .settlementCompanyCode(config.getSettlementCompanyCode())
                .batchCode(endorsementNo)
                .year(year)
                .period(period)
                .build());
        log.info("配置中返回一单一议保单{}/{}佣金费率信息：{}", policyNo, endorsementNo, settlementPolicyPrem);
        if (Objects.nonNull(settlementPolicyPrem)) {
            config.setYearRate(settlementPolicyPrem.getYearRate());
            //覆盖以前的佣金配置key
            config.setPremCode(settlementPolicyPrem.getPremCode());
        } else {
            //log.info("支出端--计算基础佣金(农保一单一议)-保单号={}", policyNo);
            /*BigDecimal csRate = ReconcileBaseHelper.buildPolicyCostCommission(policyNo);
            config.setYearRate(csRate.multiply(new BigDecimal("0.01")));*/
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-计算基础佣金-保单号={},一单一议佣金配置未找到", policyNo)));
        }
    }







    /**
     * 个险新契约/续保 根据被保人明细记录生成支出端佣金明细
     *
     * @param eventJob
     * @param subjectEnum
     * @param eventType
     * @param productMap
     * @param policyInfo
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> builderSettlementCostInfoByInsuredList(SettlementEventJobEntity eventJob,
                                                                                 CostSubjectEnum subjectEnum,
                                                                                 SettlementEventTypeEnum eventType,
                                                                                 Map<String, ProductBase> productMap,
                                                                                 EpContractInfoVo policyInfo, List<EpInsuredInfoVo> insuredInfoVos) {

        if (CollectionUtils.isEmpty(insuredInfoVos)) {
            return Collections.EMPTY_LIST;
        }
        Integer insuranceType = getInsuranceType(eventType, policyInfo.getSourcePolicyNo());
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        //
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();
        insuredInfoVos.stream().forEach(insured -> {
            List<EpPersonalProductInfoVo> personalProductLst = insured.getProductInfoList();
            if (CollectionUtils.isEmpty(personalProductLst)) {
                log.warn("支出端-根据被保人信息计算支出端佣金明细-保单号={},被保人没有险种明细：{}", eventJob.getEventBusinessCode(), insured.getInsuredName());
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-根据被保人信息计算支出端佣金明细-保单号={},被保人没有险种明细：{}", eventJob.getEventBusinessCode(), insured.getInsuredName())));
            }
            boolean hasVehicleVessel = false,calcVehicleVessel = false;
            for (EpPersonalProductInfoVo product : personalProductLst) {
                //查配置
                Integer renewalPeriod = policyInfo.getContractBaseInfo().getRenewalPeriod()!=null?policyInfo.getContractBaseInfo().getRenewalPeriod():1;
                PolicyProductPremInput input = commissionConfigService.buildBasicCommissionConfigParam(policyInfo, insuranceType, product,insured.getInsuredPolicyAge(), renewalPeriod, renewalPeriod);
                //List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(policyNo, input);
                List<CostBasicCommissionConfigDto>  configList =  commissionConfigService.getCostBasicCommissionConfig(policyNo,policyInfo.getPolicyProductType(),input);
                //遍历佣金配置
                for (CostBasicCommissionConfigDto config : configList) {
                    //初始化，生成科目信息
                    SettlementCostInfoEntity bean = initSettlementCostInfo(eventJob, subjectEnum, eventType, policyInfo, insuranceType);
                    //记录佣金匹配时间，便于冲正流程中获取
                    bean.setCostConfigMatchTime(input.getApprovedTime());
                    //记账时间
                    bean.setSettlementTime(getBasicSettlementTime(eventType, policyInfo));
                    bean.setSettlementDate(bean.getSettlementTime());

                    //设置保费
                    bean.setProductPremium(product.getPremium());
                    bean.setPremium(product.getPremium());
                    bean.setBusinessPremium(product.getPremium());
                    bean.setBusinessAccountTime(getBusinessAccountTime(eventType,policyInfo,null));


                    //佣金信息
                    calcBasicCommission(policyNo, config, bean);
                    //确认信息
                    bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                    //被保人信息
                    setInsuredInfo(insured, bean);
                    //险种信息
                    builderCostProductInfo(policyInfo, productMap, product, bean, renewalPeriod, renewalPeriod);
                    //折算保费
                    builderDiscountPremium(policyNo,policyInfo.getPolicyProductType(),bean);

                    costInfoEntities.add(bean);

                    //是否存在车船税
                    if(config.isVehicleInsurance() && Objects.equals(config.getExistVehicleVesselTax(),Boolean.TRUE)){
                        hasVehicleVessel = true;
                        //是否交强险
                        if(isCompulsoryInsurance(policyInfo.getPolicyProductType(),productMap.get(product.getProductCode()))){
                            calcVehicleVessel = true;
                            costInfoEntities.add(builderCompulsoryInsuranceCostInfo(eventType,bean,config));
                        }
                    }
                }


            }
            //有车船税，但是没有交强险
            if(hasVehicleVessel && !calcVehicleVessel){
                log.warn("支出端-根据被保人信息计算支出端佣金明细-保单号={},有车船税，没有交强险：{}", eventJob.getEventBusinessCode());
                throw new GlobalException(SettlementExceptionEnum.CONFIG_VEHICLE_VESSEL_NOT_HAVE_COMPULSORY.getException("支出端-根据被保人信息计算支出端佣金明细-保单号={},有车船税，没有交强险：{}", eventJob.getEventBusinessCode()));

            }

        });
        //设置佣金归属人信息并返回
        return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);
    }




    @Override
    public List<SettlementCostInfoEntity> builderRenewalSettlementCostInfoByInsuredList(SettlementEventJobEntity eventJob,
                                                                                        CostSubjectEnum subjectEnum,
                                                                                        SettlementEventTypeEnum eventType,
                                                                                        Map<String, ProductBase> productMap,
                                                                                        EpContractInfoVo policyInfo,
                                                                                        PolicyRenewalTermDto renewalTermDto) {
        Integer insuranceType = getInsuranceType(eventType, policyInfo.getSourcePolicyNo());
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();
        renewalTermDto.getInsuredInfoList().stream().forEach(insured-> {
            List<PolicyRenewalTermProductInfoDto> personalProductLst = insured.getProductInfoList();
            if (CollectionUtils.isEmpty(personalProductLst)) {
                log.error("支出端-根据被保人信息计算支出端佣金明细-保单号={},被保人没有险种明细：{}", eventJob.getEventBusinessCode(), insured.getInsuredName());
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-根据被保人信息计算支出端佣金明细-保单号={},被保人没有险种明细：{}", eventJob.getEventBusinessCode(), insured.getInsuredName())));
            }

            for (PolicyRenewalTermProductInfoDto product : personalProductLst) {
                //
                //log.info()
                EpProductInfoVo productVo = new EpProductInfoVo();
                BeanUtils.copyProperties(product,productVo);
                PolicyProductPremInput input = buildBasicCommissionConfigParam(policyInfo, insuranceType, productVo,insured.getInsuredPolicyAge(), renewalTermDto.getPolicyPeriodYear()==null?renewalTermDto.getPeriod():renewalTermDto.getPolicyPeriodYear(), product.getPeriod());
                //续期中险种为1年期交的短险，则承保时间为险种生效时间
                log.info("PolicyProductPremInput={}",input);
                log.info("续期中险种为1年期交的短险，则承保时间为险种生效时间,实际数据:{}/{}",product.getPaymentPeriod() ,product.getEffectiveDate());
                if(Objects.equals(product.getPaymentPeriod(), 1)){
                    input.setApprovedTime(product.getEffectiveDate());
                }
                List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(policyNo, input);
                //遍历佣金配置
                for (PolicyProductPremResult config : configList) {
                    //初始化，生成科目信息
                        SettlementCostInfoEntity bean = initSettlementCostInfo(eventJob, subjectEnum, eventType, policyInfo, insuranceType);
                    //记录佣金匹配时间，便于冲正流程中获取
                        bean.setCostConfigMatchTime(input.getApprovedTime());
                        //记账时间
                        bean.setSettlementTime(getBasicSettlementTime(eventType, policyInfo));
                        bean.setSettlementDate(bean.getSettlementTime());
                        //设置保费
                        bean.setProductPremium(product.getPremium());
                        bean.setPremium(product.getPremium());
                        bean.setBusinessPremium(product.getPremium());
                        bean.setBusinessAccountTime(getBusinessAccountTime(eventType,policyInfo,renewalTermDto));
                        //佣金信息
                        calcBasicCommission(policyNo, null, config, bean);
                        //确认信息
                        bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                        //被保人信息
                        bean.setInsuredCode(insured.getInsuredCode());
                        bean.setInsuredBirthday(insured.getInsuredBirthday());
                        bean.setInsuredGender(insured.getInsuredGender());
                        bean.setInsuredIdCard(insured.getInsuredIdCard());
                        bean.setInsuredMobile(insured.getInsuredMobile());
                        bean.setInsuredName(insured.getInsuredName());
                        //险种信息
                        builderCostProductInfo(policyInfo, productMap, productVo, bean, product.getPolicyPeriodYear(), product.getPeriod());
                        //续期数据
                        bean.setRealityTime(renewalTermDto.getPaymentTime());
                        bean.setPayableTime(DateUtil.parseDateTime(renewalTermDto.getDuePaymentTime()));
                        bean.setRenewalYear(product.getPolicyPeriodYear());
                        bean.setRenewalPeriod(product.getPeriod());

                        //折算保费
                        builderDiscountPremium(policyNo,policyInfo.getPolicyProductType(),bean);
                        costInfoEntities.add(bean);
                }
            }

        });
        //设置佣金归属人信息并返回
        return settlementCostOwnerService.builderRenewalPolicyCostOwnerInfo(policyInfo,renewalTermDto, costInfoEntities);
//        if (CollectionUtils.isEmpty(insuredInfoVos)) {
//            return Collections.EMPTY_LIST;
//        }
//        Integer insuranceType = getInsuranceType(eventType, policyInfo.getSourcePolicyNo());
//        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
//        //
//        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();
//        insuredInfoVos.stream().forEach(insured -> {
//            List<EpPersonalProductInfoVo> personalProductLst = insured.getProductInfoList();
//            if (CollectionUtils.isEmpty(personalProductLst)) {
//                log.error("支出端-根据被保人信息计算支出端佣金明细-保单号={},被保人没有险种明细：{}", eventJob.getEventBusinessCode(), insured.getInsuredName());
//                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-根据被保人信息计算支出端佣金明细-保单号={},被保人没有险种明细：{}", eventJob.getEventBusinessCode(), insured.getInsuredName())));
//            }
//            for (EpPersonalProductInfoVo product : personalProductLst) {
//                JSONObject eventProduct = getRenewalProductFromEventJob(eventJob, insured.getInsuredIdCard(), product.getProductCode());
//                Integer policyPeriodYear = eventProduct.getInteger("policyPeriodYear");
//                Integer period = eventProduct.getInteger("period");
//                PolicyProductPremInput input = buildBasicCommissionConfigParam(policyInfo, insuranceType, product,insured.getInsuredPolicyAge(), policyPeriodYear, period);
//                List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(policyNo, input);
//                //遍历佣金配置
//                for (PolicyProductPremResult config : configList) {
//                    //初始化，生成科目信息
//                    SettlementCostInfoEntity bean = initSettlementCostInfo(eventJob, subjectEnum, eventType, policyInfo.getContractCode(), insuranceType);
//                    //记录佣金匹配时间，便于冲正流程中获取
//                    bean.setCostConfigMatchTime(input.getApprovedTime());
// 记账时间
//                    bean.setSettlementTime(getBasicSettlementTime(eventType, policyInfo));
//                    bean.setSettlementDate(bean.getSettlementTime());
//                    //设置保费
//                    bean.setProductPremium(product.getPremium());
//                    bean.setPremium(product.getPremium());
//                    bean.setBusinessPremium(product.getPremium());
//                    bean.setBusinessAccountTime(getBusinessAccountTime(eventType,policyInfo,renewalTermDto));
//                    //佣金信息
//                    calcBasicCommission(policyNo, null, product, config, bean);
//                    //确认信息
//                    bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
//                    //被保人信息
//                    bean.setInsuredCode(insured.getInsuredCode());
//                    bean.setInsuredBirthday(insured.getInsuredBirthday());
//                    bean.setInsuredGender(insured.getInsuredGender());
//                    bean.setInsuredIdCard(insured.getInsuredIdCard());
//                    bean.setInsuredMobile(insured.getInsuredMobile());
//                    bean.setInsuredName(insured.getInsuredName());
//                    //险种信息
//                    builderCostProductInfo(policyInfo, productMap, product, bean, renewalTermDto.getPeriod(), renewalTermDto.getPeriod());
//                    //续期数据
//
//
//                    //折算保费
//                    builderDiscountPremium(policyNo,policyInfo.getPolicyProductType(),bean);
//                    costInfoEntities.add(bean);
//                }
//
//            }
//
//        });
//        //设置佣金归属人信息并返回
//        return settlementCostOwnerService.builderRenewalPolicyCostOwnerInfo(policyInfo,renewalTermDto, costInfoEntities);
    }
    /**
     * 个险新契约/续保 根据险种列表生成佣金记录(车险)
     *
     * @param eventJob
     * @param subjectEnum
     * @param eventType
     * @param productMap
     * @param policyInfo
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> builderRenewalSettlementCostInfoByProductList(SettlementEventJobEntity eventJob,
                                                                                            CostSubjectEnum subjectEnum,
                                                                                            SettlementEventTypeEnum eventType,
                                                                                            Map<String, ProductBase> productMap,
                                                                                            EpContractInfoVo policyInfo,
                                                                                            PolicyRenewalTermDto renewalTermDto) {
        List<EpProductInfoVo> productInfoVos = policyInfo.getProductInfoList();
        if (CollectionUtils.isEmpty(productInfoVos)) {
            return Collections.EMPTY_LIST;
        }
        Integer insuranceType = getInsuranceType(eventType, policyInfo.getSourcePolicyNo());
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();
        productInfoVos.stream().forEach(product -> {
            //查配置
            PolicyProductPremInput input = buildBasicCommissionConfigParam(policyInfo, insuranceType, product,null, renewalTermDto.getPolicyPeriodYear()==null?renewalTermDto.getPeriod():renewalTermDto.getPolicyPeriodYear(), renewalTermDto.getPeriod());
            List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(policyNo, input);
            //遍历佣金配置
            for (PolicyProductPremResult config : configList) {
                //初始化，生成科目信息
                SettlementCostInfoEntity bean = initSettlementCostInfo(eventJob, subjectEnum, eventType, policyInfo, insuranceType);
                //记录佣金匹配时间，便于冲正流程中获取
                bean.setCostConfigMatchTime(input.getApprovedTime());
                //记账时间处理
                bean.setSettlementTime(getBasicSettlementTime(eventType, policyInfo));
                bean.setSettlementDate(bean.getSettlementTime());
                //设置险种保费和实际用于计算的保费
                bean.setProductPremium(product.getPremium());
                bean.setPremium(product.getPremium());
                bean.setBusinessPremium(product.getPremium());
                bean.setBusinessAccountTime(getBusinessAccountTime(eventType,policyInfo,null));
                //佣金信息
                calcBasicCommission(policyNo, null, product, config, bean);
                //初始化确认信息
                bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                //险种信息
                builderCostProductInfo(policyInfo, productMap, product, bean,renewalTermDto.getPeriod() , renewalTermDto.getPeriod());
                //折算保费
                builderDiscountPremium(policyNo,policyInfo.getPolicyProductType(),bean);
                costInfoEntities.add(bean);
            }

        });
        //设置佣金归属人信息并返回
        return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);
    }


    /**
     * 团险新契约/增减员 根据被保人明细记录生成支出端佣金明细
     *
     * @param eventJob
     * @param subjectEnum
     * @param eventType
     * @param productMap
     * @param policyInfo
     * @return
     */
    @Override
    @Deprecated
    public List<SettlementCostInfoEntity> builderGroupSettlementCostInfoByInsuredList(SettlementEventJobEntity eventJob,
                                                                                      CostSubjectEnum subjectEnum,
                                                                                      SettlementEventTypeEnum eventType,
                                                                                      Map<String, ProductBase> productMap,
                                                                                      EpContractInfoVo policyInfo, PolicyPreservationDetailDto preservationDetail, List<GroupPreservationInsuredVo> insuredInfoVos) {

        if (CollectionUtils.isEmpty(insuredInfoVos)) {
            return Collections.EMPTY_LIST;
        }
        Integer insuranceType = getInsuranceType(eventType, policyInfo.getSourcePolicyNo());
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();

        insuredInfoVos.stream().forEach(insured -> {
            List<EpPersonalProductInfoVo> personalProductLst = insured.getProductInfoList();
            if (CollectionUtils.isEmpty(personalProductLst)) {
                log.warn("支出端-团险根据被保人信息计算支出端佣金明细-保单号={},被保人没有险种明细：{}", eventJob.getEventBusinessCode(), insured.getInsuredName());
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-团险根据被保人信息计算支出端佣金明细-保单号={},被保人没有险种明细：{}", eventJob.getEventBusinessCode(), insured.getInsuredName())));
            }

            for (EpPersonalProductInfoVo product : personalProductLst) {
                /******佣金配置查询*******/
                PolicyProductPremInput input = buildBasicCommissionConfigParam(policyInfo, insuranceType, product,insured.getInsuredPolicyAge(), 1, 1);
                //保全，则查询佣金配置时间则是被保人生效时间，减员需要取被减人当时的生效时间
                if (preservationDetail != null) {
                    input.setApprovedTime(insured.getInsuredEffectiveTime());
                }
                List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(policyNo, input);

                /******遍历佣金配置*******/
                for (PolicyProductPremResult config : configList) {
                    //初始化，生成科目信息
                    SettlementCostInfoEntity bean = initSettlementCostInfo(eventJob, subjectEnum, eventType, policyInfo, insuranceType);
                    //记录佣金匹配时间，便于冲正流程中获取
                    bean.setCostConfigMatchTime(input.getApprovedTime());
                    //记账时间
                    bean.setSettlementTime(getBasicSettlementTime(eventType, policyInfo));
                    bean.setSettlementDate(bean.getSettlementTime());
                    //设置险种保费和实际用于计算的保费
                    bean.setProductPremium(product.getPremium());

                    if (Objects.equals(insured.getOperationType(), EpPolicyDetailsReqOperationTypeEnum.NEW.getCode())
                        && Objects.equals(SettlementEventTypeEnum.GROUP_NEW_POLICY.getEventCode(),eventType.getEventCode())){
                        bean.setPremium(product.getPremium());
                        bean.setBusinessPremium(product.getPremium());
                    }else if(Objects.equals(insured.getOperationType(), EpPolicyDetailsReqOperationTypeEnum.ADD.getCode())
                            && Objects.equals(SettlementEventTypeEnum.GROUP_ADD_OR_SUBTRACT.getEventCode(),eventType.getEventCode())) {

                        bean.setPremium(product.getPremium());
                        bean.setBusinessPremium(product.getPremium());
                    } else if (Objects.equals(insured.getOperationType(), EpPolicyDetailsReqOperationTypeEnum.BATCH.getCode())
                            && Objects.equals(SettlementEventTypeEnum.GROUP_ADD_OR_SUBTRACT.getEventCode(),eventType.getEventCode())) {
                        bean.setPremium(product.getSurrenderAmount().negate());
                        bean.setBusinessPremium(product.getSurrenderAmount().negate());
                    } else {
                        log.warn("支出端-根据被保人信息计算支出端佣金明细-保单号={},不支持此被保人明细操作类型：{}", eventJob.getEventBusinessCode(), insured.getOperationType());
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-根据被保人信息计算支出端佣金明细-保单号={},不支持此被保人明细操作类型：{}", eventJob.getEventBusinessCode(), insured.getOperationType())));
                    }
                    //业务记账
                    //bean.setBusinessAccountTime(getBusinessAccountTime(eventType,policyInfo,preservationDetail));
                    if(Objects.nonNull(preservationDetail)) {
                        bean.setBusinessAccountTime(getGroupAddOrSubtractBusinessAccountTime(preservationDetail, insured.getSurrendered()));
                    }else{
                        bean.setBusinessAccountTime(getBusinessAccountTime(eventType,policyInfo,preservationDetail));
                    }
                    //设置保费,增员设置增员费用，减员设置退保金额
//                    if(Objects.equals(insured.getOperationType(),EpPolicyDetailsReqOperationTypeEnum.ADD.getCode())
//                    || Objects.equals(insured.getOperationType(),EpPolicyDetailsReqOperationTypeEnum.NEW.getCode())) {
//                        bean.setPremium(product.getPremium());
//                    }else if(Objects.equals(insured.getOperationType(),EpPolicyDetailsReqOperationTypeEnum.BATCH.getCode())){
//                        bean.setPremium(product.getSurrenderAmount().negate());
//                    }else{
//                        log.warn("支出端-根据被保人信息计算支出端佣金明细-保单号={},不支持此被保人明细操作类型：{}",eventJob.getEventBusinessCode(),insured.getOperationType());
//                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-根据被保人信息计算支出端佣金明细-保单号={},不支持此被保人明细操作类型：{}",eventJob.getEventBusinessCode(),insured.getOperationType())));
//                    }
                    //佣金归属人信息

//                    if(Objects.equals(insured.getOperationType(),EpPolicyDetailsReqOperationTypeEnum.ADD.getCode())
//                            || Objects.equals(insured.getOperationType(),EpPolicyDetailsReqOperationTypeEnum.NEW.getCode())) {

                    settlementCostOwnerService.builderGroupPolicyAddCostOwnerInfoByInsured(insured.getChannelCode(), insured, bean);
//                    }else{
//
//                    }
                    //佣金信息
                    calcBasicCommission(policyNo, insured.getAddEndorsementNo(), product, config, bean);
                    //确认信息
                    bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                    //被保人信息
                    bean.setInsuredCode(insured.getInsuredCode());
                    bean.setInsuredBirthday(insured.getInsuredBirthday());
                    bean.setInsuredGender(insured.getInsuredGender());
                    bean.setInsuredIdCard(insured.getInsuredIdCard());
                    bean.setInsuredMobile(insured.getInsuredMobile());
                    bean.setInsuredName(insured.getInsuredName());
                    //险种信息
                    builderCostProductInfo(policyInfo, productMap, product, bean, 1, 1);
                    //保全信息
                    if (Objects.nonNull(preservationDetail)) {
                        builderGroupAddOrSubtractPreservationInfo(preservationDetail, insured.getSurrendered(),null, bean);
                    }

                    //折算保费
                    builderDiscountPremium(policyNo,policyInfo.getPolicyProductType(),bean);
                    costInfoEntities.add(bean);
                }

            }

        });
//        //按增员、减员进行分组
//        Map<String,List<EpInsuredInfoVo>> insuredMap = LambdaUtils.groupBy(insuredInfoVos, EpInsuredInfoVo::getOperationType);
//        //增员佣金计算
//        List<EpInsuredInfoVo> addInsuredLst = insuredMap.get(EpPolicyDetailsReqOperationTypeEnum.ADD.getCode());
//        if(CollectionUtils.isNotEmpty(addInsuredLst)){
//            costInfoEntities.addAll(builderSettlementCostInfoByInsuredList(eventJob,subjectEnum,eventType,productMap,policyInfo,preservationDetail,addInsuredLst));
//        }
//        //减员佣金计算
//        List<EpInsuredInfoVo> subInsuredLst = insuredMap.get(EpPolicyDetailsReqOperationTypeEnum.BATCH.getCode());
//        if(CollectionUtils.isNotEmpty(subInsuredLst)){
//
//        }


        return costInfoEntities;
    }


    /**
     * 个险新契约/续保 根据险种列表生成佣金记录(车险)
     *
     * @param eventJob
     * @param subjectEnum
     * @param eventType
     * @param productMap
     * @param policyInfo
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> builderSettlementCostInfoByProductList(SettlementEventJobEntity eventJob,
                                                                                 CostSubjectEnum subjectEnum,
                                                                                 SettlementEventTypeEnum eventType,
                                                                                 Map<String, ProductBase> productMap,
                                                                                 EpContractInfoVo policyInfo
    ) {
        List<EpProductInfoVo> productInfoVos = policyInfo.getProductInfoList();
        if (CollectionUtils.isEmpty(productInfoVos)) {
            return Collections.EMPTY_LIST;
        }
        Integer insuranceType = getInsuranceType(eventType, policyInfo.getSourcePolicyNo());
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();
        boolean hasVehicleVessel = false,calcVehicleVessel = false;
        for(EpProductInfoVo product : productInfoVos){
            //查配置
            PolicyProductPremInput input = commissionConfigService.buildBasicCommissionConfigParam(policyInfo, insuranceType, product,null, 1, 1);
            //List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(policyNo, input);

            List<CostBasicCommissionConfigDto>  configList =  commissionConfigService.getCostBasicCommissionConfig(policyNo,policyInfo.getPolicyProductType(),input);
            //遍历佣金配置
            for (CostBasicCommissionConfigDto config : configList) {
                //初始化，生成科目信息
                SettlementCostInfoEntity bean = initSettlementCostInfo(eventJob, subjectEnum, eventType, policyInfo,insuranceType);
                //记录佣金匹配时间，便于冲正流程中获取
                bean.setCostConfigMatchTime(input.getApprovedTime());
                //记账时间处理
                bean.setSettlementTime(getBasicSettlementTime(eventType, policyInfo));
                bean.setSettlementDate(bean.getSettlementTime());
                //设置险种保费和实际用于计算的保费
                bean.setProductPremium(product.getPremium());
                bean.setPremium(product.getPremium());
                bean.setBusinessPremium(product.getPremium());
                bean.setBusinessAccountTime(getBusinessAccountTime(eventType,policyInfo,null));
                //佣金信息
                calcBasicCommission(policyNo, config, bean);
                //初始化确认信息
                bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                //险种信息
                builderCostProductInfo(policyInfo, productMap, product, bean, 1, 1);
                //折算保费
                builderDiscountPremium(policyNo,policyInfo.getPolicyProductType(),bean);


                costInfoEntities.add(bean);

                //是否存在车船税
                if(config.isVehicleInsurance() && Objects.equals(config.getExistVehicleVesselTax(),Boolean.TRUE)){
                    hasVehicleVessel = true;
                    //是否交强险
                    if(isCompulsoryInsurance(policyInfo.getPolicyProductType(),productMap.get(product.getProductCode()))){
                        calcVehicleVessel = true;
                        costInfoEntities.add(builderCompulsoryInsuranceCostInfo(eventType,bean,config));
                    }
                }
            }

        }

        //有车船税，但是没有交强险
        if(hasVehicleVessel && !calcVehicleVessel){
            log.warn("支出端-根据被保人信息计算支出端佣金明细-保单号={},有车船税，没有交强险：{}", eventJob.getEventBusinessCode());
            throw new GlobalException(SettlementExceptionEnum.CONFIG_VEHICLE_VESSEL_NOT_HAVE_COMPULSORY.getException("支出端-根据被保人信息计算支出端佣金明细-保单号={},有车船税，没有交强险：{}", eventJob.getEventBusinessCode()));

        }

        //设置佣金归属人信息并返回
        return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);
    }



    /**
     * 获取保单类型：1续投、0新保
     *
     * @param eventType
     * @return
     */
    public Integer getInsuranceType(SettlementEventTypeEnum eventType, String sourcePolicyNo) {
        if (LIST_RENEWAL_INSURANCE.contains(eventType.getEventCode())) {
            return 1;
        }
        if (StringUtils.isNotBlank(sourcePolicyNo)) {
            return 1;
        }
        return 0;
    }






    /**
     * 团险新契约、增减员 根据险种明细生成支出明细（没有被保人明细的团险）
     *
     * @param eventJob
     * @param subjectEnum
     * @param eventType
     * @param productMap
     * @param policyInfo
     * @param preservationDetail
     * @return
     */
    @Override
    @Deprecated
    public List<SettlementCostInfoEntity> builderGroupSettlementCostInfoByProductList(SettlementEventJobEntity eventJob,
                                                                                      CostSubjectEnum subjectEnum,
                                                                                      SettlementEventTypeEnum eventType,
                                                                                      Map<String, ProductBase> productMap,
                                                                                      EpContractInfoVo policyInfo,
                                                                                      PolicyPreservationDetailDto preservationDetail) {
        List<EpProductInfoVo> productInfoVos = policyInfo.getProductInfoList();
        if (CollectionUtils.isEmpty(productInfoVos)) {
            return Collections.EMPTY_LIST;
        }
        Integer insuranceType = getInsuranceType(eventType, policyInfo.getSourcePolicyNo());
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();
        BigDecimal dynamicPremium = BigDecimal.ZERO;
        BigDecimal totalPremium = productInfoVos.stream().map(EpProductInfoVo::getPremium).reduce(BigDecimal.ZERO, BigDecimal::add);


        //按先主险，后附加险的顺序排序，然后进行佣金计算
        productInfoVos = productInfoVos.stream().sorted(Comparator.comparing(EpProductInfoVo::getMainInsurance).reversed()).collect(Collectors.toList());
        int size = productInfoVos.size();
        for (int i = 0; i < size; i++) {

            EpProductInfoVo product = productInfoVos.get(i);
            //按险种维度分摊团险保费(保全新单保费/增减员保费)
            BigDecimal premium;
            //保全为空则为新保单

            if (preservationDetail != null) {
                if (preservationDetail.getSurrenderCash().compareTo(dynamicPremium) == 0) {
                    break;
                }
                premium = preservationDetail.getSurrenderCash().multiply(product.getPremium()).divide(totalPremium, 2, BigDecimal.ROUND_HALF_UP);
                //preservationDetail.getSurrenderCash()存在为负数，比较大小用绝对值比较
                if (dynamicPremium.abs().add(premium.abs()).compareTo(preservationDetail.getSurrenderCash().abs()) > 0 || i == size - 1) {
                    premium = preservationDetail.getSurrenderCash().subtract(dynamicPremium);
                }
                dynamicPremium = dynamicPremium.add(premium);
            } else {
                premium = product.getPremium();
            }


            //查配置输入参数
            PolicyProductPremInput input = buildBasicCommissionConfigParam(policyInfo, insuranceType, product,null, 1, 1);
            input.setApprovedTime(product.getEffectiveDate());
            List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(policyNo, input);

            //遍历佣金配置
            for (PolicyProductPremResult config : configList) {
                //初始化，生成科目信息
                SettlementCostInfoEntity bean = initSettlementCostInfo(eventJob, subjectEnum, eventType, policyInfo,insuranceType);
                //记录佣金匹配时间，便于冲正流程中获取
                bean.setCostConfigMatchTime(input.getApprovedTime());
                //记账时间处理
                bean.setSettlementTime(getBasicSettlementTime(eventType, policyInfo));
                bean.setSettlementDate(bean.getSettlementTime());
                //设置险种保费和实际用于计算的保费
                bean.setProductPremium(product.getPremium());
                bean.setPremium(premium);
                bean.setBusinessPremium(premium);
                bean.setBusinessAccountTime(getBusinessAccountTime(eventType,policyInfo,preservationDetail));
                //佣金信息
                calcBasicCommission(policyNo, preservationDetail != null ? preservationDetail.getEndorsementNo() : null, product, config, bean);
                //初始化确认信息
                bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                //险种信息
                builderCostProductInfo(policyInfo, productMap, product, bean, 1, 1);
                //保全信息
                if (preservationDetail != null) {
                    builderPreservationInfo(preservationDetail, null, bean);
                }
                //折算保费
                builderDiscountPremium(policyNo,policyInfo.getPolicyProductType(),bean);
                costInfoEntities.add(bean);
            }

        }

        //设置佣金归属人信息并返回
        if (preservationDetail != null) {
            return settlementCostOwnerService.builderPreservationCostOwnerInfo(preservationDetail, costInfoEntities);
        } else {
            return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);
        }
    }

    /**
     * 附加险解约
     *
     * @param eventJob
     * @param subjectEnum
     * @param eventType
     * @param productMap
     * @param policyInfo
     * @param preservationDetail
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> builderPolicyTerminationProductCostInfo(SettlementEventJobEntity eventJob,
                                                                                  CostSubjectEnum subjectEnum,
                                                                                  SettlementEventTypeEnum eventType,
                                                                                  Map<String, ProductBase> productMap,
                                                                                  EpContractInfoVo policyInfo,
                                                                                  PolicyPreservationDetailDto preservationDetail) {
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();
        List<ProductTerminationInfoDto> productTerminationList = preservationDetail.getProductTerminationList();
        PolicyProductTypeEnum policyProductTypeEnum = PolicyProductTypeEnum.getProdTypeEnum(policyInfo.getPolicyProductType());
        //List<String> productCodes = productTerminationList.stream().map(ProductTerminationInfoDto::getProductCode).collect(Collectors.toList());
        //List<SettlementCostInfoEntity> existsCostInfos = settlementCostInfoService.listUnCorrectionPolicyCostInfoByProductCodes(policyInfo.getContractCode(),productCodes);
        Integer insuranceType = getInsuranceType(eventType, policyInfo.getSourcePolicyNo());
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        Boolean isWhaleLongPolicy = isWhaleLongPolicy(policyInfo, productMap);
        TerminationProductDetailDto terminationProductDetailVo = preservationDetail.getTerminationProductDetailVo();
        switch (policyProductTypeEnum) {
            case PERSONAL:
            case PROPERTY: {
                for (int i = 0; i < productTerminationList.size(); i++) {
                    ProductTerminationInfoDto dto = productTerminationList.get(i);
                    List<EpInsuredInfoVo> insureds = policyInfo.getInsuredInfoList();
                    List<EpInsuredInfoVo> list = Lists.newArrayList();
                    BigDecimal totalPremium = BigDecimal.ZERO;
                    BigDecimal dynamicPremium = BigDecimal.ZERO;
                    //获取含有附加险的被保人信息和保费之和
                    for (EpInsuredInfoVo insured : insureds) {
                        Optional<EpPersonalProductInfoVo> opt = insured.getProductInfoList().stream().filter(p -> Objects.equals(p.getProductCode(), dto.getProductCode())).findFirst();
                        if (opt.isPresent()) {
                            list.add(insured);
                            totalPremium = totalPremium.add(opt.get().getPremium());
                        }
                    }
                    int size = list.size();
                    for (int j = 0; j < size; j++) {
                        EpInsuredInfoVo insured = list.get(j);
                        EpPersonalProductInfoVo product = list.get(j).getProductInfoList().stream().filter(p -> Objects.equals(p.getProductCode(), dto.getProductCode())).findFirst().get();
                        //查配置输入参数
                        Integer renewalPeriod =preservationDetail.getRenewalPeriod()!=null?preservationDetail.getRenewalPeriod():preservationDetail.getRenewalTermPeriod();
                        PolicyProductPremInput input = buildBasicCommissionConfigParam(policyInfo, insuranceType, product,insured.getInsuredPolicyAge(), renewalPeriod, renewalPeriod);
                        List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(policyNo, input);

                        //按险种维度分摊团险保费(保全新单保费/增减员保费)
                        BigDecimal surrenderPremium = dto.getSurrenderCash().multiply(product.getPremium()).divide(totalPremium, 2, BigDecimal.ROUND_HALF_UP);
                        if (dynamicPremium.abs().add(surrenderPremium.abs()).compareTo(dto.getSurrenderCash().abs()) > 0 || j == size - 1) {
                            surrenderPremium = dto.getSurrenderCash().subtract(dynamicPremium);
                        }
                        dynamicPremium = dynamicPremium.add(surrenderPremium);

                        //遍历佣金配置
                        for (PolicyProductPremResult config : configList) {
                            //初始化，生成科目信息
                            SettlementCostInfoEntity bean = initSettlementCostInfo(eventJob, subjectEnum, eventType, policyInfo,insuranceType);
                            //记录佣金匹配时间，便于冲正流程中获取
                            bean.setCostConfigMatchTime(input.getApprovedTime());
                            //记账时间处理
                            bean.setSettlementTime(getBasicSettlementTime(eventType, policyInfo));
                            bean.setSettlementDate(bean.getSettlementTime());
                            //设置险种保费和实际用于计算的保费
                            bean.setProductPremium(product.getPremium());
                            //非中和农信长险附加险解约按保费0来计算
                            if (isWhaleLongPolicy) {
                                bean.setPremium(BigDecimal.ZERO);
                            } else {
                                bean.setPremium(surrenderPremium.abs().negate());
                            }
                            bean.setBusinessPremium(surrenderPremium.abs().negate());
                            bean.setBusinessAccountTime(getBusinessAccountTime(eventType,policyInfo,preservationDetail));

                            //佣金信息
                            calcBasicCommission(policyNo, null, product, config, bean);
                            //初始化确认信息
                            bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                            //被保人信息
                            setInsuredInfo(insured, bean);
                            //险种信息
                            builderCostProductInfo(policyInfo, productMap, product, bean, renewalPeriod, renewalPeriod);
                            //保全信息
                            builderPreservationInfo(preservationDetail, surrenderPremium, bean);

                            //折算保费
                            builderDiscountPremium(policyNo,policyInfo.getPolicyProductType(),bean);
                            costInfoEntities.add(bean);
                        }
                    }
                }
                break;
            }
            case VEHICLE: {
                productTerminationList.stream().forEach(p -> {
                    Optional<EpProductInfoVo> opt = policyInfo.getProductInfoList().stream().filter(o -> Objects.equals(o.getProductCode(), p.getProductCode())).findFirst();
                    EpProductInfoVo product = opt.get();
                    //查配置输入参数
                    Integer renewalPeriod =preservationDetail.getRenewalPeriod()!=null?preservationDetail.getRenewalPeriod():preservationDetail.getRenewalTermPeriod();
                    PolicyProductPremInput input = commissionConfigService.buildBasicCommissionConfigParam(policyInfo, insuranceType, product,null, renewalPeriod, renewalPeriod);
                    List<CostBasicCommissionConfigDto> configList = commissionConfigService.getCostBasicCommissionConfig(policyNo,policyProductTypeEnum.getCode(), input);
                    //遍历佣金配置
                    for (CostBasicCommissionConfigDto config : configList) {
                        //初始化，生成科目信息
                        SettlementCostInfoEntity bean = initSettlementCostInfo(eventJob, subjectEnum, eventType, policyInfo ,insuranceType);
                        //记录佣金匹配时间，便于冲正流程中获取
                        bean.setCostConfigMatchTime(input.getApprovedTime());
                        //记账时间处理
                        bean.setSettlementTime(getBasicSettlementTime(eventType, policyInfo));
                        bean.setSettlementDate(bean.getSettlementTime());
                        //设置险种保费和实际用于计算的保费
                        bean.setProductPremium(product.getPremium());
                        bean.setPremium(p.getSurrenderCash().abs().negate());
                        bean.setBusinessPremium(p.getSurrenderCash().abs().negate());
                        bean.setBusinessAccountTime(getBusinessAccountTime(eventType,policyInfo,preservationDetail));
                        //佣金信息
                        calcBasicCommission(policyNo,  config, bean);
                        //初始化确认信息
                        bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                        //险种信息
                        builderCostProductInfo(policyInfo, productMap, product, bean, renewalPeriod, renewalPeriod);
                        //保全信息
                        builderPreservationInfo(preservationDetail, p.getSurrenderCash(), bean);

                        //折算保费
                        builderDiscountPremium(policyNo,policyInfo.getPolicyProductType(),bean);
                        costInfoEntities.add(bean);

                        //是否存在车船税
                        if(config.isVehicleInsurance() && Objects.equals(config.getExistVehicleVesselTax(),Boolean.TRUE)){

                            //是否交强险
                            if(isCompulsoryInsurance(policyInfo.getPolicyProductType(),productMap.get(product.getProductCode()))){

                                costInfoEntities.add(builderCompulsoryInsuranceCostInfo(eventType,bean,config));
                            }
                        }
                    }
                });
                break;
            }
            case GROUP: {
                log.info("团险保单不存在协议解约，不处理");
                break;
            }
            default: {
                log.warn("保单类型暂不处理.");
            }
        }
        //设置佣金归属人信息并返回
        return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);
    }



    /**
     * 犹豫期退保
     *
     * @param eventJob
     * @param subjectEnum
     * @param eventType
     * @param productMap
     * @param policyInfo
     * @param preservationDetail
     * @return
     *//*
    @Override
    public List<SettlementCostInfoEntity> builderPolicyHesitateSurrenderCostInfo(SettlementEventJobEntity eventJob,
                                                                                 CostSubjectEnum subjectEnum,
                                                                                 SettlementEventTypeEnum eventType,
                                                                                 Map<String, ProductBase> productMap,
                                                                                 EpContractInfoVo policyInfo,
                                                                                 PolicyPreservationDetailDto preservationDetail) {
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();
        EpPreserveSurrenderDetailDto detail = preservationDetail.getSurrenderDetail();
        PolicyProductTypeEnum policyProductTypeEnum = PolicyProductTypeEnum.getProdTypeEnum(policyInfo.getPolicyProductType());
        Integer insuranceType = getInsuranceType(eventType, policyInfo.getSourcePolicyNo());
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        switch (policyProductTypeEnum) {
            case PERSONAL:
            case PROPERTY: {
                //BigDecimal totalPremium = surrenderDetails.stream().map(EpPreserveSurrenderDetailVo::getPremium).reduce(BigDecimal.ZERO, BigDecimal::add);
                //boolean haveSurrenderPremium = surrenderDetails.get(0).getSurrenderPremium()==null?true:false;
                //BigDecimal dynamicPremium = BigDecimal.ZERO;
                List<EpPreserveSurrenderInsuredDto> surrenderDetails = detail.getInsuredList();
                if(CollectionUtils.isEmpty(surrenderDetails)){
                    throw new GlobalException(SettlementExceptionEnum.C_B_SURRENDER_DETAIL_NOT_EXIST.getException(StrUtil.format("支出端-基础佣金-该保单{}退保明细记录不存在,退保信息：{}", policyNo,detail)));
                }
                for (int i = 0; i < surrenderDetails.size(); i++) {
                    EpPreserveSurrenderInsuredDto dto = surrenderDetails.get(i);
                    Optional<EpInsuredInfoVo> opt = policyInfo.getInsuredInfoList().stream().filter(o -> Objects.equals(o.getInsuredCode(), dto.getInsuredCode())).findFirst();
                    if (!opt.isPresent()) {
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-犹豫期退保人员明细明细在保单详情明细中不存在-保单号={},被保人：{}", eventJob.getEventBusinessCode(), dto.getInsuredCode())));
                    }
                    List<EpPreserveSurrenderProductDto> surrenderProductDtos = dto.getInsuredProductList();
                    for (int j = 0; j < surrenderProductDtos.size(); j++) {
                        EpPreserveSurrenderProductDto productDto = surrenderProductDtos.get(j);
                        Optional<EpPersonalProductInfoVo> productOpt = opt.get().getProductInfoList().stream().filter(p -> Objects.equals(p.getProductCode(), productDto.getProductCode())).findFirst();
                        EpProductInfoVo product = productOpt.get();
                        EpInsuredInfoVo insured = opt.get();
                        Integer renewalPeriod =preservationDetail.getRenewalPeriod()!=null?preservationDetail.getRenewalPeriod():preservationDetail.getRenewalTermPeriod();
                        PolicyProductPremInput input = buildBasicCommissionConfigParam(policyInfo, insuranceType, product,insured.getInsuredPolicyAge(), renewalPeriod, renewalPeriod);
                        List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(policyNo, input);
                        for (PolicyProductPremResult config : configList) {
                            //初始化，生成科目信息
                            SettlementCostInfoEntity bean = initSettlementCostInfo(eventJob, subjectEnum, eventType, policyInfo,insuranceType);
                            //记录佣金匹配时间，便于冲正流程中获取
                            bean.setCostConfigMatchTime(input.getApprovedTime());
                            //记账时间处理
                            bean.setSettlementTime(getBasicSettlementTime(eventType, policyInfo));
                            bean.setSettlementDate(bean.getSettlementTime());
                            //设置险种保费和实际用于计算的保费
                            bean.setProductPremium(product.getPremium());
                            bean.setPremium(product.getPremium().negate());
                            bean.setBusinessPremium(product.getPremium().negate());
                            bean.setBusinessAccountTime(getBusinessAccountTime(eventType,policyInfo,preservationDetail));
                            //佣金信息
                            calcBasicCommission(policyNo, null, product, config, bean);
                            //初始化确认信息
                            bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                            //被保人信息
                            setInsuredInfo(insured, bean);

                            //险种信息
                            builderCostProductInfo(policyInfo, productMap, product, bean, renewalPeriod, renewalPeriod);
                            //保全信息
                            builderPreservationInfo(preservationDetail, product.getPremium().negate(), bean);

                            //折算保费
                            builderDiscountPremium(policyNo,policyInfo.getPolicyProductType(),bean);
                            costInfoEntities.add(bean);
                        }
                    }
                }
                break;
            }
            case VEHICLE: {
                List<EpPreserveSurrenderProductDto> surrenderDetails = detail.getProductList();
                for (int i = 0; i < surrenderDetails.size(); i++) {
                    EpPreserveSurrenderProductDto dto = surrenderDetails.get(i);
                    Optional<EpProductInfoVo> opt = policyInfo.getProductInfoList().stream().filter(o -> Objects.equals(o.getProductCode(), dto.getProductCode())).findFirst();
                    if (!opt.isPresent()) {
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-犹豫期退保险种明细在保单详情明细中不存在-保单号={},险种编码：{}", eventJob.getEventBusinessCode(), dto.getProductCode())));
                    }

                    EpProductInfoVo product = opt.get();
                    Integer renewalPeriod =preservationDetail.getRenewalPeriod()!=null?preservationDetail.getRenewalPeriod():preservationDetail.getRenewalTermPeriod();
                    PolicyProductPremInput input = commissionConfigService.buildBasicCommissionConfigParam(policyInfo, insuranceType, product,null, renewalPeriod, renewalPeriod);
                    List<CostBasicCommissionConfigDto> configList = commissionConfigService.getCostBasicCommissionConfig(policyNo,policyProductTypeEnum.getCode(), input);

                    for (CostBasicCommissionConfigDto config : configList) {
                        //初始化，生成科目信息
                        SettlementCostInfoEntity bean = initSettlementCostInfo(eventJob, subjectEnum, eventType, policyInfo,insuranceType);
                        //记录佣金匹配时间，便于冲正流程中获取
                        bean.setCostConfigMatchTime(input.getApprovedTime());
                        //记账时间处理
                        bean.setSettlementTime(getBasicSettlementTime(eventType, policyInfo));
                        bean.setSettlementDate(bean.getSettlementTime());
                        //设置险种保费和实际用于计算的保费
                        bean.setProductPremium(product.getPremium());
                        bean.setPremium(product.getPremium().negate());
                        bean.setBusinessPremium(product.getPremium().negate());
                        bean.setBusinessAccountTime(getBusinessAccountTime(eventType,policyInfo,preservationDetail));
                        //佣金信息
                        calcBasicCommission(policyNo,  config, bean);
                        //初始化确认信息
                        bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                        //险种信息
                        builderCostProductInfo(policyInfo, productMap, product, bean, renewalPeriod, renewalPeriod);
                        //保全信息
                        builderPreservationInfo(preservationDetail, product.getPremium().negate(), bean);

                        //折算保费
                        builderDiscountPremium(policyNo,policyInfo.getPolicyProductType(),bean);
                        costInfoEntities.add(bean);

                        //是否存在车船税
                        if(config.isVehicleInsurance() && Objects.equals(config.getExistVehicleVesselTax(),Boolean.TRUE)){
                            //是否交强险
                            if(isCompulsoryInsurance(policyInfo.getPolicyProductType(),productMap.get(product.getProductCode()))){
                                costInfoEntities.add(builderCompulsoryInsuranceCostInfo(bean,config));
                            }
                        }

                    }
                }
            }
            case GROUP: {
                log.warn("团险没有犹豫期退保.");
                break;
            }
            default: {
                log.warn("保单类型暂不处理.");
            }
        }
        //设置佣金归属人信息并返回
        return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);
    }*/

    /**
     * 长险保单断保
     *
     * @param eventJob
     * @param subjectEnum
     * @param eventType
     * @param productMap
     * @param policyInfo
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> builderPolicyInterruptCostInfo(SettlementEventJobEntity eventJob,
                                                                         CostSubjectEnum subjectEnum,
                                                                         SettlementEventTypeEnum eventType,
                                                                         Map<String, ProductBase> productMap,
                                                                         EpContractInfoVo policyInfo,
                                                                         PolicyInterruptDto dto) {
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();
        List<SettlementCostInfoEntity> oldList = settlementCostInfoService.listUnCorrectionPolicyCostInfoByContractCode(policyInfo.getContractCode());
        log.info("断保前的佣金记录：{}", JSON.toJSONString(oldList));
        if (CollectionUtils.isEmpty(oldList)) {
            return costInfoEntities;
        }

        //判断是否有首期新契约佣金，没有则需要抛出异常
        log.info("开始判断断保前是否有新契约佣金事件");
        Optional<SettlementCostInfoEntity> first = oldList.stream().filter(o -> Objects.equals(o.getSettlementEventCode(), PERSONAL_NEW_POLICY.getEventCode())).findFirst();
        if (!first.isPresent()) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-长险退保保全事件，-保单号={}, 历史首期新契约佣金不存在", eventJob.getEventBusinessCode())));
        }
        List<SettlementCostInfoEntity> terminationProdList = oldList.stream().filter(m -> ProductStatusEnum.isSurrenderStatus(m.getProductStatus())).collect(Collectors.toList());
        log.info("断保前的佣金记录是否有退保相关记录:{}",JSON.toJSONString(terminationProdList));
        //过滤所有险种编码、被保人编码、期数等于退保记录的险种编码、被保人编码、期数（增加期数是可能存在某期退了附加险，后面又新增了附加险）
        if (CollectionUtils.isNotEmpty(terminationProdList)) {
            List<String> keys = terminationProdList.stream().map(t -> keyLongPolicySurrender(t)).collect(Collectors.toList());
            oldList = oldList.stream().filter(o -> !keys.contains(keyLongPolicySurrender(o))).collect(Collectors.toList());
        }
        for (SettlementCostInfoEntity old : oldList) {
            costInfoEntities.add(builderPolicyInterruptCostInfoByOldCost(eventJob, eventType, policyInfo, old,dto));
        }

        return costInfoEntities;
    }

    public SettlementCostInfoEntity builderPolicyInterruptCostInfoByOldCost(SettlementEventJobEntity eventJob,SettlementEventTypeEnum eventType,EpContractInfoVo policyInfo, SettlementCostInfoEntity old,PolicyInterruptDto dto){
        SettlementCostInfoEntity bean = new SettlementCostInfoEntity();
        BeanUtils.copyProperties(old, bean);
        bean.setId(null);
        bean.setCostCode(PolicySettlementUtils.createCodeLastNumber("CC"));
        //记账时间处理
        bean.setSettlementTime(new Date());
        bean.setSettlementDate(bean.getSettlementTime());
        //事件编号
        bean.setEventSourceCode(eventJob.getPushEventCode());
        //科目类型
        bean.setSettlementSubjectCode(CostSubjectEnum.LONG_NOT_RENEWAL_REBATE_COMM.getCode());
        bean.setSettlementSubjectName(CostSubjectEnum.LONG_NOT_RENEWAL_REBATE_COMM.getName());
        //事件信息
        if (Objects.nonNull(eventType)) {
            bean.setSettlementEventCode(eventType.getEventCode());
            bean.setSettlementEventDesc(eventType.getEventDesc());
            bean.setInitialEventCode(eventType.getEventCode());
            setCommissionTypeByEventType(eventType, bean);
        }

        bean.setSettlementGenerateType(1);

        //清除确认信息
        cleanConfirmInfo(bean);
        bean.setBusinessPremium(old.getBusinessPremium().negate());
        if(old.getDiscountPremium()!=null) {
            bean.setDiscountPremium(old.getDiscountPremium().negate());
        }
        //断保时间
        bean.setBusinessAccountTime(dto.getInterruptTime());
        bean.setPremium(old.getPremium().negate());
        bean.setCostAmount(old.getCostAmount().negate());
        bean.setGrantAmount(old.getGrantAmount().negate());

        bean.setBusinessDiscountPremium(old.getBusinessDiscountPremium()!=null?old.getBusinessDiscountPremium():null);

        return bean;
    }



   /* @Override
    public List<SettlementCostInfoEntity> builderStandardSurrenderCostInfo(SettlementEventJobEntity eventJob,
                                                                           CostSubjectEnum subjectEnum,
                                                                           SettlementEventTypeEnum eventType,
                                                                           Map<String, ProductBase> productMap,
                                                                           EpContractInfoVo policyInfo,
                                                                           PolicyPreservationDetailDto preservationDetail) {
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();
        //List<EpPreserveSurrenderDetailVo> surrenderDetails = preservationDetail.getSurrenderDetail();
        PolicyProductTypeEnum policyProductTypeEnum = PolicyProductTypeEnum.getProdTypeEnum(policyInfo.getPolicyProductType());
        Integer insuranceType = getInsuranceType(eventType, policyInfo.getSourcePolicyNo());
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();

        //BigDecimal totalPremium = preservationDetail.getSurrenderDetail().stream().map(EpPreserveSurrenderDetailVo::getPremium).reduce(BigDecimal.ZERO, BigDecimal::add);
        //boolean haveDetailSurrenderPremium = preservationDetail.getSurrenderDetail().get(0).getSurrenderPremium()==null?false:true;
        //BigDecimal dynamicPremium = BigDecimal.ZERO;
        //是否长险
        Integer longShortFlag = policyInfo.getContractBaseInfo().getLongShortFlag();
        BaseSurrenderDto param = BaseSurrenderDto.builder()
                .eventJob(eventJob)
                .subjectEnum(subjectEnum)
                .eventType(eventType)
                .productMap(productMap)
                .policyInfo(policyInfo)
                .preservationDetail(preservationDetail)

                .insuranceType(insuranceType)
                .policyNo(policyNo)
                //.totalPremium(totalPremium)
                //.haveDetailSurrenderPremium(haveDetailSurrenderPremium)
                //.dynamicPremium(dynamicPremium)
                .longShortFlag(longShortFlag)
                .build();


        switch (policyProductTypeEnum) {
            case PERSONAL:
            case PROPERTY: {
                log.info("合同{}长短险标志：{}",policyInfo.getContractCode(),longShortFlag);
                if (longShortFlag == 1) {

                    builderLongPolicySurrenderCostInfo(eventJob,subjectEnum,eventType,productMap,policyInfo,preservationDetail,costInfoEntities);
                    return costInfoEntities;
                }else{
                    builderStandardSurrenderByInsuredInfo(param, costInfoEntities);
                    return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);
                }

            }
            case VEHICLE: {
                if(CollectionUtils.isNotEmpty(param.getPreservationDetail().getSurrenderDetail().getInsuredList())){
                    builderStandardSurrenderByInsuredInfo(param, costInfoEntities);
                }else {
                    builderStandardSurrenderByProductInfo(param, costInfoEntities);
                }
                return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);
            }
            case GROUP: {
                if(Objects.equals(param.getPolicyInfo().getChannelInfo().getChannelCode(),ZHNX_CHANNEL_CODE)){
                    builderGroupStandardSurrenderByInsuredInfo(param,costInfoEntities);
                    return costInfoEntities;
                }else{
                    builderStandardSurrenderByProductInfo(param,costInfoEntities);
                    return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);
                }

            }
            default: {
                log.warn("保单类型暂不处理.");
            }
        }
        return costInfoEntities;
    }*/

    /*private void builderGroupStandardSurrenderByInsuredInfo(BaseSurrenderDto param, List<SettlementCostInfoEntity> costInfoEntities) {
        List<EpPreserveSurrenderInsuredDto> surrenderDetails = param.getPreservationDetail().getSurrenderDetail().getInsuredList();
        boolean haveSurrenderPremium = surrenderDetails.get(0).getInsuredSurrenderPremium() == null ? false : true;
        //中和农信渠道，如果没有被保人退保保费则抛出异常
        if (Objects.equals(param.getPolicyInfo().getChannelInfo().getChannelCode(), ZHNX_CHANNEL_CODE) && !haveSurrenderPremium) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-团险整单退保被保人退保明细金额不存在-保单号={}", param.getEventJob().getEventBusinessCode())));
        }

        for (int i = 0; i < surrenderDetails.size(); i++) {
            EpPreserveSurrenderInsuredDto dto = surrenderDetails.get(i);
            List<EpPreserveSurrenderProductDto> surrenderProductDtos = dto.getInsuredProductList();

            BigDecimal dynamicPremium = BigDecimal.ZERO;
            BigDecimal totalPremium = surrenderProductDtos.stream().map(EpPreserveSurrenderProductDto::getPremium).reduce(BigDecimal.ZERO, BigDecimal::add);

            for (int j = 0; j < surrenderProductDtos.size(); j++) {
                EpPreserveSurrenderProductDto productDto = surrenderProductDtos.get(j);


                EpProductInfoVo product = new EpProductInfoVo();
                BeanUtils.copyProperties(productDto, product);
                Integer renewalPeriod =param.getPreservationDetail().getRenewalPeriod()!=null?param.getPreservationDetail().getRenewalPeriod():param.getPreservationDetail().getRenewalTermPeriod();
                PolicyProductPremInput input = buildBasicCommissionConfigParam(param.getPolicyInfo(), param.getInsuranceType(), product,dto.getInsuredPolicyAge(),renewalPeriod, renewalPeriod);
                List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(param.getPolicyNo(), input);

                //计算退保保费
                BigDecimal premium = dto.getInsuredSurrenderPremium().multiply(productDto.getPremium()).divide(totalPremium, 2, BigDecimal.ROUND_HALF_UP);
                if (dynamicPremium.abs().add(premium.abs()).compareTo(dto.getInsuredSurrenderPremium().abs()) > 0 || j == surrenderProductDtos.size() - 1) {
                    premium = dto.getInsuredSurrenderPremium().subtract(dynamicPremium);
                }
                dynamicPremium = dynamicPremium.add(premium);


                for (PolicyProductPremResult config : configList) {
                    //初始化，生成科目信息
                    SettlementCostInfoEntity bean = initSettlementCostInfo(param.getEventJob(), param.getSubjectEnum(), param.getEventType(), param.getPolicyInfo(),param.getInsuranceType());
                    //记录佣金匹配时间，便于冲正流程中获取
                    bean.setCostConfigMatchTime(input.getApprovedTime());
                    //记账时间处理
                    bean.setSettlementTime(getBasicSettlementTime(param.getEventType(), param.getPolicyInfo()));
                    bean.setSettlementDate(bean.getSettlementTime());
                    //设置保费,退保保费为在结算表里为负数、和实际用于计算的保费
                    bean.setProductPremium(product.getPremium());
                    bean.setPremium(premium.abs().negate());
                    bean.setBusinessPremium(premium.abs().negate());
                    bean.setSurrenderAmount(premium.abs().negate());
                    bean.setBusinessAccountTime(getBusinessAccountTime(param.getEventType(),param.getPolicyInfo(),param.getPreservationDetail()));
                    //佣金信息
                    calcBasicCommission(param.getPolicyNo(), dto.getAddEndorsementNo(), product, config, bean);
                    //初始化确认信息
                    bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                    //被保人信息
                    bean.setInsuredCode(dto.getInsuredCode());
                    bean.setInsuredBirthday(dto.getInsuredBirthday());
                    bean.setInsuredGender(dto.getInsuredGender());
                    bean.setInsuredIdCard(dto.getInsuredIdCard());
                    bean.setInsuredMobile(dto.getInsuredMobile());
                    bean.setInsuredName(dto.getInsuredName());
                    //险种信息
                    builderCostProductInfo(param.getPolicyInfo(), param.getProductMap(), product, bean,renewalPeriod, renewalPeriod);
                    //保全信息
                    builderPreservationInfo(param.getPreservationDetail(), premium.abs().negate(), bean);

                    settlementCostOwnerService.builderGroupPolicySurrenderOwnerInfoByInsured(dto.getChannelCode(),dto,bean);

                    //折算保费
                    builderDiscountPremium(param.getPolicyNo(),param.getPolicyInfo().getPolicyProductType(),bean);
                    costInfoEntities.add(bean);
                }
            }
        }

    }*/


    /*private void builderStandardSurrenderByInsuredInfo(BaseSurrenderDto param, List<SettlementCostInfoEntity> costInfoEntities) {
        List<EpPreserveSurrenderInsuredDto> surrenderDetails = param.getPreservationDetail().getSurrenderDetail().getInsuredList();


        //是否有被保人退保明细金额
        if(CollectionUtils.isEmpty(surrenderDetails)){
            throw new GlobalException(SettlementExceptionEnum.C_B_SURRENDER_DETAIL_NOT_EXIST.getException(StrUtil.format("支出端-基础佣金-该保单{}退保明细记录不存在,退保信息：{}", param.getPolicyNo(),param.getPreservationDetail())));
        }
        boolean haveSurrenderPremium = surrenderDetails.get(0).getInsuredSurrenderPremium() == null ? false : true;
        BigDecimal totalPremium = BigDecimal.ZERO;
        BigDecimal dynamicPremium = BigDecimal.ZERO;
        if (!haveSurrenderPremium) {
            for (EpPreserveSurrenderInsuredDto dto : surrenderDetails) {
                totalPremium = totalPremium.add(dto.getInsuredProductList().stream().map(EpPreserveSurrenderProductDto::getPremium).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
        }

        for (int i = 0; i < surrenderDetails.size(); i++) {
            EpPreserveSurrenderInsuredDto dto = surrenderDetails.get(i);
            Optional<EpInsuredInfoVo> opt = param.getPolicyInfo().getInsuredInfoList().stream().filter(o -> Objects.equals(o.getInsuredCode(), dto.getInsuredCode())).findFirst();
            if (!opt.isPresent()) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-标准退保人员明细明细在保单详情明细中不存在-保单号={},被保人：{}", param.getEventJob().getEventBusinessCode(), dto.getInsuredCode())));
            }
            EpInsuredInfoVo insured = opt.get();

            List<EpPreserveSurrenderProductDto> surrenderProductDtos = dto.getInsuredProductList();
            if (haveSurrenderPremium) {
                dynamicPremium = BigDecimal.ZERO;
                totalPremium = surrenderProductDtos.stream().map(EpPreserveSurrenderProductDto::getPremium).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            for (int j = 0; j < surrenderProductDtos.size(); j++) {
                EpPreserveSurrenderProductDto productDto = surrenderProductDtos.get(j);

                Optional<EpPersonalProductInfoVo> productOpt = insured.getProductInfoList().stream().filter(p -> Objects.equals(p.getProductCode(), productDto.getProductCode())).findFirst();
                EpProductInfoVo product = productOpt.get();
                Integer renewalPeriod = param.getPreservationDetail().getRenewalPeriod()!=null?param.getPreservationDetail().getRenewalPeriod():param.getPreservationDetail().getRenewalTermPeriod();
                PolicyProductPremInput input = buildBasicCommissionConfigParam(param.getPolicyInfo(), param.getInsuranceType(), product,insured.getInsuredPolicyAge(), renewalPeriod, renewalPeriod);
                List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(param.getPolicyNo(), input);

                //计算退保保费
                BigDecimal premium;
                if (haveSurrenderPremium) {
                    premium = dto.getInsuredSurrenderPremium().multiply(productDto.getPremium()).divide(totalPremium, 2, BigDecimal.ROUND_HALF_UP);
                    if (dynamicPremium.abs().add(premium.abs()).compareTo(dto.getInsuredSurrenderPremium().abs()) > 0 || j == surrenderProductDtos.size() - 1) {
                        premium = dto.getInsuredSurrenderPremium().subtract(dynamicPremium);
                    }
                    dynamicPremium = dynamicPremium.add(premium);
                } else {
                    premium = param.getPreservationDetail().getSurrenderCash().multiply(productDto.getPremium()).divide(totalPremium, 2, BigDecimal.ROUND_HALF_UP);
                    if (dynamicPremium.abs().add(premium.abs()).compareTo(param.getPreservationDetail().getSurrenderCash().abs()) > 0 || (i == surrenderDetails.size() - 1 && j == surrenderProductDtos.size() - 1)) {
                        premium = param.getPreservationDetail().getSurrenderCash().subtract(dynamicPremium);
                    }
                    dynamicPremium = dynamicPremium.add(premium);
                }

                for (PolicyProductPremResult config : configList) {
                    //初始化，生成科目信息
                    SettlementCostInfoEntity bean = initSettlementCostInfo(param.getEventJob(), param.getSubjectEnum(), param.getEventType(), param.getPolicyInfo(),param.getInsuranceType());
                    //记录佣金匹配时间，便于冲正流程中获取
                    bean.setCostConfigMatchTime(input.getApprovedTime());
                    //记账时间处理
                    bean.setSettlementTime(getBasicSettlementTime(param.getEventType(), param.getPolicyInfo()));
                    bean.setSettlementDate(bean.getSettlementTime());
                    //设置保费,退保保费为在结算表里为负数、和实际用于计算的保费
                    bean.setProductPremium(product.getPremium());
                    bean.setSurrenderAmount(premium.abs().negate());
                    bean.setPremium(premium.abs().negate());
                    bean.setBusinessPremium(premium.abs().negate());
                    bean.setBusinessAccountTime(getBusinessAccountTime(param.getEventType(),param.getPolicyInfo(),param.getPreservationDetail()));
                    //佣金信息
                    calcBasicCommission(param.getPolicyNo(), null, product, config, bean);
                    //初始化确认信息
                    bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                    //被保人信息
                    setInsuredInfo(insured, bean);
                    //险种信息
                    builderCostProductInfo(param.getPolicyInfo(), param.getProductMap(), product, bean, renewalPeriod, renewalPeriod);
                    //保全信息
                    builderPreservationInfo(param.getPreservationDetail(), premium.abs().negate(), bean);

                    //折算保费
                    builderDiscountPremium(param.getPolicyNo(),param.getPolicyInfo().getPolicyProductType(),bean);
                    costInfoEntities.add(bean);
                }
            }

        }
    }

    private void builderStandardSurrenderByProductInfo(BaseSurrenderDto param, List<SettlementCostInfoEntity> costInfoEntities) {
        List<EpPreserveSurrenderProductDto> surrenderDetails = param.getPreservationDetail().getSurrenderDetail().getProductList();
        boolean haveSurrenderPremium = surrenderDetails.get(0).getSurrenderPremium() == null ? false : true;
        BigDecimal totalPremium = surrenderDetails.stream().map(EpPreserveSurrenderProductDto::getPremium).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal dynamicPremium = BigDecimal.ZERO;
        Boolean isWhaleLong = isWhaleLongPolicy(param.getPolicyInfo(), param.getProductMap());
        for (int i = 0; i < surrenderDetails.size(); i++) {
            EpPreserveSurrenderProductDto dto = surrenderDetails.get(i);
            Optional<EpProductInfoVo> opt = param.getPolicyInfo().getProductInfoList().stream().filter(o -> Objects.equals(o.getProductCode(), dto.getProductCode())).findFirst();
            if (!opt.isPresent()) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-标准退保险种明细在保单详情明细中不存在-保单号={},险种编码：{}", param.getEventJob().getEventBusinessCode(), dto.getProductCode())));
            }
            EpProductInfoVo product = opt.get();
            Integer renewalPeriod =param.getPreservationDetail().getRenewalPeriod()!=null?param.getPreservationDetail().getRenewalPeriod():param.getPreservationDetail().getRenewalTermPeriod();
            PolicyProductPremInput input = commissionConfigService.buildBasicCommissionConfigParam(param.getPolicyInfo(), param.getInsuranceType(), product,null, renewalPeriod, renewalPeriod);
            List<CostBasicCommissionConfigDto> configList = commissionConfigService.getCostBasicCommissionConfig(param.getPolicyNo(),param.getPolicyInfo().getPolicyProductType(), input);

            //长险全额退,即退保保费为源保费
            BigDecimal surrenderPremium;
            if (param.getLongShortFlag() == 1) {
                surrenderPremium = dto.getPremium();
            } else {
                surrenderPremium = dto.getSurrenderPremium();
                if (!haveSurrenderPremium) {
                    surrenderPremium = param.getPreservationDetail().getSurrenderCash().multiply(dto.getPremium()).divide(totalPremium, 2, BigDecimal.ROUND_HALF_UP);
                    if (dynamicPremium.abs().add(surrenderPremium.abs()).compareTo(param.getPreservationDetail().getSurrenderCash().abs()) > 0 || i == surrenderDetails.size() - 1) {
                        surrenderPremium = param.getPreservationDetail().getSurrenderCash().subtract(dynamicPremium);
                    }
                    dynamicPremium = dynamicPremium.add(surrenderPremium);
                }
            }

            for (CostBasicCommissionConfigDto config : configList) {
                //初始化，生成科目信息
                SettlementCostInfoEntity bean = initSettlementCostInfo(param.getEventJob(), param.getSubjectEnum(), param.getEventType(), param.getPolicyInfo(),param.getInsuranceType());
                //记录佣金匹配时间，便于冲正流程中获取
                bean.setCostConfigMatchTime(input.getApprovedTime());
                //记账时间处理
                bean.setSettlementTime(getBasicSettlementTime(param.getEventType(), param.getPolicyInfo()));
                bean.setSettlementDate(bean.getSettlementTime());
                //设置保费,退保保费为在结算表里为负数、和实际用于计算的保费
                bean.setProductPremium(product.getPremium());
                if (isWhaleLong) {
                    bean.setPremium(BigDecimal.ZERO);
                } else {
                    bean.setPremium(surrenderPremium.abs().negate());
                }
                bean.setBusinessPremium(surrenderPremium.abs().negate());
                bean.setSurrenderAmount(surrenderPremium.abs().negate());
                bean.setBusinessAccountTime(getBusinessAccountTime(param.getEventType(),param.getPolicyInfo(),param.getPreservationDetail()));
                //佣金信息
                calcBasicCommission(param.getPolicyNo(), config, bean);
                //初始化确认信息
                bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                //险种信息
                builderCostProductInfo(param.getPolicyInfo(), param.getProductMap(), product, bean, renewalPeriod, renewalPeriod);
                //保全信息
                builderPreservationInfo(param.getPreservationDetail(), surrenderPremium, bean);

                //折算保费
                builderDiscountPremium(param.getPolicyNo(),param.getPolicyInfo().getPolicyProductType(),bean);
                costInfoEntities.add(bean);

                //是否存在车船税
                if(config.isVehicleInsurance() && Objects.equals(config.getExistVehicleVesselTax(),Boolean.TRUE)){
                    //是否交强险
                    if(isCompulsoryInsurance(param.getPolicyInfo().getPolicyProductType(),param.getProductMap().get(product.getProductCode()))){
                        costInfoEntities.add(builderCompulsoryInsuranceCostInfo(bean,config));
                    }
                }
            }
        }
    }*/



     /*@Override
    @Deprecated
   public List<SettlementCostInfoEntity> builderProtocolTerminationCostInfo(SettlementEventJobEntity eventJob,
                                                                             CostSubjectEnum subjectEnum,
                                                                             SettlementEventTypeEnum eventType,
                                                                             Map<String, ProductBase> productMap,
                                                                             EpContractInfoVo policyInfo,
                                                                             PolicyPreservationDetailDto preservationDetail) {
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();
        EpPreserveSurrenderDetailDto detail = preservationDetail.getSurrenderDetail();
        PolicyProductTypeEnum policyProductTypeEnum = PolicyProductTypeEnum.getProdTypeEnum(policyInfo.getPolicyProductType());
        Integer insuranceType = getInsuranceType(eventType, policyInfo.getSourcePolicyNo());
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        //是否长险
        Integer longShortFlag = policyInfo.getContractBaseInfo().getLongShortFlag();
        switch (policyProductTypeEnum) {
            case PERSONAL:
            case PROPERTY: {
                if (longShortFlag == 1) {
                    builderLongPolicySurrenderCostInfo(eventJob, subjectEnum, eventType, productMap, policyInfo, preservationDetail, costInfoEntities);
                    return costInfoEntities;
                } else {
                    List<EpPreserveSurrenderInsuredDto> surrenderDetails = detail.getInsuredList();
                    for (int i = 0; i < surrenderDetails.size(); i++) {
                        EpPreserveSurrenderInsuredDto dto = surrenderDetails.get(i);
                        Optional<EpInsuredInfoVo> opt = policyInfo.getInsuredInfoList().stream().filter(o -> Objects.equals(o.getInsuredCode(), dto.getInsuredCode())).findFirst();
                        if (!opt.isPresent()) {
                            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-协议解约人员明细明细在保单详情明细中不存在-保单号={},被保人：{}", eventJob.getEventBusinessCode(), dto.getInsuredCode())));
                        }
                        EpInsuredInfoVo insured = opt.get();
                        List<EpPreserveSurrenderProductDto> surrenderProductDtos = dto.getInsuredProductList();
                        for (int j = 0; j < surrenderProductDtos.size(); j++) {
                            EpPreserveSurrenderProductDto productDto = surrenderProductDtos.get(j);

                            Optional<EpPersonalProductInfoVo> productOpt = insured.getProductInfoList().stream().filter(p -> Objects.equals(p.getProductCode(), productDto.getProductCode())).findFirst();
                            EpProductInfoVo product = productOpt.get();
                            Integer renewalPeriod =preservationDetail.getRenewalPeriod()!=null?preservationDetail.getRenewalPeriod():preservationDetail.getRenewalTermPeriod();
                            PolicyProductPremInput input = buildBasicCommissionConfigParam(policyInfo, insuranceType, product,insured.getInsuredPolicyAge(), renewalPeriod, renewalPeriod);
                            List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(policyNo, input);
                            for (PolicyProductPremResult config : configList) {
                                //初始化，生成科目信息
                                SettlementCostInfoEntity bean = initSettlementCostInfo(eventJob, subjectEnum, eventType, policyInfo,insuranceType);
                                //记录佣金匹配时间，便于冲正流程中获取
                                bean.setCostConfigMatchTime(input.getApprovedTime());
                                //记账时间处理
                                bean.setSettlementTime(getBasicSettlementTime(eventType, policyInfo));
                                bean.setSettlementDate(bean.getSettlementTime());
                                //设置险种保费和实际用于计算的保费
                                bean.setProductPremium(product.getPremium());
                                bean.setSurrenderAmount(productDto.getPremium().abs().negate());
                                bean.setPremium(productDto.getPremium().abs().negate());
                                bean.setBusinessPremium(productDto.getPremium().abs().negate());
                                bean.setBusinessAccountTime(getBusinessAccountTime(eventType,policyInfo,preservationDetail));
                                //佣金信息
                                calcBasicCommission(policyNo, null, product, config, bean);
                                //初始化确认信息
                                bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                                //被保人信息
                                setInsuredInfo(insured, bean);
                                //险种信息
                                builderCostProductInfo(policyInfo, productMap, product, bean, renewalPeriod, renewalPeriod);
                                //保全信息
                                builderPreservationInfo(preservationDetail, productDto.getPremium(), bean);

                                //折算保费
                                builderDiscountPremium(policyNo,policyInfo.getPolicyProductType(),bean);
                                costInfoEntities.add(bean);
                            }
                        }


                    }
                    return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);

                }
            }
            case VEHICLE: {
                List<EpPreserveSurrenderProductDto> surrenderDetails = detail.getProductList();
                for (int i = 0; i < surrenderDetails.size(); i++) {
                    EpPreserveSurrenderProductDto dto = surrenderDetails.get(i);
                    Optional<EpProductInfoVo> opt = policyInfo.getProductInfoList().stream().filter(o -> Objects.equals(o.getProductCode(), dto.getProductCode())).findFirst();
                    if (!opt.isPresent()) {
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-协议解约险种明细在保单详情明细中不存在-保单号={},险种编码：{}", eventJob.getEventBusinessCode(), dto.getProductCode())));
                    }
                    EpProductInfoVo product = opt.get();
                    Integer renewalPeriod =preservationDetail.getRenewalPeriod()!=null?preservationDetail.getRenewalPeriod():preservationDetail.getRenewalTermPeriod();
                    PolicyProductPremInput input = commissionConfigService.buildBasicCommissionConfigParam(policyInfo, insuranceType, product,null, renewalPeriod, renewalPeriod);
                    List<CostBasicCommissionConfigDto> configList = commissionConfigService.getCostBasicCommissionConfig(policyNo,policyProductTypeEnum.getCode(), input);
                    //长险全额退,即退保保费为源保费
                    BigDecimal premium = dto.getPremium();
                    for (CostBasicCommissionConfigDto config : configList) {
                        //初始化，生成科目信息
                        SettlementCostInfoEntity bean = initSettlementCostInfo(eventJob, subjectEnum, eventType, policyInfo,insuranceType);
                        //记录佣金匹配时间，便于冲正流程中获取
                        bean.setCostConfigMatchTime(input.getApprovedTime());
                        //记账时间处理
                        bean.setSettlementTime(getBasicSettlementTime(eventType, policyInfo));
                        bean.setSettlementDate(bean.getSettlementTime());
                        //设置险种保费和实际用于计算的保费
                        bean.setProductPremium(product.getPremium());
                        bean.setPremium(premium.abs().negate());
                        bean.setSurrenderAmount(premium.abs().negate());
                        bean.setBusinessPremium(premium.abs().negate());
                        bean.setBusinessAccountTime(getBusinessAccountTime(eventType,policyInfo,preservationDetail));
                        //佣金信息
                        calcBasicCommission(policyNo, config, bean);
                        //初始化确认信息
                        bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                        //险种信息
                        builderCostProductInfo(policyInfo, productMap, product, bean,renewalPeriod, renewalPeriod);
                        //保全信息
                        builderPreservationInfo(preservationDetail, premium, bean);
                        //折算保费
                        builderDiscountPremium(policyNo,policyInfo.getPolicyProductType(),bean);
                        costInfoEntities.add(bean);

                        //是否存在车船税
                        if(config.isVehicleInsurance() && Objects.equals(config.getExistVehicleVesselTax(),Boolean.TRUE)){
                            //是否交强险
                            if(isCompulsoryInsurance(policyInfo.getPolicyProductType(),productMap.get(product.getProductCode()))){
                                costInfoEntities.add(builderCompulsoryInsuranceCostInfo(bean,config));
                            }
                        }
                    }
                }
                return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);
            }

            case GROUP: {
                break;
            }
            default: {
                log.warn("保单类型暂不处理.");
            }
        }


        return costInfoEntities;
    }*/

    private String keyLongPolicySurrender(SettlementCostInfoEntity old) {
        return old.getContractCode() + old.getProductCode() + old.getInsuredCode() + old.getRenewalPeriod();
    }


    private void builderSurrenderCostInfoByOldCostInfo(SettlementCostInfoEntity old,
                                                       SettlementEventJobEntity eventJob,
                                                       CostSubjectEnum subjectEnum,
                                                       SettlementEventTypeEnum eventType,
                                                       Map<String, ProductBase> productMap,
                                                       EpContractInfoVo policyInfo,
                                                       PolicyPreservationDetailDto preservationDetail,
                                                       List<SettlementCostInfoEntity> costInfoEntities) {
        //查询历史的未确认未冲正的且为基础佣金的记录
        List<SettlementCostInfoEntity> oldList = listPolicyUnCorrectionCostInfoByContractCode(eventType.getEventName(),policyInfo.getContractCode(),policyInfo.getContractBaseInfo().getPolicyNo());

        //获取所有历史退保记录
        List<SettlementCostInfoEntity> terminationProdList = oldList.stream().filter(m -> ProductStatusEnum.isSurrenderStatus(m.getProductStatus())).collect(Collectors.toList());
        //过滤所有险种编码、被保人编码、期数等于退保记录的险种编码、被保人编码、期数（增加期数是可能存在某期退了附加险，后面又新增了附加险）
        if (CollectionUtils.isNotEmpty(terminationProdList)) {
            List<String> keys = terminationProdList.stream().map(t -> keyLongPolicySurrender(t)).collect(Collectors.toList());
            oldList = oldList.stream().filter(o -> !keys.contains(keyLongPolicySurrender(o))).collect(Collectors.toList());
        }
        oldList = filterOldSurrenderCostInfo(oldList);

    }

    protected List<SettlementCostInfoEntity> listPolicyUnCorrectionCostInfoByContractCode(String eventName,String contractCode,String policyNo){
        List<SettlementCostInfoEntity> oldList = settlementCostInfoService.listLongPolicyUnCorrectionCostInfoByContractCode(contractCode);
        if (CollectionUtils.isEmpty(oldList)) {
            log.info("支出端-基础佣金-{}事件-查询历史佣金记录不存在-保单号={}",eventName, policyNo);
            throw new GlobalException(SettlementExceptionEnum.C_B_LONG_SURROUND_OLD_NOT_EXIST.getException(StrUtil.format("支出端-基础佣金-{}事件-查询历史佣金记录不存在-保单号={}",eventName, policyNo)));
        }
        return oldList;
    }

    /**
     * //过滤所有险种编码、被保人编码、期数等于退保记录的险种编码、被保人编码、期数（增加期数是可能存在某期退了附加险，后面又新增了附加险）
     * @param oldList
     * @return
     */
    protected List<SettlementCostInfoEntity> filterOldSurrenderCostInfo(List<SettlementCostInfoEntity> oldList){
        List<SettlementCostInfoEntity> terminationProdList = oldList.stream().filter(m -> ProductStatusEnum.isSurrenderStatus(m.getProductStatus())).collect(Collectors.toList());
        //过滤所有险种编码、被保人编码、期数等于退保记录的险种编码、被保人编码、期数（增加期数是可能存在某期退了附加险，后面又新增了附加险）
        if (CollectionUtils.isNotEmpty(terminationProdList)) {
            List<String> keys = terminationProdList.stream().map(t -> keyLongPolicySurrender(t)).collect(Collectors.toList());
            return oldList.stream().filter(o -> !keys.contains(keyLongPolicySurrender(o))).collect(Collectors.toList());
        }
        return oldList;
    }




    /*private void builderLongPolicySurrenderCostInfo(SettlementEventJobEntity eventJob,
                                                    CostSubjectEnum subjectEnum,
                                                    SettlementEventTypeEnum eventType,
                                                    Map<String, ProductBase> productMap,
                                                    EpContractInfoVo policyInfo,
                                                    PolicyPreservationDetailDto preservationDetail,
                                                    List<SettlementCostInfoEntity> costInfoEntities) {
        //长险退保查询历史的未确认未冲正的且为基础佣金的记录
        List<SettlementCostInfoEntity> oldList = listPolicyUnCorrectionCostInfoByContractCode(eventType.getEventName(),policyInfo.getContractCode(),policyInfo.getContractBaseInfo().getPolicyNo());

        //查询是否有首期记录，协议解约的情况下需要抛异常
        Optional<SettlementCostInfoEntity> first = oldList.stream().filter(o -> Objects.equals(o.getSettlementEventCode(), PERSONAL_NEW_POLICY.getEventCode())).findFirst();
        if (!first.isPresent()) {
            if(Objects.equals(eventJob.getEventType(), SettlementEventTypeEnum.PROTOCOL_TERMINATION.getEventCode())){
                throw new GlobalException(SettlementExceptionEnum.C_B_LONG_SURROUND_OLD_NOT_EXIST.getException(StrUtil.format("支出端-基础佣金-{}事件-历史新契约佣金记录不存在-保单号={}",eventType.getEventName(), policyInfo.getContractBaseInfo().getPolicyNo())));
            }
        }
        //过滤所有险种编码、被保人编码、期数等于退保记录的险种编码、被保人编码、期数（增加期数是可能存在某期退了附加险，后面又新增了附加险）
        oldList = filterOldSurrenderCostInfo(oldList);

        for (SettlementCostInfoEntity old : oldList) {
            costInfoEntities.add(builderLongSurrenderCostInfoByOldCost(eventJob, eventType, policyInfo, old, preservationDetail));
        }

    }*/



    /**
     * 支出端-佣金信息保存(不适合修改保单号事件变更)
     *
     * @param eventJob
     * @param policy
     * @param costInfoList
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveCostCommissionRecord(SettlementEventJobEntity eventJob, SettlementCostPolicyInfoEntity policy, List<SettlementCostInfoEntity> costInfoList) {
        if (CollectionUtils.isEmpty(costInfoList)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-佣金信息保存-保单号={}, 佣金记录为空", eventJob.getEventBusinessCode())));
        }

        saveCostRecord(policy,costInfoList);
    }

    /**
     * 支出端-佣金信息保存
     *
     * @param policy
     * @param costInfoList
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveCostRecord( SettlementCostPolicyInfoEntity policy, List<SettlementCostInfoEntity> costInfoList) {

        if (policy != null) {
            if (Objects.isNull(policy.getId())) {
                settlementCostPolicyInfoService.save(policy);
            }else{
                settlementCostPolicyInfoService.updateById(policy);
            }
            costInfoList.stream().forEach(c -> {

                c.setCostPolicyId(policy.getId());
            });
        }
        settlementCostInfoService.saveBatch(costInfoList);
    }

    @Override
    public void updateCostPolicyRecord(SettlementCostPolicyInfoEntity policy){
        if(Objects.isNull(policy)){
            return ;
        }
        if(Objects.nonNull(policy.getId())) {
            settlementCostPolicyInfoService.updateById(policy);
        }
    }

    /**
     * 生成源记录的对冲记录(冲正用于生成对冲记录)
     *
     * @param eventJob
     * @param eventType
     * @param old
     * @param correctionUser
     * @param correctionRemark
     * @return
     */
    @Override
    public SettlementCostInfoEntity builderOffsetCostInfo(SettlementEventJobEntity eventJob,
                                                          SettlementEventTypeEnum eventType,
                                                          SettlementCostInfoEntity old,
                                                          Date newBusinessTime,
                                                          String correctionUser,
                                                          String correctionRemark, Boolean isCorrection) {
        return builderOffsetCostInfoBySourceCode(eventJob.getPushEventCode(),eventType,old,newBusinessTime,correctionUser,correctionRemark,isCorrection);
    }

    @Override
    public SettlementCostInfoEntity builderOffsetCostInfoBySourceCode(String sourceCode,
                                                          SettlementEventTypeEnum eventType,
                                                          SettlementCostInfoEntity old,
                                                          Date newBusinessTime,
                                                          String correctionUser,
                                                          String correctionRemark, Boolean isCorrection){
        SettlementCostInfoEntity bean = new SettlementCostInfoEntity();
        BeanUtils.copyProperties(old, bean);
        bean.setId(null);
        bean.setCostCode(PolicySettlementUtils.createCodeLastNumber("CC"));
        //记账时间处理
        bean.setSettlementTime(new Date());
        bean.setSettlementDate(bean.getSettlementTime());


        //事件编号
        bean.setEventSourceCode(sourceCode);


        //事件信息
        if (Objects.nonNull(eventType)) {
            bean.setSettlementEventCode(eventType.getEventCode());
            bean.setSettlementEventDesc(eventType.getEventDesc());

        }
        setCommissionTypeByEventType(SettlementEventTypeEnum.deCode(old.getInitialEventCode()), bean);
        //科目信息与被冲正记录保持一直
        //bean.setSettlementSubjectCode(subjectEnum.getCode());
        //bean.setSettlementSubjectName(subjectEnum.getName());
        bean.setSettlementGenerateType(1);
        //是否冲正
        if (isCorrection) {
            //业务记账时间处理
            bean.setBusinessAccountTime(getCorrectionBusinessAccountTime(old.getBusinessAccountTime(),newBusinessTime));
            bean.setSourceCostCode(old.getCostCode());
            bean.setCorrectionTime(new Date());
            bean.setCorrectionFlag(1);
            if (StringUtil.isNotBlank(correctionUser)) {
                bean.setCorrectionUser(correctionUser);
            }

            if (Objects.equals(SYSTEM_CORRECTION_USER, correctionUser)) {
                bean.setCorrectionOpType(0);
            } else {
                bean.setCorrectionOpType(1);
            }
        }

        if (StringUtil.isNotBlank(correctionRemark)) {
            bean.setCorrectionRemark(correctionRemark);
        }
        //清除确认信息
        cleanConfirmInfo(bean);
        //冲正金额字段
        bean.setPremium(old.getPremium().negate());
        bean.setBusinessPremium(old.getBusinessPremium().negate());
        if(old.getDiscountPremium()!=null) {
            bean.setDiscountPremium(old.getDiscountPremium().negate());
        }
        if(bean.getSurrenderAmount()!=null) {
            bean.setSurrenderAmount(bean.getSurrenderAmount().negate());
        }
        bean.setCostAmount(old.getCostAmount().negate());
        bean.setGrantAmount(old.getGrantAmount().negate());

        //correction_flag = 1的标准保费都设置为0
        bean.setBusinessDiscountPremium(BigDecimal.ZERO);
        return bean;
    }

    protected SettlementCostInfoEntity builderLongSurrenderCostInfoByOldCost(SettlementEventJobEntity eventJob,
                                                                             SettlementEventTypeEnum eventType, EpContractInfoVo policyInfo,
                                                                             SettlementCostInfoEntity old, PolicyPreservationDetailDto preservationDetail) {
        SettlementCostInfoEntity bean = new SettlementCostInfoEntity();
        BeanUtils.copyProperties(old, bean);
        bean.setId(null);
        bean.setCostCode(PolicySettlementUtils.createCodeLastNumber("CC"));
        //记账时间处理
        bean.setSettlementTime(new Date());
        bean.setSettlementDate(bean.getSettlementTime());
        //事件编号
        bean.setEventSourceCode(eventJob.getPushEventCode());
        //事件信息
        if (Objects.nonNull(eventType)) {
            bean.setSettlementEventCode(eventType.getEventCode());
            bean.setSettlementEventDesc(eventType.getEventDesc());
            bean.setInitialEventCode(eventType.getEventCode());
            setCommissionTypeByEventType(eventType, bean);
        }

        bean.setSettlementGenerateType(1);

        //清除确认信息
        cleanConfirmInfo(bean);
        bean.setBusinessPremium(old.getBusinessPremium().negate());
        if(old.getDiscountPremium()!=null) {
            bean.setDiscountPremium(old.getDiscountPremium().negate());
        }

        bean.setBusinessAccountTime(getBusinessAccountTime(eventType,policyInfo,preservationDetail));
        //退保保费
        log.info("合同{}长险渠道:{}",old.getContractCode(),preservationDetail.getSellChannelCode());
        if (Objects.equals(preservationDetail.getSellChannelCode(), ZHNX_CHANNEL_CODE)) {
            //标准退保、当前期数大于1，回访成功的情况下就不扣钱
            boolean renewal2Surrender= Objects.equals(eventType.getEventCode(), STANDARD_SURRENDER.getEventCode())
                    && preservationDetail.getRenewalTermPeriod() > 1
                    && Objects.equals(policyInfo.getContractExtendInfo().getRevisitResult(), 1);
            log.info("合同{}当前退保类型：{},当前期数：{},回访结果：{},是否扣费：{}",old.getContractCode(),eventType.getEventCode(),preservationDetail.getRenewalTermPeriod(),policyInfo.getContractExtendInfo().getRevisitResult(),!renewal2Surrender);
            //标准退保、缴费类型为
            Optional<EpPersonalProductInfoVo> opt =  policyInfo.getInsuredInfoList().stream().flatMap(x -> x.getProductInfoList().stream()).filter(o->Objects.equals(o.getMainInsurance(),1)).findFirst();
            //长险趸交退保规则-过来犹豫期且回访成功退保，不追推广费。
            boolean onlyOneTime = Objects.equals(eventType.getEventCode(), STANDARD_SURRENDER.getEventCode())
                    && (opt.isPresent() && Objects.equals(opt.get().getPeriodType(), PolicyPaymentTypeEnum.DJ.getCode()))
                    && (policyInfo.getContractBaseInfo().getOrderTime().before(DateUtil.parseDateTime(SINGLE_PAYMENT_SPLIT_TIME))
                    ||  (!policyInfo.getContractBaseInfo().getOrderTime().before(DateUtil.parseDateTime(SINGLE_PAYMENT_SPLIT_TIME))
                    && Objects.equals(policyInfo.getContractExtendInfo().getRevisitResult(), 1)
                    //且 交单时间满 12 月后退保，注意一定要先设置当前记录的业务记账日期
                    && DateUtil.offsetMonth(DateUtil.beginOfDay(policyInfo.getContractBaseInfo().getOrderTime()),12).isBefore(bean.getBusinessAccountTime())
                    )
            );
            log.info("合同{}当前退保类型：{},缴费方式：{},回访结果：{},是否不追推广费：{}",old.getContractCode(),eventType.getEventCode(),opt.get().getPeriodType(),policyInfo.getContractExtendInfo().getRevisitResult(),!onlyOneTime);
            if (renewal2Surrender || onlyOneTime) {
                bean.setPremium(BigDecimal.ZERO);
                bean.setCostAmount(BigDecimal.ZERO);
                bean.setGrantAmount(BigDecimal.ZERO);
            } else {
                bean.setPremium(old.getPremium().negate());
                bean.setCostAmount(old.getCostAmount().negate());
                bean.setGrantAmount(old.getGrantAmount().negate());

            }
        } else {
            if (LIST_CITY_ZERO_PREMIUM_EVENT.contains(eventType.getEventCode())) {
                bean.setPremium(BigDecimal.ZERO);
                bean.setCostAmount(BigDecimal.ZERO);
                bean.setGrantAmount(BigDecimal.ZERO);
            } else {
                bean.setPremium(old.getPremium().negate());
                bean.setCostAmount(old.getCostAmount().negate());
                bean.setGrantAmount(old.getGrantAmount().negate());
            }
        }

        return bean;
    }






    public Date getGroupAddOrSubtractBusinessAccountTime(PolicyPreservationDetailDto preservationDetailDto ,Integer surrendered){
        if(Objects.equals(preservationDetailDto.getSellChannelCode(),ZHNX_CHANNEL_CODE)){
            return getCorrectionBusinessAccountTime(preservationDetailDto.getInputTime());
        }

        if(Objects.equals(surrendered,1) && preservationDetailDto.getPreservationSubtractEffectTime()!=null){
            return preservationDetailDto.getPreservationSubtractEffectTime();
        }else{
            return preservationDetailDto.getPreservationEffectTime();
        }
    }


//    /**
//     *
//     * @param eventType
//     * @param policyInfo
//     * @param obj
//     * @return
//     */
//    public Date getBusinessAccountTime(SettlementEventTypeEnum eventType,EpContractInfoVo policyInfo, Object obj) {
//
//        if (eventType == null) {
//            return new Date();
//        }
//        switch (eventType) {
//            case PERSONAL_NEW_POLICY:
//            case GROUP_NEW_POLICY:
//            case RENEWAL_POLICY:{
//                if(LIST_MANUAL_IMPORT.contains(policyInfo.getPolicySource())){
//                    //todo 下线导入的单子需要取保单创建时间
//                    return getCorrectionBusinessAccountTime(policyInfo.getContractBaseInfo().getOrderTime());
//                }else {
//
//                    //return Objects.equals(policyInfo.getChannelInfo().getChannelCode(), Constant.ZHNX_CHANNEL_CODE) ? policyInfo.getContractExtendInfo().getApprovedTime() : policyInfo.getContractExtendInfo().getOrderTime();
//                    //新契约-交单时间，对应农保系统的新契约同步进系统的时间（应该即支付时间）；
//                    return getCorrectionBusinessAccountTime(policyInfo.getContractExtendInfo().getOrderTime());
//                }
//            }
//            case RENEWAL_TERM_POLICY: {
//                PolicyRenewalTermDto termDto = (PolicyRenewalTermDto) obj;
//                Date date = termDto.getPaymentSubmitTime();
//                Date paymentTime = termDto.getPaymentTime();
//                //临时解决2024-03-26 14:59:53历史 报文中没有getPaymentSubmitTime的数据
//                if(date == null){
//                    if(paymentTime.before(DateUtil.parseDateTime("2024-03-26 14:59:53"))){
//                        date = paymentTime;
//                    }else {
//                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
//                                "支出端-长险续期PaymentSubmitTime为空，保单号=" + termDto.getPolicyNo() + "期数=" + termDto.getPeriod()));
//                    }
//                }
//                return getCorrectionBusinessAccountTime(Objects.equals(policyInfo.getChannelInfo().getChannelCode(), Constant.ZHNX_CHANNEL_CODE)? date:termDto.getPaymentTime());
//
//            }
//            case STANDARD_SURRENDER:
//            case HESITATE_SURRENDER:
//            case PROTOCOL_TERMINATION:
//            case GROUP_ADD_OR_SUBTRACT:
//            case VEHICLE_PREMIUM_INFO_CHANGE:
//            case TERMINATION_PRODUCT:{
//
//                    PolicyPreservationDetailDto preservationDetailDto = (PolicyPreservationDetailDto)obj;
//                    log.info("保全{}记录的入库时间为{}", preservationDetailDto.getPreservationCode(),
//                        preservationDetailDto.getInputTime());
//                    if(preservationDetailDto.getInputTime()==null){
//                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
//                                "保全{}记录的入库时间为空,保全编号=" + preservationDetailDto.getPreservationCode()));
//                    }
//                    //return preservationDetailDto.getPreservationEffectTime();
//                    return getCorrectionBusinessAccountTime(Objects.equals(policyInfo.getChannelInfo().getChannelCode(), Constant.ZHNX_CHANNEL_CODE)?
//                            preservationDetailDto.getInputTime():preservationDetailDto.getPreservationEffectTime());
//                }
//
//            default:
//                return new Date();
//        }
//    }

    public static Date getCorrectionBusinessAccountTime(Date oldBusinessAccountTime,Date newBusinessAccountTime){
        if(newBusinessAccountTime == null){
            return getCorrectionBusinessAccountTime(oldBusinessAccountTime);
        }
        if(oldBusinessAccountTime.after(newBusinessAccountTime)){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("冲正源数据的业务记账日期{}晚于冲正后业务记账时间{}",oldBusinessAccountTime,newBusinessAccountTime)));
        }
        if(DateUtil.isSameMonth(newBusinessAccountTime,oldBusinessAccountTime)){
            return oldBusinessAccountTime;
        }

        Date thisMonth = DateUtil.beginOfMonth(newBusinessAccountTime);
        Date oldMonth = DateUtil.beginOfMonth(oldBusinessAccountTime);
        if(DateUtil.betweenMonth(oldMonth,thisMonth,false)==1){
            Date settlementDate = DateUtil.offsetDay(thisMonth,autoCostSettlementDay-1);
            if(newBusinessAccountTime.after(settlementDate)){
                return newBusinessAccountTime;
            }else{
                return oldBusinessAccountTime;
            }
        }else{
            return newBusinessAccountTime;
        }
    }

//    public static Date getCorrectionBusinessAccountTime(Date oldBusinessAccountTime){
//        if(oldBusinessAccountTime == null){
//            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("冲正源数据的业务记账日期为空")));
//        }
//
//        Date now = DateUtil.date();
//        if(oldBusinessAccountTime.after(now)){
//            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("冲正源数据的业务记账日期{}晚于当前时间{}",oldBusinessAccountTime,now)));
//        }
//        if(DateUtil.isSameMonth(now,oldBusinessAccountTime)){
//            return oldBusinessAccountTime;
//        }
//        Date thisMonth = DateUtil.beginOfMonth(now);
//        Date oldMonth = DateUtil.beginOfMonth(oldBusinessAccountTime);
//        if(DateUtil.betweenMonth(oldMonth,thisMonth,false)==1){
//            Date settlementDate = DateUtil.offsetDay(thisMonth,autoCostSettlementDay-1);
//            if(now.after(settlementDate)){
//                return now;
//            }else{
//                return oldBusinessAccountTime;
//            }
//        }else{
//            return now;
//        }
//
//    }



    public static Date test(Date oldBusinessAccountTime){
        if(oldBusinessAccountTime == null){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("冲正源数据的业务记账日期为空")));
        }

        Date now = DateUtil.date();
        if(oldBusinessAccountTime.after(now)){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("冲正源数据的业务记账日期{}晚于当前时间{}",oldBusinessAccountTime,now)));
        }
        if(DateUtil.isSameMonth(now,oldBusinessAccountTime)){
            return oldBusinessAccountTime;
        }
        Date thisMonth = DateUtil.beginOfMonth(now);
        Date oldMonth = DateUtil.beginOfMonth(oldBusinessAccountTime);
        if(DateUtil.betweenMonth(oldMonth,thisMonth,false)==1){
            Date settlementDate = DateUtil.offsetDay(thisMonth,(5-1));
            System.out.println("settlementDate="+settlementDate);
            if(now.after(settlementDate)){
                return now;
            }else{
                return oldBusinessAccountTime;
            }
        }else{
            return now;
        }
    }

    public static void  main(String[] args){

        System.out.println(test(DateUtil.parseDate("2023-12-05")));
        System.out.println(test(DateUtil.parseDate("2023-11-04")));
        System.out.println(test(DateUtil.parseDate("2023-11-06")));
        System.out.println(test(DateUtil.parseDate("2023-10-06")));
    }

    public static EpContractInfoVo findFirstPolicy(String sourcePolicyNo, int n) {

        if (n > 12) {
            log.warn(StrUtil.format("保单号查询已到最大层级深度-{}", sourcePolicyNo));
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("保单号查询已到最大层级深度-{}", sourcePolicyNo)));
        }


        EpContractInfoVo contractInfoVo = SpringContextUtils.getBean("policyCenterBaseClient", PolicyCenterBaseClient.class).getPolicyInfoByPolicyCode(sourcePolicyNo);

        if (Objects.isNull(contractInfoVo)) {
            log.warn(StrUtil.format("未查到原保单号详情信息-{}", sourcePolicyNo));
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("未查到原保单号详情信息-{}", sourcePolicyNo)));
        }

        if (StrUtil.isEmpty(contractInfoVo.getSourcePolicyNo())) {
            return contractInfoVo;
        }

        if (StrUtil.isNotEmpty(contractInfoVo.getSourcePolicyNo())) {
            return findFirstPolicy(contractInfoVo.getSourcePolicyNo(), n+1);
        }

        return null;
    }



}
