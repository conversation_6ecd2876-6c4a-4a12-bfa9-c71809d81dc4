package com.mpolicy.settlement.core.service.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.policy.client.EpPolicyClient;
import com.mpolicy.policy.client.PolicyGovernClient;
import com.mpolicy.policy.client.PolicyRenewalTermClient;
import com.mpolicy.policy.client.standard.StandardPremiumClient;
import com.mpolicy.policy.common.enums.EpPolicyDetailsReqOperationTypeEnum;
import com.mpolicy.policy.common.enums.EpPolicyDetailsReqScopeEnum;
import com.mpolicy.policy.common.ep.policy.EpContractBaseInfoVo;
import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.policy.common.ep.policy.event.settlement.PolicyCorrectedVo;
import com.mpolicy.policy.common.ep.policy.preserve.EpPreservationV2Vo;
import com.mpolicy.policy.common.ep.policy.qry.details.EpPolicyDetailsReqVo;
import com.mpolicy.policy.common.ep.policy.qry.details.EpPolicyDetailsResVo;
import com.mpolicy.policy.common.ep.policy.qry.details.GroupPreservationInsuredVo;
import com.mpolicy.policy.common.ep.policy.qry.list.EpPolicyInsuredListResVo;
import com.mpolicy.policy.common.ep.policy.referrer.GroupPolicyRecommenderChangeVo;
import com.mpolicy.policy.common.ep.policy.referrer.PolicyRecommenderChangeVo;
import com.mpolicy.policy.common.ep.policy.referrer.PolicyRenewalTermRecommenderChangeVo;
import com.mpolicy.policy.common.policy.renewal.response.PolicyRenewalTermOutput;
import com.mpolicy.policy.common.policy.vo.base.MainProductInfoVo;
import com.mpolicy.policy.common.standard.PolicyStandardPremiumVo;
import com.mpolicy.policy.common.standard.PreservationStandardPremiumVo;
import com.mpolicy.policy.common.standard.RenewalTermStandardPremiumVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 保单中心基础feign服务
 *
 * <AUTHOR>
 * @since 2023-05-22 12:03
 */
@Component
@Slf4j
public class PolicyCenterBaseClient {

    private static Integer ERROR_CODE_40010 = 40010;
    private static Integer MAX_QUERY_INSURED_SIZE = 1500;

    @Autowired
    private EpPolicyClient policyClient;

    @Autowired
    private PolicyGovernClient policyGovernClient;

    @Autowired
    private PolicyRenewalTermClient policyRenewalTermClient;

    @Autowired
    protected StandardPremiumClient standardPremiumClient;

    /**
     * 刷数业务回执确认
     *
     * @param governApplyCode 申请业务编号
     * @param confirmServer   确认服务
     * @param confirmModel    确认服务模块
     * <AUTHOR>
     * @since 2024/2/5
     */
    public String governApplyConfirm(String governApplyCode, String confirmServer, String confirmModel) {
        Result<String> result = policyGovernClient.governApplyConfirm(governApplyCode, confirmServer, confirmModel);
        if (!result.isSuccess()) {
            log.warn("刷数业务回执确认，申请业务编号={} msg={}", governApplyCode, JSON.toJSONString(result));
            throw new GlobalException(result);
        }
        return result.getData();
    }

    /**
     * 根据合同号获取保单详情
     *
     * @param contractCode 保单合同号
     * @return com.mpolicy.policy.common.ep.policy.EpContractInfoVo
     * <AUTHOR>
     * @since 2023/5/23 09:26
     */
    public EpContractInfoVo getPolicyInfoByContractCode(String contractCode) {
        Result<EpContractInfoVo> result = policyClient.queryPolicyContractDetailInfo(contractCode);
        if (!result.isSuccess()) {
            log.warn("结算服务获取保单详情失败，请求合同号={} msg={}", contractCode, JSON.toJSONString(result));
            throw new GlobalException(result);
        }
        EpContractInfoVo contractInfo = result.getData();
        // 保单不存在
        if (contractInfo == null) {
            throw new GlobalException(
                BasicCodeMsg.SERVER_ERROR.setMsg("团险新契约获取保单详情失败，保单合同号=" + contractCode));
        }
        if (CollUtil.isNotEmpty(contractInfo.getInsuredInfoList())) {
            contractInfo.getInsuredInfoList().forEach(insured -> {
                // 出生日期为空,那么读取身份证上的日期和年龄
                if (insured.getInsuredBirthday() == null && StrUtil.isNotBlank(
                    insured.getInsuredIdCard()) && IdcardUtil.isValidCard(insured.getInsuredIdCard())) {
                    insured.setInsuredBirthday(IdcardUtil.getBirthDate(insured.getInsuredIdCard()));
                }
            });
        }
        //处理一下被保人的出生日期
        // 处理一下期次信息
        EpContractBaseInfoVo contractBaseInfo = contractInfo.getContractBaseInfo();
        if (contractBaseInfo.getRenewalPeriod() == null) {
            contractBaseInfo.setRenewalPeriod(1);
        }
        return contractInfo;
    }

    /**
     * 根据保单号获取保单详情
     *
     * @param policyCode 保单号
     * @return com.mpolicy.policy.common.ep.policy.EpContractInfoVo
     * <AUTHOR>
     * @since 2023/5/23 09:26
     */
    public EpContractInfoVo getPolicyInfoByPolicyCode(String policyCode) {
        return getPolicyInfoByPolicyCode(policyCode, true);
    }

    /**
     * 根据保单号获取保单详情
     *
     * @param policyCode 保单号
     * @param throwEx    出现异常是否抛出异常
     * @return com.mpolicy.policy.common.ep.policy.EpContractInfoVo
     * <AUTHOR>
     * @since 2023/5/23 09:26
     */
    public EpContractInfoVo getPolicyInfoByPolicyCode(String policyCode, boolean throwEx) {
        Result<EpContractInfoVo> result = policyClient.queryPolicyContractDetailInfoByPolicyNo(policyCode);
        if (!result.isSuccess()) {
            log.warn("结算服务获取保单详情失败，请求保单号={} msg={}", policyCode, JSON.toJSONString(result));
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        // 保单不存在
        if (result.getData() == null) {
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        EpContractInfoVo res = result.getData();
        // 如果是线下单，必须存在代理人信息
        if (res.getContractBaseInfo().getSalesType() != null && res.getContractBaseInfo().getSalesType() == 1) {
            if (res.getChannelDistributionCode().toLowerCase().startsWith("xj") && res.getAgentInfoList().isEmpty()) {
                if (throwEx) {
                    log.info("保单号{}为线下单,如果是小鲸渠道，需要校验代理人信息",policyCode);
                    throw new GlobalException(
                        BasicCodeMsg.SERVER_ERROR.setMsg("保单为线下单,未获取到保单代理人信息，保单号=" + policyCode));
                }
                return res;
            }
        }
        // 处理一下期次信息
        EpContractBaseInfoVo contractBaseInfo = res.getContractBaseInfo();
        if (contractBaseInfo.getRenewalPeriod() == null) {
            contractBaseInfo.setRenewalPeriod(1);
        }
        return res;
    }

    public static void main(String[] args) {
        System.out.println("xJssss".toLowerCase().startsWith("xj"));
    }

    /**
     * 获取团险新契约被保人列表
     *
     * @param contractCode
     * @return
     */
    public List<GroupPreservationInsuredVo> listGroupNewInsured(String contractCode) {
        EpPolicyDetailsReqVo req = new EpPolicyDetailsReqVo();
        req.setContractCode(contractCode);
        Set set = Sets.newHashSet();
        set.add(EpPolicyDetailsReqScopeEnum.GROUP_PRESERVATION_INSUREDS.getCode());
        req.setScope(set);
        req.setOperationType(Sets.newHashSet(Arrays.asList(EpPolicyDetailsReqOperationTypeEnum.NEW.getCode())));
        return listInsured(req);
    }

    /**
     * 获取团险增减员被保人列表
     *
     * @param contractCode
     * @return
     */
    public List<GroupPreservationInsuredVo> listGroupPreservationInsured(String contractCode, String preservationCode) {
        EpPolicyDetailsReqVo req = new EpPolicyDetailsReqVo();
        req.setContractCode(contractCode);
        req.setPreservationCode(preservationCode);
        Set set = Sets.newHashSet();
        set.add(EpPolicyDetailsReqScopeEnum.GROUP_PRESERVATION_INSUREDS.getCode());
        req.setScope(set);

        return listInsured(req);
    }

    /**
     * 获取团险在保的被保人列表
     *
     * @param contractCode
     * @return
     */
    public List<GroupPreservationInsuredVo> listGroupAll(String contractCode) {
        EpPolicyDetailsReqVo req = new EpPolicyDetailsReqVo();
        req.setContractCode(contractCode);
        Set set = Sets.newHashSet();
        set.add(EpPolicyDetailsReqScopeEnum.GROUP_PRESERVATION_INSUREDS.getCode());
        req.setScope(set);
        req.setInsuredIsProtect(Boolean.TRUE);
        return listInsured(req);
    }

    public List<GroupPreservationInsuredVo> listInsuredDetailByInsuredCodes(EpPolicyDetailsReqVo req,
        List<String> insuredCodes) {
        if (Objects.isNull(req)) {
            throw new GlobalException(
                BasicCodeMsg.SERVER_ERROR.setMsg("结算服务获取被保人列表失败，合同号=" + req.getContractCode()));
        }
        req.setInsuredCodes(insuredCodes);
        Result<EpPolicyDetailsResVo> result = policyClient.queryEpPolicyDetailsExpand(req);
        if (!result.isSuccess()) {
            log.warn("结算服务获取被保人列表失败，请求合同号={},入参={}, msg={}", req.getContractCode(), req,
                JSON.toJSONString(result));
            throw new GlobalException(result);
        }
        // 保单不存在
        if (result.getData() == null) {
            throw new GlobalException(
                BasicCodeMsg.SERVER_ERROR.setMsg("结算服务获取被保人列表失败，合同号=" + req.getContractCode()));
        }

        return result.getData().getGroupPreservationInsureds();
    }

    private List<GroupPreservationInsuredVo> listInsured(EpPolicyDetailsReqVo req) {
        try {
            return listInsuredDetail(req);
        } catch (GlobalException e) {

            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(e.getMsg()));
        }
    }

    private List<GroupPreservationInsuredVo> listInsuredDetail(EpPolicyDetailsReqVo req) {
        Result<EpPolicyDetailsResVo> result = policyClient.queryEpPolicyDetailsExpand(req);
        if (!result.isSuccess()) {
            if (Objects.equals(result.getCode(), ERROR_CODE_40010)) {
                return listMultiQueryInsured(req);
            }
            log.warn("结算服务获取被保人列表失败，请求合同号={},入参={}, msg={}", req.getContractCode(), req,
                JSON.toJSONString(result));
            throw new GlobalException(result);
        }
        // 保单不存在
        if (result.getData() == null) {
            throw new GlobalException(
                BasicCodeMsg.SERVER_ERROR.setMsg("结算服务获取被保人列表失败，合同号=" + req.getContractCode()));
        }

        return result.getData().getGroupPreservationInsureds();
    }

    public List<GroupPreservationInsuredVo> listMultiQueryInsured(EpPolicyDetailsReqVo req) {
        //这个地方有个风险，返回的编码
        List<String> totalInsuredCodes = listInsuredCodes(req);

        List<GroupPreservationInsuredVo> ret = Lists.newArrayList();
        int toIndex = MAX_QUERY_INSURED_SIZE;
        int size = totalInsuredCodes.size();
        log.info("支出端-获取被保人总数：{}", size);
        for (int i = 0; i < size; i += toIndex) {
            if (i + toIndex > size) {
                toIndex = size - i;

            }
            log.info("支出端-获取被保人详情-当前下标为{},{}的被保人详情。", i, i + toIndex);
            ret.addAll(listInsuredDetailByInsuredCodes(req, totalInsuredCodes.subList(i, i + toIndex)));
        }

        return ret;
    }

    /**
     * 获取被保人编码
     *
     * @param req
     * @return
     */
    public List<String> listInsuredCodes(EpPolicyDetailsReqVo req) {
        Result<EpPolicyInsuredListResVo> result = policyClient.queryInsuredListExpand(req);
        if (!result.isSuccess()) {

            log.warn("结算服务获取被保人编码列表，请求合同号={},入参={}, msg={}", req.getContractCode(), req,
                JSON.toJSONString(result));
            throw new GlobalException(result);
        }
        // 保单不存在
        if (result.getData() == null) {
            throw new GlobalException(
                BasicCodeMsg.SERVER_ERROR.setMsg("结算服务获取被保人编码列表失败，合同号=" + req.getContractCode()));
        }

        return result.getData().getInsuredCodes();
    }

    /**
     * 获取保全明细，目前只有退保明细和附加险解约明细
     *
     * @param preservationCode
     * @return
     */
    public EpPreservationV2Vo getPreservationDetail(String preservationCode) {

        Result<EpPreservationV2Vo> result = policyClient.preservationDetail(preservationCode);
        if (!result.isSuccess()) {
            log.warn("结算服务获取保全明细记录，请求保全编号={},入参={}, msg={}", preservationCode,
                JSON.toJSONString(result));
            throw new GlobalException(result);
        }
        EpPreservationV2Vo data = result.getData();
        //保全不存在
        if (data == null) {
            throw new GlobalException(
                BasicCodeMsg.SERVER_ERROR.setMsg("结算服务获取保全明细记录，请求保全编号=" + preservationCode));
        }
        // 处理一下续投的期次信息 原保单号不是空 同时期次为空或者小于1的时候将值设置成2
        if (StrUtil.isNotBlank(
            data.getSourcePolicyNo()) && (data.getRenewalPeriod() == null || data.getRenewalPeriod() < 2)) {
            data.setRenewalPeriod(2);
        }
        log.info("保全编码={},保全详情信息={}",preservationCode,JSONUtil.toJsonStr(data));
        return data;
    }

    /**
     * 获取保单变更流水记录
     * @param flowId 流水id
     * @param contractCode 合同号
     * @return 结果数据
     */
    public PolicyCorrectedVo queryPolicyCorrectedInfo(String flowId,String contractCode) {

        Result<PolicyCorrectedVo> result = policyClient.queryPolicyCorrectedInfo(flowId, contractCode);
        if (!result.isSuccess()) {
            log.warn("结算服务获取保单变更流水记录，请求流水id={},入参={}, msg={}", flowId,contractCode,
                JSON.toJSONString(result));
            throw new GlobalException(result);
        }
        PolicyCorrectedVo data = result.getData();
        //保全不存在
        if (data == null) {
            throw new GlobalException(
                BasicCodeMsg.SERVER_ERROR.setMsg("结算服务未获取保单变更流水记录，请求流水id=" + flowId));
        }
        log.info("流水ID={},修改详情信息={}",flowId,JSONUtil.toJsonStr(data));
        return data;
    }

    /**
     * 获取续期详情
     *
     * @param policyNo 保单号
     * @param period   期次
     * @return
     */
    public PolicyRenewalTermOutput getPolicyRenewalTermDetail(String policyNo, Integer period) {
        Result<PolicyRenewalTermOutput> result = policyRenewalTermClient.policyRenewalTermDetail(policyNo, period);
        if (!result.isSuccess()) {
            log.warn("结算服务获取续期记录异常，请求保单号={},期次={}, 异常信息msg={}", policyNo, period,
                result.getMsg());
            throw new GlobalException(result);
        }
        //续期详情不存在
        if (result.getData() == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                "结算服务获取续期详情失败，请求保单号=" + policyNo + "续期期次=" + period));
        }
        return result.getData();
    }

    /**
     * 获取长险断保明细
     *
     * @param preservationCode
     * @return
     */
   /* public EpPreservationV2Vo getPolicyInterruptDetail(String preservationCode) {
        Result<EpPreservationV2Vo> result = policyClient.preservationDetail(preservationCode);
        if (!result.isSuccess()) {
            log.warn("结算服务获取保全明细记录，请求保全编号={},入参={}, msg={}", preservationCode, JSON.toJSONString(result));
            throw new GlobalException(result);
        }
        //保全不存在
        if (result.getData() == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("结算服务获取保全明细记录，请求保全编号=" + preservationCode));
        }

        return result.getData();
    }*/

    /**
     * 根据保单号获取主险编码
     *
     * @param policyNos 保单号集合限制最大条数1000
     */
    public List<MainProductInfoVo> findMainProductInfoListByPolicyNos(List<String> policyNos) {
        Result<List<MainProductInfoVo>> result = policyClient.findMainProductInfoListByPolicyNos(policyNos);
        if (!result.isSuccess()) {
            log.warn("保单号集合获取主险信息异常，请求保单号集合={},msg={}", JSONUtil.toJsonStr(policyNos),
                result.getMsg());
            throw new GlobalException(result);
        }
        return result.getData();
    }

    /**
     * 查询保单被保人初始渠道推荐人变更数据
     * @param contractCode
     * @param flowId
     * @return
     */
    public GroupPolicyRecommenderChangeVo queryPolicyInsuredRecommenderChange(String contractCode,String flowId) {
        Result<GroupPolicyRecommenderChangeVo> result = policyClient.policyInsuredRecommenderChange(flowId,contractCode);
        if (!result.isSuccess()) {
            log.warn("查询保单被保人初始渠道推荐人变更数据异常，请求合同号={},flowId={},msg={}", contractCode,flowId,
                    result.getMsg());
            throw new GlobalException(result);
        }
        return result.getData();
    }

    /**
     * 查询保单初始渠道推荐人变更数据
     * @param contractCode
     * @param flowId
     * @return
     */
    public PolicyRecommenderChangeVo queryPolicyRecommenderChange(String contractCode,String flowId) {
        Result<PolicyRecommenderChangeVo> result = policyClient.policyRecommenderChange(flowId,contractCode);
        if (!result.isSuccess()) {
            log.warn("查询保单初始渠道推荐人变更数据异常，请求合同号={},flowId={},msg={}", contractCode,flowId,
                    result.getMsg());
            throw new GlobalException(result);
        }
        return result.getData();
    }

    /**
     * 查询保单初始渠道推荐人变更数据
     * @param contractCode
     * @param flowId
     * @return
     */
    public PolicyRenewalTermRecommenderChangeVo queryPolicyRenewalTermRecommenderChangeVo(String contractCode,Integer period,String flowId) {
        Result<PolicyRenewalTermRecommenderChangeVo> result = policyClient.policyRenewalTermRecommenderChange(flowId,period,contractCode);
        if (!result.isSuccess()) {
            log.warn("查询保单初始渠道推荐人变更数据异常，请求合同号={},flowId={},msg={}", contractCode,flowId,
                    result.getMsg());
            throw new GlobalException(result);
        }
        return result.getData();
    }

    /**
     * 根据合同编号获取新契约标准保费
     *
     * @param contractCode 合同编号
     */
    public PolicyStandardPremiumVo queryPolicyStandardPremium(String contractCode) {
        Result<PolicyStandardPremiumVo> result = standardPremiumClient.queryPolicyStandardPremium(contractCode);
        if (!result.isSuccess()) {
            log.warn("根据合同编号获取新契约标准保费，请求合同编号={},msg={}", contractCode,result.getMsg());
            throw new GlobalException(result);
        }
        return result.getData();
    }

    /**
     * 根据合同编号、期数获取续期标准保费
     * @param contractCode
     * @param period
     * @return
     */
    public RenewalTermStandardPremiumVo queryRenewalTermStandardPremium(String contractCode,Integer period) {
        Result<RenewalTermStandardPremiumVo> result = standardPremiumClient.queryRenewalTermStandardPremium(contractCode,period);
        if (!result.isSuccess()) {
            log.warn("根据合同编号、期数获取续期标准保费，请求合同编号={},期数={},msg={}", contractCode,period,result.getMsg());
            throw new GlobalException(result);
        }
        return result.getData();
    }

    /**
     * 根据保全编号获取保全标准保费
     * @param preservationCode
     * @return
     */
    public PreservationStandardPremiumVo queryCorrectedStandardPremium(String preservationCode) {
        Result<PreservationStandardPremiumVo> result = standardPremiumClient.queryCorrectedStandardPremium(preservationCode);
        if (!result.isSuccess()) {
            log.warn("根据保全编号获取保全标准保费，请求保全编号={},msg={}", preservationCode,result.getMsg());
            throw new GlobalException(result);
        }
        return result.getData();
    }
}

