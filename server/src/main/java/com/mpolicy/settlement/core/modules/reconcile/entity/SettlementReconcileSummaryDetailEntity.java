package com.mpolicy.settlement.core.modules.reconcile.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;

/**
 * 保司结算对账单汇总明细
 * 
 * <AUTHOR>
 * @date 2023-05-21 22:28:34
 */
@TableName("settlement_reconcile_summary_detail")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementReconcileSummaryDetailEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 结算汇总编号
	 */
	private String summaryCode;
	/**
	 * 对账保单业务汇总编号
	 */
	private String billCode;
	/**
	 * 对账唯一单号
	 */
	private String reconcileCode;
	/**
	 * 对账单名称
	 */
	private String reconcileName;
	/**
	 * 对账月度
	 */
	private String reconcileMonth;
	/**
	 * 保险公司编码
	 */
	private String companyCode;
	/**
	 * 保险公司名称
	 */
	private String companyName;
	/**
	 * 投保单号
	 */
	private String applicantPolicyNo;
	/**
	 * 保单号
	 */
	private String policyNo;
	/**
	 * 科目编码
	 */
	private String reconcileSubjectCode;
	/**
	 * 科目名称
	 */
	private String reconcileSubjectName;
	/**
	 * 险种编码
	 */
	private String productCode;
	/**
	 * 险种名称
	 */
	private String productName;
	/**
	 * 协议产品编码
	 */
	private String protocolProductCode;
	/**
	 * 协议产品名称
	 */
	private String protocolProductName;
	/**
	 * 续期年期
	 */
	private Integer renewalYear;
	/**
	 * 续期期次
	 */
	private Integer renewalPeriod;
	/**
	 * 保额
	 */
	private BigDecimal coverage;
	/**
	 * 保额单位;保额单位，0：元，1：份，2：元/天
	 */
	private Integer coverageUnit;
	/**
	 * 保额单位名称
	 */
	private String coverageUnitName;
	/**
	 * 保障期间类型
	 */
	private String insuredPeriodType;
	/**
	 * 保障时长
	 */
	private Integer insuredPeriod;
	/**
	 * 缴费方式
	 */
	private String periodType;
	/**
	 * 缴费期间类型
	 */
	private String paymentPeriodType;
	/**
	 * 缴费时长
	 */
	private Integer paymentPeriod;
	/**
	 * 结算费率
	 */
	private String settlementRate;
	/**
	 * 小鲸手续费金额
	 */
	private BigDecimal settlementAmount;
	/**
	 * 对账数据来源;0保单明细-1小鲸导入
	 */
	private Integer billSource;
	/**
	 * 是否删除;0有效-1删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
@TableField(fill = FieldFill.INSERT)
	private Integer revision;
}
