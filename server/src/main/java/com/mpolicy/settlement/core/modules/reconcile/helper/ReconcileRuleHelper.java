package com.mpolicy.settlement.core.modules.reconcile.helper;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileSubjectOnlineEnum;
import com.mpolicy.settlement.core.modules.contract.helper.ContractBaseHelper;
import com.mpolicy.settlement.core.modules.protocol.dto.ProtocolInsuranceProductInfo;
import com.mpolicy.settlement.core.modules.protocol.helper.ProtocolBaseHelper;
import com.mpolicy.settlement.core.modules.protocol.service.ProtocolInsuranceProductDetailService;
import com.mpolicy.settlement.core.modules.protocol.service.ProtocolInsuranceProductService;
import com.mpolicy.settlement.core.modules.reconcile.dto.rule.ReconcileCompanyInfo;
import com.mpolicy.settlement.core.modules.reconcile.dto.rule.ReconcileCompanyRuleInfo;
import com.mpolicy.settlement.core.modules.reconcile.dto.rule.ReconcileCompanySubjectDetail;
import com.mpolicy.settlement.core.modules.reconcile.entity.*;
import com.mpolicy.settlement.core.modules.reconcile.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 协议管理规则支持服务
 *
 * <AUTHOR>
 * @since 2023/05/20
 */

@Slf4j
@Component
public class ReconcileRuleHelper {

    public static ReconcileRuleHelper reconcileRuleHelper;
    public final static int EDN_MONTH_DAY = 99;
    private final static List<String> SERVICE_FEE_LIST = Arrays.asList(
            ReconcileSubjectOnlineEnum.TECHNICAL_SERVICE_FEE.getCode(),
            ReconcileSubjectOnlineEnum.CONSULTING_SERVICE_FEE.getCode(),
            ReconcileSubjectOnlineEnum.PROMOTION_SERVICE_FEE.getCode(),
            ReconcileSubjectOnlineEnum.EXTENSION_SERVICE_FEE.getCode(),
            ReconcileSubjectOnlineEnum.MODERN_SERVICE_FEE.getCode());

    @Autowired
    IRedisService redisService;

    @Autowired
    SettlementReconcileCompanySubjectService settlementReconcileCompanySubjectService;

    @Autowired
    SettlementReconcileContractInfoService settlementReconcileContractInfoService;

    @Autowired
    SettlementReconcileCompanyInfoService settlementReconcileCompanyInfoService;

    @Autowired
    SettlementReconcileCompanySubjectProductService settlementReconcileCompanySubjectProductService;

    @Autowired
    SettlementReconcileCompanySubjectPolicyMethodService settlementReconcileCompanySubjectPolicyMethodService;

    @Autowired
    ProtocolInsuranceProductDetailService protocolInsuranceProductDetailService;

    @Autowired
    ProtocolInsuranceProductService protocolInsuranceProductService;

    @PostConstruct
    public void init() {
        reconcileRuleHelper = this;
        // redis服务
        reconcileRuleHelper.redisService = this.redisService;
        reconcileRuleHelper.settlementReconcileCompanySubjectService = this.settlementReconcileCompanySubjectService;
        reconcileRuleHelper.settlementReconcileCompanyInfoService = this.settlementReconcileCompanyInfoService;
        reconcileRuleHelper.settlementReconcileCompanySubjectProductService =
                this.settlementReconcileCompanySubjectProductService;
        reconcileRuleHelper.settlementReconcileContractInfoService = this.settlementReconcileContractInfoService;
        reconcileRuleHelper.settlementReconcileCompanySubjectPolicyMethodService =
                this.settlementReconcileCompanySubjectPolicyMethodService;
    }


    /**
     * 根据day获取需要生成的对账单  day=99为最后一日  1 2 / 3 4
     *
     * @return com.mpolicy.order.enums.InsureOrderStatusEnum
     * <AUTHOR>
     * @since 2022/5/26
     */
    public static List<ReconcileCompanyInfo> queryReconcileCompanyInfoList(int day) {
        //获取day天结算的科目信息
        Map<String, List<SettlementReconcileCompanySubjectEntity>> companySubjectMap =
                reconcileRuleHelper.settlementReconcileCompanySubjectService
                        .lambdaQuery()
                        .eq(SettlementReconcileCompanySubjectEntity::getSubjectRuleStatus, StatusEnum.NORMAL.getCode())
                        .and(f -> f.eq(EDN_MONTH_DAY == day,
                                        SettlementReconcileCompanySubjectEntity::getStatementDateMonthEnd,
                                        StatusEnum.NORMAL.getCode())
                                .or()
                                .eq(SettlementReconcileCompanySubjectEntity::getStatementDate, EDN_MONTH_DAY == day ?
                                        DateUtil.endOfMonth(new Date()).dayOfMonth() : day))
                        .list().stream().collect(Collectors.groupingBy(SettlementReconcileCompanySubjectEntity::getReconcileCompanyCode));
        if (companySubjectMap.isEmpty()) {
            return Collections.emptyList();
        }
        //获取保司配置信息
        Map<String, SettlementReconcileCompanyInfoEntity> settlementReconcileCompanyMap =
                reconcileRuleHelper.settlementReconcileCompanyInfoService
                        .lambdaQuery()
                        .in(SettlementReconcileCompanyInfoEntity::getReconcileCompanyCode,
                                new ArrayList<>(companySubjectMap.keySet()))
                        .list().stream()
                        .collect(Collectors.toMap(SettlementReconcileCompanyInfoEntity::getReconcileCompanyCode, v -> v));

        //获取保司配置信息
        Map<String, SettlementReconcileContractInfoEntity> settlementReconcileContractMap =
                reconcileRuleHelper.settlementReconcileContractInfoService
                        .lambdaQuery()
                        .in(SettlementReconcileContractInfoEntity::getReconcileCompanyCode,
                                new ArrayList<>(companySubjectMap.keySet()))
                        .list().stream()
                        .collect(Collectors.toMap(SettlementReconcileContractInfoEntity::getReconcileCompanyCode, v -> v));
        List<ReconcileCompanyInfo> resultList = new ArrayList<>();
        companySubjectMap.forEach((key, list) -> {
            if (!settlementReconcileCompanyMap.containsKey(key) && !settlementReconcileContractMap.containsKey(key)) {
                log.info("保司对账单={}配置信息不存在,不生成对账单", key);
                return;
            }
            // 开始处理一家保司 一个日期的对账单 然后进行合并处理
            Map<String, List<SettlementReconcileCompanySubjectEntity>> mergeCompanySubjectMap =
                    list.stream().collect(Collectors.groupingBy(SettlementReconcileCompanySubjectEntity::getMergeCode));

            // 开始处理
            mergeCompanySubjectMap.forEach((mergeCode, settlementReconcileCompanySubjectList) -> {
                ReconcileCompanyInfo result = null;
                if (settlementReconcileCompanyMap.containsKey(key)) {
                    result = BeanUtil.copyProperties(settlementReconcileCompanyMap.get(key),
                            ReconcileCompanyInfo.class);
                }
                if (settlementReconcileContractMap.containsKey(key)) {
                    result = BeanUtil.copyProperties(settlementReconcileContractMap.get(key),
                            ReconcileCompanyInfo.class);
                }
                if (result == null) {
                    return;
                }
                SettlementReconcileCompanySubjectEntity settlementReconcileCompanySubject =
                        settlementReconcileCompanySubjectList.get(0);
                result.setStatementDate(settlementReconcileCompanySubject.getStatementDate());
                result.setStatementDateMonthEnd(settlementReconcileCompanySubject.getStatementDateMonthEnd());
                List<ReconcileCompanyRuleInfo> companyRuleList = settlementReconcileCompanySubjectList.stream()
                        .map(m -> BeanUtil.copyProperties(m, ReconcileCompanyRuleInfo.class))
                        .collect(Collectors.toList());
                //保司规则
                result.setReconcileType(settlementReconcileCompanySubject.getReconcileType());
                result.setMergeCode(mergeCode);
                result.setCompanyRuleList(companyRuleList);
                resultList.add(result);
            });

        });
        return resultList;
    }


    /**
     * 获取规则细则，包含【规则范围险种集合】
     * 1.存在
     *
     * @param reconcileCompanyCode 结算配置编码
     * @param reconcileType        结算类型 0:协议 1:合约
     * @param subjectRuleCodeList  结算规则编码
     * @return
     */
    public static List<ReconcileCompanySubjectDetail> queryReconcileCompanySubjectDetailList(String reconcileCompanyCode, Integer reconcileType, List<String> subjectRuleCodeList) {
        log.info("开始获取[{}]结算配置编码[{}]规则编码={}的结算规则信息", reconcileType == 0 ? "协议" : "合约", reconcileCompanyCode,
                JSONUtil.toJsonStr(subjectRuleCodeList));
        //获取结算的科目信息
        List<SettlementReconcileCompanySubjectEntity> reconcileCompanySubjectList =
                reconcileRuleHelper.settlementReconcileCompanySubjectService.lambdaQuery()
                        .in(SettlementReconcileCompanySubjectEntity::getSubjectRuleCode, subjectRuleCodeList)
                        .list();
        if (reconcileCompanySubjectList.isEmpty()) {
            log.info("[{}]结算配置编码[{}]没有匹配到结算规则信息", reconcileType == 0 ? "协议" : "合约", reconcileCompanyCode);
            return Collections.emptyList();
        }
        // 先获取一下全部产品信息和险种集合
        List<ProtocolInsuranceProductInfo> insuranceProductList = ReconcileRuleHelper.findInsuranceProductList(reconcileCompanyCode,
                reconcileType);
        log.info("开始获取[{}]结算配置编码[{}]规则编码={}的结算规则的产品险种信息={}", reconcileType == 0 ? "协议" : "合约", reconcileCompanyCode,
                JSONUtil.toJsonStr(subjectRuleCodeList), JSONUtil.toJsonStr(insuranceProductList));
        Map<String, ProtocolInsuranceProductInfo> insuranceProductInfoMap =
                insuranceProductList.stream().collect(Collectors.toMap(ProtocolInsuranceProductInfo::getInsuranceProductCode, v -> v));
        // 获取科目列表详情
        List<ReconcileCompanySubjectDetail> resultList = reconcileCompanySubjectList.stream()
                .map(m -> getReconcileCompanySubjectDetail(m, insuranceProductInfoMap))
                .collect(Collectors.toList());
        log.info("开始获取[{}]结算配置编码[{}] 规则编码={}的科目列表={}", reconcileType == 0 ? "协议" : "合约", reconcileCompanyCode,
                JSONUtil.toJsonStr(subjectRuleCodeList), JSONUtil.toJsonStr(resultList));
        return resultList;
    }


    /**
     * 处理科目详情
     *
     * @param settlementReconcileCompanySubject 结算科目配置
     * @param insuranceProductInfoMap           产品和险种信息
     * @return
     */
    private static ReconcileCompanySubjectDetail getReconcileCompanySubjectDetail(SettlementReconcileCompanySubjectEntity settlementReconcileCompanySubject, Map<String, ProtocolInsuranceProductInfo> insuranceProductInfoMap) {
        ReconcileCompanySubjectDetail info = BeanUtil.copyProperties(settlementReconcileCompanySubject,
                ReconcileCompanySubjectDetail.class);
        // 处理科目信息 如果是合约线上科目就改成 服务费
        if (SERVICE_FEE_LIST.contains(info.getReconcileSubjectCode())) {
            info.setReconcileSubjectCode(ReconcileSubjectOnlineEnum.SERVICE_FEE.getCode());
        }
        if (insuranceProductInfoMap.isEmpty()) {
            info.setProtocolInsuranceProductInfoList(Collections.emptyList());
            info.setProductCodeList(Collections.emptyList());
            return info;
        }
        // 获取配置的产品
        List<String> subjectProductList =
                reconcileRuleHelper.settlementReconcileCompanySubjectProductService.lambdaQuery()
                        .eq(SettlementReconcileCompanySubjectProductEntity::getSubjectRuleCode,
                                settlementReconcileCompanySubject.getSubjectRuleCode())
                        .list().stream().map(SettlementReconcileCompanySubjectProductEntity::getInsuranceProductCode)
                        .collect(Collectors.toList());

        // 适用范围0:通用 1:指定险种
        if (settlementReconcileCompanySubject.getSubjectScope().equals(StatusEnum.NORMAL.getCode())) {
            // 获取了指定的险种信息
            List<ProtocolInsuranceProductInfo> protocolInsuranceProductInfoList =
                    insuranceProductInfoMap.keySet().stream()
                            .filter(subjectProductList::contains)
                            .map(insuranceProductInfoMap::get)
                            .collect(Collectors.toList());
            info.setProtocolInsuranceProductInfoList(protocolInsuranceProductInfoList);

        } else {
            // 选择了通用但是需要排除的险种
            List<ProtocolInsuranceProductInfo> protocolInsuranceProductInfoList =
                    insuranceProductInfoMap.keySet().stream()
                            .filter(f -> !subjectProductList.contains(f))
                            .map(insuranceProductInfoMap::get)
                            .collect(Collectors.toList());
            info.setProtocolInsuranceProductInfoList(protocolInsuranceProductInfoList);
        }
        if (CollUtil.isEmpty(info.getProtocolInsuranceProductInfoList())) {
            info.setProductCodeList(Collections.emptyList());
            return info;
        }
        log.info("科目编码={}所有产品险种信息={}", info.getReconcileSubjectCode(),
                JSONUtil.toJsonStr(info.getProtocolInsuranceProductInfoList()));
        // 处理一下险种信息
        List<String> productCodeList = info.getProtocolInsuranceProductInfoList().stream()
                .map(ProtocolInsuranceProductInfo::getProductCodeList)
                .filter(CollUtil::isNotEmpty)
                .flatMap(Collection::stream)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        info.setProductCodeList(productCodeList);
        // 处理一下保全的保单方式
        List<SettlementReconcileCompanySubjectPolicyMethodEntity> preservationPolicyMethodList =
                reconcileRuleHelper.settlementReconcileCompanySubjectPolicyMethodService.lambdaQuery()
                        .eq(SettlementReconcileCompanySubjectPolicyMethodEntity::getSubjectRuleCode, info.getSubjectRuleCode())
                        .list();
        info.setPreservationPolicyMethodList(preservationPolicyMethodList);
        return info;
    }

    /**
     * 获取所有产品信息以及关联的险种编码
     *
     * @param reconcileCompanyCode 结算配置编码
     * @param reconcileType        结算类型0:协议 1:合约
     * @return
     */
    public static List<ProtocolInsuranceProductInfo> findInsuranceProductList(String reconcileCompanyCode,
                                                                              Integer reconcileType) {
        if (reconcileType == null || reconcileType == 0) {
            //获取协议关联的产品信息和险种
            SettlementReconcileCompanyInfoEntity settlementReconcileCompanyInfo =
                    Optional.ofNullable(reconcileRuleHelper.settlementReconcileCompanyInfoService.lambdaQuery()
                            .eq(SettlementReconcileCompanyInfoEntity::getReconcileCompanyCode, reconcileCompanyCode)
                            .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("保司配置不存在")));
            return ProtocolBaseHelper.queryProtocolInsuranceProductList(settlementReconcileCompanyInfo.getInnerSignatoryCode(), settlementReconcileCompanyInfo.getExternalSignatoryType(), settlementReconcileCompanyInfo.getExternalSignatoryCode(), StrUtil.split(settlementReconcileCompanyInfo.getCompanyCode(), ','));
        } else {
            //获取合约关联的产品信息和险种
            SettlementReconcileContractInfoEntity settlementReconcileContractInfo =
                    Optional.ofNullable(reconcileRuleHelper.settlementReconcileContractInfoService.lambdaQuery()
                            .eq(SettlementReconcileContractInfoEntity::getReconcileCompanyCode, reconcileCompanyCode)
                            .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("合约配置不存在")));
            return ContractBaseHelper.queryContractInsuranceProductList(settlementReconcileContractInfo.getInnerSignatoryCode(), settlementReconcileContractInfo.getInnerSignatoryType(), settlementReconcileContractInfo.getExternalSignatoryCode(), settlementReconcileContractInfo.getExternalSignatoryType());
        }

    }

}
