package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.common.reconcile.company.ReconcileRuleFileTemplate;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileTemplateEnum;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementReconcileFileDao;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementReconcilePolicyFileDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileFileEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcilePolicyFileEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcilePolicyFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("settlementReconcilePolicyFileService")
public class SettlementReconcilePolicyFileServiceImpl extends ServiceImpl<SettlementReconcilePolicyFileDao, SettlementReconcilePolicyFileEntity> implements SettlementReconcilePolicyFileService {

    @Autowired
    private SettlementReconcilePolicyFileDao settlementReconcilePolicyFileDao;


    @Autowired
    private SettlementReconcileFileDao settlementReconcileFileDao;


    /**
     * 保存模版数据
     *
     * @param reconcileCode
     * @param readFile
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveReconcileRuleFileTemplate(String reconcileCode, String fileCode, List<ReconcileRuleFileTemplate> readFile) {
        if (CollUtil.isEmpty(readFile)) {
            return;
        }
        SettlementReconcileFileEntity settlementReconcileFile = new LambdaQueryChainWrapper<>(settlementReconcileFileDao)
                .eq(SettlementReconcileFileEntity::getReconcileCode, reconcileCode)
                .eq(SettlementReconcileFileEntity::getReconcileFileCode, fileCode)
                .last("limit 1").one();
        if (settlementReconcileFile == null) {
            return;
        }
        log.info("处理文件id={},内容={}", settlementReconcileFile.getId(), JSONUtil.toJsonStr(readFile));
        Integer fileType = ReconcileTemplateEnum.getFileType(settlementReconcileFile.getReconcileFileType());
        CollUtil.split(readFile, 5000).forEach(action -> {
            log.info("处理文件内容={}", JSONUtil.toJsonStr(action));
            List<SettlementReconcilePolicyFileEntity> settlementReconcilePolicyFileList = action.stream().map(m -> {
                SettlementReconcilePolicyFileEntity settlementReconcilePolicyFile = BeanUtil.copyProperties(m, SettlementReconcilePolicyFileEntity.class);
                settlementReconcilePolicyFile.setReconcileCode(reconcileCode);
                settlementReconcilePolicyFile.setFileCode(fileCode);
                settlementReconcilePolicyFile.setFileType(fileType);
                settlementReconcilePolicyFile.setCompanyAmount(m.getCompanyAmount().setScale(2, RoundingMode.HALF_UP));
                return settlementReconcilePolicyFile;
            }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(settlementReconcilePolicyFileList)) {
                log.info("保存文件到数据库中={}", JSONUtil.toJsonStr(settlementReconcilePolicyFileList));
                settlementReconcilePolicyFileDao.insertBatchSomeColumn(settlementReconcilePolicyFileList);
            }
        });
    }
}
