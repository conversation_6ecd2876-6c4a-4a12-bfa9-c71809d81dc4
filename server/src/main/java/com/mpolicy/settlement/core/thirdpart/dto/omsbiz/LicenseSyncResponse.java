package com.mpolicy.settlement.core.thirdpart.dto.omsbiz;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 工商信息同步响应结果
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LicenseSyncResponse {

    /**
     * 响应码
     */
    private String code;

    /**
     * 分页数据
     */
    private LicenseSyncPageData data;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误上下文
     */
    private Object errorContext;

    /**
     * 错误消息
     */
    private String errorMsg;

    /**
     * 消息
     */
    private String message;

    /**
     * 是否成功
     */
    private Boolean success;
}
