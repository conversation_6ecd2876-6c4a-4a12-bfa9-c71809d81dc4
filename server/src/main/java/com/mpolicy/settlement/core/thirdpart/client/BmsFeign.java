package com.mpolicy.settlement.core.thirdpart.client;

import com.mpolicy.settlement.core.thirdpart.request.DbcBusinessIncentiveCalculateDataAddDTO;
import com.mpolicy.settlement.core.thirdpart.response.BmsResult;
import com.mpolicy.settlement.core.thirdpart.response.ChongHoResult;
import com.mpolicy.settlement.core.thirdpart.response.UserDetailVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * bms-api
 *
 * <AUTHOR>
 */
@FeignClient(name = "bms-service-biz", url = "${bms.api.url}")
@Configuration
public interface BmsFeign {
    @ApiOperation(
            value = "根据身份证查询用户基本信息、任职信息、岗位和角色信息",
            notes = "/user/idcard/{idcard}/detail"
    )
    @GetMapping({"/user/idcard/{idcard}/detail"})
    BmsResult<UserDetailVO> getUserDetailVOByIdcard(@PathVariable("idcard") String var1);
}
