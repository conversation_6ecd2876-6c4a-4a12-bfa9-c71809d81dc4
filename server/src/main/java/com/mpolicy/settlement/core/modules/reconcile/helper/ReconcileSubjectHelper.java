package com.mpolicy.settlement.core.modules.reconcile.helper;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.settlement.core.common.SettlementCoreCenterKeys;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementSubjectDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementSubjectEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ReconcileSubjectHelper {


    public static ReconcileSubjectHelper reconcileRuleHelper;

    @Autowired
    IRedisService redisService;
    @Autowired
    SettlementSubjectDao settlementSubjectDao;

    @PostConstruct
    public void init() {
        reconcileRuleHelper = this;
        // redis服务
        reconcileRuleHelper.redisService = this.redisService;
        reconcileRuleHelper.settlementSubjectDao = this.settlementSubjectDao;
    }


    public static String matchSearchName(String subjectName) {
        String subjectCode = reconcileRuleHelper.redisService.hget(SettlementCoreCenterKeys.SETTLEMENT_SUBJECT, "name", subjectName, String.class);
        if (StrUtil.isNotBlank(subjectCode)) {
            return subjectCode;
        } else {
            Map<String, String> map = new LambdaQueryChainWrapper<>(reconcileRuleHelper.settlementSubjectDao)
                    .list().stream().collect(Collectors.toMap(SettlementSubjectEntity::getSubjectName, SettlementSubjectEntity::getSubjectCode));
            // 存入缓存
            log.info("存入缓存中的科目信息name为={}", JSONUtil.toJsonStr(map));
            reconcileRuleHelper.redisService.hmset(SettlementCoreCenterKeys.SETTLEMENT_SUBJECT, "name", map);
            return map.get(subjectName);
        }
    }

    public static String matchSearchCode(String subjectCode) {
        String subjectName = reconcileRuleHelper.redisService.hget(SettlementCoreCenterKeys.SETTLEMENT_SUBJECT, "code", subjectCode, String.class);
        if (StrUtil.isNotBlank(subjectName)) {
            return subjectName;
        } else {
            Map<String, String> map = new LambdaQueryChainWrapper<>(reconcileRuleHelper.settlementSubjectDao)
                    .list().stream().collect(Collectors.toMap(SettlementSubjectEntity::getSubjectCode, SettlementSubjectEntity::getSubjectName));
            // 存入缓存
            log.info("存入缓存中的科目信息code为={}", JSONUtil.toJsonStr(map));
            reconcileRuleHelper.redisService.hmset(SettlementCoreCenterKeys.SETTLEMENT_SUBJECT, "code", map);
            return map.get(subjectCode);
        }
    }
}
