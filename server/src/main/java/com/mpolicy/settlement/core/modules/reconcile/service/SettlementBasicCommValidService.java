package com.mpolicy.settlement.core.modules.reconcile.service;

import com.mpolicy.settlement.core.common.commission.dto.IncomeProfitInfoParamsDto;
import com.mpolicy.settlement.core.common.commission.dto.IncomeRateOut;
import com.mpolicy.settlement.core.common.commission.dto.ValidateCostConfigParam;
import com.mpolicy.settlement.core.common.commission.dto.ValidateResultDto;

import java.util.List;

public interface SettlementBasicCommValidService {
    /**
     * 验证支出端佣金配置是否存在
     * @param params
     * @return
     */
    List<ValidateResultDto> validateCostConfig(List<ValidateCostConfigParam> params);

    List<ValidateResultDto> validateIncomeConfig(List<ValidateCostConfigParam> params);

    void addFilterWhitePolicy(String policyNo, int op);

    List<IncomeRateOut> queryIncomeRateConfig(IncomeProfitInfoParamsDto profitInfoParamsDto);
}
