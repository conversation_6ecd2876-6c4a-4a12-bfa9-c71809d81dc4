package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import cn.hutool.core.date.StopWatch;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.common.keys.AdminCommonKeys;
import com.mpolicy.settlement.core.modules.common.ExportBasicService;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementPolicyInfoService;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcileHelperService;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcileInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 保司结算对账单帮助服务实现
 *
 * <AUTHOR>
 * @date 2023-06-27 18:02
 */
@Slf4j
@Service
public class SettlementReconcileHelperServiceImpl extends ExportBasicService implements SettlementReconcileHelperService {

    @Autowired
    private SettlementReconcileInfoService settlementReconcileInfoService;

    @Autowired
    private SettlementPolicyInfoService settlementPolicyInfoService;

    @Autowired
    private IRedisService redisService;

    @Override
    public void createPolicySummary(String reconcileCode, String uuid) {
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        SettlementReconcileInfoEntity reconcileInfo = settlementReconcileInfoService.lambdaQuery()
                .eq(SettlementReconcileInfoEntity::getReconcileCode, reconcileCode).one();
        if (reconcileInfo == null) {
            redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, "2");
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("对账单纪录不存在"));
        }

        // 获取结算清单明细
        List<SettlementPolicyInfoEntity> settlementPolicyList = settlementPolicyInfoService.querySettlementPolicyInfoList(reconcileInfo);
        // 如果记录不存在
        if (settlementPolicyList.isEmpty()) {
            reconcileInfo.setPolicyCount(0);
            settlementReconcileInfoService.updateById(reconcileInfo);
            redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, "2");
            return;
        }
        //
        settlementReconcileInfoService.settlementPolicyInfoToXls(reconcileInfo, settlementPolicyList);
        redisService.set(AdminCommonKeys.HANDLE_STATUS, uuid, "2");


    }
}
