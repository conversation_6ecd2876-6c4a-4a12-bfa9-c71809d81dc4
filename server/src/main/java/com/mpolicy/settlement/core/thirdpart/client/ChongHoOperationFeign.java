package com.mpolicy.settlement.core.thirdpart.client;


import com.mpolicy.settlement.core.thirdpart.request.WhaleAddCommissionPushQuery;
import com.mpolicy.settlement.core.thirdpart.response.AddCommissionSettlementPushDto;
import com.mpolicy.settlement.core.thirdpart.response.ChongHoResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 农保-加佣api
 *
 * <AUTHOR>
 */
@FeignClient(name = "chongHo-service", url = "${chongho.feign.operation}")
@Configuration
public interface ChongHoOperationFeign {

    /**
     * 加佣明细分页查询
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "加佣明细分页查询")
    @PostMapping("/back/whaleCommission/query/getSettlementDataByPage")
    ChongHoResult<List<AddCommissionSettlementPushDto>> getSettlementDataByPage(@RequestBody WhaleAddCommissionPushQuery query);


    @ApiOperation(value = "加佣-同步业财结算状态")
    @PostMapping("/back/whaleCommission/syncSettlementStatus")
    void syncSettlementStatus(@RequestParam("uuids") List<String> uuids);
}
