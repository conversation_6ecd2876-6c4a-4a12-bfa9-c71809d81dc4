package com.mpolicy.settlement.core.modules.reconcile.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileConfirmEntity;

import java.util.List;

/**
 * 保司结算对账单汇总表
 *
 * <AUTHOR>
 * @date 2023-05-23 14:32:21
 */
public interface SettlementReconcileConfirmService extends IService<SettlementReconcileConfirmEntity> {

    /**
     * 批量保存数据
     *
     * @param listData 数据集合
     * @return void
     * <AUTHOR>
     * @since 2023/8/31
     */
    void saveList(List<SettlementReconcileConfirmEntity> listData);

    /**
     * 批量插入
     *
     * @param batchList
     */
    void insertBatchSomeColumn(List<SettlementReconcileConfirmEntity> batchList);
}

