package com.mpolicy.settlement.core.modules.reconcile.entity;

import com.baomidou.mybatisplus.annotation.*;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;

/**
 * 保司结算对账单
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:39
 */
@TableName("settlement_reconcile_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementReconcileInfoEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId
    private Integer id;
    /**
     * 对账唯一单号
     */
    private String reconcileCode;
    /**
     * 对账单名称
     */
    private String reconcileName;
    /**
     * 结算类型0:小鲸 1:非小鲸
     */
    private Integer reconcileType;
    /**
     * 对账月度
     */
    private String reconcileMonth;
    /**
     * 负责人
     */
    private String headName;
    /**
     * 对账保单月度
     */
    private Integer policyMonth;

    /**
     * 生成账单日
     */
    private String createReconcileDay;
    /**
     * 对账日
     */
    private Integer reconcileDay;
    /**
     * 保司配置编码
     */
    private String reconcileCompanyCode;
    /**
     * 配置对账单规则编码集合
     */
    private String subjectRuleCodes;
    /**
     * 合并编码
     */
    private String mergeCode;
    /**
     * 产品所属保险公司编码
     */
    private String productCompanyCode;
    /**
     * 产品所属保险公司名称
     */
    private String productCompanyName;
    /**
     * 保险公司编码
     */
    private String companyCode;
    /**
     * 保险公司名称
     */
    private String companyName;
    /**
     * 内部签署方类型 组织机构 公司 个人
     */
    private String innerSignatoryType;
    /**
     * 内部签署方编码
     */
    private String innerSignatoryCode;
    /**
     * 内部签署方名称
     */
    private String innerSignatoryName;
    /**
     * 外部签署方类型字典
     */
    private String externalSignatoryType;
    /**
     * 外部签署方编码
     */
    private String externalSignatoryCode;
    /**
     * 外部签署方名称
     */
    private String externalSignatoryName;
    /**
     * 保司对账单文件数量
     */
    private Integer companyBillCount;
    /**
     * 对账保单数量
     */
    private Integer policyCount;
    /**
     * 小鲸手续费金额
     */
    private BigDecimal xiaowhaleAmount;
    /**
     * 保司手续费金额
     */
    private BigDecimal companyAmount;
    /**
     * 差异金额
     */
    private BigDecimal diffAmount;
    /**
     * 已开票金额
     */
    private BigDecimal invoicedAmount;
    /**
     * 差异冲正金额
     */
    private BigDecimal reversalAmount;
    /**
     * 开票状态
     */
    private Integer invoiceStatus;
    /**
     * 开票中金额
     */
    private BigDecimal invoiceAmount;
    /**
     * 可开票金额
     */
    private BigDecimal invoicableAmount;

    /**
     * 对账单下载地址
     */
    private String reconcileXlsUrl;

    /**
     * 对账单下载地址
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String reconcilePolicyUrl;

    /**
     * 对账单状态;0待对账1账单生成中2对账中3对账已完成4对账单生成失败
     */
    private Integer reconcileStatus;
    /**
     * 对账操作员
     */
    private String reconcileUser;
    /**
     * 对账完成时间
     */
    private Date reconcileTime;
    /**
     * 是否删除;0有效-1删除
     */
    @TableLogic
    private Integer deleted;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新人
     */
    private String updateUser;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 乐观锁
     */
    @Version
    @TableField(fill = FieldFill.INSERT)
    private Integer revision;

}
