package com.mpolicy.settlement.core.service.common;

import com.mpolicy.agent.client.AgentClient;
import com.mpolicy.agent.common.model.agent.AgentInfoBasicsOut;
import com.mpolicy.agent.common.model.agent.AgentInfoListOut;
import com.mpolicy.agent.common.model.agent.AllAgentInfoListVo;
import com.mpolicy.agent.common.model.product.AllSellProductListOut;
import com.mpolicy.agent.common.model.product.AllSellProductListVo;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 代理人基础服务
 *
 * <AUTHOR>
 * @since 2023-09-15 19:00
 */
@Component
@Slf4j
public class AgentBaseService {

    @Autowired
    private AgentClient agentClient;

    /**
     * 根据代理人编码获取代理人信息
     *
     * @param agentCode 代理人编码
     * @param throwEx   不存在是否抛出异常
     * @return com.mpolicy.agent.common.model.agent.AgentInfoBasicsOut
     * <AUTHOR>
     * @since 2023/9/15 17:14
     */
    public AgentInfoBasicsOut getAgentInfoByAgentCode(String agentCode, boolean throwEx) {
        Result<AgentInfoBasicsOut> result = agentClient.findAgentInfoBasics(agentCode);
        if (!result.isSuccess()) {
            log.warn("获取代理人信息失败，代理人编码={}，是否需要抛出异常={} msg={} ", agentCode, throwEx,
                result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        return result.getData();
    }

    public List<AllSellProductListOut> findAllSellProductList(boolean throwEx) {
        AllSellProductListVo query = new AllSellProductListVo();
        Result<List<AllSellProductListOut>> result = agentClient.findAllSellProductList(query);
        if (!result.isSuccess()) {
            log.warn("获取获取售卖商品列表(全部)失败,是否需要抛出异常={} msg={} ", throwEx, result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        return result.getData();
    }

    public List<AgentInfoListOut> findAllAgentInfoList(boolean throwEx) {
        AllAgentInfoListVo query = new AllAgentInfoListVo();
        Result<List<AgentInfoListOut>> result = agentClient.findAllAgentInfoList(query);
        if (!result.isSuccess()) {
            log.warn("获取代理人列表(全部)失败,是否需要抛出异常={} msg={} ", throwEx, result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        return result.getData();
    }
}
