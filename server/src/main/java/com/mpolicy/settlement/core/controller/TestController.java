package com.mpolicy.settlement.core.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.mpolicy.common.result.Result;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileTemplateEnum;
import com.mpolicy.settlement.core.modules.add.job.SettlementAddCostJob;
import com.mpolicy.settlement.core.modules.autocost.dto.CostAutoRecord;
import com.mpolicy.settlement.core.modules.autocost.dto.CostSubjectCalculateRuleConfig;
import com.mpolicy.settlement.core.modules.autocost.dto.SettlementCostInfoDto;
import com.mpolicy.settlement.core.modules.autocost.dto.base.SettlementCostInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.base.SettlementCostQuery;
import com.mpolicy.settlement.core.modules.autocost.factory.SubjectCalculateFactory;
import com.mpolicy.settlement.core.modules.autocost.factory.SubjectDataFactory;
import com.mpolicy.settlement.core.modules.autocost.handler.SubjectCalculateHandler;
import com.mpolicy.settlement.core.modules.autocost.handler.SubjectDataBuilderHandler;
import com.mpolicy.settlement.core.modules.autocost.helper.GroovyHelper;
import com.mpolicy.settlement.core.modules.autocost.job.BranchDirectorJob;
import com.mpolicy.settlement.core.modules.autocost.job.DbcBusinessIncentiveJob;
import com.mpolicy.settlement.core.modules.autocost.job.HrGrantPersonSyncJob;
import com.mpolicy.settlement.core.modules.autocost.job.SettlementCostAccountJob;
import com.mpolicy.settlement.core.modules.autocost.project.calculate.rule.CostSubjectCalculateRuleService;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementAutoCostBasicService;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementBaseCommService;
import com.mpolicy.settlement.core.modules.distribution.job.SettlementDistributionCostJob;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.UpdateEventJobStatusDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementStandardPremiumEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.event.factory.SettlementEventFactory;
import com.mpolicy.settlement.core.modules.reconcile.event.factory.SettlementStandardPremiumEventFactory;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.SettlementEventHandler;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.SettlementStandardPremiumEventHandler;
import com.mpolicy.settlement.core.modules.reconcile.helper.ReconcileBaseHelper;
import com.mpolicy.settlement.core.modules.reconcile.job.SettlementCostInfoJob;
import com.mpolicy.settlement.core.modules.reconcile.job.SettlementEventJob;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementEventJobService;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementStandardPremiumEventJobService;
import com.mpolicy.web.common.annotation.Lock;
import com.mpolicy.web.common.annotation.PassToken;
import com.xxl.job.core.context.XxlJobHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/24 10:22 上午
 * @Version 1.0
 */
@RestController
@RequestMapping("/test")
@Api(tags = "结算测试接口")
@Slf4j
public class TestController {

    @Autowired
    private SettlementEventJobService settlementEventJobService;

    @Autowired
    SettlementCostAccountJob settlementCostAccountJob;
    @Autowired
    HrGrantPersonSyncJob hrGrantPersonSyncJob;

    @Autowired
    private SettlementStandardPremiumEventJobService settlementStandardPremiumEventJobService;

    @ApiOperation(value = "手工处理支出端事件", notes = "手工处理支出端事件")
    @PostMapping("/manualCostEvent/{eventId}")
    @PassToken
    @Lock(keys = "#eventId", attemptTimeout = 500)
    public Result<String> manualCostEvent(@PathVariable @ApiParam(name = "eventId", value = "********************") Integer eventId,
                                              @RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员", example = "张三") String userName) {

        SettlementEventJobEntity eventJob = settlementEventJobService.getById(eventId);
        if(eventJob.getCostEventStatus()!=null && (eventJob.getCostEventStatus() == 1 || eventJob.getCostEventStatus() == 3)){
            return Result.success("状态为已处理，不可在运行");
        }
        SettlementEventHandler settlementEventHandler = SettlementEventFactory.getSettlementEventHandler(eventJob.getEventType());
        if (settlementEventHandler != null) {
            XxlJobHelper.log("执行获取需要处理的事件任务,事件类型={}", eventJob.getEventType());
            // 事件执行
            settlementEventHandler.handle(eventJob);
        } else {
            log.warn(StrUtil.format("事件类型类型不支持，事件类型={}", eventJob.getEventType()));
        }
        return Result.success("success");
    }

    @ApiOperation(value = "手工处理标保事件", notes = "手工处理标保事件")
    @PostMapping("/manualStandardEvent/{eventId}")
    @PassToken
    @Lock(keys = "#eventId", attemptTimeout = 500)
    public Result<String> manualStandardEvent(@PathVariable @ApiParam(name = "eventId", value = "********************") Integer eventId,
                                          @RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员", example = "张三") String userName) {

        SettlementStandardPremiumEventJobEntity eventJob = settlementStandardPremiumEventJobService.getById(eventId);

        SettlementStandardPremiumEventHandler eventHandler = SettlementStandardPremiumEventFactory.getSettlementEventHandler(eventJob.getEventType());
        if (eventHandler != null) {
            XxlJobHelper.log("执行获取需要处理的标保事件任务,事件类型={}", eventJob.getEventType());
            // 事件执行
            eventHandler.handle(eventJob);
        } else {
            log.warn(StrUtil.format("标保事件类型类型不支持，事件类型={}", eventJob.getEventType()));
        }
        return Result.success("success");
    }



    @Autowired
    private SettlementEventJob settlementEventJob;
    @ApiOperation(value = "手工运行支出基础佣金失败补偿", notes = "手工运行支出基础佣金失败补偿")
    @PostMapping("/testSettlementCostFailEventJob")
    @PassToken
    public Result<String> testSettlementCostFailEventJob(@RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员", example = "张三") String userName) {

        settlementEventJob.settlementCostEventJob();
        return Result.success("success");
    }

    @ApiOperation(value = "手工修改支出端状态", notes = "手工修改支出端状态")
    @PostMapping("/updateCostEventStatus")
    @PassToken
    @Lock(keys = "#eventId", attemptTimeout = 500)
    public Result<String> updateCostEventStatus(@RequestParam(value = "eventId") @ApiParam(name = "eventId", value = "********************") Integer eventId,
                                                @RequestParam(value = "costEventStatus") @ApiParam(name = "costEventStatus", value = "********************") Integer costEventStatus,

                                              @RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员", example = "张三") String userName) {

        SettlementEventJobEntity eventJob = settlementEventJobService.getById(eventId);
        if(Objects.nonNull(eventJob)) {
            settlementEventJobService.lambdaUpdate().set(SettlementEventJobEntity::getCostEventStatus, costEventStatus)
                    .set(SettlementEventJobEntity::getChannelCode, "zhnx")
                    .eq(SettlementEventJobEntity::getId, eventId).update();
        }

        return Result.success("success");
    }

    @ApiOperation(value = "手工批量修改支出端状态", notes = "手工批量修改支出端状态")
    @PostMapping("/batchUpdateCostEventStatus")
    @PassToken
    public Result<String> batchUpdateCostEventStatus(@RequestBody UpdateEventJobStatusDto updateEventJobStatusDto) {
        if(updateEventJobStatusDto == null || CollectionUtils.isEmpty(updateEventJobStatusDto.getEventIds()) || updateEventJobStatusDto.getCostEventStatus() == null){
            return Result.success("success");
        }

        settlementEventJobService.lambdaUpdate().set(SettlementEventJobEntity::getCostEventStatus, updateEventJobStatusDto.getCostEventStatus())
                .set(SettlementEventJobEntity::getCostEventMessage,updateEventJobStatusDto.getCostEventMsg())
                .in(SettlementEventJobEntity::getId, updateEventJobStatusDto.getEventIds())
                .update();


        return Result.success("success");
    }



    @Autowired
    CostSubjectCalculateRuleService costSubjectCalculateRuleService;
    @Autowired
    GroovyHelper groovyHelper;
    @ApiOperation(value = "手工处理计算规则", notes = "手工处理计算规则")
    @PostMapping("/manualCostEvent/testCalculateRule")
    @PassToken
    public Result<String> testCalculateRule(@RequestParam(value = "renewalRate") BigDecimal renewalRate) {
        CostAutoRecord costAutoRecord = new CostAutoRecord();
        costAutoRecord.setRenewalRate(renewalRate);

        log.info("运行前的参数={}",costAutoRecord);
        Map<String, CostSubjectCalculateRuleConfig> ruleConfigMap =  costSubjectCalculateRuleService.mapRuleConfig(CostSubjectEnum.LONG_PROMOTION.getCode());
        for(String key : ruleConfigMap.keySet()){
            try {
                Map<String,Object> params = Maps.newHashMap();
                params.put("renewalRate",renewalRate);
                CostSubjectCalculateRuleConfig config = ruleConfigMap.get(key);
                //groovyHelper.clearByCode(CostSubjectEnum.LONG_PROMOTION.getCode(),key);
                costSubjectCalculateRuleService.setFactorValueByScript(costAutoRecord, config, params);
            }catch (Exception g){
                log.warn("运行失败",g);
            }
        }
        log.info("运行后的参数={}",costAutoRecord);
        return Result.success("success");
    }

    @Autowired
    SettlementBaseCommService settlementBaseCommService;

    @ApiOperation(value = "手工处理获取数据", notes = "手工处理获取数据")
    @PostMapping("/manualCostEvent/testListCostInfo")
    @PassToken
    public Result<List<SettlementCostInfo>> testListCostInfo(@RequestBody SettlementCostQuery query) {

        List<SettlementCostInfo> dto = settlementBaseCommService.getSettlementBaseCommList(query);
        return Result.success(dto);
    }




    @ApiOperation(value = "手工计算科目结算数据", notes = "手工计算科目结算数据")
    @PostMapping("/manualCostEvent/testCalculate")
    @PassToken
    public Result<String> testCalculate(@RequestParam(value = "programmeCode") String programmeCode,@RequestParam(value = "subjectCode") String subjectCode,
    @RequestParam(value = "costSettlementCycle") String costSettlementCycle) {
        CostSubjectEnum costSubject = CostSubjectEnum.matchSearchCode(subjectCode);
        SubjectCalculateHandler builderCalculateHandler = SubjectCalculateFactory.getSubjectCalculateHandler(costSubject);
        builderCalculateHandler.handle(programmeCode,costSettlementCycle,true);
        return Result.success("success");
    }

    @ApiOperation(value = "手工抽取科目结算数据", notes = "手工抽取科目结算数据")
    @PostMapping("/manualCostEvent/testData")
    @PassToken
    public Result<String> testData(@RequestParam(value = "programmeCode") String programmeCode,@RequestParam(value = "subjectCode") String subjectCode,
                                        @RequestParam(value = "costSettlementCycle") String costSettlementCycle) {
        CostSubjectEnum costSubject = CostSubjectEnum.matchSearchCode(subjectCode);
        SubjectDataBuilderHandler builderCalculateHandler = SubjectDataFactory.getSubjectBuilderDataHandler(costSubject);
        builderCalculateHandler.handle(costSettlementCycle,true);
        return Result.success("success");
    }

    @ApiOperation(value = "根据支出基础佣金编码查询佣金信息", notes = "根据支出基础佣金编码查询佣金信息")
    @PostMapping("/manualCostEvent/getByCostCode")
    @PassToken
    public Result<List<SettlementCostInfoDto>> getByCostCode(@RequestParam(value = "costCode") String costCode) {

        return Result.success(settlementBaseCommService.listByCostCode(Arrays.asList(costCode)));
    }
    @ApiOperation(value = "根据合同号列表处理佣金事件", notes = "根据合同号列表处理佣金事件")
    @PostMapping("/manualCostEvent/manualCostEventByContractCode")
    @PassToken
    public Result<String> manualCostEventByContractCode(@RequestBody ManualCostEventDto manualCostEventDto,
                                              @RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员", example = "张三") String userName) {

        List<SettlementEventJobEntity> eventJobList = settlementEventJobService.lambdaQuery().in(SettlementEventJobEntity::getContractCode,manualCostEventDto.getContractCodeList()).orderByAsc(SettlementEventJobEntity::getId).list();
        Set<String> errors = Sets.newHashSet();
        for(int i=0 ;i<eventJobList.size();i++) {
            SettlementEventJobEntity eventJob = eventJobList.get(i);
            if(errors.contains(eventJob.getContractCode())){
                continue;
            }
            //已成功的，不再处理
            if(eventJob.getCostEventStatus() == 1){
                continue;
            }
            SettlementEventHandler settlementEventHandler = SettlementEventFactory.getSettlementEventHandler(eventJob.getEventType());
            if (settlementEventHandler != null) {
                XxlJobHelper.log("执行获取需要处理的事件任务,事件类型={}", eventJob.getEventType());
                // 事件执行
                settlementEventHandler.handle(eventJob);
                //执行失败，则跳出循环，不能执行后续的任务
                if(eventJob.getCostEventStatus() == -1){
                    log.warn("执行失败，添加到错误队列中");
                    errors.add(eventJob.getContractCode());
                }
            } else {
                log.warn(StrUtil.format("事件类型类型不支持，事件类型={}", eventJob.getEventType()));
            }
        }
        return Result.success("失败合同编号:"+JSON.toJSONString(errors));
    }
    @Data
    public static class ManualCostEventDto{
        private List<String> contractCodeList;
    }

    @ApiOperation(value = "根据商品编码获取商品名称", notes = "根据商品编码获取商品名称")
    @PostMapping("/product/testGetSellProductName")
    @PassToken
    public Result<String> testGetSellProductName(@RequestParam(value = "sellProductCode") String sellProductCode) {
        log.info("sellProductCode={}",sellProductCode);
        return Result.success(ReconcileBaseHelper.getSellProductName(sellProductCode));
    }

    @ApiOperation(value = "开始批量操作结算账户", notes = "开始批量操作结算账户")
    @PostMapping("/cost/testBatchCostAccountHandler")
    @PassToken
    public Result<String> testBatchCostAccountHandler(@RequestParam(value = "calcTime") String calcTime) {
        log.info("calcTime={}",calcTime);
        if(StringUtils.isBlank(calcTime)) {
            settlementCostAccountJob.batchCostAccountHandler();

        }else{
            settlementCostAccountJob.doCalc(DateUtil.parseDate(calcTime));
        }
        return Result.success("success");
    }

    @Autowired
    private SettlementCostInfoJob settlementCostInfoJob;

    @ApiOperation(value = "补偿佣金归属人第三方任职信息", notes = "补偿佣金归属人第三方任职信息")
    @PostMapping("/cost/compensateOwnerThirdStatus")
    @PassToken
    public Result<String> compensateOwnerThirdStatus(@RequestParam(value = "start") String start) {
        log.info("补偿开始时间={}",start);
        if(StringUtils.isBlank(start)){
            return Result.success("补偿开始时间不能为空");
        }
        if(DateUtil.parseDate(start).before(DateUtil.parseDate("2024-01-01"))){
            start = "2024-01-01";
        }
        settlementCostInfoJob.doCompensate(DateUtil.parseDate(start));
        return Result.success("success");
    }

    @Autowired
    private SettlementAddCostJob settlementAddCostJob;

    @ApiOperation(value = "补偿佣金归属人第三方任职信息", notes = "补偿佣金归属人第三方任职信息")
    @PostMapping("/cost/SettlementAddCostJob")
    @PassToken
    public Result<String> SettlementAddCostJob() {

        settlementAddCostJob.commissionSyncRecordHandler();
        return Result.success("success");
    }

    @Autowired
    private BranchDirectorJob branchDirectorJob;
    @ApiOperation(value = "主任绩效寻人job", notes = "主任绩效寻人job")
    @PostMapping("/settlement/generateBranchDirectorJob")
    @PassToken
    public Result<String> generateBranchDirectorJob() {

        branchDirectorJob.lookBranchDirectorJob();
        return Result.success("success");
    }
    @ApiOperation(value = "生服对接人寻人job", notes = "生服对接人寻人job")
    @PostMapping("/settlement/generateBranchPcoJob")
    @PassToken
    public Result<String> generateBranchPcoJob() {

        branchDirectorJob.lookBranchPcoJob();
        return Result.success("success");
    }
    @Autowired
    private DbcBusinessIncentiveJob dbcBusinessIncentiveJob;
    @ApiOperation(value = "主任绩效同步", notes = "主任绩效同步")
    @PostMapping("/settlement/directorIncentiveJob")
    @PassToken
    public Result<String> directorIncentiveJob() {

        dbcBusinessIncentiveJob.directorIncentiveJob();
        return Result.success("success");
    }

    @ApiOperation(value = "督导绩效同步", notes = "督导绩效同步")
    @PostMapping("/settlement/supervisorIncentiveJob")
    @PassToken
    public Result<String> supervisorIncentiveJob() {

        dbcBusinessIncentiveJob.supervisorIncentiveJob();
        return Result.success("success");
    }

    @Autowired
    private SettlementDistributionCostJob settlementDistributionCostJob;

    @ApiOperation(value = "分销佣金批量同步", notes = "分销佣金批量同步")
    @PostMapping("/cost/distCommissionSyncRecordHandler")
    @PassToken
    public Result<String> distCommissionSyncRecordHandler() {

        settlementDistributionCostJob.distCommissionSyncRecordHandler();
        return Result.success("success");
    }


    @ApiOperation(value = "同步发放人员", notes = "同步发放人员")
    @PostMapping("/cost/hrGrantPersonSyncJob")
    @PassToken
    public Result<String> hrGrantPersonSyncJob() {

        hrGrantPersonSyncJob.hrGrantPersonSyncJob();
        return Result.success("success");
    }

}
