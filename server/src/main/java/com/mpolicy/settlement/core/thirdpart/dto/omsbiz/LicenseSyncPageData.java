package com.mpolicy.settlement.core.thirdpart.dto.omsbiz;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 工商信息同步分页数据
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LicenseSyncPageData {

    /**
     * 结束行号
     */
    private Integer endRow;

    /**
     * 是否有下一页
     */
    private Boolean hasNextPage;

    /**
     * 是否有上一页
     */
    private Boolean hasPreviousPage;

    /**
     * 是否是第一页
     */
    private Boolean isFirstPage;

    /**
     * 是否是最后一页
     */
    private Boolean isLastPage;

    /**
     * 工商信息列表
     */
    private List<LicenseInfoDto> list;

    /**
     * 导航第一页
     */
    private Integer navigateFirstPage;

    /**
     * 导航最后一页
     */
    private Integer navigateLastPage;

    /**
     * 导航页数
     */
    private Integer navigatePages;

    /**
     * 导航页码数组
     */
    private List<Integer> navigatepageNums;

    /**
     * 下一页
     */
    private Integer nextPage;

    /**
     * 当前页码
     */
    private Integer pageNum;

    /**
     * 页大小
     */
    private Integer pageSize;

    /**
     * 总页数
     */
    private Integer pages;

    /**
     * 上一页
     */
    private Integer prePage;

    /**
     * 当前页数据量
     */
    private Integer size;

    /**
     * 开始行号
     */
    private Integer startRow;

    /**
     * 总记录数
     */
    private Integer total;
}
