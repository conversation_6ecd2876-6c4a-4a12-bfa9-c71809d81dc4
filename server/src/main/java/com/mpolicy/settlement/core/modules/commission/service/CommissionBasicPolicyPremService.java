package com.mpolicy.settlement.core.modules.commission.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.settlement.core.modules.commission.entity.CommissionBasicPolicyPremEntity;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CommissionBasicPolicyPremService extends IService<CommissionBasicPolicyPremEntity> {

    int batchInsert(List<CommissionBasicPolicyPremEntity> insertList);
    /**
     * 批量更新
     * @param updateList
     */
    void batchUpdateById(List<CommissionBasicPolicyPremEntity> updateList);
}
