package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementReconcileCompanySubjectProductDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileCompanySubjectProductEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcileCompanySubjectProductService;

/**
 * 关联险种
 *
 * <AUTHOR>
 * @date 2023-05-22 09:21:26
 */
@Slf4j
@Service("settlementReconcileCompanySubjectProductService")
public class SettlementReconcileCompanySubjectProductServiceImpl extends ServiceImpl<SettlementReconcileCompanySubjectProductDao, SettlementReconcileCompanySubjectProductEntity> implements SettlementReconcileCompanySubjectProductService {

}
