package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.policy.common.enums.EpPolicyDetailsReqOperationTypeEnum;
import com.mpolicy.policy.common.enums.PolicyPaymentTypeEnum;
import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.policy.common.ep.policy.EpInsuredInfoVo;
import com.mpolicy.policy.common.ep.policy.EpPersonalProductInfoVo;
import com.mpolicy.policy.common.ep.policy.EpProductInfoVo;
import com.mpolicy.policy.common.ep.policy.qry.details.GroupPreservationInsuredVo;
import com.mpolicy.product.common.base.ProductBase;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.enums.SettlementExceptionEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.ConfirmStatusEnum;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.*;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.CostBasicCommissionConfigDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.CostTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.PolicyProductTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.ProductStatusEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyProductPremInput;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyProductPremResult;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementCostGroupProcessService;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import com.mpolicy.settlement.core.utils.LambdaUtils;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.mpolicy.settlement.core.common.Constant.ZHNX_CHANNEL_CODE;
import static com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum.PERSONAL_NEW_POLICY;
import static com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum.STANDARD_SURRENDER;

@Service("settlementCostGroupProcessService")
@Slf4j
public class SettlementCostGroupProcessServiceImpl extends SettlementCostProcessServiceImpl implements SettlementCostGroupProcessService {

    public List<SettlementCostInfoEntity> builderNewGroupSettlementCostInfoByInsuredList(SettlementEventJobEntity eventJob,
                                                                                      CostSubjectEnum subjectEnum,
                                                                                      SettlementEventTypeEnum eventType,
                                                                                      Map<String, ProductBase> productMap,
                                                                                      EpContractInfoVo policyInfo, PolicyPreservationDetailDto preservationDetail){
        List<EpPreserveAddSubtractDetailDto> detailDtos = preservationDetail.getAddOrSubtractList();
        if (CollectionUtils.isEmpty(detailDtos)) {
            return Collections.EMPTY_LIST;
        }
        Integer insuranceType = getInsuranceType(eventType, policyInfo.getSourcePolicyNo());
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();

        Map<String,List<EpPreserveAddSubtractDetailDto>> addSubMap = LambdaUtils.groupBy(detailDtos,EpPreserveAddSubtractDetailDto::getPeopleType);
        //加人
        builderGroupAddCostInfoByInsuredList(eventJob,subjectEnum,eventType,productMap,policyInfo,preservationDetail,insuranceType,addSubMap.get("加人"),costInfoEntities);
        //减人
        builderGroupSubCostInfoByInsuredList(eventJob,subjectEnum,eventType,productMap,policyInfo,preservationDetail,insuranceType,addSubMap.get("减人"),costInfoEntities);

        return costInfoEntities;
    }

    public void builderGroupAddCostInfoByInsuredList(SettlementEventJobEntity eventJob,
                                                     CostSubjectEnum subjectEnum,
                                                     SettlementEventTypeEnum eventType,
                                                     Map<String, ProductBase> productMap,
                                                     EpContractInfoVo policyInfo, PolicyPreservationDetailDto preservationDetail,Integer insuranceType,
                                                     List<EpPreserveAddSubtractDetailDto> addInsuredList,List<SettlementCostInfoEntity> costInfoEntities){

        if(CollectionUtils.isEmpty(addInsuredList)){
            return ;
        }
        log.info("支出端--开始处理团险增员记录，记录数{}",addInsuredList.size());
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        Integer renewalYear = preservationDetail.getRenewalPeriod()!=null?preservationDetail.getRenewalPeriod():1;
        Integer renewalPeriod = renewalYear;
        addInsuredList.stream().forEach(insured->{
            log.info("支出端--开始计算被保人{}/{}的承保(增员)佣金",insured.getInsuredName(),insured.getInsuredCode());
            List<EpPreserveProductDto> personalProductLst = insured.getInsuredProductList();
            if (CollectionUtils.isEmpty(personalProductLst)) {
                log.warn("支出端-团险增员根据被保人信息计算支出端佣金明细-保单号={},被保人没有险种明细：{}", eventJob.getEventBusinessCode(), insured.getInsuredName());
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-团险增员根据被保人信息计算支出端佣金明细-保单号={},被保人没有险种明细：{}", eventJob.getEventBusinessCode(), insured.getInsuredName())));
            }

            for (EpPreserveProductDto product : personalProductLst) {
                PolicyProductPremInput input = commissionConfigService.buildBasicCommissionConfigParam(policyInfo, insuranceType, product,null, renewalYear, renewalPeriod);
                List<CostBasicCommissionConfigDto> configList = commissionConfigService.getCostBasicCommissionConfig(policyNo,PolicyProductTypeEnum.GROUP.getCode(), input);
                for (CostBasicCommissionConfigDto config : configList) {
                    //初始化，生成科目信息
                    SettlementCostInfoEntity bean = initSettlementCostInfo(eventJob, subjectEnum, eventType, policyInfo,insuranceType);
                    //记录佣金匹配时间，便于冲正流程中获取
                    bean.setCostConfigMatchTime(input.getApprovedTime());
                    //记账时间处理
                    bean.setSettlementTime(getBasicSettlementTime(eventType, policyInfo));
                    bean.setSettlementDate(bean.getSettlementTime());
                    //设置险种保费和实际用于计算的保费
                    bean.setProductPremium(product.getPremium());
                    bean.setPremium(product.getPremium());
                    bean.setBusinessPremium(product.getPremium());

                    if(Objects.nonNull(preservationDetail)) {
                        bean.setBusinessAccountTime(getGroupAddOrSubtractBusinessAccountTime(preservationDetail, 0));
                    }else{
                        bean.setBusinessAccountTime(getBusinessAccountTime(eventType,policyInfo,preservationDetail));
                    }
                    settlementCostOwnerService.builderGroupPolicyNewAddCostOwnerInfoByInsured(insured.getChannelCode(), insured, bean);

                    //佣金信息

                    calcBasicCommission(policyNo,insured.getAddEndorsementNo(), config, bean);
                    //确认信息
                    bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                    //被保人信息
                    bean.setInsuredCode(insured.getInsuredCode());
                    if(StringUtil.isNotBlank(insured.getInsuredBirthday())) {
                        bean.setInsuredBirthday(DateUtil.parseDate(insured.getInsuredBirthday()));
                    }
                    if(StringUtils.isNotBlank(insured.getInsuredGender())) {
                        bean.setInsuredGender(Objects.equals(insured.getInsuredGender(),"男")?1:0);
                    }
                    bean.setInsuredIdCard(insured.getInsuredIdCard());
                    bean.setInsuredMobile(insured.getInsuredMobile());
                    bean.setInsuredName(insured.getInsuredName());
                    //险种信息
                    builderCostProductInfo(policyInfo, productMap, product, bean, renewalYear, renewalPeriod);
                    //保全信息
                    if (Objects.nonNull(preservationDetail)) {
                        builderGroupAddOrSubtractPreservationInfo(preservationDetail, 1,null, bean);
                    }

                    //折算保费
                    builderDiscountPremium(policyNo,policyInfo.getPolicyProductType(),bean);
                    costInfoEntities.add(bean);
                }
            }
        });
        log.info("支出端--团险增员记录处理完成");
    }



    public void builderGroupSubCostInfoByInsuredList(SettlementEventJobEntity eventJob,
                                                     CostSubjectEnum subjectEnum,
                                                     SettlementEventTypeEnum eventType,
                                                     Map<String, ProductBase> productMap,
                                                     EpContractInfoVo policyInfo, PolicyPreservationDetailDto preservationDetail,Integer insuranceType,
                                                     List<EpPreserveAddSubtractDetailDto> subInsuredList,List<SettlementCostInfoEntity> costInfoEntities){
        if(CollectionUtils.isEmpty(subInsuredList)){
            return ;
        }
        Integer renewalYear = preservationDetail.getRenewalPeriod()!=null?preservationDetail.getRenewalPeriod():1;
        Integer renewalPeriod = renewalYear;
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        List<String> insuredCodes = subInsuredList.stream().map(EpPreserveAddSubtractDetailDto::getInsuredCode).collect(Collectors.toList());
        List<SettlementCostInfoEntity> listAddCostInfo = settlementCostInfoService.lambdaQuery().eq(SettlementCostInfoEntity::getPolicyNo,policyNo)
                .in(SettlementCostInfoEntity::getInsuredCode,insuredCodes)
                .eq(SettlementCostInfoEntity::getCorrectionFlag,0)
                .list();
        //校验历史记录是否已经存在退保记录，如果存在则跑出异常

        Optional<SettlementCostInfoEntity> existSurrender = listAddCostInfo.stream().filter(p->ProductStatusEnum.isSurrenderStatus(p.getProductStatus())).findFirst();
        if(existSurrender.isPresent()){
            String msg = StrUtil.format("支出端--团险减员保全，获取减员人员{}/{}的原始承保记录中存在已退保记录,基础佣金编码costCode={}",
                    existSurrender.get().getInsuredCode(),existSurrender.get().getInsuredName(),existSurrender.get().getCostCode());
            log.warn(msg);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(msg));
        }
        Map<String,List<SettlementCostInfoEntity>> addCostInfoMap = LambdaUtils.groupBy(listAddCostInfo,SettlementCostInfoEntity::getInsuredCode);


        subInsuredList.stream().forEach(insured-> {
            log.info("支出端--开始计算被保人{}/{}的退保(减员)佣金",insured.getInsuredName(),insured.getInsuredCode());
            List<EpPreserveProductDto> personalProductLst = insured.getInsuredProductList();
            if (CollectionUtils.isEmpty(personalProductLst)) {
                log.warn("支出端-团险减员根据被保人信息计算支出端佣金明细-保单号={},被保人没有险种明细：{}", eventJob.getEventBusinessCode(), insured.getInsuredName());
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-团险减员根据被保人信息计算支出端佣金明细-保单号={},被保人没有险种明细：{}", eventJob.getEventBusinessCode(), insured.getInsuredName())));
            }
            SettlementCostInfoEntity bean =null;
            for (EpPreserveProductDto product : personalProductLst) {
                List<SettlementCostInfoEntity> personalOldAddList = addCostInfoMap.get(insured.getInsuredCode());

                //找不到记录，则降级处理，从佣金配置中查佣金配置()
                if (CollectionUtils.isEmpty(personalOldAddList)) {
                    PolicyProductPremInput input = commissionConfigService.buildBasicCommissionConfigParam(policyInfo, insuranceType, product,null, renewalYear, renewalPeriod);
                    List<CostBasicCommissionConfigDto> configList = commissionConfigService.getCostBasicCommissionConfig(policyNo,PolicyProductTypeEnum.GROUP.getCode(), input);
                    for (CostBasicCommissionConfigDto config : configList) {
                        //初始化，生成科目信息
                        bean = initSettlementCostInfo(eventJob, subjectEnum, eventType, policyInfo,insuranceType);
                        //记录佣金匹配时间，便于冲正流程中获取
                        bean.setCostConfigMatchTime(input.getApprovedTime());
                        //记账时间处理
                        bean.setSettlementTime(getBasicSettlementTime(eventType, policyInfo));
                        bean.setSettlementDate(bean.getSettlementTime());
                        //设置险种保费和实际用于计算的保费
                        bean.setProductPremium(product.getPremium());
                        bean.setPremium(product.getSurrenderPremium());
                        bean.setBusinessPremium(product.getSurrenderPremium());
                        bean.setSurrenderAmount(product.getSurrenderPremium());

                        if(Objects.nonNull(preservationDetail)) {
                            bean.setBusinessAccountTime(getGroupAddOrSubtractBusinessAccountTime(preservationDetail, 0));
                        }else{
                            bean.setBusinessAccountTime(getBusinessAccountTime(eventType,policyInfo,preservationDetail));
                        }
                        settlementCostOwnerService.builderGroupPolicyNewAddCostOwnerInfoByInsured(insured.getChannelCode(), insured, bean);

                        //佣金信息

                        calcBasicCommission(policyNo,insured.getAddEndorsementNo(), config, bean);
                        //确认信息
                        bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                        //被保人信息
                        bean.setInsuredCode(insured.getInsuredCode());
                        if(StringUtil.isNotBlank(insured.getInsuredBirthday())) {
                            bean.setInsuredBirthday(DateUtil.parseDate(insured.getInsuredBirthday()));
                        }
                        if(StringUtils.isNotBlank(insured.getInsuredGender())) {
                            bean.setInsuredGender(Objects.equals(insured.getInsuredGender(),"男")?1:0);
                        }
                        bean.setInsuredIdCard(insured.getInsuredIdCard());
                        bean.setInsuredMobile(insured.getInsuredMobile());
                        bean.setInsuredName(insured.getInsuredName());
                        //险种信息
                        builderCostProductInfo(policyInfo, productMap, product, bean, renewalYear, renewalPeriod);
                        //保全信息
                        if (Objects.nonNull(preservationDetail)) {
                            builderGroupAddOrSubtractPreservationInfo(preservationDetail, 1,null, bean);
                        }

                        //折算保费
                        builderDiscountPremium(policyNo,policyInfo.getPolicyProductType(),bean);
                        costInfoEntities.add(bean);
                    }

                } else {
                    List<SettlementCostInfoEntity> personProductOldList = personalOldAddList.stream().filter(o -> Objects.equals(o.getProductCode(), product.getProductCode())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(personProductOldList)) {
                        String msg = StrUtil.format("支出端--团险减员保全，获取减员人员{}/{}的原始承保记录中找不到险种{}的承保记录",
                                insured.getInsuredCode(),insured.getInsuredName(),product.getProductCode());
                        log.warn(msg);
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(msg));
                    }
                    for (SettlementCostInfoEntity old : personProductOldList) {

                        bean = initSettlementCostInfo(eventJob, subjectEnum, eventType, policyInfo,insuranceType);
                        //记录佣金匹配时间，便于冲正流程中获取
                        bean.setCostConfigMatchTime(old.getCostConfigMatchTime());
                        //记账时间处理
                        bean.setSettlementTime(getBasicSettlementTime(eventType, policyInfo));
                        bean.setSettlementDate(bean.getSettlementTime());
                        //设置保费,退保保费为在结算表里为负数、和实际用于计算的保费
                        bean.setProductPremium(product.getPremium());
                        bean.setPremium(product.getSurrenderPremium());
                        bean.setBusinessPremium(product.getSurrenderPremium());
                        bean.setSurrenderAmount(product.getSurrenderPremium());
                        bean.setBusinessAccountTime(getGroupAddOrSubtractBusinessAccountTime(preservationDetail, 1));



                        //佣金信息
                        builderBasicCommission(old.getSettlementInstitution(), old.getSettlementInstitutionName(), bean.getPremium(), old.getCostRate(), old.getCostConfigKey(), CostTypeEnum.BASIC, bean);
                        bean.setSingleProposeFlag(old.getSingleProposeFlag());
                        bean.setGrantRate(old.getGrantRate());
                        bean.setGrantAmount(PolicySettlementUtils.calcAmtByPercent(bean.getCostAmount(), old.getGrantRate(), 2));
                        //初始化确认信息
                        bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                        //被保人信息
                        bean.setInsuredCode(old.getInsuredCode());
                        bean.setInsuredBirthday(old.getInsuredBirthday());
                        bean.setInsuredGender(old.getInsuredGender());
                        bean.setInsuredIdCard(old.getInsuredIdCard());
                        bean.setInsuredMobile(old.getInsuredMobile());
                        bean.setInsuredName(old.getInsuredName());


                        //险种信息
                        builderCostProductInfo(policyInfo,productMap, product, bean, renewalYear, renewalPeriod);
                        //保全信息
                        //保全信息
                        if (Objects.nonNull(preservationDetail)) {
                            builderGroupAddOrSubtractPreservationInfo(preservationDetail, 1,null, bean);
                        }
                        //归属人
                        settlementCostOwnerService.builderOwnerInfoByOldCostInfo(old, bean);
                        //折算保费
                        builderDiscountPremium(policyNo, policyInfo.getPolicyProductType(), bean);
                        //佣金配置是否可变(已确认 或者 被退保记录就是不可变（手工冲正）)
                        if (Objects.equals(old.getConfirmStatus(), ConfirmStatusEnum.CONFIRMED.getCode())
                                || Objects.equals(old.getImmutableFlag(), 1)) {
                            bean.setImmutableFlag(1);
                        }
                        costInfoEntities.add(bean);
                    }
                }
            }
        });


    }
    /**
     * 团险新契约/增减员 根据被保人明细记录生成支出端佣金明细
     *
     * @param eventJob
     * @param subjectEnum
     * @param eventType
     * @param productMap
     * @param policyInfo
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> builderGroupSettlementCostInfoByInsuredList(SettlementEventJobEntity eventJob,
                                                                                      CostSubjectEnum subjectEnum,
                                                                                      SettlementEventTypeEnum eventType,
                                                                                      Map<String, ProductBase> productMap,
                                                                                      EpContractInfoVo policyInfo, PolicyPreservationDetailDto preservationDetail,
                                                                                      List<GroupPreservationInsuredVo> insuredInfoVos) {

        if (CollectionUtils.isEmpty(insuredInfoVos)) {
            return Collections.EMPTY_LIST;
        }
        //团险续保，续投年期默认为2
        Integer renewalYear = Objects.equals(SettlementEventTypeEnum.RENEWAL_POLICY,eventType)?2:1;
        Integer renewalPeriod = renewalYear;
        Integer insuranceType = getInsuranceType(eventType, policyInfo.getSourcePolicyNo());
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();
        insuredInfoVos.stream().forEach(insured -> {
            List<EpPersonalProductInfoVo> personalProductLst = insured.getProductInfoList();
            if (CollectionUtils.isEmpty(personalProductLst)) {
                log.warn("支出端-团险根据被保人信息计算支出端佣金明细-保单号={},被保人没有险种明细：{}", eventJob.getEventBusinessCode(), insured.getInsuredName());
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-团险根据被保人信息计算支出端佣金明细-保单号={},被保人没有险种明细：{}", eventJob.getEventBusinessCode(), insured.getInsuredName())));
            }

            for (EpPersonalProductInfoVo product : personalProductLst) {
                /******佣金配置查询*******/
                PolicyProductPremInput input = buildBasicCommissionConfigParam(policyInfo, insuranceType, product,insured.getInsuredPolicyAge(), renewalYear, renewalPeriod);
                //保全，则查询佣金配置时间则是被保人生效时间，减员需要取被保人当时的生效时间
                if (preservationDetail != null) {
                    input.setApprovedTime(insured.getInsuredEffectiveTime());
                }
                List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(policyNo, input);

                /******遍历佣金配置*******/
                for (PolicyProductPremResult config : configList) {
                    //初始化，生成科目信息
                    SettlementCostInfoEntity bean = initSettlementCostInfo(eventJob, subjectEnum, eventType, policyInfo, insuranceType);
                    //记录佣金匹配时间，便于冲正流程中获取
                    bean.setCostConfigMatchTime(input.getApprovedTime());
                    //记账时间
                    bean.setSettlementTime(getBasicSettlementTime(eventType, policyInfo));
                    bean.setSettlementDate(bean.getSettlementTime());
                    //设置险种保费和实际用于计算的保费
                    bean.setProductPremium(product.getPremium());

                    if (Objects.equals(insured.getOperationType(), EpPolicyDetailsReqOperationTypeEnum.NEW.getCode())
                            || Objects.equals(insured.getOperationType(), EpPolicyDetailsReqOperationTypeEnum.ADD.getCode())) {

                        bean.setPremium(product.getPremium());
                        bean.setBusinessPremium(product.getPremium());
                    } else if (Objects.equals(insured.getOperationType(), EpPolicyDetailsReqOperationTypeEnum.BATCH.getCode())) {
                        bean.setPremium(product.getSurrenderAmount().negate());
                        bean.setBusinessPremium(product.getSurrenderAmount().negate());
                    } else {
                        log.warn("支出端-根据被保人信息计算支出端佣金明细-保单号={},不支持此被保人明细操作类型：{}", eventJob.getEventBusinessCode(), insured.getOperationType());
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-根据被保人信息计算支出端佣金明细-保单号={},不支持此被保人明细操作类型：{}", eventJob.getEventBusinessCode(), insured.getOperationType())));
                    }
                    //业务记账
                    //bean.setBusinessAccountTime(getBusinessAccountTime(eventType,policyInfo,preservationDetail));
                    if(Objects.nonNull(preservationDetail)) {
                        bean.setBusinessAccountTime(getGroupAddOrSubtractBusinessAccountTime(preservationDetail, insured.getSurrendered()));
                    }else{
                        bean.setBusinessAccountTime(getBusinessAccountTime(eventType,policyInfo,preservationDetail));
                    }
                    settlementCostOwnerService.builderGroupPolicyAddCostOwnerInfoByInsured(insured.getChannelCode(), insured, bean);

                    //佣金信息
                    calcBasicCommission(policyNo, insured.getAddEndorsementNo(), product, config, bean);
                    //确认信息
                    bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                    //被保人信息
                    bean.setInsuredCode(insured.getInsuredCode());
                    bean.setInsuredBirthday(insured.getInsuredBirthday());
                    bean.setInsuredGender(insured.getInsuredGender());
                    bean.setInsuredIdCard(insured.getInsuredIdCard());
                    bean.setInsuredMobile(insured.getInsuredMobile());
                    bean.setInsuredName(insured.getInsuredName());
                    //险种信息
                    builderCostProductInfo(policyInfo, productMap, product, bean, renewalYear, renewalPeriod);
                    //保全信息
                    if (Objects.nonNull(preservationDetail)) {
                        builderGroupAddOrSubtractPreservationInfo(preservationDetail, insured.getSurrendered(),null, bean);
                    }

                    //折算保费
                    builderDiscountPremium(policyNo,policyInfo.getPolicyProductType(),bean);
                    costInfoEntities.add(bean);
                }

            }

        });


        return costInfoEntities;
    }


    /**
     * 团险新契约、增减员 根据险种明细生成支出明细（没有被保人明细的团险）
     *
     * @param eventJob
     * @param subjectEnum
     * @param eventType
     * @param productMap
     * @param policyInfo
     * @param preservationDetail
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> builderGroupSettlementCostInfoByProductList(SettlementEventJobEntity eventJob,
                                                                                      CostSubjectEnum subjectEnum,
                                                                                      SettlementEventTypeEnum eventType,
                                                                                      Map<String, ProductBase> productMap,
                                                                                      EpContractInfoVo policyInfo,
                                                                                      PolicyPreservationDetailDto preservationDetail) {
        List<EpProductInfoVo> productInfoVos = policyInfo.getProductInfoList();
        if (CollectionUtils.isEmpty(productInfoVos)) {
            return Collections.EMPTY_LIST;
        }
        //团险续保，续投年期默认为2
        Integer renewalYear = Objects.equals(SettlementEventTypeEnum.RENEWAL_POLICY,eventType)?2:
                (Objects.equals(SettlementEventTypeEnum.GROUP_ADD_OR_SUBTRACT,eventType) && preservationDetail.getRenewalPeriod()!=null
                        ?preservationDetail.getRenewalPeriod():1);
        Integer renewalPeriod = renewalYear;

        Integer insuranceType = getInsuranceType(eventType, policyInfo.getSourcePolicyNo());
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();
        BigDecimal dynamicPremium = BigDecimal.ZERO;
        BigDecimal totalPremium = productInfoVos.stream().map(EpProductInfoVo::getPremium).reduce(BigDecimal.ZERO, BigDecimal::add);


        //按先主险，后附加险的顺序排序，然后进行佣金计算
        productInfoVos = productInfoVos.stream().sorted(Comparator.comparing(EpProductInfoVo::getMainInsurance).reversed()).collect(Collectors.toList());
        int size = productInfoVos.size();
        for (int i = 0; i < size; i++) {

            EpProductInfoVo product = productInfoVos.get(i);
            //按险种维度分摊团险保费(保全新单保费/增减员保费)
            BigDecimal premium;
            //保全为空则为新保单

            if (preservationDetail != null) {
                if (preservationDetail.getSurrenderCash().compareTo(dynamicPremium) == 0) {
                    break;
                }
                premium = preservationDetail.getSurrenderCash().multiply(product.getPremium()).divide(totalPremium, 2, BigDecimal.ROUND_HALF_UP);
                //preservationDetail.getSurrenderCash()存在为负数，比较大小用绝对值比较
                if (dynamicPremium.abs().add(premium.abs()).compareTo(preservationDetail.getSurrenderCash().abs()) > 0 || i == size - 1) {
                    premium = preservationDetail.getSurrenderCash().subtract(dynamicPremium);
                }
                dynamicPremium = dynamicPremium.add(premium);
            } else {
                premium = product.getPremium();
            }


            //查配置输入参数
            PolicyProductPremInput input = buildBasicCommissionConfigParam(policyInfo, insuranceType, product,null, renewalYear, renewalPeriod);
            input.setApprovedTime(product.getEffectiveDate());
            List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(policyNo, input);

            //遍历佣金配置
            for (PolicyProductPremResult config : configList) {
                //初始化，生成科目信息
                SettlementCostInfoEntity bean = initSettlementCostInfo(eventJob, subjectEnum, eventType, policyInfo,insuranceType);
                //记录佣金匹配时间，便于冲正流程中获取
                bean.setCostConfigMatchTime(input.getApprovedTime());
                //记账时间处理
                bean.setSettlementTime(getBasicSettlementTime(eventType, policyInfo));
                bean.setSettlementDate(bean.getSettlementTime());
                //设置险种保费和实际用于计算的保费
                bean.setProductPremium(product.getPremium());
                bean.setPremium(premium);
                bean.setBusinessPremium(premium);
                bean.setBusinessAccountTime(getBusinessAccountTime(eventType,policyInfo,preservationDetail));
                //佣金信息
                calcBasicCommission(policyNo, preservationDetail != null ? preservationDetail.getEndorsementNo() : null, product, config, bean);
                //初始化确认信息
                bean.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                //险种信息
                builderCostProductInfo(policyInfo, productMap, product, bean, renewalYear ,renewalPeriod);
                //保全信息
                if (preservationDetail != null) {
                    builderPreservationInfo(preservationDetail, null, bean);
                }
                //折算保费
                builderDiscountPremium(policyNo,policyInfo.getPolicyProductType(),bean);
                costInfoEntities.add(bean);
            }

        }

        //设置佣金归属人信息并返回
        if (preservationDetail != null) {
            return settlementCostOwnerService.builderPreservationCostOwnerInfo(preservationDetail, costInfoEntities);
        } else {
            return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);
        }
    }
}
