package com.mpolicy.settlement.core.modules.commission.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.modules.commission.dao.CommissionBasicInfoDao;
import com.mpolicy.settlement.core.modules.commission.entity.CommissionBasicInfoEntity;
import com.mpolicy.settlement.core.modules.commission.service.CommissionBasicInfoService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service("commissionBasicInfoService")
public class CommissionBasicInfoServiceImpl extends ServiceImpl<CommissionBasicInfoDao, CommissionBasicInfoEntity> implements CommissionBasicInfoService {

}
