package com.mpolicy.settlement.core.modules.reconcile.service;

import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.policy.common.ep.policy.EpInsuredInfoVo;
import com.mpolicy.policy.common.ep.policy.EpProductInfoVo;
import com.mpolicy.policy.common.ep.policy.qry.details.GroupPreservationInsuredVo;
import com.mpolicy.product.common.base.ProductBase;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.PolicyPreservationDetailDto;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.BasicCostEventDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 支出端计算过程服务
 * @date 2023/8/16 2:48 下午
 * @Version 1.0
 */
public interface SettlementCostCalcProcessService {
    /**
     * 参数验证
     * @param eventJob
     * @param policyInfo
     */
    void validParam(SettlementEventJobEntity eventJob, EpContractInfoVo policyInfo, Runnable validCustomize);
    /**
     * 获取险种配置信息，返回以险种编码为主键的map对象
     * @param productInfoVos
     * @return 险种编码为主键的map对象
     */
    Map<String, ProductBase> getProductBaseMap(List<EpProductInfoVo> productInfoVos);

    /**
     * 保存支出端佣金记录
     * @param eventJob
     * @param policy
     * @param costInfoList
     */
    void saveCostCommissionRecord(SettlementEventJobEntity eventJob, SettlementCostPolicyInfoEntity policy, List<SettlementCostInfoEntity> costInfoList);

    /**
     * 支出端-创建保单基础信息
     * @param policyInfo
     * @return
     */
    SettlementCostPolicyInfoEntity buildSettlementCostPolicyInfo(EpContractInfoVo policyInfo);

    /**
     * 组装退保更新保单基础信息
     * @param policyInfo
     * @param entity
     */
    void builderUpdateSettlementCostPolicyInfo(EpContractInfoVo policyInfo,SettlementCostPolicyInfoEntity entity);

    /**
     * 支出端-根据险种列表生成佣金记录(车险、没有被保人明细的团险)
     * @param eventDto     事件及科目信息
     * @param productMap   险种信息
     * @param policyInfo   保单信息
     * @return
     */
    List<SettlementCostInfoEntity> builderSettlementCostInfoByProductList(BasicCostEventDto eventDto, Map<String,ProductBase> productMap, EpContractInfoVo policyInfo);
    /**
     * 支出端-团险根据被保人列表生成佣金记录
     * @param eventDto     事件及科目信息
     * @param productMap
     * @param policyInfo
     * @param preservationDetail
     * @return
     */
    List<SettlementCostInfoEntity> builderGroupSettlementCostInfoByProductList(BasicCostEventDto eventDto,
                                                                               Map<String,ProductBase> productMap,
                                                                               EpContractInfoVo policyInfo,
                                                                               PolicyPreservationDetailDto preservationDetail);
    /**
     * 支出端-根据被保人列表生成佣金记录
     * @param eventDto     事件信息
     * @param productMap   险种信息
     * @param policyInfo   保单信息
     * @return
     */
    List<SettlementCostInfoEntity> builderSettlementCostInfoByInsuredList(BasicCostEventDto eventDto, Map<String,ProductBase> productMap, EpContractInfoVo policyInfo, List<EpInsuredInfoVo> insuredInfoVos);

    /**
     * 支出端-根据被保人列表生成佣金记录
     * @param eventDto     事件及科目信息
     * @param productMap   险种信息
     * @param policyInfo   保单信息
     * @return
     */
    List<SettlementCostInfoEntity> builderRenewalSettlementCostInfoByInsuredList(BasicCostEventDto eventDto, Map<String,ProductBase> productMap, EpContractInfoVo policyInfo, List<EpInsuredInfoVo> insuredInfoVos);

    /**
     *
     * @param eventDto     事件及科目信息
     * @param productMap
     * @param policyInfo
     * @param preservationDetail
     * @param insuredInfoVos
     * @return
     */
    List<SettlementCostInfoEntity> builderGroupSettlementCostInfoByInsuredList(BasicCostEventDto eventDto,
                                                                               Map<String,ProductBase> productMap,
                                                                               EpContractInfoVo policyInfo, PolicyPreservationDetailDto preservationDetail, List<GroupPreservationInsuredVo> insuredInfoVos);

    /**
     * 附加险解约
     * @param eventDto     事件及科目信息
     * @param productMap
     * @param policyInfo
     * @param preservationDetail
     * @return
     */
    List<SettlementCostInfoEntity> builderPolicyTerminationProductCostInfo(BasicCostEventDto eventDto,
                                                                           Map<String,ProductBase> productMap,
                                                                           EpContractInfoVo policyInfo,
                                                                           PolicyPreservationDetailDto preservationDetail);

    /**
     * 犹豫期退保
     * @param eventDto     事件及科目信息
     * @param productMap
     * @param policyInfo
     * @param preservationDetail
     * @return
     */
    List<SettlementCostInfoEntity> builderPolicyHesitateSurrenderCostInfo(BasicCostEventDto eventDto,
                                                                          Map<String,ProductBase> productMap,
                                                                          EpContractInfoVo policyInfo,
                                                                          PolicyPreservationDetailDto preservationDetail);

    /**
     * 标准退保
     * @param eventDto     事件及科目信息
     * @param productMap
     * @param policyInfo
     * @param preservationDetail
     * @return
     */
    List<SettlementCostInfoEntity> builderStandardSurrenderCostInfo(BasicCostEventDto eventDto,
                                                                    Map<String,ProductBase> productMap,
                                                                    EpContractInfoVo policyInfo,
                                                                    PolicyPreservationDetailDto preservationDetail);

    /**
     * 协议解约
     * @param eventDto     事件及科目信息
     * @param productMap
     * @param policyInfo
     * @param preservationDetail
     * @return
     */
    List<SettlementCostInfoEntity> builderProtocolTerminationCostInfo(BasicCostEventDto eventDto,
                                                                      Map<String,ProductBase> productMap,
                                                                      EpContractInfoVo policyInfo,
                                                                      PolicyPreservationDetailDto preservationDetail);
}
