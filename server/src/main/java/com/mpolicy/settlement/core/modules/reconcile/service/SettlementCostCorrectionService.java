package com.mpolicy.settlement.core.modules.reconcile.service;

import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.product.common.base.ProductBase;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.govern.dto.proejct.ChangePolicyReferrerData;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.GroupPolicyRecommenderChangeDto;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.PolicyPreservationDetailDto;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.PolicyRecommenderChangeDto;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.PreservationCustomerChangeDto;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.CostCorrectionDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 结算支出端冲正服务
 *
 * <AUTHOR>
 */
public interface SettlementCostCorrectionService {
    /**
     * 费率变更冲正
     * @param eventJob
     * @param eventType
     * @param newBusinessTime
     * @param contactCodes
     * @param deleteKeys
     * @return
     */
    CostCorrectionDto builderConfigChangeCostInfo(SettlementEventJobEntity eventJob,
                                                  SettlementEventTypeEnum eventType,
                                                  Date newBusinessTime,
                                                  List<String> contactCodes,
                                                  List<String> deleteKeys);

    /**
     * 一单一议费率变更冲正
     * @param eventJob
     * @param eventType
     * @param contactCodes
     * @param configKeys
     * @return
     */
    CostCorrectionDto builderPolicyPremChangeCostInfo(SettlementEventJobEntity eventJob,
                                                      SettlementEventTypeEnum eventType,
                                                      Date newBusinessTime,
                                                      List<String> contactCodes,List<String> configKeys);

    /**
     * 初始推荐人变更冲正
     * @param eventJob
     * @param eventType
     * @param costPolicy
     * @param preservationDetail
     * @return
     */
    CostCorrectionDto builderCustomerManagerChangeCostInfo(SettlementEventJobEntity eventJob,
                                                           SettlementEventTypeEnum eventType,
                                                           SettlementCostPolicyInfoEntity costPolicy,
                                                           PolicyPreservationDetailDto preservationDetail);

    CostCorrectionDto builderChangeEventCustomerManagerChangeCostInfo(SettlementEventJobEntity eventJob,
                                                                      SettlementEventTypeEnum eventType,
                                                                      SettlementCostPolicyInfoEntity costPolicy,
                                                                      ChangePolicyReferrerData policyReferrerData);

    /**
     * 分单推荐人变更冲正
     * @param eventJob
     * @param eventType
     * @param costPolicy
     * @param preservationDetail
     * @return
     */
    CostCorrectionDto builderSplitCustomerManagerChangeCostInfo(SettlementEventJobEntity eventJob,
                                                                SettlementEventTypeEnum eventType,
                                                                SettlementCostPolicyInfoEntity costPolicy,
                                                                PolicyPreservationDetailDto preservationDetail);
    /**
     * 分单渠道推荐人（对应农保管护经理)导入变更(非保全逻辑)
     * @param eventJob
     * @param eventType
     * @param costPolicy
     * @param changeDto
     * @return
     */
    CostCorrectionDto builderGroupPolicyRecommenderChangeCostInfo(SettlementEventJobEntity eventJob,
                                                                  SettlementEventTypeEnum eventType,
                                                                  SettlementCostPolicyInfoEntity costPolicy,
                                                                  GroupPolicyRecommenderChangeDto changeDto);

    /**
     * 保单维度渠道推荐人（对应农保管护经理)导入变更(非保全逻辑)
     * @param eventJob
     * @param eventType
     * @param costPolicy
     * @param changeDto
     * @return
     */
    CostCorrectionDto builderPolicyRecommenderChangeCostInfo(SettlementEventJobEntity eventJob,
                                                             SettlementEventTypeEnum eventType,
                                                             SettlementCostPolicyInfoEntity costPolicy,
                                                             PolicyRecommenderChangeDto changeDto);

    /**
     * 续期保单-推荐人变更事件
     * @param eventJob
     * @param eventType
     * @param costPolicy
     * @param changeDto
     * @return
     */
    CostCorrectionDto builderPolicyTermReferrerChangeCostInfo(SettlementEventJobEntity eventJob,
                                                              SettlementEventTypeEnum eventType,
                                                              SettlementCostPolicyInfoEntity costPolicy,
                                                              PolicyRecommenderChangeDto changeDto);

    /**
     * 基于事件触发创建结算支出端佣金记录
     * @param eventJob
     * @param costCorrectionDto
     */
    void saveCostCommissionRecord(SettlementEventJobEntity eventJob, CostCorrectionDto costCorrectionDto);

    /**
     *
     * @param costCorrectionDto
     */
    void saveCostCommission(CostCorrectionDto costCorrectionDto);

    public SettlementCostInfoEntity builderCustomerManagerChangeNewCostInfo(SettlementEventJobEntity eventJob,
                                                                            SettlementEventTypeEnum eventType,
                                                                            SettlementCostInfoEntity old,
                                                                            Date newBusinessTime,
                                                                            PreservationCustomerChangeDto afterCustomerManager);

    CostCorrectionDto builderProductChangeEventCorrectionCostInfo(SettlementEventJobEntity eventJob,
                                                                  CostSubjectEnum subjectEnum,
                                                                  SettlementEventTypeEnum eventType,
                                                                  EpContractInfoVo policyInfo,
                                                                  Map<String, ProductBase> productMap,
                                                                  SettlementCostPolicyInfoEntity costPolicy,
                                                                  PolicyPreservationDetailDto preservationDetail);

    CostCorrectionDto builderChannelChangeEventCorrectionCostInfo(SettlementEventJobEntity eventJob,
                                                                  CostSubjectEnum subjectEnum,
                                                                  SettlementEventTypeEnum eventType, EpContractInfoVo policyInfo,
                                                                  Map<String, ProductBase> productMap,
                                                                  SettlementCostPolicyInfoEntity costPolicy,
                                                                  PolicyPreservationDetailDto preservationDetail);
}
