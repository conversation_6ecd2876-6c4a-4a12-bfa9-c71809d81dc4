package com.mpolicy.settlement.core.modules.reconcile.service;

import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.policy.common.ep.policy.EpProductInfoVo;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.EpPreserveProductDto;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.CostBasicCommissionConfigDto;
import com.mpolicy.settlement.core.modules.reconcile.enums.PolicyProductTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyProductPremInput;

import java.util.List;
/**
 * <AUTHOR>
 * @description 支出端基础佣金费率配置服务
 * @date 2023/8/16 2:48 下午
 * @Version 1.0
 */
public interface CostBasicCommissionConfigService {
    /**
     * 支出端-组装获取基础佣金配置的入参(被保人参数+续期参数除外)
     * @param policyInfo
     * @param insuranceType
     * @param product
     * @param insuredPolicyAge
     * @param renewalYear
     * @param renewalPeriod
     * @return
     */
    PolicyProductPremInput buildBasicCommissionConfigParam(EpContractInfoVo policyInfo,
                                                           Integer insuranceType,
                                                           EpProductInfoVo product,
                                                           Integer insuredPolicyAge,
                                                           Integer renewalYear,
                                                           Integer renewalPeriod);

    /**
     * 支出端-组装获取基础佣金配置的入参(被保人参数+续期参数除外)
     * @param policyInfo
     * @param insuranceType
     * @param product
     * @param insuredPolicyAge
     * @param renewalYear
     * @param renewalPeriod
     * @return
     */
    PolicyProductPremInput buildBasicCommissionConfigParam(EpContractInfoVo policyInfo,
                                                           Integer insuranceType,
                                                           EpPreserveProductDto product,
                                                           Integer insuredPolicyAge,
                                                           Integer renewalYear,
                                                           Integer renewalPeriod);
    /**
     * 获取基础佣金配置
     * @param policyNo
     * @param input
     * @return
     */
    List<CostBasicCommissionConfigDto> getCostBasicCommissionConfig(String policyNo, String policyProductType, PolicyProductPremInput input);

    /**
     * 获取基础佣金配置
     * @param policyNo
     * @param endorsementNo
     * @param input
     * @return
     */
    List<CostBasicCommissionConfigDto> getCostBasicCommissionConfig(String policyNo,String endorsementNo,String policyProductType, PolicyProductPremInput input);

    List<CostBasicCommissionConfigDto> listCostBasicCommissionConfig(String policyNo,String endorsementNo, PolicyProductPremInput input);
}
