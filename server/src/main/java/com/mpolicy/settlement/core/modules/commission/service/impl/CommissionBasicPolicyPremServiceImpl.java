package com.mpolicy.settlement.core.modules.commission.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.common.Constant;
import com.mpolicy.settlement.core.modules.commission.dao.CommissionBasicPolicyPremDao;
import com.mpolicy.settlement.core.modules.commission.entity.CommissionBasicPolicyPremEntity;
import com.mpolicy.settlement.core.modules.commission.service.CommissionBasicPolicyPremService;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.mpolicy.settlement.core.common.Constant.MAX_BATCH_INSERT_NUM;

/**
 * <AUTHOR>
 */
@Service("commissionBasicPolicyPremService")
public class CommissionBasicPolicyPremServiceImpl extends ServiceImpl<CommissionBasicPolicyPremDao, CommissionBasicPolicyPremEntity> implements CommissionBasicPolicyPremService {


    public int batchInsert(List<CommissionBasicPolicyPremEntity> insertList){
        if(CollectionUtils.isEmpty(insertList)){
            return 0;
        }
        int result = 0;
        // 批量写入
        if (insertList.size() > MAX_BATCH_INSERT_NUM) {
            List<List<CommissionBasicPolicyPremEntity>> partition = ListUtils.partition(insertList, MAX_BATCH_INSERT_NUM);
            for (List<CommissionBasicPolicyPremEntity> x : partition) {
                result = result + baseMapper.insertBatchSomeColumn(x);
            }
        } else {
            result = baseMapper.insertBatchSomeColumn(insertList);
        }
        return result;
    }
    public void batchUpdateById(List<CommissionBasicPolicyPremEntity> updateList){
        if(CollectionUtils.isEmpty(updateList)){
            return ;
        }
        updateBatchById(updateList);
    }
}
