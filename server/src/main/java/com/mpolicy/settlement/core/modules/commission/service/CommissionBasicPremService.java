package com.mpolicy.settlement.core.modules.commission.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.settlement.core.modules.commission.entity.CommissionBasicPremEntity;

import java.util.List;

/**
 * 基础佣金费率
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-31 18:39:39
 */
public interface CommissionBasicPremService extends IService<CommissionBasicPremEntity> {
    /**
     * 批量更新监测编码
     * @param list
     * @return
     */
    Integer batchUpdateMonitorCode(List<CommissionBasicPremEntity> list);
}

