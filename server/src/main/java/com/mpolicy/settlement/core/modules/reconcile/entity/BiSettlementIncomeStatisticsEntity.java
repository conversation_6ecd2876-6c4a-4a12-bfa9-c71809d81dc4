package com.mpolicy.settlement.core.modules.reconcile.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName("bi_settlement_income_statistics")
public class BiSettlementIncomeStatisticsEntity implements Serializable {
    private static final long serialVersionUID = 9128268480611602244L;

    /**
     * 结构编码
     */
    @TableId
    private Integer id;
    private String orgCode;
    /**
     * 机构名称
     */
    private String orgName;
    /**
     * 日期
     */
    private String dateStr;

    private BigDecimal dayApplyRevenue = BigDecimal.ZERO;
    private BigDecimal dayRenewalTermRevenue = BigDecimal.ZERO;
    private BigDecimal dayTotalRevenue = BigDecimal.ZERO;

    private BigDecimal weekApplyRevenue = BigDecimal.ZERO;
    private BigDecimal weekRenewalTermRevenue = BigDecimal.ZERO;
    private BigDecimal weekTotalRevenue = BigDecimal.ZERO;

    private BigDecimal monthApplyRevenue = BigDecimal.ZERO;
    private BigDecimal monthRenewalTermRevenue = BigDecimal.ZERO;
    private BigDecimal monthTotalRevenue = BigDecimal.ZERO;

    private BigDecimal yearApplyRevenue = BigDecimal.ZERO;
    private BigDecimal yearRenewalTermRevenue = BigDecimal.ZERO;
    private BigDecimal yearTotalRevenue = BigDecimal.ZERO;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

}
