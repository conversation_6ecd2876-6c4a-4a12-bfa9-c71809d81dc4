package com.mpolicy.settlement.core.modules.reconcile.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("bi_settlement_income_regulatory_submit")
public class BiSettlementIncomeRegulatorySubmitEntity implements Serializable {

    private static final long serialVersionUID = 558332039048879576L;
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 单据明细唯一编号
     */
    private String settlementCode;

    /**
     * 处理日期天(yyyy-MM-dd)
     */
    private String dayStr;

    /**
     * 结算类型0:小鲸 1:非小鲸
     */
    private Integer reconcileType;

    /**
     * 科目编码
     */
    private String settlementSubjectCode;

    /**
     * 科目名称
     */
    private String settlementSubjectName;

    /**
     * 保单合同号
     */
    private String contractCode;

    /**
     * 保单号
     */
    private String policyNo;

    /**
     * 第三方保单号
     */
    private String thirdPolicyNo;

    /**
     * 保单产品类型;个团车财
     */
    private String policyProductType;

    /**
     * 保险公司编码
     */
    private String companyCode;

    /**
     * 保险公司名称
     */
    private String companyName;

    /**
     * 销售方式;0:网销 1:线下
     */
    private Byte salesType;

    /**
     * 销售平台
     */
    private String salesPlatform;

    /**
     * 险种编码
     */
    private String productCode;

    /**
     * 险种名称
     */
    private String productName;

    /**
     * 险种大类
     */
    private String productGroup;

    /**
     * 二级分类编码
     */
    private String level2Code;

    /**
     * 三级分类编码
     */
    private String level3Code;
    /**
     * 二级分类名称
     */
    private String level2Name;

    /**
     * 三级分类名称
     */
    private String level3Name;

    /**
     * 险种类型
     */
    private String productType;

    /**
     * 续期年期
     */
    private Integer renewalYear;

    /**
     * 续期期数
     */
    private Integer renewalPeriod;

    /**
     * 保费
     */
    private BigDecimal premium;

    /**
     * 保全申请编号
     */
    private String preservationCode;

    /**
     * 批单号
     */
    private String endorsementNo;

    /**
     * 结算金额
     */
    private BigDecimal settlementAmount;

    /**
     * 结算月份
     */
    private String settlementMonth;

    /**
     * 完成对账单号
     */
    private String reconcileCode;

    /**
     * 对账完成时间
     */
    private Date reconcileTime;

    /**
     * 投保类型0:新单  1:续期
     */
    private Integer insuranceType;

    /**
     * 报送机构编码
     */
    private String reportOrgCode;

    /**
     * 报送机构名称
     */
    private String reportOrgName;
    /**
     * 报送机构编码
     */
    private String lawType;

    /**
     * 报送机构名称
     */
    private String lawTypeDesc;
    /**
     * 是否仅报送收入 0:否 1:是
     */
    private Integer onlyReportIncome;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}