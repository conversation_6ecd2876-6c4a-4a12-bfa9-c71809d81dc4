package com.mpolicy.settlement.core.modules.reconcile.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.settlement.core.common.reconcile.RefreshSettlementEventJobVo;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;

import java.util.Date;
import java.util.List;

/**
 * 结算交互事件受理表
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:33
 */
public interface SettlementEventJobService extends IService<SettlementEventJobEntity> {
    /**
     * 重新刷新对账job
     * @param params
     */
    void refreshSettlementEventJob(RefreshSettlementEventJobVo params);

    /**
     * 查询某个时间端后面的失败记录
     * @param startTime
     * @return
     */
    List<SettlementEventJobEntity> listSettlementCostFailEventJob(Date startTime);

    /**
     * 查询某个时间端后面的记录
     * @param startTime
     * @return
     */
    List<SettlementEventJobEntity> listSettlementCostEventJob(Date startTime);

    /**
     *
     * @param startTime
     * @return
     */
    List<SettlementEventJobEntity> listSettlementEventFailUnCalcCostEventJob(Date startTime);

    /**
     * 根据错误编码查询失败记录
     * @param costErrorCode
     * @return
     */
    List<SettlementEventJobEntity> listSettlementCostFailRecordByCostErrorCode(String costErrorCode);
}

