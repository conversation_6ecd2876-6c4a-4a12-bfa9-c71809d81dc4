package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.common.Constant;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.autocost.dto.CostAutoRecord;
import com.mpolicy.settlement.core.modules.autocost.dto.SettlementCostInfoDto;
import com.mpolicy.settlement.core.modules.autocost.dto.base.SettlementCostQuery;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostAutoRecordEntity;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementCostInfoDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.CommissionTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.CostTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementCostInfoService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.C;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.mpolicy.settlement.core.common.Constant.MAX_BATCH_INSERT_NUM;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/16 3:15 上午
 * @Version 1.0
 */
@Slf4j
@Service("settlementCostInfoService")
public class SettlementCostInfoServiceImpl  extends ServiceImpl<SettlementCostInfoDao, SettlementCostInfoEntity> implements SettlementCostInfoService {
    /**
     * 根据保单号和事件编码获取支出佣金记录个数
     * @param policyNo
     * @param settlementEventCode
     */
    @Override
    public Integer countEventCostInfoByPolicyNo(String policyNo, String settlementEventCode,Integer renewalPeriod) {
        return baseMapper.countEventCostInfoByPolicyNo(policyNo, settlementEventCode,renewalPeriod);
    }

    /**
     * 根据源事件编号获取支出佣金记录个数
     * @param eventSourceCode
     */
    @Override
    public Integer countEventCostInfoByEventSourceCode(String eventSourceCode) {
        return baseMapper.countEventCostInfoByEventSourceCode(eventSourceCode);
    }

    /**
     * 根据合同号查询未冲正数据，已冲正数据已经一正一负抵消了
     * @param contractCode
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> listUnCorrectionCostInfoByContractCode(String contractCode){
        return lambdaQuery().eq(SettlementCostInfoEntity::getContractCode,contractCode)
                     .eq(SettlementCostInfoEntity::getCorrectionFlag,0)
                     .in(SettlementCostInfoEntity::getCostType, Arrays.asList(CostTypeEnum.BASIC.getCode(),CostTypeEnum.ADD.getCode()))
                     .list();

    }

    /**
     * 根据合同号列表查询未冲正数据且未确认的数据（一单一议变更用）
     * @param contractCodes
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> listUnCorrectionCostInfoByContractCodes(List<String> contractCodes){
        if(contractCodes.size()> Constant.MAX_QUERY_SIZE){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("超过按合同编号最大查询记录数，最大查询记录数为{}", Constant.MAX_QUERY_SIZE)));
        }
        return lambdaQuery().in(SettlementCostInfoEntity::getContractCode,contractCodes)
                .eq(SettlementCostInfoEntity::getCorrectionFlag,0)
                .eq(SettlementCostInfoEntity::getConfirmStatus,0)
                .in(SettlementCostInfoEntity::getCommissionType,Arrays.asList(CommissionTypeEnum.COMMON.getCode(),
                        CommissionTypeEnum.CORRECT_INC_OR_DEC.getCode(),
                        CommissionTypeEnum.RENEWAL.getCode(),
                        CommissionTypeEnum.CORRECT_SURRENDER.getCode(), CommissionTypeEnum.CORRECT_SPECIAL_DEDUCTION.getCode()))
                .in(SettlementCostInfoEntity::getCostType, Arrays.asList(CostTypeEnum.BASIC.getCode()))
                .list();

    }

    @Override
    public List<SettlementCostInfoEntity> listUnCorrectionCostInfoByPolicyNos(List<String> policyNos){
        if(policyNos.size()> Constant.MAX_QUERY_SIZE){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("超过按保单号最大查询记录数，最大查询记录数为{}", Constant.MAX_QUERY_SIZE)));
        }
        return lambdaQuery().in(SettlementCostInfoEntity::getPolicyNo,policyNos)
                .eq(SettlementCostInfoEntity::getCorrectionFlag,0)
                .in(SettlementCostInfoEntity::getCommissionType,Arrays.asList(CommissionTypeEnum.COMMON.getCode(),
                        CommissionTypeEnum.CORRECT_INC_OR_DEC.getCode(),
                        CommissionTypeEnum.RENEWAL.getCode(),
                        CommissionTypeEnum.CORRECT_SURRENDER.getCode(), CommissionTypeEnum.CORRECT_SPECIAL_DEDUCTION.getCode()))
                .in(SettlementCostInfoEntity::getCostType, Arrays.asList(CostTypeEnum.BASIC.getCode()))
                .list();

    }



    /**
     * 根据合同号查询未冲正数据，已冲正数据已经一正一负抵消了，(分单推荐人变更）
     * 注意：分单推荐人变更，需要处理历史已经确认的单子
     * @param contractCode
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> listUnCorrectionCostInfoByInsuredCodes(String contractCode,List<String> insuredCodes){
        return lambdaQuery().eq(SettlementCostInfoEntity::getContractCode,contractCode)
                .eq(SettlementCostInfoEntity::getCorrectionFlag,0)
               // .eq(SettlementCostInfoEntity::getConfirmStatus,0)
                .in(CollectionUtils.isNotEmpty(insuredCodes),SettlementCostInfoEntity::getInsuredCode,insuredCodes)
                .in(SettlementCostInfoEntity::getCommissionType,Arrays.asList(CommissionTypeEnum.COMMON.getCode(),
                        CommissionTypeEnum.CORRECT_INC_OR_DEC.getCode(),
                        CommissionTypeEnum.RENEWAL.getCode(),
                        CommissionTypeEnum.CORRECT_SURRENDER.getCode(), CommissionTypeEnum.CORRECT_SPECIAL_DEDUCTION.getCode()))
                .in(SettlementCostInfoEntity::getCostType, Arrays.asList(CostTypeEnum.BASIC.getCode()))
                .list();

    }

    /**
     * 根据合同号查询未冲正数据，已冲正数据已经一正一负抵消了
     * @param contractCode
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> listUnCorrectionGroupPolicyCostInfoByInsuredCodes(String contractCode,List<String> insuredCodes){
        return lambdaQuery().eq(SettlementCostInfoEntity::getContractCode,contractCode)
                .eq(SettlementCostInfoEntity::getCorrectionFlag,0)
                .in(CollectionUtils.isNotEmpty(insuredCodes),SettlementCostInfoEntity::getInsuredCode,insuredCodes)
                .in(SettlementCostInfoEntity::getCommissionType,Arrays.asList(CommissionTypeEnum.COMMON.getCode(),
                        CommissionTypeEnum.CORRECT_INC_OR_DEC.getCode(),
                        CommissionTypeEnum.RENEWAL.getCode(),
                        CommissionTypeEnum.CORRECT_SURRENDER.getCode(), CommissionTypeEnum.CORRECT_SPECIAL_DEDUCTION.getCode()))
                .in(SettlementCostInfoEntity::getCostType, Arrays.asList(CostTypeEnum.BASIC.getCode()))
                .list();

    }

    /**
     * 根据合同号查询未冲正数据，已冲正数据已经一正一负抵消了
     * @param contractCode
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> listUnCorrectionPolicyCostInfoByProductCodes(String contractCode,List<String> productCodes){
        return lambdaQuery().eq(SettlementCostInfoEntity::getContractCode,contractCode)
                .eq(SettlementCostInfoEntity::getCorrectionFlag,0)
                .in(CollectionUtils.isNotEmpty(productCodes),SettlementCostInfoEntity::getProductCode,productCodes)
                .in(SettlementCostInfoEntity::getCommissionType,Arrays.asList(CommissionTypeEnum.COMMON.getCode(),
                        CommissionTypeEnum.CORRECT_INC_OR_DEC.getCode(),
                        CommissionTypeEnum.RENEWAL.getCode(),
                        CommissionTypeEnum.CORRECT_SURRENDER.getCode(), CommissionTypeEnum.CORRECT_SPECIAL_DEDUCTION.getCode()))
                .in(SettlementCostInfoEntity::getCostType, Arrays.asList(CostTypeEnum.BASIC.getCode()))
                .list();

    }
    @Override
    public Integer countUnCorrectionPolicyCostInfoByCostConfigKeys(List<String> configKeys){
        return lambdaQuery()
                .eq(SettlementCostInfoEntity::getCorrectionFlag,0)
                .eq(SettlementCostInfoEntity::getConfirmStatus,0)
                .in(SettlementCostInfoEntity::getCostConfigKey,configKeys)
                .in(SettlementCostInfoEntity::getCommissionType,Arrays.asList(CommissionTypeEnum.COMMON.getCode(),
                        CommissionTypeEnum.CORRECT_INC_OR_DEC.getCode(),
                        CommissionTypeEnum.RENEWAL.getCode(),
                        CommissionTypeEnum.CORRECT_SURRENDER.getCode(), CommissionTypeEnum.CORRECT_SPECIAL_DEDUCTION.getCode())).count();
    }

    /**
     * 根据佣金配置key列表查询所对应的合同编号
     * @param configKeys
     * @return
     */
    @Override
    public List<String> listContractCodeByCostConfigKeys(List<String> configKeys){
        return baseMapper.listContractCodeByCostConfigKeys(configKeys);
    }

    /**
     * 按合同编号、佣金配置key查询所对应的佣金记录（不包括佣金记录佣金比例不可变记录）
     * @param contractCodes
     * @param configKeys
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> listUnCorrectionPolicyCostInfoByCostConfigKeys(List<String> contractCodes,List<String> configKeys){

        if(contractCodes.size()> Constant.MAX_QUERY_SIZE){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("超过按合同编号最大查询记录数，最大查询记录数为{}", Constant.MAX_QUERY_SIZE)));
        }

        return lambdaQuery()
                .in(SettlementCostInfoEntity::getContractCode,contractCodes)
                .eq(SettlementCostInfoEntity::getCorrectionFlag,0)
                .eq(SettlementCostInfoEntity::getConfirmStatus,0)
                .eq(SettlementCostInfoEntity::getImmutableFlag,0)
                .in(CollectionUtils.isNotEmpty(configKeys),SettlementCostInfoEntity::getCostConfigKey,configKeys)
                .in(SettlementCostInfoEntity::getCommissionType,Arrays.asList(CommissionTypeEnum.COMMON.getCode(),
                        CommissionTypeEnum.CORRECT_INC_OR_DEC.getCode(),
                        CommissionTypeEnum.RENEWAL.getCode(),
                        CommissionTypeEnum.CORRECT_SURRENDER.getCode(), CommissionTypeEnum.CORRECT_SPECIAL_DEDUCTION.getCode())).list();
    }

    /**
     * 按保单号查询所对应的佣金记录
     * @param policyNo
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> listUnCorrectionPolicyCostInfoByPolicyNo(String policyNo){
        if(StringUtils.isBlank(policyNo)){
            return Collections.emptyList();
        }

        return lambdaQuery()
                .eq(SettlementCostInfoEntity::getCorrectionFlag,0)
                .eq(SettlementCostInfoEntity::getConfirmStatus,0)
                .inSql(SettlementCostInfoEntity::getContractCode, StrUtil.format("SELECT ec.contract_code FROM settlement_cost_policy_info ec  WHERE ec.policy_no = '{}' ", policyNo))
                .list();
    }

    /**
     * 根据佣金配置keys查询未冲正数据且未确认的单子
     * @param configKeys
     * @param pageSize
     * @param pageCount
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> pageUnCorrectionPolicyCostInfoByCostConfigKeys(List<String> configKeys,Integer pageSize,Integer pageCount){
        if(pageSize> Constant.MAX_PAGE_COUNT){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("超过最大查询记录数，最大查询记录数为{}", Constant.MAX_PAGE_COUNT)));
        }

        return lambdaQuery()
                .eq(SettlementCostInfoEntity::getCorrectionFlag,0)
                .eq(SettlementCostInfoEntity::getConfirmStatus,0)
                .in(SettlementCostInfoEntity::getCostConfigKey,configKeys)
                .in(SettlementCostInfoEntity::getCommissionType,Arrays.asList(CommissionTypeEnum.COMMON.getCode(),
                        CommissionTypeEnum.CORRECT_INC_OR_DEC.getCode(),
                        CommissionTypeEnum.RENEWAL.getCode(),
                        CommissionTypeEnum.CORRECT_SURRENDER.getCode(), CommissionTypeEnum.CORRECT_SPECIAL_DEDUCTION.getCode()))
                .last(StrUtil.format(" limit  {},{} ",(pageCount-1)*pageSize,pageSize)).list();

    }



    /**
     * 根据合同号查询未冲正数据，已冲正数据已经一正一负抵消了（管护经理变更、分单管护经理变更）
     * 注意：包括已确认的单子
     * @param contractCode
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> listUnCorrectionPolicyCostInfoByContractCode(String contractCode){
        /*return lambdaQuery().eq(SettlementCostInfoEntity::getContractCode,contractCode)
                .eq(SettlementCostInfoEntity::getCorrectionFlag,0)
                //.eq(SettlementCostInfoEntity::getConfirmStatus,0)
                .in(SettlementCostInfoEntity::getCommissionType,Arrays.asList(CommissionTypeEnum.COMMON.getCode(),
                        CommissionTypeEnum.CORRECT_INC_OR_DEC.getCode(),
                        CommissionTypeEnum.RENEWAL.getCode(),
                        CommissionTypeEnum.CORRECT_SURRENDER.getCode()))
                .list();*/
        return listUnCorrectionPolicyCostInfoByContractCodeAndRenewal(contractCode,null);

    }

    /**
     * 根据合同号查询未冲正数据，已冲正数据已经一正一负抵消了（管护经理变更、分单管护经理变更）
     * 注意：包括已确认的单子
     * @param contractCode
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> listUnCorrectionPolicyCostInfoByContractCodeAndRenewal(String contractCode,Integer renewalPeriod){
        return lambdaQuery().eq(SettlementCostInfoEntity::getContractCode,contractCode)
                .eq(SettlementCostInfoEntity::getCorrectionFlag,0)
                .eq(renewalPeriod!=null,SettlementCostInfoEntity::getRenewalPeriod,renewalPeriod)
                .in(SettlementCostInfoEntity::getCommissionType,Arrays.asList(CommissionTypeEnum.COMMON.getCode(),
                        CommissionTypeEnum.CORRECT_INC_OR_DEC.getCode(),
                        CommissionTypeEnum.RENEWAL.getCode(),
                        CommissionTypeEnum.CORRECT_SURRENDER.getCode(), CommissionTypeEnum.CORRECT_SPECIAL_DEDUCTION.getCode()))
                .list();

    }

    /**
     * 根据合同号、险种编号查询未冲正数据，已冲正数据已经一正一负抵消了（险种变更）
     * 注意：包括已确认的单子
     * @param contractCode
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> listUnCorrectionByContractCodeAndProductCodes(String contractCode,List<String> productCodes){
        if(StringUtils.isBlank(contractCode) || CollectionUtils.isEmpty(productCodes)){
            return Collections.emptyList();
        }
        return lambdaQuery().eq(SettlementCostInfoEntity::getContractCode,contractCode)
                .eq(SettlementCostInfoEntity::getCorrectionFlag,0)
                .in(SettlementCostInfoEntity::getProductCode,productCodes)
                .in(SettlementCostInfoEntity::getCommissionType,Arrays.asList(CommissionTypeEnum.COMMON.getCode(),
                        CommissionTypeEnum.CORRECT_INC_OR_DEC.getCode(),
                        CommissionTypeEnum.RENEWAL.getCode(),
                        CommissionTypeEnum.CORRECT_SURRENDER.getCode(), CommissionTypeEnum.CORRECT_SPECIAL_DEDUCTION.getCode()))
                .list();

    }

    /**
     * 根据合同号、渠道编码查询未冲正数据，已冲正数据已经一正一负抵消了（渠道变更）
     * 注意：包括已确认的单子
     * @param contractCode
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> listUnCorrectionByContractCodeAndChannelCode(String contractCode,String channelCode){
        if(StringUtils.isBlank(contractCode) || StringUtils.isBlank(channelCode)){
            return Collections.emptyList();
        }
        return lambdaQuery().eq(SettlementCostInfoEntity::getContractCode,contractCode)
                .eq(SettlementCostInfoEntity::getCorrectionFlag,0)
                .eq(SettlementCostInfoEntity::getOwnerChannelCode,channelCode)
                .in(SettlementCostInfoEntity::getCommissionType,Arrays.asList(CommissionTypeEnum.COMMON.getCode(),
                        CommissionTypeEnum.CORRECT_INC_OR_DEC.getCode(),
                        CommissionTypeEnum.RENEWAL.getCode(),
                        CommissionTypeEnum.CORRECT_SURRENDER.getCode(), CommissionTypeEnum.CORRECT_SPECIAL_DEDUCTION.getCode()))
                .list();

    }


    /**
     * 根据event_source_code查询基础佣金明细
     * @param eventSourceCode
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> listCostInfoByEventSourceCode(String eventSourceCode){
        return lambdaQuery().eq(SettlementCostInfoEntity::getEventSourceCode,eventSourceCode)
                .list();

    }

    /**
     * 根据个险合同号查询未冲正数据原始新契约数据
     * 注意：包括已确认的单子
     * @param contractCode
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> listPersonNewPolicyCostInfoByContractCode(String contractCode){
        return lambdaQuery().eq(SettlementCostInfoEntity::getContractCode,contractCode)
                .eq(SettlementCostInfoEntity::getCorrectionFlag,0)
                .eq(SettlementCostInfoEntity::getSettlementEventCode, SettlementEventTypeEnum.PERSONAL_NEW_POLICY.getEventCode())
                .in(SettlementCostInfoEntity::getCommissionType,Arrays.asList(CommissionTypeEnum.COMMON.getCode(),
                        CommissionTypeEnum.CORRECT_INC_OR_DEC.getCode(),
                        CommissionTypeEnum.RENEWAL.getCode(),
                        CommissionTypeEnum.CORRECT_SURRENDER.getCode(), CommissionTypeEnum.CORRECT_SPECIAL_DEDUCTION.getCode()))
                .list();

    }

    /**
     * 退保查询历史的未冲正的佣金的记录(退保事件专用)，
     * 注意不能查询commission_type等于CommissionTypeEnum.CORRECT_SPECIAL_DEDUCTION.getCode()，不然回存在：合同号+险种编码+被保人编码+期数重复的情况
     * @param contractCode
     * @return 返回结果包括的科目有：首续年佣金、长险暂发、长险补发、车船税
     */
    @Override
    public List<SettlementCostInfoEntity> listLongPolicyUnCorrectionCostInfoByContractCode(String contractCode){
        return lambdaQuery().eq(SettlementCostInfoEntity::getContractCode,contractCode)
                .eq(SettlementCostInfoEntity::getCorrectionFlag,0)

                .in(SettlementCostInfoEntity::getSettlementSubjectCode, Arrays.asList(CostSubjectEnum.FIRST_BASIC_COMM.getCode(),
                        CostSubjectEnum.VEHICLE_VESSEL_TAX.getCode(),CostSubjectEnum.LONG_PROMOTION.getCode(),CostSubjectEnum.LONG_REISSUE_PROMOTION.getCode()))
                .in(SettlementCostInfoEntity::getCommissionType,Arrays.asList(CommissionTypeEnum.COMMON.getCode(),
                        CommissionTypeEnum.CORRECT_INC_OR_DEC.getCode(),
                        CommissionTypeEnum.RENEWAL.getCode()))
                .list();

    }

    /**
     * 根据id设置冲正标志为已冲正
     * @param ids
     * @return
     */
    @Override
    public Boolean updateCorrectionFlagDoneByIds(List<Integer> ids){
        return lambdaUpdate().set(SettlementCostInfoEntity::getCorrectionFlag,1)
                .set(SettlementCostInfoEntity::getBusinessDiscountPremium, BigDecimal.ZERO)
                .in(SettlementCostInfoEntity::getId,ids).update();

    }

    /**
     * 根据id设置冲正标志为已冲正
     * @param ids
     * @return
     */
    @Override
    public Boolean updateCostInfoConfigKey(List<Integer> ids,String newConfigKey){
        if(CollectionUtils.isEmpty(ids) || StringUtil.isBlank(newConfigKey)){
            return Boolean.FALSE;
        }
        return lambdaUpdate().set(SettlementCostInfoEntity::getCostConfigKey,newConfigKey)
                .in(SettlementCostInfoEntity::getId,ids).update();

    }



    @Override
    public int saveList(List<SettlementCostInfoDto> list) {
        if(CollectionUtils.isEmpty(list)){
            return 0;
        }
        // 进行批量vo 转换 po
        List<SettlementCostInfoEntity> listData = list.stream().map(x -> {
            SettlementCostInfoEntity bean = new SettlementCostInfoEntity();
            BeanUtils.copyProperties(x, bean);
            return bean;
        }).collect(Collectors.toList());

        return batchSave(listData);
    }

    public int batchSave(List<SettlementCostInfoEntity> list){
        if(CollectionUtils.isEmpty(list)){
            return 0;
        }
        int result = 0;
        // 批量写入
        if (list.size() > MAX_BATCH_INSERT_NUM) {
            List<List<SettlementCostInfoEntity>> partition = ListUtils.partition(list, MAX_BATCH_INSERT_NUM);
            for (List<SettlementCostInfoEntity> x : partition) {
                result = result + baseMapper.insertBatchSomeColumn(x);
            }
        } else {
            result = baseMapper.insertBatchSomeColumn(list);
        }
        return result;
    }

    public int updateConfirmStatus(){
        return 0;
    }

    public SettlementCostInfoEntity getByCostCode(String costCode){
        if(StringUtils.isBlank(costCode)){
            return null;
        }
        return this.lambdaQuery().eq(SettlementCostInfoEntity::getCostCode,costCode).eq(SettlementCostInfoEntity::getDeleted,0).one();
    }

    public int batchUpdateOwnerThirdStatus(List<SettlementCostInfoEntity> list){
        if(CollectionUtils.isEmpty(list)){
            return 0;
        }
        List<Integer> ids = list.stream().filter(c-> Objects.nonNull(c.getId())).map(SettlementCostInfoEntity::getId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(ids)){
            return 0;
        }
        return this.baseMapper.updateOwnerThirdStatus(list);
    }
}
