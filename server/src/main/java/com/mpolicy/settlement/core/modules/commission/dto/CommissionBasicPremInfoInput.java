package com.mpolicy.settlement.core.modules.commission.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class CommissionBasicPremInfoInput implements Serializable {

    /**
     * 保险公司编码
     */
    private String companyCode;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 险种编码
     */
    private String productCode;
    /**
     * 保单号
     */
    private String policyNo;
    /**
     * 小鲸主险编码
     */
    private String mainProductCode;
    /**
     * 投保计划
     */
    private String productPlan;
    /**
     * 适用分公司(代理人所属组织机构-快照)
     */
    private String orgCode;
    /**
     * 保障期间类型
     */
    private String insuredPeriodType;
    /**
     * 保障时长
     */
    private Integer insuredPeriod;
    /**
     * 缴费方式
     */
    private String periodType;
    /**
     * 缴费期间类型
     */
    private String paymentPeriodType;
    /**
     * 缴费时长
     */
    private Integer paymentPeriod;
    /**
     * 续期年期
     */
    private Integer renewalYear;
    /**
     * 续期期次
     */
    private Integer renewalPeriod;
    /**
     * 投保类nt:新单/ 1:续保
     */
    private Integer insuranceType;
    /**
     * 结算客户中文名称 必填
     */
    private String settlementSubjectName;
    /**
     * 承保时间
     */
    private Date approvedTime;
    /**
     * 投保人性别 0:女 1:男 可能为空
     */
    private Integer applicantGender;
    /**
     * 投保人出生日期
     */
    private Date applicantBirthday;
    /**
     * 被保人性别 0:女 1:男 可能为空
     */
    private Integer insuredGender;
    /**
     * 投保人出生日期
     */
    private Date insuredBirthday;
    /**
     * 被保人投保时年龄
     */
    private Integer insuredPolicyAge;
    /**
     * 销售nt0:网销 1:线下
     */
    private Integer salesType;
    /**
     * 是否自保件 0:否;1:是
     */
    private Integer selfPreservation;
}
