package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementCostPolicyInfoDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementCostPolicyInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/16 3:16 上午
 * @Version 1.0
 */
@Slf4j
@Service("settlementCostPolicyInfoService")
public class SettlementCostPolicyInfoServiceImpl extends ServiceImpl<SettlementCostPolicyInfoDao, SettlementCostPolicyInfoEntity> implements SettlementCostPolicyInfoService {

    @Override
    public SettlementCostPolicyInfoEntity getCostPolicyInfoEntityByContractCode(String contractCode){
        if(StringUtils.isBlank(contractCode)){
            return null;
        }
        return lambdaQuery().eq(SettlementCostPolicyInfoEntity::getContractCode, contractCode).one();
    }


    public SettlementCostPolicyInfoEntity getCostPolicyInfoEntityByContractCodeAndPolicyNo(String contractCode, String policyNo) {
        if(StringUtils.isBlank(contractCode)){
            return null;
        }
        return lambdaQuery()
                .eq(SettlementCostPolicyInfoEntity::getContractCode, contractCode)
                .eq(SettlementCostPolicyInfoEntity::getPolicyNo, policyNo)
                .one();
    }

    @Override
    public List<SettlementCostPolicyInfoEntity> listCostPolicyInfoEntityByContractCodes(List<String> contractCodes){
        if(CollectionUtils.isEmpty(contractCodes)){
            return Collections.emptyList();
        }
        return lambdaQuery().in(SettlementCostPolicyInfoEntity::getContractCode, contractCodes).list();
    }


    public List<SettlementCostPolicyInfoEntity> listCostPolicyInfoEntityByPolicyNos(List<String> policyNos){
        if(CollectionUtils.isEmpty(policyNos)){
            return Collections.emptyList();
        }
        return lambdaQuery().in(SettlementCostPolicyInfoEntity::getPolicyNo, policyNos).list();
    }

    public List<Integer> listNullGoodsNameRecords(Integer num){
        return this.baseMapper.listNullGoodsNameRecords(num);
    }

    public Integer updateGoodsNameByIds(List<Integer> idList){
        if(CollectionUtils.isEmpty(idList)){
            return 0;
        }
        return this.baseMapper.updateGoodsNameByIds(idList);
    }
}
