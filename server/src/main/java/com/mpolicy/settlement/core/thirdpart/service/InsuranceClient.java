package com.mpolicy.settlement.core.thirdpart.service;

import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.thirdpart.client.InsuranceFeign;
import com.mpolicy.settlement.core.thirdpart.request.DistributionCommissionQuery;
import com.mpolicy.settlement.core.thirdpart.request.WhaleAddCommissionPushQuery;
import com.mpolicy.settlement.core.thirdpart.response.AddCommissionSettlementPushDto;
import com.mpolicy.settlement.core.thirdpart.response.ChongHoResult;
import com.mpolicy.settlement.core.thirdpart.response.OrderDistributionCommissionListVo;
import com.mpolicy.settlement.core.thirdpart.response.OrderDistributionCommissionVo;
import com.netflix.discovery.converters.Auto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class InsuranceClient {

    @Autowired
    private InsuranceFeign insuranceFeign;

    /**
     * 分页获取分销明细数据，约定按id升序排列（为了断点续传）
     * @param query
     * @return
     */
    public List<OrderDistributionCommissionVo> getDistributionCommissionByPage(DistributionCommissionQuery query){
        ChongHoResult<List<OrderDistributionCommissionVo>> result = null;
        try {
            result = insuranceFeign.getDistributionCommissionByPage(query);
        } catch (Exception e) {
            log.warn("获取分销数据异常,请开发人员及时排查问题", e);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("获取分销数据异常,请开发人员及时排查问题"));
        }
        if (!result.isSuccess()) {
            log.warn("获取分销数据不成功,请开发人员及时排查问题", result);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("获取分销数据不成功，请联系开发人员"));
        }
        return result.getData();
    }

    /**
     * 分页获取分销明细数据，约定按id升序排列（为了断点续传）
     * @param query
     * @return
     */
    public List<OrderDistributionCommissionListVo> getDistributionCommissionByPageV2(DistributionCommissionQuery query){
        ChongHoResult<List<OrderDistributionCommissionListVo>> result = null;
        try {
            result = insuranceFeign.getDistributionCommissionByPageV2(query);
        } catch (Exception e) {
            log.warn("获取分销数据异常,请开发人员及时排查问题", e);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("获取分销数据异常,请开发人员及时排查问题"));
        }
        if (!result.isSuccess()) {
            log.warn("获取分销数据不成功,请开发人员及时排查问题", result);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("获取分销数据不成功，请联系开发人员"));
        }
        return result.getData();
    }

    public boolean updateSyncState(List<Long> ids){
        if(CollectionUtils.isEmpty(ids)){
            log.info("待同步更新记录数为0");
            return false;
        }

        ChongHoResult<Void> result = null;
        try {
            result = insuranceFeign.updateSyncState(ids);
        } catch (Exception e) {
            log.warn("获取分销数据异常,请开发人员及时排查问题", e);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("获取分销数据异常,请开发人员及时排查问题"));
        }
        if (!result.isSuccess()) {
            log.warn("获取分销数据不成功,请开发人员及时排查问题", result);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("获取分销数据不成功，请联系开发人员"));
        }
        return true;

//        try {
//            insuranceFeign.updateSyncState(ids);
//            return true;
//        } catch (Exception e) {
//            log.warn("获取分销数据异常,请开发人员及时排查问题", e);
//            return false;
//        }
    }
}
