package com.mpolicy.settlement.core.modules.reconcile.event.project.policy;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.policy.common.enums.PreservationProjectEnum;
import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.policy.common.ep.policy.EpInsuredInfoVo;
import com.mpolicy.policy.common.ep.policy.EpProductInfoVo;
import com.mpolicy.policy.common.ep.policy.preserve.*;
import com.mpolicy.policy.common.policy.renewal.response.PolicyRenewalTermBaseInfo;
import com.mpolicy.policy.common.policy.renewal.response.PolicyRenewalTermInsuredInfo;
import com.mpolicy.policy.common.policy.renewal.response.PolicyRenewalTermOutput;
import com.mpolicy.policy.common.policy.renewal.response.PolicyRenewalTermProductInfo;
import com.mpolicy.product.common.base.ProductBase;
import com.mpolicy.settlement.core.enums.InsuranceTypeEnum;
import com.mpolicy.settlement.core.enums.SettlementPolicyHandlerEnum;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.PolicyProductTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.ProductStatusEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.factory.SettlementPolicyFactory;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.SettlementPolicyHandler;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import com.mpolicy.settlement.core.modules.reconcile.vo.BasisSettlementPolicyInfoDomain;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mpolicy.settlement.core.common.Constant.POLICY_RENEWAL_FIRST_APPROVE_TIME_PRODUCTS;
import static com.mpolicy.settlement.core.modules.reconcile.service.impl.SettlementCostProcessServiceImpl.findFirstPolicy;

/**
 * 处理保全
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SettlementPolicyPreservationHandler extends SettlementPolicyHandler {

    @Override
    public void afterPropertiesSet() {

        SettlementPolicyFactory.register(SettlementPolicyHandlerEnum.PRESERVATION, this);
    }

    /**
     * 构建基础结算明细(不包含费率匹配)
     *
     * @param domain 生成计算信息条件
     * @return
     */
    @Override
    public List<SettlementPolicyInfoEntity> buildBasisSettlementPolicyInfo(BasisSettlementPolicyInfoDomain domain) {
        //校验一下数据
        this.checkBasisSettlementPolicyInfoDomain(domain);
        // 保全编码
        String preservationCode = domain.getPreservationCode();
        if (StrUtil.isBlank(preservationCode)) {
            throw new GlobalException(
                    BasicCodeMsg.SERVER_ERROR.setMsg("保全事件,保全编码不能为空,保单号=" + domain.getPolicyNo()));
        }
        if (CollUtil.isEmpty(domain.getProductInfoList())) {
            throw new GlobalException(
                    BasicCodeMsg.SERVER_ERROR.setMsg("没有险种信息不处理,保全编码=" + preservationCode));
        }
        // 获取保全详情
        EpPreservationV2Vo preservationDetail = this.policyCenterBaseClient.getPreservationDetail(preservationCode);
        if (preservationDetail == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("保全详情不存在,保全编码=" + preservationCode));
        }
        //处理一下,如果是退保类型的,需要将保费设置为负数
        PreservationProjectEnum preservationProjectEnum = PreservationProjectEnum.decode(preservationDetail.getPreservationProject());

        // 构建明细基本信息
        SettlementPolicyInfoEntity sourceBean = buildSettlementPolicyInfoBasis(domain, preservationDetail);
        List<SettlementPolicyInfoEntity> result = Collections.emptyList();
        switch (preservationProjectEnum) {
            case TERMINATION_PRODUCT: {
                // 解除附加险
                result = handleTerminationProduct(preservationDetail, domain, sourceBean);
                break;
            }
            case PROTOCOL_TERMINATION: {
                // 处理协议解约
                result = handleProtocolTermination(preservationDetail, domain, sourceBean);
                break;
            }
            case VEHICLE_INFO_CHANGE: {
                // 处理车险信息表更
                result = handleVehicleInfoChange(preservationDetail, sourceBean, domain.getContractInfo());
                break;
            }
            case POLICY_REFUND: {
                // 处理保全保单退费
                result = handlePolicyRefund(preservationDetail, sourceBean);
                break;
            }
            case SURRENDER:
            case HESITATION_SURRENDER: {
                // 处理标准退保/犹豫期退保
                result = handlePreserveSurrender(preservationDetail, sourceBean, domain.getContractInfo());
                break;
            }
            case ADD_OR_SUBTRACT: {
                // 处理团险增减员
                result = handleAddOrSubtract(preservationDetail, sourceBean, domain.getContractInfo());
                break;
            }
            // 重新缴费
            case POLICY_REPAY: {
                result = handlePolicyRepay(preservationDetail, sourceBean);
                break;
            }
            // 特殊补退费
            case POLICY_SUPPLEMENT_PREMIUM: {
                result = handlePolicySupplementPremium(preservationDetail, sourceBean);
                break;
            }
            default: {
                log.info("未对接的保全事件={}", preservationProjectEnum.getName());
                result = Collections.emptyList();
                break;
            }
        }
        Map<String, Date> sourcePlicyMap = new HashMap<>();
        for (SettlementPolicyInfoEntity policyInfoEntity : result) {
            if (POLICY_RENEWAL_FIRST_APPROVE_TIME_PRODUCTS.contains(policyInfoEntity.getProductCode())) {
                if (sourcePlicyMap.containsKey(domain.getContractInfo().getSourcePolicyNo())) {
                    policyInfoEntity.setApprovedTime(sourcePlicyMap.get(domain.getContractInfo().getSourcePolicyNo()));
                } else if (StrUtil.isNotEmpty(domain.getContractInfo().getSourcePolicyNo())) {
                    EpContractInfoVo firstContractInfoVo = findFirstPolicy(domain.getContractInfo().getSourcePolicyNo(), 0);
                    sourcePlicyMap.put(domain.getContractInfo().getSourcePolicyNo(), firstContractInfoVo.getContractExtendInfo().getApprovedTime());
                    policyInfoEntity.setApprovedTime(firstContractInfoVo.getContractExtendInfo().getApprovedTime());
                }

            }
        }


        return result;
    }

    /**
     * 处理保单重新补退费
     * @param preservationDetail 保全详情
     * @param sourceBean 基础信息
     */
    private List<SettlementPolicyInfoEntity> handlePolicySupplementPremium(EpPreservationV2Vo preservationDetail, SettlementPolicyInfoEntity sourceBean) {
        EpPreserveSurrenderDetailVo policySupplementDetail = preservationDetail.getPolicySupplementDetail();
        if (policySupplementDetail == null) {
            throw new GlobalException(
                    BasicCodeMsg.SERVER_ERROR.setMsg("保全编码=" + preservationDetail.getPreservationCode() + "，保单特殊补退费明细不存在"));
        }
        List<SettlementPolicyInfoEntity> settlementPolicyInfoList = new ArrayList<>();
        List<EpPreserveSurrenderInsuredVo> insuredList = policySupplementDetail.getInsuredList();
        List<EpPreserveSurrenderProductVo> productList = policySupplementDetail.getProductList();
        // 精确到人的维度就先处理人的,没有人的话 就处理处理到险种维度就行
        if (CollUtil.isNotEmpty(insuredList)) {
            List<String> productCodeList = insuredList.stream().flatMap(x -> x.getInsuredProductList().stream()).map(EpPreserveProductInsuredMapVo::getProductCode).distinct().collect(Collectors.toList());
            if (!CollUtil.isEmpty(productCodeList)) {
                Map<String, ProductBase> productMap = this.getProductInfoMapByProductCodes(productCodeList);
                // 退保被保人记录
                insuredList.forEach(insured -> {
                    insured.getInsuredProductList().forEach(x -> {
                        SettlementPolicyInfoEntity bean = BeanUtil.copyProperties(sourceBean, SettlementPolicyInfoEntity.class);
                        bean.setSettlementCode(PolicySettlementUtils.createCodeLastUuid("SC"));
                        // 险种信息
                        bean.setProductCode(x.getProductCode());
                        bean.setProductName(x.getProductName());
                        bean.setPlanCode(x.getPlanCode());
                        bean.setPlanName(x.getPlanName());
                        bean.setMainInsurance(x.getMainInsurance());
                        bean.setAdditionalRisksType(x.getAdditionalRisksType());
                        bean.setEffectiveDate(x.getEffectiveDate());
                        bean.setEndDate(DateUtil.formatDateTime(x.getEndDate()));
                        bean.setInsuredPeriodType(x.getInsuredPeriodType());
                        bean.setInsuredPeriod(x.getInsuredPeriod());
                        bean.setPeriodType(x.getPeriodType());
                        bean.setPaymentPeriodType(x.getPaymentPeriodType());
                        bean.setPaymentPeriod(x.getPaymentPeriod());
                        bean.setDrawAge(x.getAnnDrawAge());
                        bean.setPremium(x.getBusinessPremium());
                        // 如果保费为0 不生成明细
                        if (bean.getPremium().compareTo(BigDecimal.ZERO)==0) {
                            return;
                        }
                        bean.setProductPremiumTotal(preservationDetail.getSurrenderCash());
                        bean.setCopies(x.getCopies());
                        // 险种分类信息
                        if (productMap.containsKey(x.getProductCode())) {
                            ProductBase productBase = productMap.get(x.getProductCode());
                            if (StrUtil.isBlank(bean.getProductName())) {
                                bean.setProductName(productBase.getProductName());
                            }
                            bean.setProductType(productBase.getProductType());
                            bean.setProductGroup(productBase.getProductGroup());
                            bean.setLevel2Code(productBase.getLevel2Code());
                            bean.setLevel3Code(productBase.getLevel3Code());
                            bean.setLongShortFlag(productBase.getLongShortFlag());
                        } else {
                            throw new GlobalException(
                                    BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + x.getProductCode()));
                        }
                        // 被保人信息
                        bean.setInsuredName(insured.getInsuredName());
                        bean.setInsuredMobile(insured.getInsuredMobile());
                        bean.setInsuredIdCard(insured.getInsuredIdCard());
                        bean.setInsuredBirthday(insured.getInsuredBirthday());
                        bean.setInsuredGender(insured.getInsuredGender());
                        if (bean.getInsuredBirthday() != null) {
                            bean.setInsuredPolicyAge(DateUtil.age(bean.getInsuredBirthday(), x.getEffectiveDate()));
                        }
                        settlementPolicyInfoList.add(bean);
                    });
                });
            }
        }
        // 如果没有人的维度信息在去处理险种维度的数据
        if (settlementPolicyInfoList.isEmpty() && CollUtil.isNotEmpty(productList)) {
            List<String> productCodeList = productList.stream().map(EpPreserveSurrenderProductVo::getProductCode).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(productCodeList)) {
                log.info("保全编码={},退保险种信息不存在", preservationDetail.getPreservationCode());
                return Collections.emptyList();
            }
            Map<String, ProductBase> productMap = this.getProductInfoMapByProductCodes(productCodeList);
            productList.forEach(x -> {
                SettlementPolicyInfoEntity bean = BeanUtil.copyProperties(sourceBean, SettlementPolicyInfoEntity.class);
                bean.setSettlementCode(PolicySettlementUtils.createCodeLastUuid("SC"));
                // 险种信息
                bean.setProductCode(x.getProductCode());
                bean.setProductName(x.getProductName());
                bean.setPlanCode(x.getPlanCode());
                bean.setPlanName(x.getPlanName());
                bean.setMainInsurance(x.getMainInsurance());
                bean.setAdditionalRisksType(x.getAdditionalRisksType());
                bean.setEffectiveDate(x.getEffectiveDate());
                bean.setEndDate(DateUtil.formatDateTime(x.getEndDate()));
                bean.setInsuredPeriodType(x.getInsuredPeriodType());
                bean.setInsuredPeriod(x.getInsuredPeriod());
                bean.setPeriodType(x.getPeriodType());
                bean.setPaymentPeriodType(x.getPaymentPeriodType());
                bean.setPaymentPeriod(x.getPaymentPeriod());
                bean.setDrawAge(x.getAnnDrawAge());
                bean.setPremium(x.getSurrenderPremium());
                // 如果保费为0 不生成明细
                if (bean.getPremium().compareTo(BigDecimal.ZERO)==0) {
                    return;
                }
                bean.setProductPremiumTotal(preservationDetail.getSurrenderCash());
                bean.setCopies(x.getCopies());
                // 险种分类信息
                if (productMap.containsKey(x.getProductCode())) {
                    ProductBase productBase = productMap.get(x.getProductCode());
                    if (StrUtil.isBlank(bean.getProductName())) {
                        bean.setProductName(productBase.getProductName());
                    }
                    bean.setProductType(productBase.getProductType());
                    bean.setProductGroup(productBase.getProductGroup());
                    bean.setLevel2Code(productBase.getLevel2Code());
                    bean.setLevel3Code(productBase.getLevel3Code());
                    bean.setLongShortFlag(productBase.getLongShortFlag());
                } else {
                    throw new GlobalException(
                            BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + x.getProductCode()));
                }
                // 被保人信息如果没有的话 就不填写了
                settlementPolicyInfoList.add(bean);
            });
        }
        return settlementPolicyInfoList;
    }

    /**
     * 处理保单重新缴费(短期险重新缴费)
     *
     * @param preservationDetail 保全明细
     * @param sourceBean         基本信息
     * @return 结算明细
     */
    private List<SettlementPolicyInfoEntity> handlePolicyRepay(EpPreservationV2Vo preservationDetail, SettlementPolicyInfoEntity sourceBean) {
        // 保全操作类型:0=新契约保全，1=续期保全
        if (preservationDetail.getInsuranceType() == 1) {
            throw new GlobalException(
                    BasicCodeMsg.SERVER_ERROR.setMsg("保全编码=" + preservationDetail.getPreservationCode() + "，续期不支持重新缴费"));
        }
        EpPreserveSurrenderDetailVo policyRepayDetail = preservationDetail.getPolicyRepayDetail();
        if (policyRepayDetail == null) {
            throw new GlobalException(
                    BasicCodeMsg.SERVER_ERROR.setMsg("保全编码=" + preservationDetail.getPreservationCode() + "，保单续费明细不存在"));
        }
        List<SettlementPolicyInfoEntity> settlementPolicyInfoList = new ArrayList<>();
        List<EpPreserveSurrenderInsuredVo> insuredList = policyRepayDetail.getInsuredList();
        List<EpPreserveSurrenderProductVo> productList = policyRepayDetail.getProductList();
        // 精确到人的维度就先处理人的,没有人的话 就处理处理到险种维度就行
        if (CollUtil.isNotEmpty(insuredList)) {
            List<String> productCodeList = insuredList.stream().flatMap(x -> x.getInsuredProductList().stream()).map(EpPreserveProductInsuredMapVo::getProductCode).distinct().collect(Collectors.toList());
            if (!CollUtil.isEmpty(productCodeList)) {
                Map<String, ProductBase> productMap = this.getProductInfoMapByProductCodes(productCodeList);
                // 退保被保人记录
                insuredList.forEach(insured -> {
                    insured.getInsuredProductList().forEach(x -> {
                        SettlementPolicyInfoEntity bean = BeanUtil.copyProperties(sourceBean, SettlementPolicyInfoEntity.class);
                        bean.setSettlementCode(PolicySettlementUtils.createCodeLastUuid("SC"));
                        // 险种信息
                        bean.setProductCode(x.getProductCode());
                        bean.setProductName(x.getProductName());
                        bean.setPlanCode(x.getPlanCode());
                        bean.setPlanName(x.getPlanName());
                        bean.setMainInsurance(x.getMainInsurance());
                        bean.setAdditionalRisksType(x.getAdditionalRisksType());
                        bean.setEffectiveDate(x.getEffectiveDate());
                        bean.setEndDate(DateUtil.formatDateTime(x.getEndDate()));
                        bean.setInsuredPeriodType(x.getInsuredPeriodType());
                        bean.setInsuredPeriod(x.getInsuredPeriod());
                        bean.setPeriodType(x.getPeriodType());
                        bean.setPaymentPeriodType(x.getPaymentPeriodType());
                        bean.setPaymentPeriod(x.getPaymentPeriod());
                        bean.setDrawAge(x.getAnnDrawAge());
                        bean.setPremium(x.getPremium());
                        bean.setProductPremiumTotal(preservationDetail.getSurrenderCash());
                        bean.setCopies(x.getCopies());
                        // 如果保费为0 不生成明细
                        if (bean.getPremium().compareTo(BigDecimal.ZERO)==0) {
                            return;
                        }
                        // 险种分类信息
                        if (productMap.containsKey(x.getProductCode())) {
                            ProductBase productBase = productMap.get(x.getProductCode());
                            if (StrUtil.isBlank(bean.getProductName())) {
                                bean.setProductName(productBase.getProductName());
                            }
                            bean.setProductType(productBase.getProductType());
                            bean.setProductGroup(productBase.getProductGroup());
                            bean.setLevel2Code(productBase.getLevel2Code());
                            bean.setLevel3Code(productBase.getLevel3Code());
                            bean.setLongShortFlag(productBase.getLongShortFlag());
                        } else {
                            throw new GlobalException(
                                    BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + x.getProductCode()));
                        }
                        // 被保人信息
                        bean.setInsuredName(insured.getInsuredName());
                        bean.setInsuredMobile(insured.getInsuredMobile());
                        bean.setInsuredIdCard(insured.getInsuredIdCard());
                        bean.setInsuredBirthday(insured.getInsuredBirthday());
                        bean.setInsuredGender(insured.getInsuredGender());
                        if (bean.getInsuredBirthday() != null) {
                            bean.setInsuredPolicyAge(DateUtil.age(bean.getInsuredBirthday(), x.getEffectiveDate()));
                        }
                        settlementPolicyInfoList.add(bean);
                    });
                });
            }
        }
        // 如果没有人的维度信息在去处理险种维度的数据
        if (settlementPolicyInfoList.isEmpty() && CollUtil.isNotEmpty(productList)) {
            List<String> productCodeList = productList.stream().map(EpPreserveSurrenderProductVo::getProductCode).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(productCodeList)) {
                log.info("保全编码={},退保险种信息不存在", preservationDetail.getPreservationCode());
                return Collections.emptyList();
            }
            Map<String, ProductBase> productMap = this.getProductInfoMapByProductCodes(productCodeList);
            productList.forEach(x -> {
                SettlementPolicyInfoEntity bean = BeanUtil.copyProperties(sourceBean, SettlementPolicyInfoEntity.class);
                bean.setSettlementCode(PolicySettlementUtils.createCodeLastUuid("SC"));
                // 险种信息
                bean.setProductCode(x.getProductCode());
                bean.setProductName(x.getProductName());
                bean.setPlanCode(x.getPlanCode());
                bean.setPlanName(x.getPlanName());
                bean.setMainInsurance(x.getMainInsurance());
                bean.setAdditionalRisksType(x.getAdditionalRisksType());
                bean.setEffectiveDate(x.getEffectiveDate());
                bean.setEndDate(DateUtil.formatDateTime(x.getEndDate()));
                bean.setInsuredPeriodType(x.getInsuredPeriodType());
                bean.setInsuredPeriod(x.getInsuredPeriod());
                bean.setPeriodType(x.getPeriodType());
                bean.setPaymentPeriodType(x.getPaymentPeriodType());
                bean.setPaymentPeriod(x.getPaymentPeriod());
                bean.setDrawAge(x.getAnnDrawAge());
                bean.setPremium(x.getPremium());
                bean.setProductPremiumTotal(preservationDetail.getSurrenderCash());
                bean.setCopies(x.getCopies());
                // 如果保费为0 不生成明细
                if (bean.getPremium().compareTo(BigDecimal.ZERO)==0) {
                    return;
                }
                // 险种分类信息
                if (productMap.containsKey(x.getProductCode())) {
                    ProductBase productBase = productMap.get(x.getProductCode());
                    if (StrUtil.isBlank(bean.getProductName())) {
                        bean.setProductName(productBase.getProductName());
                    }
                    bean.setProductType(productBase.getProductType());
                    bean.setProductGroup(productBase.getProductGroup());
                    bean.setLevel2Code(productBase.getLevel2Code());
                    bean.setLevel3Code(productBase.getLevel3Code());
                    bean.setLongShortFlag(productBase.getLongShortFlag());
                } else {
                    throw new GlobalException(
                            BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + x.getProductCode()));
                }
                // 被保人信息如果没有的话 就不填写了
                settlementPolicyInfoList.add(bean);
            });
        }
        return settlementPolicyInfoList;
    }

    /**
     * 处理团险增减员
     *
     * @param preservationDetail 保全详情
     * @param sourceBean         公共信息
     * @return 结算明细基础数据
     */
    private List<SettlementPolicyInfoEntity> handleAddOrSubtract(EpPreservationV2Vo preservationDetail,
                                                                 SettlementPolicyInfoEntity sourceBean,
                                                                 EpContractInfoVo contractInfo) {
        List<EpPreserveAddSubtractDetailVo> addOrSubtractList = preservationDetail.getAddOrSubtractList();
        if (CollUtil.isEmpty(addOrSubtractList)) {
            log.info("保全编码={},团险增减员记录不存在", preservationDetail.getPreservationCode());
            //为了处理这个情况 获取保单信息中的主险信息生成明细
            List<SettlementPolicyInfoEntity> settlementPolicyInfoList = new ArrayList<>();
            contractInfo.getProductInfoList().stream().filter(f -> StatusEnum.NORMAL.getCode().equals(f.getMainInsurance()))
                    .findFirst().ifPresent(x -> {
                        ProductBase productBase = getProductInfoByProductCode(x.getProductCode());
                        SettlementPolicyInfoEntity bean = BeanUtil.copyProperties(sourceBean, SettlementPolicyInfoEntity.class);
                        bean.setSettlementCode(PolicySettlementUtils.createCodeLastUuid("SC"));
                        // 险种信息
                        bean.setProductCode(x.getProductCode());
                        bean.setProductName(x.getProductName());
                        bean.setPlanCode(x.getPlanCode());
                        bean.setPlanName(x.getPlanName());
                        bean.setMainInsurance(x.getMainInsurance());
                        bean.setAdditionalRisksType(x.getAdditionalRisksType());
                        bean.setEffectiveDate(x.getEffectiveDate());
                        bean.setEndDate(DateUtil.formatDateTime(x.getEndDate()));
                        bean.setInsuredPeriodType(x.getInsuredPeriodType());
                        bean.setInsuredPeriod(x.getInsuredPeriod());
                        bean.setPeriodType(x.getPeriodType());
                        bean.setPaymentPeriodType(x.getPaymentPeriodType());
                        bean.setPaymentPeriod(x.getPaymentPeriod());
                        bean.setDrawAge(x.getAnnDrawAge());
                        bean.setPremium(preservationDetail.getSurrenderCash());
                        // 险种总保费为正常保费
                        bean.setProductPremiumTotal(bean.getPremium());
                        bean.setCopies(x.getCopies());
                        // 险种分类信息
                        if (StrUtil.isBlank(bean.getProductName())) {
                            bean.setProductName(productBase.getProductName());
                        }
                        bean.setProductType(productBase.getProductType());
                        bean.setProductGroup(productBase.getProductGroup());
                        bean.setLevel2Code(productBase.getLevel2Code());
                        bean.setLevel3Code(productBase.getLevel3Code());
                        bean.setLongShortFlag(productBase.getLongShortFlag());
                        settlementPolicyInfoList.add(bean);
                    });
            return settlementPolicyInfoList;
        }

        List<SettlementPolicyInfoEntity> settlementPolicyInfoList = new ArrayList<>();
        // 取值主险信息 如果没有主险信息 就随便取值一个
        addOrSubtractList.stream().findFirst().ifPresent(insured -> {
            if (CollUtil.isEmpty(insured.getInsuredProductList())) {
                log.info("保全编码={},团险增减员记录险种信息不存在", preservationDetail.getPreservationCode());
                return;
            }
            EpPreserveProductInsuredMapVo x = insured.getInsuredProductList().stream().filter(f -> StatusEnum.NORMAL.getCode().equals(f.getMainInsurance())).findFirst()
                    .orElseGet(() -> insured.getInsuredProductList().get(0));
            ProductBase productBase = getProductInfoByProductCode(x.getProductCode());
            if (StrUtil.isNotBlank(insured.getInsuredIdCard())) {
                //兼容证件号前后空格的问题,后续保单中心处理
                insured.setInsuredIdCard(insured.getInsuredIdCard().trim());
            }
            SettlementPolicyInfoEntity bean = BeanUtil.copyProperties(sourceBean, SettlementPolicyInfoEntity.class);
            bean.setSettlementCode(PolicySettlementUtils.createCodeLastUuid("SC"));
            // 险种信息
            bean.setProductCode(x.getProductCode());
            bean.setProductName(x.getProductName());
            bean.setPlanCode(x.getPlanCode());
            bean.setPlanName(x.getPlanName());
            bean.setMainInsurance(x.getMainInsurance());
            bean.setAdditionalRisksType(x.getAdditionalRisksType());
            bean.setEffectiveDate(x.getEffectiveDate());
            bean.setEndDate(DateUtil.formatDateTime(x.getEndDate()));
            bean.setInsuredPeriodType(x.getInsuredPeriodType());
            bean.setInsuredPeriod(x.getInsuredPeriod());
            bean.setPeriodType(x.getPeriodType());
            bean.setPaymentPeriodType(x.getPaymentPeriodType());
            bean.setPaymentPeriod(x.getPaymentPeriod());
            bean.setDrawAge(x.getAnnDrawAge());
            bean.setPremium(preservationDetail.getSurrenderCash());
            // 险种总保费为正常保费
            bean.setProductPremiumTotal(bean.getPremium());
            bean.setCopies(x.getCopies());
            // 险种分类信息
            if (StrUtil.isBlank(bean.getProductName())) {
                bean.setProductName(productBase.getProductName());
            }
            bean.setProductType(productBase.getProductType());
            bean.setProductGroup(productBase.getProductGroup());
            bean.setLevel2Code(productBase.getLevel2Code());
            bean.setLevel3Code(productBase.getLevel3Code());
            bean.setLongShortFlag(productBase.getLongShortFlag());
            // 被保人信息
            bean.setInsuredName(insured.getInsuredName());
            bean.setInsuredMobile(insured.getInsuredMobile());
            bean.setInsuredIdCard(insured.getInsuredIdCard());
            if (StrUtil.isNotBlank(insured.getInsuredBirthday())) {
                bean.setInsuredBirthday(DateUtil.parse(insured.getInsuredBirthday()));
            }
            if (StrUtil.isNotBlank(insured.getInsuredGender())) {
                bean.setInsuredGender("男".equals(insured.getInsuredGender()) ? 1 : "女".equals(insured.getInsuredGender()) ? 0 : -1);
            }
            if (StrUtil.isNotBlank(insured.getInsuredIdCard()) && "身份证".equals(insured.getInsuredIdType())) {
                bean.setInsuredBirthday(IdcardUtil.getBirthDate(insured.getInsuredIdCard()));
                bean.setInsuredGender(IdcardUtil.getGenderByIdCard(insured.getInsuredIdCard()));
                log.info("正常解析被保人证件号={},出生日期={}", insured.getInsuredIdCard(), bean.getInsuredBirthday());
            }
            if (bean.getInsuredBirthday() != null) {
                log.info("出生日期={},承保时间={}", bean.getInsuredBirthday(), x.getEffectiveDate());
                bean.setInsuredPolicyAge(DateUtil.age(bean.getInsuredBirthday(), x.getEffectiveDate()));
            }
            settlementPolicyInfoList.add(bean);
        });
        return settlementPolicyInfoList;
    }

    /**
     * 保单退费
     *
     * @param preservationDetail 保全详情
     * @param sourceBean         结算基础明细信息
     * @return
     */
    private List<SettlementPolicyInfoEntity> handlePolicyRefund(EpPreservationV2Vo preservationDetail, SettlementPolicyInfoEntity sourceBean) {
        if (preservationDetail.getPolicyRefundDetail() == null) {
            throw new GlobalException(
                    BasicCodeMsg.SERVER_ERROR.setMsg("保全编码=" + preservationDetail.getPreservationCode() + "，保单退费明细不存在"));
        }
        List<SettlementPolicyInfoEntity> settlementPolicyInfoList = new ArrayList<>();
        List<EpPreserveSurrenderInsuredVo> insuredList = preservationDetail.getPolicyRefundDetail().getInsuredList();
        List<EpPreserveSurrenderProductVo> productList = preservationDetail.getPolicyRefundDetail().getProductList();
        // 精确到人的维度就先处理人的,没有人的话 就处理处理到险种维度就行
        if (CollUtil.isNotEmpty(insuredList)) {
            List<String> productCodeList = insuredList.stream().flatMap(x -> x.getInsuredProductList().stream()).map(EpPreserveProductInsuredMapVo::getProductCode).distinct().collect(Collectors.toList());
            if (!CollUtil.isEmpty(productCodeList)) {
                Map<String, ProductBase> productMap = this.getProductInfoMapByProductCodes(productCodeList);
                // 退保被保人记录
                insuredList.forEach(insured -> {
                    insured.getInsuredProductList().forEach(x -> {
                        SettlementPolicyInfoEntity bean = BeanUtil.copyProperties(sourceBean, SettlementPolicyInfoEntity.class);
                        bean.setSettlementCode(PolicySettlementUtils.createCodeLastUuid("SC"));
                        // 险种信息
                        bean.setProductCode(x.getProductCode());
                        bean.setProductName(x.getProductName());
                        bean.setPlanCode(x.getPlanCode());
                        bean.setPlanName(x.getPlanName());
                        bean.setMainInsurance(x.getMainInsurance());
                        bean.setAdditionalRisksType(x.getAdditionalRisksType());
                        bean.setEffectiveDate(x.getEffectiveDate());
                        bean.setEndDate(DateUtil.formatDateTime(x.getEndDate()));
                        bean.setInsuredPeriodType(x.getInsuredPeriodType());
                        bean.setInsuredPeriod(x.getInsuredPeriod());
                        bean.setPeriodType(x.getPeriodType());
                        bean.setPaymentPeriodType(x.getPaymentPeriodType());
                        bean.setPaymentPeriod(x.getPaymentPeriod());
                        bean.setDrawAge(x.getAnnDrawAge());
                        bean.setPremium(x.getSurrenderPremium());
                        bean.setProductPremiumTotal(bean.getPremium());
                        bean.setCopies(x.getCopies());
                        // 如果保费为0 不生成明细
                        if (bean.getPremium().compareTo(BigDecimal.ZERO)==0) {
                            return;
                        }

                        // 险种分类信息
                        if (productMap.containsKey(x.getProductCode())) {
                            ProductBase productBase = productMap.get(x.getProductCode());
                            if (StrUtil.isBlank(bean.getProductName())) {
                                bean.setProductName(productBase.getProductName());
                            }
                            bean.setProductType(productBase.getProductType());
                            bean.setProductGroup(productBase.getProductGroup());
                            bean.setLevel2Code(productBase.getLevel2Code());
                            bean.setLevel3Code(productBase.getLevel3Code());
                            bean.setLongShortFlag(productBase.getLongShortFlag());
                        } else {
                            throw new GlobalException(
                                    BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + x.getProductCode()));
                        }
                        // 被保人信息
                        bean.setInsuredName(insured.getInsuredName());
                        bean.setInsuredMobile(insured.getInsuredMobile());
                        bean.setInsuredIdCard(insured.getInsuredIdCard());
                        bean.setInsuredBirthday(insured.getInsuredBirthday());
                        bean.setInsuredGender(insured.getInsuredGender());
                        if (bean.getInsuredBirthday() != null) {
                            bean.setInsuredPolicyAge(DateUtil.age(bean.getInsuredBirthday(), x.getEffectiveDate()));
                        }
                        settlementPolicyInfoList.add(bean);
                    });
                });
            }
        }
        // 如果没有人的维度信息在去处理险种维度的数据
        if (settlementPolicyInfoList.isEmpty() && CollUtil.isNotEmpty(productList)) {
            List<String> productCodeList = productList.stream().map(EpPreserveSurrenderProductVo::getProductCode).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(productCodeList)) {
                log.info("保全编码={},退保险种信息不存在", preservationDetail.getPreservationCode());
                return Collections.emptyList();
            }
            Map<String, ProductBase> productMap = this.getProductInfoMapByProductCodes(productCodeList);
            productList.forEach(x -> {
                SettlementPolicyInfoEntity bean = BeanUtil.copyProperties(sourceBean, SettlementPolicyInfoEntity.class);
                bean.setSettlementCode(PolicySettlementUtils.createCodeLastUuid("SC"));
                // 险种信息
                bean.setProductCode(x.getProductCode());
                bean.setProductName(x.getProductName());
                bean.setPlanCode(x.getPlanCode());
                bean.setPlanName(x.getPlanName());
                bean.setMainInsurance(x.getMainInsurance());
                bean.setAdditionalRisksType(x.getAdditionalRisksType());
                bean.setEffectiveDate(x.getEffectiveDate());
                bean.setEndDate(DateUtil.formatDateTime(x.getEndDate()));
                bean.setInsuredPeriodType(x.getInsuredPeriodType());
                bean.setInsuredPeriod(x.getInsuredPeriod());
                bean.setPeriodType(x.getPeriodType());
                bean.setPaymentPeriodType(x.getPaymentPeriodType());
                bean.setPaymentPeriod(x.getPaymentPeriod());
                bean.setDrawAge(x.getAnnDrawAge());
                bean.setPremium(x.getSurrenderPremium());
                bean.setProductPremiumTotal(bean.getPremium());
                bean.setCopies(x.getCopies());
                // 如果保费为0 不生成明细
                if (bean.getPremium().compareTo(BigDecimal.ZERO)==0) {
                    return;
                }

                // 险种分类信息
                if (productMap.containsKey(x.getProductCode())) {
                    ProductBase productBase = productMap.get(x.getProductCode());
                    if (StrUtil.isBlank(bean.getProductName())) {
                        bean.setProductName(productBase.getProductName());
                    }
                    bean.setProductType(productBase.getProductType());
                    bean.setProductGroup(productBase.getProductGroup());
                    bean.setLevel2Code(productBase.getLevel2Code());
                    bean.setLevel3Code(productBase.getLevel3Code());
                    bean.setLongShortFlag(productBase.getLongShortFlag());
                } else {
                    throw new GlobalException(
                            BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + x.getProductCode()));
                }
                // 被保人信息如果没有的话 就不填写了
                settlementPolicyInfoList.add(bean);
            });
        }
        return settlementPolicyInfoList;
    }

    /**
     * 处理车险保费表更
     *
     * @param preservationDetail 保全详情
     * @param sourceBean         基础信息
     * @param contractInfo       保单信息
     * @return 明细列表
     */
    private List<SettlementPolicyInfoEntity> handleVehicleInfoChange(EpPreservationV2Vo preservationDetail,
                                                                     SettlementPolicyInfoEntity sourceBean,
                                                                     EpContractInfoVo contractInfo) {
        if (preservationDetail.getSurrenderDetail() == null) {
            throw new GlobalException(
                    BasicCodeMsg.SERVER_ERROR.setMsg("保全编码=" + preservationDetail.getPreservationCode() + "，车险保费变更明细不存在="));
        }
        //如果是团险 直接取值主险信息就行
        PolicyProductTypeEnum prodTypeEnum = PolicyProductTypeEnum.getProdTypeEnum(contractInfo.getPolicyProductType());
        if (PolicyProductTypeEnum.VEHICLE != prodTypeEnum) {
            throw new GlobalException(
                    BasicCodeMsg.SERVER_ERROR.setMsg("保全编码=" + preservationDetail.getPreservationCode() + "，不是车险不处理"));
        }

        List<SettlementPolicyInfoEntity> settlementPolicyInfoList = new ArrayList<>();
        List<EpPreserveSurrenderInsuredVo> insuredList = preservationDetail.getSurrenderDetail().getInsuredList();
        List<EpPreserveSurrenderProductVo> productList = preservationDetail.getSurrenderDetail().getProductList();
        // 精确到人的维度就先处理人的,没有人的话 就处理处理到险种维度就行
        if (CollUtil.isNotEmpty(insuredList)) {
            List<String> productCodeList = insuredList.stream().flatMap(x -> x.getInsuredProductList().stream()).map(EpPreserveProductInsuredMapVo::getProductCode).distinct().collect(Collectors.toList());
            if (!CollUtil.isEmpty(productCodeList)) {
                Map<String, ProductBase> productMap = this.getProductInfoMapByProductCodes(productCodeList);
                // 退保被保人记录
                insuredList.forEach(insured -> {
                    insured.getInsuredProductList().forEach(x -> {
                        SettlementPolicyInfoEntity bean = BeanUtil.copyProperties(sourceBean, SettlementPolicyInfoEntity.class);
                        bean.setSettlementCode(PolicySettlementUtils.createCodeLastUuid("SC"));
                        // 险种信息
                        bean.setProductCode(x.getProductCode());
                        bean.setProductName(x.getProductName());
                        bean.setPlanCode(x.getPlanCode());
                        bean.setPlanName(x.getPlanName());
                        bean.setMainInsurance(x.getMainInsurance());
                        bean.setAdditionalRisksType(x.getAdditionalRisksType());
                        bean.setEffectiveDate(x.getEffectiveDate());
                        bean.setEndDate(DateUtil.formatDateTime(x.getEndDate()));
                        bean.setInsuredPeriodType(x.getInsuredPeriodType());
                        bean.setInsuredPeriod(x.getInsuredPeriod());
                        bean.setPeriodType(x.getPeriodType());
                        bean.setPaymentPeriodType(x.getPaymentPeriodType());
                        bean.setPaymentPeriod(x.getPaymentPeriod());
                        bean.setDrawAge(x.getAnnDrawAge());
//                        if (ProductStatusEnum.isEffectiveStatus(x.getProductStatus())) {
//                            bean.setPremium(x.getPremium());
//                        } else {
//                            bean.setPremium(x.getSurrenderPremium());
//                        }
//
//                        if (Objects.isNull(bean.getPremium())) {
//                            throw new GlobalException(
//                                    BasicCodeMsg.SERVER_ERROR.setMsg("保费信息不存在-{}" + preservationDetail.getPreservationCode()));
//                        }
                        bean.setPremium(x.getBusinessPremium());
                        bean.setProductPremiumTotal(bean.getPremium());
                        bean.setCopies(x.getCopies());
                        // 如果保费为0 不生成明细
                        if (bean.getPremium().compareTo(BigDecimal.ZERO)==0) {
                            return;
                        }

                        // 险种分类信息
                        if (productMap.containsKey(x.getProductCode())) {
                            ProductBase productBase = productMap.get(x.getProductCode());
                            if (StrUtil.isBlank(bean.getProductName())) {
                                bean.setProductName(productBase.getProductName());
                            }
                            bean.setProductType(productBase.getProductType());
                            bean.setProductGroup(productBase.getProductGroup());
                            bean.setLevel2Code(productBase.getLevel2Code());
                            bean.setLevel3Code(productBase.getLevel3Code());
                            bean.setLongShortFlag(productBase.getLongShortFlag());
                        } else {
                            throw new GlobalException(
                                    BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + x.getProductCode()));
                        }
                        // 被保人信息
                        bean.setInsuredName(insured.getInsuredName());
                        bean.setInsuredMobile(insured.getInsuredMobile());
                        bean.setInsuredIdCard(insured.getInsuredIdCard());
                        bean.setInsuredBirthday(insured.getInsuredBirthday());
                        bean.setInsuredGender(insured.getInsuredGender());
                        if (bean.getInsuredBirthday() != null) {
                            bean.setInsuredPolicyAge(DateUtil.age(bean.getInsuredBirthday(), x.getEffectiveDate()));
                        }
                        settlementPolicyInfoList.add(bean);
                    });
                });
            }
        }
        // 如果没有人的维度信息在去处理险种维度的数据
        if (settlementPolicyInfoList.isEmpty() && CollUtil.isNotEmpty(productList)) {
            List<String> productCodeList = productList.stream().map(EpPreserveSurrenderProductVo::getProductCode).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(productCodeList)) {
                log.info("保全编码={},退保险种信息不存在", preservationDetail.getPreservationCode());
                return Collections.emptyList();
            }
            Map<String, ProductBase> productMap = this.getProductInfoMapByProductCodes(productCodeList);
            productList.forEach(x -> {
                SettlementPolicyInfoEntity bean = BeanUtil.copyProperties(sourceBean, SettlementPolicyInfoEntity.class);
                bean.setSettlementCode(PolicySettlementUtils.createCodeLastUuid("SC"));
                // 险种信息
                bean.setProductCode(x.getProductCode());
                bean.setProductName(x.getProductName());
                bean.setPlanCode(x.getPlanCode());
                bean.setPlanName(x.getPlanName());
                bean.setMainInsurance(x.getMainInsurance());
                bean.setAdditionalRisksType(x.getAdditionalRisksType());
                bean.setEffectiveDate(x.getEffectiveDate());
                bean.setEndDate(DateUtil.formatDateTime(x.getEndDate()));
                bean.setInsuredPeriodType(x.getInsuredPeriodType());
                bean.setInsuredPeriod(x.getInsuredPeriod());
                bean.setPeriodType(x.getPeriodType());
                bean.setPaymentPeriodType(x.getPaymentPeriodType());
                bean.setPaymentPeriod(x.getPaymentPeriod());
                bean.setDrawAge(x.getAnnDrawAge());

                if (ProductStatusEnum.isEffectiveStatus(x.getProductStatus())) {
                    bean.setPremium(x.getPremium());
                } else {
                    bean.setPremium(x.getSurrenderPremium());
                }

                bean.setProductPremiumTotal(bean.getPremium());
                bean.setCopies(x.getCopies());

                if (Objects.isNull(bean.getPremium())) {
                    throw new GlobalException(
                            BasicCodeMsg.SERVER_ERROR.setMsg("保费信息不存在-{}" + preservationDetail.getPreservationCode()));
                }
                // 如果保费为0 不生成明细
                if (bean.getPremium().compareTo(BigDecimal.ZERO)==0) {
                    return;
                }
                // 险种分类信息
                if (productMap.containsKey(x.getProductCode())) {
                    ProductBase productBase = productMap.get(x.getProductCode());
                    if (StrUtil.isBlank(bean.getProductName())) {
                        bean.setProductName(productBase.getProductName());
                    }
                    bean.setProductType(productBase.getProductType());
                    bean.setProductGroup(productBase.getProductGroup());
                    bean.setLevel2Code(productBase.getLevel2Code());
                    bean.setLevel3Code(productBase.getLevel3Code());
                    bean.setLongShortFlag(productBase.getLongShortFlag());
                } else {
                    throw new GlobalException(
                            BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + x.getProductCode()));
                }
                // 被保人信息如果没有的话 就不填写了
                settlementPolicyInfoList.add(bean);
            });
        }
        return settlementPolicyInfoList;
    }

    /**
     * 处理标准退保
     */
    private List<SettlementPolicyInfoEntity> handlePreserveSurrender(EpPreservationV2Vo preservationDetail,
                                                                     SettlementPolicyInfoEntity sourceBean,
                                                                     EpContractInfoVo contractInfo) {
        if (preservationDetail.getSurrenderDetail() == null) {
            throw new GlobalException(
                    BasicCodeMsg.SERVER_ERROR.setMsg("保全编码=" + preservationDetail.getPreservationCode() + "，退保明细不存在="));
        }
        List<SettlementPolicyInfoEntity> settlementPolicyInfoList = new ArrayList<>();
        //如果是团险 直接取值主险信息就行
        PolicyProductTypeEnum prodTypeEnum = PolicyProductTypeEnum.getProdTypeEnum(contractInfo.getPolicyProductType());
        if (PolicyProductTypeEnum.GROUP == prodTypeEnum) {
            List<EpPreserveSurrenderProductVo> productList = preservationDetail.getSurrenderDetail().getProductList();
            if (CollUtil.isNotEmpty(productList)) {
                productList.stream().filter(f -> StatusEnum.NORMAL.getCode().equals(f.getMainInsurance()))
                        .findFirst().ifPresent(x -> {
                            ProductBase productBase = getProductInfoByProductCode(x.getProductCode());
                            SettlementPolicyInfoEntity bean = BeanUtil.copyProperties(sourceBean, SettlementPolicyInfoEntity.class);
                            bean.setSettlementCode(PolicySettlementUtils.createCodeLastUuid("SC"));
                            // 险种信息
                            bean.setProductCode(x.getProductCode());
                            bean.setProductName(x.getProductName());
                            bean.setPlanCode(x.getPlanCode());
                            bean.setPlanName(x.getPlanName());
                            bean.setMainInsurance(x.getMainInsurance());
                            bean.setAdditionalRisksType(x.getAdditionalRisksType());
                            bean.setEffectiveDate(x.getEffectiveDate());
                            bean.setEndDate(DateUtil.formatDateTime(x.getEndDate()));
                            bean.setInsuredPeriodType(x.getInsuredPeriodType());
                            bean.setInsuredPeriod(x.getInsuredPeriod());
                            bean.setPeriodType(x.getPeriodType());
                            bean.setPaymentPeriodType(x.getPaymentPeriodType());
                            bean.setPaymentPeriod(x.getPaymentPeriod());
                            bean.setDrawAge(x.getAnnDrawAge());
                            bean.setPremium(preservationDetail.getSurrenderCash().abs().negate());
                            // 险种总保费为正常保费
                            bean.setProductPremiumTotal(bean.getPremium());
                            bean.setCopies(x.getCopies());
                            // 险种分类信息
                            if (StrUtil.isBlank(bean.getProductName())) {
                                bean.setProductName(productBase.getProductName());
                            }
                            bean.setProductType(productBase.getProductType());
                            bean.setProductGroup(productBase.getProductGroup());
                            bean.setLevel2Code(productBase.getLevel2Code());
                            bean.setLevel3Code(productBase.getLevel3Code());
                            bean.setLongShortFlag(productBase.getLongShortFlag());
                            bean.setRenewalPeriod(1);
                            settlementPolicyInfoList.add(bean);
                        });
            }
        } else {
            List<EpPreserveSurrenderInsuredVo> insuredList = preservationDetail.getSurrenderDetail().getInsuredList();
            List<EpPreserveSurrenderProductVo> productList = preservationDetail.getSurrenderDetail().getProductList();
            // 精确到人的维度就先处理人的,没有人的话 就处理处理到险种维度就行
            if (CollUtil.isNotEmpty(insuredList)) {
                List<String> productCodeList = insuredList.stream().flatMap(x -> x.getInsuredProductList().stream()).map(EpPreserveProductInsuredMapVo::getProductCode).distinct().collect(Collectors.toList());
                if (!CollUtil.isEmpty(productCodeList)) {
                    Map<String, ProductBase> productMap = this.getProductInfoMapByProductCodes(productCodeList);
                    // 退保被保人记录
                    insuredList.forEach(insured -> {
                        insured.getInsuredProductList().forEach(x -> {
                            SettlementPolicyInfoEntity bean = BeanUtil.copyProperties(sourceBean, SettlementPolicyInfoEntity.class);
                            bean.setSettlementCode(PolicySettlementUtils.createCodeLastUuid("SC"));
                            // 险种信息
                            bean.setProductCode(x.getProductCode());
                            bean.setProductName(x.getProductName());
                            bean.setPlanCode(x.getPlanCode());
                            bean.setPlanName(x.getPlanName());
                            bean.setMainInsurance(x.getMainInsurance());
                            bean.setAdditionalRisksType(x.getAdditionalRisksType());
                            bean.setEffectiveDate(x.getEffectiveDate());
                            bean.setEndDate(DateUtil.formatDateTime(x.getEndDate()));
                            bean.setInsuredPeriodType(x.getInsuredPeriodType());
                            bean.setInsuredPeriod(x.getInsuredPeriod());
                            bean.setPeriodType(x.getPeriodType());
                            bean.setPaymentPeriodType(x.getPaymentPeriodType());
                            bean.setPaymentPeriod(x.getPaymentPeriod());
                            bean.setDrawAge(x.getAnnDrawAge());
                            bean.setPremium(x.getSurrenderPremium());
                            bean.setProductPremiumTotal(bean.getPremium());
                            bean.setCopies(x.getCopies());
                            // 如果保费为0 不生成明细
                            if (bean.getPremium().compareTo(BigDecimal.ZERO)==0) {
                                return;
                            }
                            // 险种分类信息
                            if (productMap.containsKey(x.getProductCode())) {
                                ProductBase productBase = productMap.get(x.getProductCode());
                                if (StrUtil.isBlank(bean.getProductName())) {
                                    bean.setProductName(productBase.getProductName());
                                }
                                bean.setProductType(productBase.getProductType());
                                bean.setProductGroup(productBase.getProductGroup());
                                bean.setLevel2Code(productBase.getLevel2Code());
                                bean.setLevel3Code(productBase.getLevel3Code());
                                bean.setLongShortFlag(productBase.getLongShortFlag());
                            } else {
                                throw new GlobalException(
                                        BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + x.getProductCode()));
                            }
                            // 被保人信息
                            bean.setInsuredName(insured.getInsuredName());
                            bean.setInsuredMobile(insured.getInsuredMobile());
                            bean.setInsuredIdCard(insured.getInsuredIdCard());
                            bean.setInsuredBirthday(insured.getInsuredBirthday());
                            bean.setInsuredGender(insured.getInsuredGender());
                            if (bean.getInsuredBirthday() != null) {
                                bean.setInsuredPolicyAge(DateUtil.age(bean.getInsuredBirthday(), x.getEffectiveDate()));
                            }
                            settlementPolicyInfoList.add(bean);
                        });
                    });
                }
            }
            // 如果没有人的维度信息在去处理险种维度的数据
            if (settlementPolicyInfoList.isEmpty() && CollUtil.isNotEmpty(productList)) {
                List<String> productCodeList = productList.stream().map(EpPreserveSurrenderProductVo::getProductCode).distinct().collect(Collectors.toList());
                if (CollUtil.isEmpty(productCodeList)) {
                    log.info("保全编码={},退保险种信息不存在", preservationDetail.getPreservationCode());
                    return Collections.emptyList();
                }
                Map<String, ProductBase> productMap = this.getProductInfoMapByProductCodes(productCodeList);
                productList.forEach(x -> {
                    SettlementPolicyInfoEntity bean = BeanUtil.copyProperties(sourceBean, SettlementPolicyInfoEntity.class);
                    bean.setSettlementCode(PolicySettlementUtils.createCodeLastUuid("SC"));
                    // 险种信息
                    bean.setProductCode(x.getProductCode());
                    bean.setProductName(x.getProductName());
                    bean.setPlanCode(x.getPlanCode());
                    bean.setPlanName(x.getPlanName());
                    bean.setMainInsurance(x.getMainInsurance());
                    bean.setAdditionalRisksType(x.getAdditionalRisksType());
                    bean.setEffectiveDate(x.getEffectiveDate());
                    bean.setEndDate(DateUtil.formatDateTime(x.getEndDate()));
                    bean.setInsuredPeriodType(x.getInsuredPeriodType());
                    bean.setInsuredPeriod(x.getInsuredPeriod());
                    bean.setPeriodType(x.getPeriodType());
                    bean.setPaymentPeriodType(x.getPaymentPeriodType());
                    bean.setPaymentPeriod(x.getPaymentPeriod());
                    bean.setDrawAge(x.getAnnDrawAge());
                    bean.setPremium(x.getSurrenderPremium());
                    bean.setProductPremiumTotal(bean.getPremium());
                    bean.setCopies(x.getCopies());
                    // 如果保费为0 不生成明细
                    if (bean.getPremium().compareTo(BigDecimal.ZERO)==0) {
                        return;
                    }
                    // 险种分类信息
                    if (productMap.containsKey(x.getProductCode())) {
                        ProductBase productBase = productMap.get(x.getProductCode());
                        if (StrUtil.isBlank(bean.getProductName())) {
                            bean.setProductName(productBase.getProductName());
                        }
                        bean.setProductType(productBase.getProductType());
                        bean.setProductGroup(productBase.getProductGroup());
                        bean.setLevel2Code(productBase.getLevel2Code());
                        bean.setLevel3Code(productBase.getLevel3Code());
                        bean.setLongShortFlag(productBase.getLongShortFlag());
                    } else {
                        throw new GlobalException(
                                BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + x.getProductCode()));
                    }
                    // 被保人信息如果没有的话 就不填写了
                    settlementPolicyInfoList.add(bean);
                });
            }
        }
        return settlementPolicyInfoList;
    }

    /**
     * 处理附加险解约
     */

    private List<SettlementPolicyInfoEntity> handleTerminationProduct(EpPreservationV2Vo preservationDetail,
                                                                      BasisSettlementPolicyInfoDomain domain,
                                                                      SettlementPolicyInfoEntity sourceBean) {
        List<SettlementPolicyInfoEntity> settlementPolicyInfoList = new ArrayList<>();
        List<EpPreserveProductVo> terminationProductList = preservationDetail.getTerminationProductList();
        if (CollUtil.isEmpty(terminationProductList)) {
            throw new GlobalException(
                    BasicCodeMsg.SERVER_ERROR.setMsg("附加险解约没有险种信息,保全编码=" + preservationDetail.getPreservationCode()));
        }
        // 合同信息
        EpContractInfoVo contractInfo = domain.getContractInfo();
        // 险种保费信息
        Map<String, BigDecimal> terminationProductMap = terminationProductList.stream()
                .collect(Collectors.toMap(EpPreserveProductVo::getProductCode, EpPreserveProductVo::getSurrenderCash));
        // 获取险种编码和详情map
        Map<String, ProductBase> productMap =
                this.getProductInfoMapByProductCodes(new ArrayList<>(terminationProductMap.keySet()));
        // 险种与被保人的关系
        Map<String, EpInsuredInfoVo> insuredMap = new HashMap<>();
        if (domain.getPolicyProductTypeEnum() == PolicyProductTypeEnum.PERSONAL || domain.getPolicyProductTypeEnum() == PolicyProductTypeEnum.PROPERTY) {
            domain.getContractInfo().getInsuredInfoList().forEach(action -> {
                action.getProductInfoList().forEach(productInfo -> {
                    if (!insuredMap.containsKey(productInfo.getProductCode()) && terminationProductMap.containsKey(
                            productInfo.getProductCode())) {
                        insuredMap.put(productInfo.getProductCode(), action);
                    }
                });
            });
        }
        // 险种编码与投保的险种信息
        Map<String, EpProductInfoVo> productInfoMap = contractInfo.getProductInfoList().stream()
                .collect(Collectors.toMap(EpProductInfoVo::getProductCode, Function.identity()));
        terminationProductMap.forEach((productCode, surrenderCash) -> {
            if (!productInfoMap.containsKey(productCode)) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                        "附加险解约险种编码[" + productCode + "]不在保单险种信息中,保全编码=" + preservationDetail.getPreservationCode()));
            }
            EpProductInfoVo x = productInfoMap.get(productCode);

            SettlementPolicyInfoEntity bean = BeanUtil.copyProperties(sourceBean, SettlementPolicyInfoEntity.class);
            bean.setSettlementCode(PolicySettlementUtils.createCodeLastUuid("SC"));
            // 被保人信息
            if (insuredMap.containsKey(productCode)) {
                EpInsuredInfoVo insured = insuredMap.get(productCode);
                bean.setInsuredName(insured.getInsuredName());
                bean.setInsuredMobile(insured.getInsuredMobile());
                bean.setInsuredIdCard(insured.getInsuredIdCard());
                bean.setInsuredGender(insured.getInsuredGender());
                bean.setInsuredBirthday(insured.getInsuredBirthday());
                bean.setInsuredPolicyAge(insured.getInsuredPolicyAge());
            }
            // 险种信息
            bean.setProductCode(x.getProductCode());
            bean.setProductName(x.getProductName());
            bean.setPlanCode(x.getPlanCode());
            bean.setPlanName(x.getPlanName());
            bean.setMainInsurance(x.getMainInsurance());
            bean.setAdditionalRisksType(x.getAdditionalRisksType());
            bean.setEffectiveDate(contractInfo.getContractExtendInfo().getEnforceTime());
            bean.setEndDate(PolicySettlementUtils.getInsuredEndDate(bean.getEffectiveDate(), x.getInsuredPeriod(),
                    x.getInsuredPeriodType()));
            bean.setCoverage(x.getCoverage());
            bean.setCoverageUnit(x.getCoverageUnit());
            bean.setCoverageUnitName(x.getCoverageUnitName());
            bean.setInsuredPeriodType(x.getInsuredPeriodType());
            bean.setInsuredPeriod(x.getInsuredPeriod());
            bean.setPeriodType(x.getPeriodType());
            bean.setPaymentPeriodType(x.getPaymentPeriodType());
            bean.setPaymentPeriod(x.getPaymentPeriod());
            bean.setDrawAge(x.getAnnDrawAge());
            bean.setPremium(surrenderCash.abs().negate());
            // 新契约个险，险种总保费为正常保费
            bean.setProductPremiumTotal(bean.getPremium());
            bean.setCopies(x.getCopies());
            if (bean.getPremium().compareTo(BigDecimal.ZERO)==0) {
                return;
            }
            // 其他信息
            if (productMap.containsKey(x.getProductCode())) {
                ProductBase productBase = productMap.get(x.getProductCode());
                if (StrUtil.isBlank(bean.getProductName())) {
                    bean.setProductName(productBase.getProductName());
                }
                bean.setProductType(productBase.getProductType());
                bean.setProductGroup(productBase.getProductGroup());
                bean.setLevel2Code(productBase.getLevel2Code());
                bean.setLevel3Code(productBase.getLevel3Code());
                bean.setLongShortFlag(productBase.getLongShortFlag());
                if (StrUtil.isBlank(productBase.getLevel2Code())) {
                    throw new GlobalException(
                            BasicCodeMsg.SERVER_ERROR.setMsg("险种编码=" + x.getProductCode() + "不存在二级分类"));
                }
            } else {
                // 这个情况是不存在的 但是为了严谨一点 加上了...
                throw new GlobalException(
                        BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + x.getProductCode()));
            }
            settlementPolicyInfoList.add(bean);
        });
        return settlementPolicyInfoList;
    }


    private List<SettlementPolicyInfoEntity> handleProtocolTermination(EpPreservationV2Vo preservationDetail,
                                                                       BasisSettlementPolicyInfoDomain domain,
                                                                       SettlementPolicyInfoEntity sourceBean) {
        List<SettlementPolicyInfoEntity> settlementPolicyInfoList = settlementPolicyFullRefundHandler(sourceBean, domain);
        Integer period = preservationDetail.getRenewalTermPeriod() == null ? 1 : preservationDetail.getRenewalTermPeriod();
        // 期数大于1 处理续期协议解约
        if (period > 1) {
            // 协议解约
            for (int i = period; i > 1; i--) {
                settlementPolicyInfoList.addAll(buildPolicyRenewalTerm(sourceBean, domain, i));
            }
        }
        return settlementPolicyInfoList;
    }

    /**
     * 处理续期的协议解约
     */
    private List<SettlementPolicyInfoEntity> buildPolicyRenewalTerm(SettlementPolicyInfoEntity sourceBean,
                                                                    BasisSettlementPolicyInfoDomain domain,
                                                                    Integer period) {
        List<SettlementPolicyInfoEntity> settlementPolicyInfoList = new ArrayList<>();
        // 获取续期详情
        PolicyRenewalTermOutput policyRenewalTermDetail = policyCenterBaseClient.getPolicyRenewalTermDetail(domain.getPolicyNo(), period);
        if (policyRenewalTermDetail == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                    "续期详情不存在,保单号=" + domain.getPolicyNo() + "期次=" + domain.getPeriod()));
        }
        PolicyRenewalTermBaseInfo baseInfo = policyRenewalTermDetail.getBaseInfo();
        if (baseInfo == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                    "续期基本信息不存在,保单号=" + domain.getPolicyNo() + "期次=" + domain.getPeriod()));
        }
        Date duePaymentTime = baseInfo.getDuePaymentTime();
        Date paymentTime = baseInfo.getPaymentTime();
        if (duePaymentTime == null || paymentTime == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                    StrUtil.format("续期事件 保单号={}, 缺少应收或实收时间信息", domain.getPolicyNo())));
        }
        // 只有个财才会有续期,其他类型暂时没有续期事件,所以判断一下被保人信息是否存在,如果不存在,那么直接报错
        List<PolicyRenewalTermInsuredInfo> insuredList = policyRenewalTermDetail.getInsuredList();
        if (CollUtil.isEmpty(insuredList)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                    "续期详情没有被保人信息,保单号=" + domain.getPolicyNo() + "期次=" + domain.getPeriod()));
        }
        List<String> productCodeList = insuredList.stream().flatMap(x -> x.getProductInfoList().stream()).map(PolicyRenewalTermProductInfo::getProductCode).distinct().collect(Collectors.toList());
        Map<String, ProductBase> productMap = this.getProductInfoMapByProductCodes(productCodeList);
        // 将他的续期信息替换基础信息
        sourceBean.setPayableTime(duePaymentTime);
        sourceBean.setRealityTime(paymentTime);
        sourceBean.setPaymentPeriodType(baseInfo.getPaymentType());
        EpContractInfoVo contractInfo = domain.getContractInfo();
        switch (domain.getPolicyProductTypeEnum()) {
            case PERSONAL:
            case PROPERTY: {
                // 处理个财保单信息
                insuredList.forEach(insured -> insured.getProductInfoList().forEach(x -> {
                    SettlementPolicyInfoEntity bean =
                            BeanUtil.copyProperties(sourceBean, SettlementPolicyInfoEntity.class);
                    bean.setSettlementCode(PolicySettlementUtils.createCodeLastUuid("SC"));
                    // 被保人信息
                    bean.setInsuredName(insured.getInsuredName());
                    bean.setInsuredMobile(insured.getInsuredMobile());
                    bean.setInsuredIdCard(insured.getInsuredIdCard());
                    bean.setInsuredGender(insured.getInsuredGender());
                    bean.setInsuredBirthday(insured.getInsuredBirthday());
                    bean.setInsuredPolicyAge(insured.getInsuredPolicyAge());
                    // 险种信息
                    bean.setProductCode(x.getProductCode());
                    bean.setProductName(x.getProductName());
                    bean.setPlanCode(x.getPlanCode());
                    bean.setPlanName(x.getPlanName());
                    bean.setMainInsurance(x.getMainInsurance());
                    bean.setAdditionalRisksType(x.getAdditionalRisksType());
                    bean.setEffectiveDate(contractInfo.getContractExtendInfo().getEnforceTime());
                    bean.setEndDate(
                            PolicySettlementUtils.getInsuredEndDate(bean.getEffectiveDate(), x.getInsuredPeriod(),
                                    x.getInsuredPeriodType()));
                    bean.setCoverage(x.getCoverage());
                    bean.setCoverageUnit(x.getCoverageUnit());
                    bean.setCoverageUnitName(x.getCoverageUnitName());
                    bean.setInsuredPeriodType(x.getInsuredPeriodType());
                    bean.setInsuredPeriod(x.getInsuredPeriod());
                    bean.setPeriodType(x.getPeriodType());
                    bean.setPaymentPeriodType(x.getPaymentPeriodType());
                    bean.setPaymentPeriod(x.getPaymentPeriod());
                    bean.setDrawAge(x.getAnnDrawAge());
                    bean.setPremium(x.getPremium().abs().negate());
                    bean.setProductPremiumTotal(bean.getPremium());
                    bean.setCopies(x.getCopies());
                    bean.setInsuranceType(InsuranceTypeEnum.RENEW_INSURANCE.getCode());
                    bean.setRenewalYear(period);
                    bean.setRenewalPeriod(period);
                    // 如果保费为0 不生成明细
                    if (bean.getPremium().compareTo(BigDecimal.ZERO)==0) {
                        return;
                    }
                    // 其他信息
                    if (productMap.containsKey(x.getProductCode())) {
                        ProductBase productBase = productMap.get(x.getProductCode());
                        if (StrUtil.isBlank(bean.getProductName())) {
                            bean.setProductName(productBase.getProductName());
                        }
                        bean.setProductGroup(productBase.getProductGroup());
                        bean.setProductType(productBase.getProductType());
                        bean.setLevel2Code(productBase.getLevel2Code());
                        bean.setLevel3Code(productBase.getLevel3Code());
                        bean.setLongShortFlag(productBase.getLongShortFlag());
                        if (StrUtil.isBlank(productBase.getLevel2Code())) {
                            throw new GlobalException(
                                    BasicCodeMsg.SERVER_ERROR.setMsg("险种编码=" + x.getProductCode() + "不存在二级分类"));
                        }
                    } else {
                        // 这个情况是不存在的 但是为了严谨一点 加上了...
                        throw new GlobalException(
                                BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + x.getProductCode()));
                    }
                    settlementPolicyInfoList.add(bean);
                }));
                break;
            }
            case GROUP:
            case VEHICLE: {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                        StrUtil.format("保单类型={},不支持协议解约事件,保单号={}," + "期数={}",
                                domain.getPolicyProductTypeEnum().getPolicyProductType(), domain.getPolicyNo(),
                                domain.getPeriod())));
            }
        }

        return settlementPolicyInfoList;
    }

    /**
     * 处理续期首年协议解约
     */
    private List<SettlementPolicyInfoEntity> settlementPolicyFullRefundHandler(SettlementPolicyInfoEntity sourceBean,
                                                                               BasisSettlementPolicyInfoDomain domain) {
        Map<String, ProductBase> productMap = this.getProductInfoMap(domain);
        EpContractInfoVo contractInfo = domain.getContractInfo();
        List<SettlementPolicyInfoEntity> settlementPolicyInfoList = new ArrayList<>();
        switch (domain.getPolicyProductTypeEnum()) {
            case PERSONAL:
            case PROPERTY: {
                // 处理个财保单信息
                domain.getContractInfo().getInsuredInfoList().forEach(insured -> {
                    insured.getProductInfoList().forEach(x -> {
                        SettlementPolicyInfoEntity bean =
                                BeanUtil.copyProperties(sourceBean, SettlementPolicyInfoEntity.class);
                        bean.setSettlementCode(PolicySettlementUtils.createCodeLastUuid("SC"));
                        // 被保人信息
                        bean.setInsuredName(insured.getInsuredName());
                        bean.setInsuredMobile(insured.getInsuredMobile());
                        bean.setInsuredIdCard(insured.getInsuredIdCard());
                        bean.setInsuredGender(insured.getInsuredGender());
                        bean.setInsuredBirthday(insured.getInsuredBirthday());
                        bean.setInsuredPolicyAge(insured.getInsuredPolicyAge());
                        // 险种信息
                        bean.setProductCode(x.getProductCode());
                        bean.setProductName(x.getProductName());
                        bean.setPlanCode(x.getPlanCode());
                        bean.setPlanName(x.getPlanName());
                        bean.setMainInsurance(x.getMainInsurance());
                        bean.setAdditionalRisksType(x.getAdditionalRisksType());
                        bean.setEffectiveDate(contractInfo.getContractExtendInfo().getEnforceTime());
                        bean.setEndDate(
                                PolicySettlementUtils.getInsuredEndDate(bean.getEffectiveDate(), x.getInsuredPeriod(),
                                        x.getInsuredPeriodType()));
                        bean.setCoverage(x.getCoverage());
                        bean.setCoverageUnit(x.getCoverageUnit());
                        bean.setCoverageUnitName(x.getCoverageUnitName());
                        bean.setInsuredPeriodType(x.getInsuredPeriodType());
                        bean.setInsuredPeriod(x.getInsuredPeriod());
                        bean.setPeriodType(x.getPeriodType());
                        bean.setPaymentPeriodType(x.getPaymentPeriodType());
                        bean.setPaymentPeriod(x.getPaymentPeriod());
                        bean.setDrawAge(x.getAnnDrawAge());
                        bean.setPremium(x.getPremium().abs().negate());
                        // 新契约个险，险种总保费为正常保费
                        bean.setProductPremiumTotal(bean.getPremium());
                        bean.setCopies(x.getCopies());
                        // 新单首年
                        bean.setInsuranceType(InsuranceTypeEnum.NEW_ORDER.getCode());
                        bean.setRenewalPeriod(1);
                        bean.setRenewalYear(1);
                        // 如果保费为0 不生成明细
                        if (bean.getPremium().compareTo(BigDecimal.ZERO)==0) {
                            return;
                        }
                        // 其他信息
                        if (productMap.containsKey(x.getProductCode())) {
                            ProductBase productBase = productMap.get(x.getProductCode());
                            if (StrUtil.isBlank(bean.getProductName())) {
                                bean.setProductName(productBase.getProductName());
                            }
                            bean.setProductType(productBase.getProductType());
                            bean.setProductGroup(productBase.getProductGroup());
                            bean.setLevel2Code(productBase.getLevel2Code());
                            bean.setLevel3Code(productBase.getLevel3Code());
                            bean.setLongShortFlag(productBase.getLongShortFlag());
                            if (StrUtil.isBlank(productBase.getLevel2Code())) {
                                throw new GlobalException(
                                        BasicCodeMsg.SERVER_ERROR.setMsg("险种编码=" + x.getProductCode() + "不存在二级分类"));
                            }
                        } else {
                            // 这个情况是不存在的 但是为了严谨一点 加上了...
                            throw new GlobalException(
                                    BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + x.getProductCode()));
                        }
                        settlementPolicyInfoList.add(bean);
                    });
                });
                break;
            }
            case GROUP:
            case VEHICLE: {
                // 团险 车险只到险种维度,不处理人员信息
                domain.getProductInfoList().forEach(x -> {
                    SettlementPolicyInfoEntity bean =
                            BeanUtil.copyProperties(sourceBean, SettlementPolicyInfoEntity.class);
                    bean.setSettlementCode(PolicySettlementUtils.createCodeLastUuid("SC"));
                    // 险种信息
                    bean.setProductCode(x.getProductCode());
                    bean.setProductName(x.getProductName());
                    bean.setPlanCode(x.getPlanCode());
                    bean.setPlanName(x.getPlanName());
                    bean.setMainInsurance(x.getMainInsurance());
                    bean.setAdditionalRisksType(x.getAdditionalRisksType());
                    bean.setEffectiveDate(contractInfo.getContractExtendInfo().getEnforceTime());
                    bean.setEndDate(
                            PolicySettlementUtils.getInsuredEndDate(bean.getEffectiveDate(), x.getInsuredPeriod(),
                                    x.getInsuredPeriodType()));
                    bean.setCoverage(x.getCoverage());
                    bean.setCoverageUnit(x.getCoverageUnit());
                    bean.setCoverageUnitName(x.getCoverageUnitName());
                    bean.setInsuredPeriodType(x.getInsuredPeriodType());
                    bean.setInsuredPeriod(x.getInsuredPeriod());
                    bean.setPeriodType(x.getPeriodType());
                    bean.setPaymentPeriodType(x.getPaymentPeriodType());
                    bean.setPaymentPeriod(x.getPaymentPeriod());
                    bean.setDrawAge(x.getAnnDrawAge());
                    bean.setPremium(x.getPremium().abs().negate());
                    // 险种总保费为正常保费
                    bean.setProductPremiumTotal(bean.getPremium());
                    bean.setCopies(x.getCopies());
                    // 新单首年
                    bean.setInsuranceType(InsuranceTypeEnum.NEW_ORDER.getCode());
                    bean.setRenewalPeriod(1);
                    bean.setRenewalYear(1);
                    // 如果保费为0 不生成明细
                    if (bean.getPremium().compareTo(BigDecimal.ZERO)==0) {
                        return;
                    }
                    // 险种分类信息
                    if (productMap.containsKey(x.getProductCode())) {
                        ProductBase productBase = productMap.get(x.getProductCode());
                        if (StrUtil.isBlank(bean.getProductName())) {
                            bean.setProductName(productBase.getProductName());
                        }
                        bean.setProductType(productBase.getProductType());
                        bean.setProductGroup(productBase.getProductGroup());
                        bean.setLevel2Code(productBase.getLevel2Code());
                        bean.setLevel3Code(productBase.getLevel3Code());
                        bean.setLongShortFlag(productBase.getLongShortFlag());
                    } else {
                        throw new GlobalException(
                                BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + x.getProductCode()));
                    }
                    settlementPolicyInfoList.add(bean);
                });
                break;
            }
        }
        return settlementPolicyInfoList;
    }
}
