package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementReconcileSummaryInfoDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileSummaryInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcileSummaryInfoService;

/**
 * 保司结算对账单汇总申请单
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:34
 */
@Slf4j
@Service("settlementReconcileSummaryInfoService")
public class SettlementReconcileSummaryInfoServiceImpl extends ServiceImpl<SettlementReconcileSummaryInfoDao, SettlementReconcileSummaryInfoEntity> implements SettlementReconcileSummaryInfoService {

}
