package com.mpolicy.settlement.core.thirdpart.service;

import com.alibaba.fastjson.JSON;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.thirdpart.client.BmsFeign;
import com.mpolicy.settlement.core.thirdpart.client.DbcFeign;
import com.mpolicy.settlement.core.thirdpart.request.DbcBusinessIncentiveCalculateDataAddDTO;
import com.mpolicy.settlement.core.thirdpart.response.BmsResult;
import com.mpolicy.settlement.core.thirdpart.response.ChongHoResult;
import com.mpolicy.settlement.core.thirdpart.response.UserDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class BmsClient {
    @Autowired
    private BmsFeign bmsFeign;


    public UserDetailVO getUserDetailVOByIdcard(String idCardNo){
        BmsResult<UserDetailVO> result = null;
        try {
            log.info("查询证件号为{}的详细信息",idCardNo);
            result = bmsFeign.getUserDetailVOByIdcard(idCardNo);
        } catch (Exception e) {
            log.warn("查询证件号为{}的详细信息异常", idCardNo,e);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("查询证件号为"+idCardNo+"的详细信息异常,请开发人员及时排查问题"));
        }
        if (!result.isSuccess()) {
            log.warn("查询证件号为{}的详细信息失败{}",idCardNo, JSON.toJSON(result) );
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("查询证件号为"+idCardNo+"的详细信息失败"));
        }
        return result.getData();
    }
}
