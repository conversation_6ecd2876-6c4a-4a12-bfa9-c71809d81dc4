package com.mpolicy.settlement.core.service.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.Result;
import com.mpolicy.product.client.OpenProductClient;
import com.mpolicy.product.client.ProductClient;
import com.mpolicy.product.common.base.ProductBase;
import com.mpolicy.product.common.company.CompanyInfo;
import com.mpolicy.product.common.portfolio.PortfolioBase;
import com.mpolicy.product.common.portfolio.PortfolioDetail;
import com.mpolicy.settlement.core.enums.SettlementExceptionEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 产品中心基础feign服务
 *
 * <AUTHOR>
 * @since 2023-05-22 12:03
 */
@Service
@Slf4j
public class ProductBaseService {

    @Autowired
    private ProductClient productClient;

    @Autowired
    private OpenProductClient openProductClient;

    /**
     * 根据保司编码获取保司的信息
     *
     * @param companyCode 保司编码
     * @return 险种类型
     */
    public CompanyInfo getCompanyInfo(String companyCode) {
        return getCompanyInfo(companyCode, true);
    }

    /**
     * 根据保司编码获取保司的信息
     *
     * @param companyCode 保司编码
     * @param isMust      是否必须获取到结果
     * @return 险种类型
     */
    public CompanyInfo getCompanyInfo(String companyCode, boolean isMust) {
        Result<CompanyInfo> companyInfoResult = productClient.queryCompanyInfo(companyCode);
        if (isMust && !companyInfoResult.isSuccess()) {
            throw new GlobalException(companyInfoResult);
        }
        return companyInfoResult.getData();
    }

    /**
     * 根据组合编码获取组合基本信息
     *
     * @param portfolioCode: 组合编码
     * <AUTHOR>
     * @since 2022/4/17
     */
    public PortfolioBase getPortfolioBaseInfo(String portfolioCode) {
        Result<PortfolioBase> result = productClient.queryPortfolioInfo(portfolioCode);
        if (!result.isSuccess()) {
            log.warn("根据组合获取产品中心组合基本信息错误，msg={}", result.getMsg());
            throw new GlobalException(result);
        }
        return result.getData();
    }

    /**
     * 根据组合编码获取组合扩展信息
     *
     * @param portfolioCode: 组合编码
     * @return {@link PortfolioBase }
     * <AUTHOR>
     * @since 2022/4/17
     */
    public PortfolioDetail getPortfolioDetail(String portfolioCode) {
        Result<PortfolioDetail> result = productClient.queryPortfolioDetail(portfolioCode);
        if (!result.isSuccess()) {
            log.warn("根据组合获取产品中心组合详细信息错误，msg={}", result.getMsg());
            throw new GlobalException(result);
        }
        return result.getData();
    }

    /**
     * 根据险种编码获取险种列表
     *
     * @param productCodeList 险种编码
     */
    public List<ProductBase> getProductBaseList(List<String> productCodeList) {
        Result<List<ProductBase>> result = productClient.queryProductListV2(productCodeList);
        if (!result.isSuccess()) {
            throw new GlobalException(SettlementExceptionEnum.PRODUCT_RETURN_EXCEPTION.getException(
                StrUtil.format("产品服务-返回:{},入参:{}", result, productCodeList)));
        }
        if (CollUtil.isEmpty(result.getData())) {
            return Collections.emptyList();
        }
        return result.getData();
    }

    /**
     * [map]根据险种代码集合获取信息信息
     *
     * @param productCodeList 险种代码集合
     * @return Map险种集合 key=险种编码 value=险种信息
     * <AUTHOR>
     * @since 2023/11/18 17:40
     */
    public Map<String, ProductBase> mapProductBase(List<String> productCodeList) {
        Result<List<ProductBase>> response = productClient.queryProductListV2(productCodeList);
        if (!response.isSuccess()) {
            throw new GlobalException(response);
        }
        List<ProductBase> productList = response.getData();
        Map<String, ProductBase> result = new HashMap<>();
        if (CollUtil.isNotEmpty(productList)) {
            result = productList.stream()
                .collect(Collectors.toMap(ProductBase::getProductCode, Function.identity(), (k1, k2) -> k1));
        }
        return result;
    }

    /**
     * 根据险种编码获取险种的信息
     *
     * @param productCode 险种编码
     * @return 险种类型
     */
    public ProductBase getProductInfo(String productCode) {
        Result<ProductBase> productBaseResult = productClient.queryProductInfo(productCode);
        log.info("产品中心返回险种数据:{},{}", productCode, productBaseResult);
        if (productBaseResult.isSuccess()) {
            return productBaseResult.getData();
        } else {
            throw new GlobalException(productBaseResult);
        }
    }

    /**
     * 获取所有农机险险种集合
     *
     * @return 农机险险种集合
     * <AUTHOR>
     * @since 2023/11/6 11:20
     */
    public List<ProductBase> getAgriculturalProductList() {
        Result<List<ProductBase>> result = productClient.queryAgriculturalProductList();
        if (!result.isSuccess()) {
            throw new GlobalException(result);
        }
        return result.getData();
    }

    /**
     * 获取保司列表
     *
     * @return
     */
    public List<CompanyInfo> findInsuranceCompanyList(List<String> companyCodeList) {
        Result<List<CompanyInfo>> result = productClient.queryCompanyListByCodes(companyCodeList);
        if (!result.isSuccess()) {
            throw new GlobalException(result);
        }
        List<CompanyInfo> list = result.getData();
        return list == null ? Collections.emptyList() : list;
    }

    public List<String> listAllPerformanceAllocationProductCode(){
        Result<List<String>> result = openProductClient.listAllPerformanceAllocationProductCode();
        if (!result.isSuccess()) {
            log.info("调用产品中心获取可分配绩效的险种列表异常，{}", JSONObject.toJSONString(result));
            throw new GlobalException(result);
        }
        List<String> list = result.getData();
        return list == null ? Collections.emptyList() : list;
    }
}
