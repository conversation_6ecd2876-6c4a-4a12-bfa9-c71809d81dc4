package com.mpolicy.settlement.core.modules.reconcile.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementInnerCompanyLicenseInfoEntity;

import java.util.List;

/**
 * 结算内部公司工商信息服务接口
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
public interface SettlementInnerCompanyLicenseInfoService extends IService<SettlementInnerCompanyLicenseInfoEntity> {

    /**
     * 批量保存或更新工商信息
     * 
     * @param licenseInfoList 工商信息列表
     */
    void saveOrUpdateBatch(List<SettlementInnerCompanyLicenseInfoEntity> licenseInfoList);

    /**
     * 根据统一社会信用代码查询工商信息
     * 
     * @param socialCreditCode 统一社会信用代码
     * @return 工商信息
     */
    SettlementInnerCompanyLicenseInfoEntity getBySocialCreditCode(String socialCreditCode);
}
