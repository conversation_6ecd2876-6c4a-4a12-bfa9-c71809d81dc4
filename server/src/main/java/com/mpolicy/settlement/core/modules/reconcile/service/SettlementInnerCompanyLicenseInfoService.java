package com.mpolicy.settlement.core.modules.reconcile.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementInnerCompanyLicenseInfoEntity;

import java.util.List;

/**
 * 结算内部公司工商信息服务接口
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
public interface SettlementInnerCompanyLicenseInfoService extends IService<SettlementInnerCompanyLicenseInfoEntity> {

    /**
     * 批量保存或更新工商信息（使用INSERT INTO ... ON DUPLICATE KEY UPDATE）
     *
     * @param licenseInfoList 工商信息列表
     */
    void batchInsertOrUpdate(List<SettlementInnerCompanyLicenseInfoEntity> licenseInfoList);

    /**
     * 根据统一社会信用代码查询工商信息（从缓存中获取）
     *
     * @param socialCreditCode 统一社会信用代码
     * @return 工商信息
     */
    SettlementInnerCompanyLicenseInfoEntity getBySocialCreditCodeFromCache(String socialCreditCode);

    /**
     * 刷新缓存
     */
    void refreshCache();

    /**
     * 根据统一社会信用代码查询工商信息（直接查询数据库）
     *
     * @param socialCreditCode 统一社会信用代码
     * @return 工商信息
     */
    SettlementInnerCompanyLicenseInfoEntity getBySocialCreditCode(String socialCreditCode);

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    String getCacheStatistics();

    /**
     * 获取缓存中所有工商信息
     *
     * @return 所有缓存的工商信息列表
     */
    List<SettlementInnerCompanyLicenseInfoEntity> getAllFromCache();
}
