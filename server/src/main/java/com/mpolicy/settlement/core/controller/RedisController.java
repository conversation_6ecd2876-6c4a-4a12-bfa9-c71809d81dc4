package com.mpolicy.settlement.core.controller;

import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.common.result.Result;
import com.mpolicy.settlement.core.common.SettlementCoreCenterKeys;
import com.mpolicy.web.common.annotation.PassToken;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 缓存服务 controller
 *
 * <AUTHOR>
 * @date 2022-10-11 12:30
 */
@RestController
@RequestMapping("/redis")
@Api(tags = "缓存服务")
@Slf4j
public class RedisController {

    @Autowired
    private IRedisService iRedisService;

    /**
     * 测试获取redis value
     */
    @ApiOperation(value = "缓存设置值", notes = "缓存设置值")
    @PostMapping("/set/{key}/{value}")
    @PassToken
    public Result<String> setRedisValue(@PathVariable @ApiParam(name = "key", value = "缓存key") String key,
                                        @PathVariable @ApiParam(name = "value", value = "value") String value) {
        boolean setStatus = iRedisService.set(SettlementCoreCenterKeys.TEST_KEY, key, value);
        log.info("设置缓存状态={}", setStatus);
        return Result.success();
    }

    /**
     * 测试获取redis value
     */
    @ApiOperation(value = "缓存根据key取值", notes = "缓存根据key取值")
    @GetMapping("/value/{key}")
    @PassToken
    public Result<String> getRedisValue(@PathVariable @ApiParam(name = "key", value = "缓存key") String key) {
        String value = iRedisService.get(SettlementCoreCenterKeys.TEST_KEY, key, String.class);
        return Result.success(value);
    }
}
