package com.mpolicy.settlement.core.modules.reconcile.dao;

import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementInnerCompanyLicenseInfoEntity;
import com.mpolicy.service.common.mapper.ImsBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 结算内部公司工商信息
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
public interface SettlementInnerCompanyLicenseInfoDao extends ImsBaseMapper<SettlementInnerCompanyLicenseInfoEntity> {

    /**
     * 批量插入或更新工商信息
     *
     * @param licenseInfoList 工商信息列表
     * @return 影响行数
     */
    int batchInsertOrUpdate(@Param("list") List<SettlementInnerCompanyLicenseInfoEntity> licenseInfoList);
}
