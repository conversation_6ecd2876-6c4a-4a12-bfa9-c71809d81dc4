package com.mpolicy.settlement.core.modules.reconcile.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 保司结算对账单差异待办
 * 
 * <AUTHOR>
 * @date 2023-05-21 22:28:34
 */
@TableName("settlement_reconcile_diff_backlog")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementReconcileDiffBacklogEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 差异受理编号
	 */
	private String backlogCode;
	/**
	 * 汇总对账纪录详情编号
	 */
	private String billCode;
	/**
	 * 对账唯一单号
	 */
	private String reconcileCode;
	/**
	 * 差异类型
	 */
	private String diffType;
	/**
	 * 差异类型名称
	 */
	private String diffName;
	/**
	 * 差异原因编码
	 */
	private String diffWhy;
	/**
	 * 差异原因名称
	 */
	private String diffWhyName;
	/**
	 * 差异保单编号
	 */
	private String policyNo;
	/**
	 * 是否自动冲正
	 */
	private Integer autoCorrect;
	/**
	 * 受理用户编号
	 */
	private String acceptUserCode;
	/**
	 * 受理用户名称
	 */
	private String acceptUserName;
	/**
	 * 差异处理说明
	 */
	private String diffDesc;

	/**
	 * 后续待办处理状态 后续待办处理状态0待处理1处理完成2无需处理
	 */
	private Integer followStatus;

	/**
	 * 差异处理完成时间
	 */
	private Date diffFinishTime;
	/**
	 * 差异处理结果
	 */
	private String diffResult;
	/**
	 * 差异处理状态0待处理1已处理
	 */
	private Integer diffStatus;
	/**
	 * 是否启用;0未启用1启用
	 */
	private Integer backlogEnable;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private Integer revision;
}
