package com.mpolicy.settlement.core.controller;


import com.mpolicy.common.redis.redisson.RedissLockUtil;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.strategy.redisson.enums.LockModel;
import com.mpolicy.common.utils.thread.ThreadUtils;
import com.mpolicy.settlement.core.common.reconcile.Message;
import com.mpolicy.web.common.annotation.Lock;
import com.mpolicy.web.common.annotation.PassToken;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 分布式锁 controller
 *
 * <AUTHOR>
 * @date 2022-10-11 11:27:48
 */
@RestController
@RequestMapping("/lock")
@Api(tags = "分布式锁")
@Slf4j
public class LockController {

    /**
     * 非阻塞锁
     */
    @ApiOperation(value = "非阻塞锁1", notes = "非阻塞锁1")
    @PostMapping("/no_block/{key}")
    @Lock(keys = "#key", attemptTimeout = 1000)
    @PassToken
    public Result<String> noBlockKey(@PathVariable @ApiParam(name = "key", value = "缓存key") String key,
                                     @RequestBody @ApiParam(name = "message") Message message) {
        ThreadUtils.sleep(10000);
        return Result.success();
    }

    /**
     * 非阻塞锁
     */
    @ApiOperation(value = "非阻塞锁2", notes = "非阻塞锁2")
    @PostMapping("/no_block/2/{key}")
    @Lock(keys = "#message.event", attemptTimeout = 1000)
    @PassToken
    public Result<String> noBlock2(@PathVariable @ApiParam(name = "key", value = "缓存key") String key,
                                   @RequestBody @ApiParam(name = "message") Message message) {
        ThreadUtils.sleep(10000);
        return Result.success();
    }

    /**
     * 联锁非阻塞锁
     * 需要 keys 都能获取到锁成功，才算成功
     * 红锁的话，则是大部分获取成功就算成功
     */
    @ApiOperation(value = "联锁非阻塞锁", notes = "联锁非阻塞锁")
    @PostMapping("/multipleLock/{key}")
    @Lock(keys = {"#message.event", "#message.msgType"}, attemptTimeout = 100, lockModel = LockModel.MULTIPLE)
    @PassToken
    public Result<String> multipleLock(@PathVariable @ApiParam(name = "key", value = "缓存key") String key,
                                       @RequestBody @ApiParam(name = "message") Message message) {
        ThreadUtils.sleep(10000);
        return Result.success();
    }

    /**
     * 阻塞锁
     */
    @ApiOperation(value = "阻塞锁", notes = "阻塞锁")
    @PostMapping("/block/{key}")
    @Lock(keys = "#message.event", attemptTimeout = -1)
    @PassToken
    public Result<String> blockLock(@PathVariable @ApiParam(name = "key", value = "缓存key") String key,
                                    @RequestBody @ApiParam(name = "message") Message message) {
        ThreadUtils.sleep(10000);
        return Result.success();
    }

    /**
     * 获取分布式锁
     */
    @ApiOperation(value = "获取分布式锁", notes = "获取分布式锁")
    @GetMapping("/lock/getLock")
    @PassToken
    public Result<String> getLock() {
        RedissLockUtil.tryLock("xiaoma", 200,-1);
        ThreadUtils.sleep(30000);
        RedissLockUtil.unlock("xiaoma");
        return Result.success();
    }
}
