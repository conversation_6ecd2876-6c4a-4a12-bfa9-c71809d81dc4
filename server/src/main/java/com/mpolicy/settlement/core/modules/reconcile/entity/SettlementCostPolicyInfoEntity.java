package com.mpolicy.settlement.core.modules.reconcile.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 结算支出端保单信息表
 * 
 * <AUTHOR>
 * @date 2023-08-09 22:28:39
 */
@TableName("settlement_cost_policy_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementCostPolicyInfoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;

	/**
	 * 保单合同号
	 */
	private String contractCode;
	/**
	 * 投保单号
	 */
	private String applicantPolicyNo;
	/**
	 * 小鲸保单号
	 */
	private String policyNo;
	/**
	 * 保司保单号
	 */
	private String companyPolicyNo;
	/**
	 * 代理人机构
	 */
	private String orgCode;

	/**
	 * 保单产品类型;个团车财
	 */
	private String policyProductType;
	/**
	 * 续投状态-1无、0到期前、1、到期后
	 */
	private Integer gracePeriodStatus;
	/**
	 * 保单状态
	 */
	private String policyStatus;
	/**
	 * 退保所在期数,自动结算用
	 */
	private Integer surrenderTermPeriod;
	/**
	 * 是否犹豫期退保;-1无0否1是
	 */
	private Integer hesitateSurrender;
	/**
	 * 保单来源
	 */
	private String policySource;

	/**
	 * 三方编码
	 */
	private String serialNumber;

	/**
	 * 保险公司编码
	 */
	private String companyCode;
	/**
	 * 保险公司名称
	 */
	private String companyName;
	/**
	 * 产品编码
	 */
	private String commodityCode;
	/**
	 * 产品名称
	 */
	private String commodityName;
	/**
	 * 产品类型
	 */
	private String commodityType;
	/**
	 * 主险编码
	 */
	private String mainProductCode;
	/**
	 * 主险名称
	 */
	private String mainProductName;

	/**
	 * 主险编码
	 */
	private Integer longShortFlag;
	/**
	 * 销售方式;0:网销 1:线下
	 */
	private Integer salesType;
	/**
	 * 是否自保件 0:否;1:是
	 */
	private Integer selfPreservation;
	/**
	 * 销售平台
	 */
	private String salesPlatform;
	/**
	 * 出单客户编号
	 */
	private String customerCode;
	/**
	 * 投保时间
	 */
	private Date applicantTime;
	/**
	 * 交单时间
	 */
	private Date orderTime;
	/**
	 * 承保时间
	 */
	private Date approvedTime;
	/**
	 * 生效时间
	 */
	private Date enforceTime;

	/**
	 * 回访状态 是否需要录入回访 1需要;0不需要
	 */
	private Integer revisitStatus;
	/**
	 * 回访结果  1成功;0失败
	 */
	private Integer revisitResult;
	/**
	 * 结算中心收到回访成功时间
	 */
	private Date revisitSuccessTime;
	/**
	 * 回访时间
	 */
	private Date revisitTime;
	/**
	 * 回执状态 是否需要录入回执 1需要;0不需要
	 */
	private Integer receiptStatus;
	/**
	 * 回执时间
	 */
	private Date receiptTime;

	/***** 投保人信息 ******/
	/**
	 * 投保人姓名
	 */
	private String applicantName;

	/**
	 * 投保人手机号码
	 */
	private String applicantMobile;

	/**
	 * 投保主体证件号
	 */
	private String applicantIdCard;

	/**
	 * 投保人出生日期
	 */
	private Integer applicantGender;

	/**
	 * 投保人出生日期
	 */
	private Date applicantBirthday;

	/**
	 * 投保人投保时年龄
	 */
	private Integer applicantAge;
	/**
	 * 整村推进 0否 1是
	 */
	private Integer ruralProxyFlag;
	/**
	 * 活动编码
	 */
	private String productActivityCode;
	/**
	 * 是否分销单 0否 1是
	 */
	private Integer distributionFlag;

	/**
	 * 附加险解约的险种编码串，以,隔开
	 */
	private String terminationProductCode;
	/**
	 * 车船税
	 */
	private BigDecimal vehicleVesselTax;
	/**
	 * 车船税率
	 */
	private BigDecimal vehicleVesselTaxRate;
	/**
	 * 车险评分等级
	 */
	private String vehicleBusinessScore;

	/**
	 * 是否删除;0有效-1删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private Integer revision;


}
