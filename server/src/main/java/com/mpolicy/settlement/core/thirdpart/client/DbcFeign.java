package com.mpolicy.settlement.core.thirdpart.client;

import com.mpolicy.settlement.core.thirdpart.request.DbcBusinessIncentiveCalculateDataAddDTO;
import com.mpolicy.settlement.core.thirdpart.request.WhaleAddCommissionPushQuery;
import com.mpolicy.settlement.core.thirdpart.response.AddCommissionSettlementPushDto;
import com.mpolicy.settlement.core.thirdpart.response.ChongHoResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
/**
 * 分销-api
 *
 * <AUTHOR>
 */
@FeignClient(name = "dbc-service", url = "${dbc-service.url}")
@Configuration
public interface DbcFeign {
    /**
     * 批量保存激励费计算基础数据
     *
     * @param addRecordList
     * @return
     */
    @ApiOperation(value = "批量保存激励费计算基础数据")
    @PostMapping("/bms-dbc-system/businessIncentiveCalculateData/saveBatch")
    ChongHoResult<Boolean> saveBatch(@RequestBody List<DbcBusinessIncentiveCalculateDataAddDTO> addRecordList);
}
