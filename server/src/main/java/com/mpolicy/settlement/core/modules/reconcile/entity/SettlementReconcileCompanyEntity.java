package com.mpolicy.settlement.core.modules.reconcile.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 保司结算对账单关联保司对账单数据
 * 
 * <AUTHOR>
 * @date 2023-05-21 22:28:34
 */
@TableName("settlement_reconcile_company")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementReconcileCompanyEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 保司对账业务编号
	 */
	private String companyBillCode;
	/**
	 * 对账保单业务汇总编号
	 */
	private String billCode;
	private String billKey;
	/**
	 * 对账唯一单号
	 */
	private String reconcileCode;
	/**
	 * 对账单名称
	 */
	private String reconcileName;
	/**
	 * 对账月度
	 */
	private String reconcileMonth;
	/**
	 * 保单号
	 */
	private String policyNo;
	/**
	 * 批单号
	 */
	private String endorsementNo;
	/**
	 * 险种编码
	 */
	private String productCode;
	/**
	 * 险种名称
	 */
	private String productName;
	/**
	 * 协议险种编码
	 */
	private String protocolProductCode;
	/**
	 * 协议险种名称
	 */
	private String protocolProductName;
	/**
	 * 记账科目编码
	 */
	private String settlementSubjectCode;
	/**
	 * 记账科目名称
	 */
	private String settlementSubjectName;
	/**
	 * 续期年期
	 */
	private Integer renewalYear;
	/**
	 * 续期期次
	 */
	private Integer renewalPeriod;
	/**
	 * 实收保费
	 */
	private BigDecimal realityPremium;
	/**
	 * 手续费占比
	 */
	private String settlementRate;
	/**
	 * 手续费金额
	 */
	private BigDecimal companyAmount;
	/**
	 * 来源文件类别
	 */
	private String reconcileFileType;
	/**
	 * 来源文件编码
	 */
	private String reconcileFileCode;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private Integer revision;
}
