package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.mpolicy.settlement.core.modules.reconcile.dao.SettlementReconcileOpeLogDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileOpeLogEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcileOpeLogService;

/**
 * 保司结算对账单操作纪录表
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:33
 */
@Slf4j
@Service("settlementReconcileOpeLogService")
public class SettlementReconcileOpeLogServiceImpl extends ServiceImpl<SettlementReconcileOpeLogDao, SettlementReconcileOpeLogEntity> implements SettlementReconcileOpeLogService {

}
