package com.mpolicy.settlement.core.modules.commission.helper;

import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementInnerCompanyLicenseInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementInnerCompanyLicenseInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;

/**
 * 集团内部公司判断工具类
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Slf4j
@Component
public class InnerCompanyHelper {

    private static InnerCompanyHelper innerCompanyHelper;

    @Autowired
    private SettlementInnerCompanyLicenseInfoService settlementInnerCompanyLicenseInfoService;

    @PostConstruct
    public void init() {
        innerCompanyHelper = this;
        innerCompanyHelper.settlementInnerCompanyLicenseInfoService = this.settlementInnerCompanyLicenseInfoService;
    }

    /**
     * 判断投保人是否为集团内部公司
     * 
     * @param applicantIdCard 投保人证件号（统一社会信用代码）
     * @param applicantType 投保人类型 0:非自然人; 1:自然人
     * @return true-是集团内部公司，false-不是集团内部公司
     */
    public static boolean isInnerCompany(String applicantIdCard, Integer applicantType) {
        // 如果投保人类型为自然人，直接返回false
        if (applicantType == null || applicantType == 1) {
            log.debug("投保人类型为自然人，不是集团内部公司，投保人证件号：{}", applicantIdCard);
            return false;
        }
        
        // 如果投保人证件号为空，返回false
        if (!StringUtils.hasText(applicantIdCard)) {
            log.debug("投保人证件号为空，无法判断是否为集团内部公司");
            return false;
        }
        
        try {
            // 从缓存中查询工商信息
            SettlementInnerCompanyLicenseInfoEntity licenseInfo = 
                    innerCompanyHelper.settlementInnerCompanyLicenseInfoService.getBySocialCreditCodeFromCache(applicantIdCard);
            
            boolean isInner = licenseInfo != null;
            log.debug("投保人证件号：{}，是否为集团内部公司：{}", applicantIdCard, isInner);
            
            return isInner;
        } catch (Exception e) {
            log.error("判断投保人是否为集团内部公司时发生异常，投保人证件号：{}", applicantIdCard, e);
            return false;
        }
    }

    /**
     * 判断是否为汇友诉责险且投保人为集团内部公司
     * 
     * @param productCode 商品编码
     * @param mainProductCode 组合编码
     * @param applicantIdCard 投保人证件号
     * @param applicantType 投保人类型
     * @return true-是汇友诉责险且投保人为集团内部公司，false-不是
     */
    public static boolean isHuiyouLiabilityInsuranceWithInnerCompany(String productCode, String mainProductCode, 
                                                                     String applicantIdCard, Integer applicantType) {
        // 汇友诉责险的商品编码和组合编码
        final String HUIYOU_PRODUCT_CODE = "P20250620091654878998";
        final String HUIYOU_MAIN_PRODUCT_CODE = "HYXHSSBQ25052301";
        
        // 判断是否为汇友诉责险
        boolean isHuiyouProduct = HUIYOU_PRODUCT_CODE.equals(productCode) || HUIYOU_MAIN_PRODUCT_CODE.equals(mainProductCode);
        
        if (!isHuiyouProduct) {
            return false;
        }
        
        log.info("检测到汇友诉责险，商品编码：{}，组合编码：{}，开始判断投保人是否为集团内部公司", productCode, mainProductCode);
        
        // 判断投保人是否为集团内部公司
        boolean isInner = isInnerCompany(applicantIdCard, applicantType);
        
        if (isInner) {
            log.info("汇友诉责险且投保人为集团内部公司，投保人证件号：{}，基础佣金和加佣费率将设置为0", applicantIdCard);
        } else {
            log.info("汇友诉责险但投保人不是集团内部公司，投保人证件号：{}，按正常费率处理", applicantIdCard);
        }
        
        return isInner;
    }
}
