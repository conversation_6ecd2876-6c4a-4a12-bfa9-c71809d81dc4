package com.mpolicy.settlement.core.modules.reconcile.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostPolicyInfoEntity;

import java.util.List;

/**
 * 结算支出端保单信息表
 *
 * <AUTHOR>
 */
public interface SettlementCostPolicyInfoService extends IService<SettlementCostPolicyInfoEntity> {
    /**
     * 根据合同号获取支出端保单基础信息
     * @param contractCode
     * @return
     */
    SettlementCostPolicyInfoEntity getCostPolicyInfoEntityByContractCode(String contractCode);

    //SettlementCostPolicyInfoEntity getCostPolicyInfoEntityByContractCodeAndPolicyNo(String contractCode, String policyNo);

    /**
     * 根据合同编号列表获取支出佣金保单信息
     * @param contractCodes
     * @return
     */
    List<SettlementCostPolicyInfoEntity> listCostPolicyInfoEntityByContractCodes(List<String> contractCodes);

    /**
     * 根据保单号列表获取支出佣金保单信息
     * @param policyNos
     * @return
     */
    //List<SettlementCostPolicyInfoEntity> listCostPolicyInfoEntityByPolicyNos(List<String> policyNos);

    /**
     *
     * @param idList
     * @return
     */
    Integer updateGoodsNameByIds(List<Integer> idList);

    /**
     *
     * @param num
     * @return
     */
    List<Integer> listNullGoodsNameRecords(Integer num);
}

