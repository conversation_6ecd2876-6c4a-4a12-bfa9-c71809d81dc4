package com.mpolicy.settlement.core.modules.reconcile.helper;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.open.common.cfpamf.hr.EmployeeByCodesRequest;
import com.mpolicy.open.common.cfpamf.hr.OrganizationRequest;
import com.mpolicy.open.common.cfpamf.hr.OrganizationResp;
import com.mpolicy.open.common.cfpamf.hr.PostingInfoResp;
import com.mpolicy.settlement.core.common.SettlementCoreCenterKeys;
import com.mpolicy.settlement.core.modules.reconcile.dto.cache.EmployeePostRecordCache;
import com.mpolicy.settlement.core.modules.reconcile.dto.employee.EmployeePostRecord;
import com.mpolicy.settlement.core.service.common.OpenApiBaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.NORM_DATE_PATTERN;
import static com.alibaba.fastjson.JSONValidator.Type.Array;

/**
 * 员工职位信息相关辅助类
 *
 * <AUTHOR>
 * @since 2024-07-28 12:24
 */
@Slf4j
@Component
public class EmployeePostRecordHelper {

    public static EmployeePostRecordHelper employeePostRecordHelper;

    @Autowired
    IRedisService redisService;


    @Autowired
    private OpenApiBaseService openApiBaseService;


    @PostConstruct
    public void init() {
        employeePostRecordHelper = this;
        // redis服务
        employeePostRecordHelper.redisService = this.redisService;
        employeePostRecordHelper.openApiBaseService = this.openApiBaseService;
    }

    /**
     * 初始换机构分支缓存
     *
     * <AUTHOR>
     * @since 2023/11/28 12:33
     */
    public static EmployeePostRecordCache getEmployeePostRecordCache(String employeeCode) {
        EmployeePostRecordCache postRecordCache = employeePostRecordHelper.redisService.get(SettlementCoreCenterKeys.EMPLOYEE_POST_RECORD,employeeCode,EmployeePostRecordCache.class);
        //缓存一天，日期不是当天的话，则重新获取
        String nowStr = DateUtil.format(DateUtil.date(),NORM_DATE_PATTERN);
        if(postRecordCache == null || !Objects.equals(nowStr,postRecordCache.getCacheDate())){
            List<EmployeePostRecord> list = queryRecordByEmployeeCodes(Arrays.asList(employeeCode));
            if(!CollectionUtils.isEmpty(list)){
                postRecordCache = new EmployeePostRecordCache();
                BeanUtils.copyProperties(list.get(0),postRecordCache);
                postRecordCache.setCacheDate(nowStr);
                employeePostRecordHelper.redisService.set(SettlementCoreCenterKeys.EMPLOYEE_POST_RECORD, employeeCode, JSONArray.toJSON(postRecordCache));
            }
        }
        return postRecordCache;
    }

    public static List<EmployeePostRecord> queryRecordByEmployeeCodes(List<String> employeeCodes) {
        List<EmployeePostRecord> list = new ArrayList<>();
        EmployeeByCodesRequest request= new EmployeeByCodesRequest();
        Integer page = 1;
        request.setPage(page);
        request.setSize(200);
        request.setYear(null);
        request.setMonth(null);
        request.setEmployeeCodes(employeeCodes);
        // 2-1 分页获取分支信息
        List<PostingInfoResp> postList = employeePostRecordHelper.openApiBaseService.getRecordByEmployeeCodes(request, true);
        if (postList.isEmpty()) {
            return Collections.EMPTY_LIST;
        }

        postList.forEach(x -> {
            EmployeePostRecord bean = new EmployeePostRecord();
            BeanUtils.copyProperties(x, bean);

            list.add(bean);
        });
        return list;
    }
}