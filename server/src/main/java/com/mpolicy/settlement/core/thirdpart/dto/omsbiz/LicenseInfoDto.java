package com.mpolicy.settlement.core.thirdpart.dto.omsbiz;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 工商信息DTO
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LicenseInfoDto {

    /**
     * 经营状态 1、经营中 2 已注销 3注销中 4待注销
     */
    private Integer businessLicenseStatus;

    /**
     * 工商信息性质(0：总子公司 1：分支机构)
     */
    private Integer businessNature;

    /**
     * id
     */
    private Integer id;

    /**
     * 营业执照名称
     */
    private String licenseName;

    /**
     * 营业执照类型
     */
    private Integer licenseType;

    /**
     * 经营类型 1-业务主体；2-合规主体
     */
    private Integer managementType;

    /**
     * 修改时间（时间戳）
     */
    private Date modifyDate;

    /**
     * 统一社会信用代码/税务登记证号码
     */
    private String socialCreditCode;

    /**
     * 主体类型 1-小贷公司；2-项目公司；3-咨询公司
     */
    private Integer subjectType;
}
