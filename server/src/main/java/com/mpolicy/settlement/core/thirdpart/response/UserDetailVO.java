package com.mpolicy.settlement.core.thirdpart.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class UserDetailVO extends UserListVO implements Serializable {
    @ApiModelProperty("是否总部")
    private Boolean isHeadOrg;

    @ApiModelProperty("兼职信息")
    private List<UserPartTimerVO> userPartTimerList;

    @ApiModelProperty("岗位信息")
    private List<UserPostVO> postList;
    @ApiModelProperty("角色信息")
    private List<UserRoleVO> roleList;

    public UserDetailVO() {
    }


    @Data
    public static class UserRoleVO {
        @ApiModelProperty("系统Id")
        private String systemId;
        @ApiModelProperty("系统名称")
        private String systemShortName;
        @ApiModelProperty("角色Id")
        private Integer roleId;
        @ApiModelProperty("角色编码")
        private String roleCode;
        @ApiModelProperty("角色名称")
        private String roleName;
        @ApiModelProperty("用户角色类型(1、用户角色，2、岗位角色)")
        private Integer userRoleType;
        @ApiModelProperty("岗位Id")
        private Integer postId;
        @ApiModelProperty("人力岗位Id")
        private Integer hrPostId;
        @ApiModelProperty("岗位名称")
        private String postName;
        @ApiModelProperty("HR任职记录code,任职唯一识别编码")
        private String jobCode;
        @ApiModelProperty("所属组织Id")
        private Integer orgId;
        @ApiModelProperty("所属HR组织机构ID")
        private Integer hrOrgId;
        @ApiModelProperty("树结构路径")
        private String hrOrgTreePath;
        @ApiModelProperty("所属组织名称")
        private String orgName;


    }

    @Data
    public static class UserPostVO {
        @ApiModelProperty("北森用户Id")
        private Integer hrUserId;
        @ApiModelProperty("行政上级/直线经理Id")
        private String userAdminId;
        @ApiModelProperty("行政上级/直线经理")
        private String userAdminName;
        @ApiModelProperty("业务上级/虚线经理Id")
        private String userMasterId;
        @ApiModelProperty("业务上级/虚线经理")
        private String userMasterName;
        @ApiModelProperty("所属岗位postId")
        private Integer postId;
        @ApiModelProperty("所属岗位(职务)")
        private String postName;
        @ApiModelProperty("北森岗位Id")
        private Integer hrPostId;
        @ApiModelProperty("所属岗位(职务)状态：0:停用,1:启用)")
        private Integer postStatus;
        @ApiModelProperty("所属组织(Id)")
        private Integer orgId;
        @ApiModelProperty("所属组织(Code)")
        private String orgCode;
        @ApiModelProperty("所属组织(名称)")
        private String orgName;
        @ApiModelProperty("机构表的hrOrgId")
        private Integer hrOrgId;
        @ApiModelProperty("所属组织层级结构")
        private String hrOrgTreePath;
        @ApiModelProperty("所属组织兼职工号，如果没有就是主职工号")
        private String jobNumber;
        @ApiModelProperty("任职类型（0:正职、1:兼职）")
        private Integer serviceType;
    }
}
