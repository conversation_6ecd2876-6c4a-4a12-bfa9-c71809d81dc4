package com.mpolicy.settlement.core.modules.reconcile.helper;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mpolicy.agent.common.model.product.AllSellProductListOut;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.other.BigDecimalUtils;
import com.mpolicy.policy.common.enums.PolicyPaymentPeriodTypeEnum;
import com.mpolicy.policy.common.enums.PolicyPaymentTypeEnum;
import com.mpolicy.policy.common.policy.vo.base.MainProductInfoVo;
import com.mpolicy.product.common.base.ProductBase;
import com.mpolicy.settlement.core.common.SettlementCoreCenterKeys;
import com.mpolicy.settlement.core.common.reconcile.SettlementStatusInput;
import com.mpolicy.settlement.core.common.reconcile.SettlementStatusOut;
import com.mpolicy.settlement.core.common.reconcile.company.ReconcileRuleFileTemplate;
import com.mpolicy.settlement.core.common.reconcile.enums.*;
import com.mpolicy.settlement.core.enums.InsuranceTypeEnum;
import com.mpolicy.settlement.core.modules.protocol.dto.ProtocolInsuranceProductInfoOut;
import com.mpolicy.settlement.core.modules.protocol.helper.ProtocolBaseHelper;
import com.mpolicy.settlement.core.modules.reconcile.dto.cache.SellProductCache;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.CsCommissionDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.*;
import com.mpolicy.settlement.core.modules.reconcile.enums.PolicyProductTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementGenerateTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.factory.PremEventFactory;
import com.mpolicy.settlement.core.modules.reconcile.event.factory.ReconcileTemplateFactory;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.PremEventHandler;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.ReconcileTemplateHandler;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.*;
import com.mpolicy.settlement.core.modules.reconcile.service.*;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import com.mpolicy.settlement.core.service.common.AgentBaseService;
import com.mpolicy.settlement.core.service.common.PolicyCenterBaseClient;
import com.mpolicy.settlement.core.service.common.ProductBaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 协议管理基础辅助服务
 *
 * <AUTHOR>
 * @since 2023/05/20
 */
@Component
@Slf4j
public class ReconcileBaseHelper {

    public static ReconcileBaseHelper reconcileBaseHelper;

    @Autowired
    protected ProductBaseService productBaseService;

    @Autowired
    IRedisService redisService;

    @Autowired
    private SettlementReconcileInfoService settlementReconcileInfoService;
    @Autowired
    private SettlementReconcilePolicyFileService settlementReconcilePolicyFileService;

    @Autowired
    private SettlementReconcilePolicyService settlementReconcilePolicyService;

    @Autowired
    private SettlementReconcileCompanyService settlementReconcileCompanyService;

    @Autowired
    private SettlementReconcileSubjectService settlementReconcileSubjectService;

    @Autowired
    private SettlementReconcileConfirmService settlementReconcileConfirmService;

    @Autowired
    private SettlementReconcileDiffBacklogService settlementReconcileDiffBacklogService;

    @Autowired
    private CsVehicleCommissionDetailService csVehicleCommissionDetailService;

    @Autowired
    private SettlementPolicyInfoService settlementPolicyInfoService;
    @Autowired
    protected TransactionTemplate transactionTemplate;

    @Autowired
    private PolicyCenterBaseClient policyCenterBaseClient;

    @Autowired
    private AgentBaseService agentBaseService;
    private final static List<String> SERVICE_FEE_LIST = Arrays.asList(
            ReconcileSubjectOnlineEnum.TECHNICAL_SERVICE_FEE.getCode(),
            ReconcileSubjectOnlineEnum.CONSULTING_SERVICE_FEE.getCode(),
            ReconcileSubjectOnlineEnum.PROMOTION_SERVICE_FEE.getCode(),
            ReconcileSubjectOnlineEnum.EXTENSION_SERVICE_FEE.getCode(),
            ReconcileSubjectOnlineEnum.MODERN_SERVICE_FEE.getCode());


    /**
     * 这些产品是需要进场产品编码拆分的
     */
    private static final List<String> SPLIT_INSURANCE_PRODUCT_CODE_LIST =
            CollUtil.newArrayList("150", "172", "173", "180", "190", "573", "923");

    private static List<String> RECONCILE_SUBJECT_LIST =
            CollUtil.newArrayList(ReconcileSubjectOnlineEnum.FIRST_YR_COMM.getCode(),
                    ReconcileSubjectOnlineEnum.CONSULTING_SERVICE_FEE.getCode(), ReconcileSubjectOnlineEnum.PROMOTION_SERVICE_FEE.getCode(),
                    ReconcileSubjectOnlineEnum.MODERN_SERVICE_FEE.getCode(), ReconcileSubjectOnlineEnum.EXTENSION_SERVICE_FEE.getCode(),
                    ReconcileSubjectOnlineEnum.TECHNICAL_SERVICE_FEE.getCode(),
                    ReconcileSubjectOnlineEnum.SERVICE_FEE.getCode());

    @PostConstruct
    public void init() {
        reconcileBaseHelper = this;
        // redis服务
        reconcileBaseHelper.redisService = this.redisService;
        reconcileBaseHelper.policyCenterBaseClient = this.policyCenterBaseClient;
        reconcileBaseHelper.productBaseService = this.productBaseService;
        reconcileBaseHelper.settlementReconcileSubjectService = this.settlementReconcileSubjectService;
        reconcileBaseHelper.settlementReconcileInfoService = this.settlementReconcileInfoService;
        reconcileBaseHelper.settlementReconcilePolicyService = this.settlementReconcilePolicyService;
        reconcileBaseHelper.settlementReconcileCompanyService = this.settlementReconcileCompanyService;
        reconcileBaseHelper.settlementReconcileConfirmService = this.settlementReconcileConfirmService;
        reconcileBaseHelper.settlementReconcileDiffBacklogService = this.settlementReconcileDiffBacklogService;
        reconcileBaseHelper.csVehicleCommissionDetailService = this.csVehicleCommissionDetailService;
        reconcileBaseHelper.transactionTemplate = this.transactionTemplate;
        reconcileBaseHelper.agentBaseService = this.agentBaseService;
        reconcileBaseHelper.settlementReconcilePolicyFileService = this.settlementReconcilePolicyFileService;
        reconcileBaseHelper.settlementPolicyInfoService = this.settlementPolicyInfoService;

    }

    /**
     * 重置对账 2: 清空线上对账明细 settlement_reconcile_policy 纪录表 3: 清空包含线上和线下的保司对账单 settlement_reconcile_company 5: 清空对账单汇总纪录
     * settlement_reconcile_confirm 1: 清空差异待办实现纪录 settlement_reconcile_diff_backlog
     *
     * @param reconcileCode 对账唯一单号
     * <AUTHOR>
     * @since 2023/5/22 21:03
     */
    public static void resetReconcile(String reconcileCode) {
        // 判断对账单状态为未完成对账状态
        SettlementReconcileInfoEntity settlementReconcileInfo =
                reconcileBaseHelper.settlementReconcileInfoService.getOne(
                        new LambdaQueryWrapper<SettlementReconcileInfoEntity>().eq(
                                SettlementReconcileInfoEntity::getReconcileCode, reconcileCode));
        if (settlementReconcileInfo.getReconcileStatus() == 3) {
            log.info("对账单已经完成对账，无法进行重置={}", reconcileCode);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("对账单已经完成对账，无法进行重置"));
        }
        Boolean sendResult = reconcileBaseHelper.transactionTemplate.execute((status) -> {
            // 执行重置
            try {
                reconcileBaseHelper.settlementReconcilePolicyService.remove(
                        Wrappers.<SettlementReconcilePolicyEntity>lambdaQuery()
                                .eq(SettlementReconcilePolicyEntity::getReconcileCode, reconcileCode));
                reconcileBaseHelper.settlementReconcileCompanyService.remove(
                        Wrappers.<SettlementReconcileCompanyEntity>lambdaQuery()
                                .eq(SettlementReconcileCompanyEntity::getReconcileCode, reconcileCode));
                reconcileBaseHelper.settlementReconcileConfirmService.remove(
                        Wrappers.<SettlementReconcileConfirmEntity>lambdaQuery()
                                .eq(SettlementReconcileConfirmEntity::getReconcileCode, reconcileCode));
                reconcileBaseHelper.settlementReconcileDiffBacklogService.remove(
                        Wrappers.<SettlementReconcileDiffBacklogEntity>lambdaQuery()
                                .eq(SettlementReconcileDiffBacklogEntity::getReconcileCode, reconcileCode));
                // 重置为待对账
                settlementReconcileInfo.setCompanyAmount(BigDecimal.ZERO);
                settlementReconcileInfo.setXiaowhaleAmount(BigDecimal.ZERO);
                settlementReconcileInfo.setDiffAmount(BigDecimal.ZERO);
                settlementReconcileInfo.setReversalAmount(BigDecimal.ZERO);
                settlementReconcileInfo.setReconcileStatus(ReconcileStatusEnum.TO_BE_RECONCILE.getStatusCode());
                reconcileBaseHelper.settlementReconcileInfoService.updateById(settlementReconcileInfo);
                log.info("重置对账单完成，重置对账单单号={}", reconcileCode);
                // 更新对账科目信息
                reconcileBaseHelper.settlementReconcileSubjectService.updateSettlementReconcileSubject(reconcileCode);

                return true;
            } catch (Exception e) {
                status.setRollbackOnly();
                log.warn("重置对账单完成出现异常：", e);
                return false;
            }
        });
        if (Boolean.FALSE.equals(sendResult)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("重置对账单完成，请稍后重试"));
        }
    }

    /**
     * 加载规则文件缓存数据
     *
     * @param reconcileTemplate 对账单模版类型
     * @param fileCode          文件编码
     * <AUTHOR>
     * @since 2023/5/22 21:39
     */
    public static List<ReconcileRuleFileTemplate> loadReconcileRuleFileData(ReconcileTemplateEnum reconcileTemplate,
                                                                            String fileCode, String reconcileCode) {
        ReconcileTemplateHandler invokeStrategy = ReconcileTemplateFactory.getInvoke(reconcileTemplate.getCode());
        List<ReconcileRuleFileTemplate> resultList = invokeStrategy.readFile(fileCode, reconcileCode);
        return resultList;
    }

    /**
     * 将上传的模版转成小鲸对账单 小鲸对账单必须要有险种信息
     *
     * @param reconcileInfo
     * @param reconcileRuleFileTemplates
     * @return
     */
    public static List<SettlementReconcilePolicyEntity> buildSettlementReconcilePolicy(
            SettlementReconcileInfoEntity reconcileInfo,
            List<SettlementReconcilePolicyFileEntity> reconcileRuleFileTemplates, Map<String, ProductBase> productMap) {
        List<SettlementReconcilePolicyEntity> result = new ArrayList<>();
        // 遍历小鲸对账单数据
        reconcileRuleFileTemplates.forEach(x -> {
            // 如果没有匹配到险种信息 那个小鲸是没有对账清单的
            if (StrUtil.isBlank(x.getProductCode()) || !productMap.containsKey(x.getProductCode())) {
                return;
            }
            SettlementReconcilePolicyEntity bean = new SettlementReconcilePolicyEntity();
            BeanUtils.copyProperties(reconcileInfo, bean);
            // 生成policyBillCode 编号 + 系统生成标记
            bean.setPolicyBillCode(PolicySettlementUtils.createCodeLastUuid("PB"));
            bean.setReconcileGenerateType(SettlementGenerateTypeEnum.OFFLINE_RECONCILE_CONFIRM.getCode());
            ProductBase productBase = productMap.get(x.getProductCode());
            if (StrUtil.isBlank(productBase.getLevel2Code())) {
                throw new GlobalException(
                        BasicCodeMsg.SERVER_ERROR.setMsg("险种编码=" + x.getProductCode() + "不存在二级分类"));
            }
            // 其他信息
            bean.setPolicyNo(x.getPolicyNo());
            bean.setEndorsementNo(x.getBatchCode());
            bean.setProductCode(x.getProductCode());
            bean.setProductName(x.getProductName());
            bean.setProtocolProductCode(x.getInsuranceProductCode());
            bean.setProtocolProductName(x.getInsuranceProductName());
            bean.setProductGroup(productBase.getProductGroup());
            bean.setPolicyProductType(productBase.getProductGroup());
            bean.setLevel2Code(productBase.getLevel2Code());
            bean.setLevel3Code(productBase.getLevel3Code());
            bean.setReconcileSubjectCode(x.getReconcileSubjectCode());
            bean.setReconcileSubjectName(x.getReconcileSubjectName());
            bean.setCompanyCode(productBase.getCompanyCode());
            bean.setCompanyName(productBase.getCompanyName());
            // 处理新单或者续期
            if (StringUtils.equals(x.getInsuranceType(), InsuranceTypeEnum.NEW_ORDER.getDesc())) {
                bean.setInsuranceType(0);
                PolicyProductTypeEnum prodTypeEnum = PolicyProductTypeEnum.getProdTypeEnum(bean.getPolicyProductType());
                if (PolicyProductTypeEnum.GROUP == prodTypeEnum) {
                    bean.setSettlementEventCode(SettlementEventTypeEnum.GROUP_NEW_POLICY.getEventCode());
                    bean.setSettlementEventDesc(SettlementEventTypeEnum.GROUP_NEW_POLICY.getEventName());
                } else {
                    bean.setSettlementEventCode(SettlementEventTypeEnum.PERSONAL_NEW_POLICY.getEventCode());
                    bean.setSettlementEventDesc(SettlementEventTypeEnum.PERSONAL_NEW_POLICY.getEventName());
                }
            } else {
                bean.setInsuranceType(1);
                bean.setSettlementEventCode(SettlementEventTypeEnum.RENEWAL_TERM_POLICY.getEventCode());
                bean.setSettlementEventDesc(SettlementEventTypeEnum.RENEWAL_TERM_POLICY.getEventName());
            }
            bean.setRenewalPeriod(x.getRenewalPeriod());
            bean.setPremium(x.getRealityPremium());
            bean.setProductPremiumTotal(x.getRealityPremium());
            bean.setSettlementRate(x.getSettlementRate() + "");
            bean.setSettlementAmount(x.getCompanyAmount().setScale(2, RoundingMode.HALF_UP));
            bean.setRevision(1);
            bean.setCreateUser(reconcileInfo.getUpdateUser());
            bean.setUpdateUser(reconcileInfo.getUpdateUser());
            result.add(bean);
        });
        return result;
    }

    /**
     * 将上传的模版转成保司对账单
     *
     * @param reconcileInfo
     * @param reconcileRuleFileTemplates
     * @return
     */
    public static List<SettlementReconcileCompanyEntity> buildSettlementReconcileCompany(
            SettlementReconcileInfoEntity reconcileInfo,
            List<SettlementReconcilePolicyFileEntity> reconcileRuleFileTemplates) {
        List<SettlementReconcileCompanyEntity> result = new ArrayList<>();
        // 处理小鲸对账单转换为对账单
        reconcileRuleFileTemplates.forEach(x -> {
            SettlementReconcileCompanyEntity bean = new SettlementReconcileCompanyEntity();
            BeanUtils.copyProperties(reconcileInfo, bean);
            // 生成policyBillCode 编号 + 系统生成标记
            bean.setCompanyBillCode(PolicySettlementUtils.createCodeLastNumber("CB"));
            // 其他信息
            bean.setPolicyNo(x.getPolicyNo());
            bean.setEndorsementNo(x.getBatchCode());
            bean.setProductCode(x.getProductCode());
            bean.setProductName(x.getProductName());
            bean.setProtocolProductCode(x.getInsuranceProductCode());
            bean.setProtocolProductName(x.getInsuranceProductName());
            // 科目信息
            String subjectCode = ReconcileSubjectHelper.matchSearchName(x.getReconcileSubjectName());
            if (StrUtil.isBlank(subjectCode)) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("科目名称" + x.getReconcileSubjectName() + "没有配置"));
            }
            bean.setSettlementSubjectCode(subjectCode);
            bean.setSettlementSubjectName(x.getReconcileSubjectName());
            // 结算信息
            bean.setRenewalPeriod(x.getRenewalPeriod());
            bean.setRealityPremium(x.getRealityPremium());
            bean.setSettlementRate(x.getSettlementRate() + "");
            bean.setCompanyAmount(x.getCompanyAmount());
            // 续期年期 + 续期期次
            bean.setRenewalYear(x.getRenewalYear());
            bean.setRenewalPeriod(x.getRenewalPeriod());
            bean.setRevision(1);
            result.add(bean);
        });
        return result;
    }

    private static String getKey(String policyNo, String settlementSubjectCode, String protocolProductCode,
                                 String productCode) {
        String subjectCode = settlementSubjectCode;
        if (SERVICE_FEE_LIST.contains(settlementSubjectCode)) {
            subjectCode = ReconcileSubjectOnlineEnum.SERVICE_FEE.getCode();
        }
        // 这些是按照保单+险种维度
        if (SPLIT_INSURANCE_PRODUCT_CODE_LIST.contains(protocolProductCode)) {
            return DigestUtil.md5Hex(
                    StrUtil.format("保单号=[{}]_科目编码=[{}]_险种编码=[{}]", policyNo.trim(), subjectCode,
                            StrUtil.nullToDefault(productCode, "").trim()));
        }
        return DigestUtil.md5Hex(StrUtil.format("保单号=[{}]_科目编码=[{}]", policyNo.trim(), subjectCode));
    }

    /**
     * 结算小鲸保单数据转确认单
     *
     * @param bean
     * @param settlementReconcilePolicy
     */
    private static void buildReconcilePolicyToReconcileConfirm(SettlementReconcileConfirmEntity bean,
                                                               SettlementReconcilePolicyEntity settlementReconcilePolicy) {
        String searchName = ReconcileSubjectHelper.matchSearchCode(settlementReconcilePolicy.getReconcileSubjectCode());
        if (StrUtil.isBlank(searchName)) {
            throw new GlobalException(
                    BasicCodeMsg.SERVER_ERROR.setMsg("科目编码不不存在=" + settlementReconcilePolicy.getReconcileSubjectCode()));
        }
        // 取出第一个险种信息构建【费率表 + 其他信息, 不采用复制属性方法】
        bean.setPolicyNo(settlementReconcilePolicy.getPolicyNo());
        bean.setReconcileSubjectCode(settlementReconcilePolicy.getReconcileSubjectCode());
        bean.setReconcileSubjectName(settlementReconcilePolicy.getReconcileSubjectName());
        bean.setApplicantPolicyNo(settlementReconcilePolicy.getApplicantPolicyNo());
        bean.setProductCode(settlementReconcilePolicy.getProductCode());
        bean.setProductName(settlementReconcilePolicy.getProductName());
        bean.setProtocolProductCode(settlementReconcilePolicy.getProtocolProductCode());
        bean.setProtocolProductName(settlementReconcilePolicy.getProtocolProductName());
        bean.setPlanCode(settlementReconcilePolicy.getPlanCode());
        bean.setPlanName(settlementReconcilePolicy.getPlanName());
        bean.setProductGroup(settlementReconcilePolicy.getProductGroup());
        bean.setLevel2Code(settlementReconcilePolicy.getLevel2Code());
        bean.setLevel3Code(settlementReconcilePolicy.getLevel3Code());
        bean.setRenewalYear(settlementReconcilePolicy.getRenewalYear());
        bean.setRenewalPeriod(settlementReconcilePolicy.getRenewalPeriod());
        bean.setCoverage(settlementReconcilePolicy.getCoverage());
        bean.setCoverageUnit(settlementReconcilePolicy.getCoverageUnit());
        bean.setCoverageUnitName(settlementReconcilePolicy.getCoverageUnitName());
        bean.setInsuredPeriodType(settlementReconcilePolicy.getInsuredPeriodType());
        bean.setInsuredPeriod(settlementReconcilePolicy.getInsuredPeriod());
        bean.setPeriodType(settlementReconcilePolicy.getPeriodType());
        bean.setPaymentPeriodType(settlementReconcilePolicy.getPaymentPeriodType());
        bean.setPaymentPeriod(settlementReconcilePolicy.getPaymentPeriod());
        // 设置批改单号和保全生效时间
        bean.setEndorsementNoList(settlementReconcilePolicy.getEndorsementNo());
        if (StringUtils.isNotBlank(
                settlementReconcilePolicy.getEndorsementNo()) && settlementReconcilePolicy.getEndorsementNo()
                .split(",").length > 1) {
            bean.setEndorsementNo(settlementReconcilePolicy.getEndorsementNo().split(",")[0]);
        }
        bean.setPreservationEffectTime(settlementReconcilePolicy.getPreservationEffectTime());
        // 获取险种总保费
        bean.setPremium(settlementReconcilePolicy.getPremium());
        bean.setProductPremiumTotal(settlementReconcilePolicy.getProductPremiumTotal());
        bean.setXiaowhaleAmount(settlementReconcilePolicy.getSettlementAmount().setScale(3, RoundingMode.HALF_UP));
        bean.setXiaowhaleSettlementRate(settlementReconcilePolicy.getSettlementRate());
        bean.setPolicyProductType(settlementReconcilePolicy.getPolicyProductType());
    }

    private static SettlementReconcileConfirmEntity initSettlementReconcileConfirm(
            SettlementReconcileInfoEntity reconcileInfo, ReconcileModelEnum reconcileModelEnum) {
        SettlementReconcileConfirmEntity bean = new SettlementReconcileConfirmEntity();
        // 生成policyBillCode 编号 + 系统生成标记
        bean.setBillCode(PolicySettlementUtils.createCodeLastNumber("BC"));
        BeanUtils.copyProperties(reconcileInfo, bean);
        bean.setReconcileModel(reconcileModelEnum.getCode());
        //小鲸对账数据
        bean.setXiaowhaleAmount(BigDecimal.ZERO);
        bean.setPremium(BigDecimal.ZERO);
        bean.setProductPremiumTotal(BigDecimal.ZERO);
        bean.setXiaowhaleSettlementRate("0");
        //保司对账数据
        bean.setCompanyPremium(BigDecimal.ZERO);
        bean.setCompanyAmount(BigDecimal.ZERO);
        bean.setCompanySettlementRate("0");
        //对账数据
        bean.setSettlementAmount(BigDecimal.ZERO);
        bean.setSettlementPremium(BigDecimal.ZERO);
        bean.setSettlementRate("0");

        bean.setDiffFlag(0);
        bean.setRevision(1);
        bean.setBillEnable(1);
        bean.setBillSource(0);
        return bean;
    }

    /**
     * 构建线上对账单汇总信息
     *
     * @param reconcileInfo    对账单信息
     * @param reconcilePolicy  我司线上对账单
     * @param reconcileCompany 保司线上对账单
     * @return com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileConfirmEntity
     * <AUTHOR>
     * @since 2023/5/29 13:27
     */
    public static List<SettlementReconcileConfirmEntity> buildSettlementReconcileConfirm(
            SettlementReconcileInfoEntity reconcileInfo, List<SettlementReconcilePolicyEntity> reconcilePolicy,
            List<SettlementReconcileCompanyEntity> reconcileCompany) {
        List<SettlementReconcileConfirmEntity> resultList = new ArrayList<>();
        // 如果文件都是空 那么就不处理了.
        if (!reconcilePolicy.isEmpty() || !reconcileCompany.isEmpty()) {
            //获取他们的险种信息进行查询处理
            // 我司线上对账单险种集合
            List<String> reconcilePolicyProductCode =
                    reconcilePolicy.stream().map(SettlementReconcilePolicyEntity::getProductCode)
                            .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
            List<String> reconcileCompanyProductCode =
                    reconcileCompany.stream().map(SettlementReconcileCompanyEntity::getProductCode)
                            .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
            // 合并险种信息(没有保司的对账单险种信息 因为线上的险种信息都以我司险种信息为准)
            Set<String> productCodeSet =
                    CollUtil.unionDistinct(reconcilePolicyProductCode, reconcileCompanyProductCode);
            log.info("合并后险种编码集合信息={}", productCodeSet);
            Map<String, ProductBase> productBaseMap = new HashMap<>();
            if (!productCodeSet.isEmpty()) {
                productBaseMap.putAll(
                        reconcileBaseHelper.productBaseService.getProductBaseList(new ArrayList<>(productCodeSet)).stream()
                                .collect(Collectors.toMap(ProductBase::getProductCode, v -> v)));
                if (productCodeSet.size() != productBaseMap.size()) {
                    log.info("缺失险种信信息原险种编码={},获取到险种编码={}", productCodeSet, productBaseMap.keySet());
                }
            }
            // 合并线上对账
            resultList.addAll(
                    mergeOnlineReconcileConfirm(reconcileInfo, reconcilePolicy, reconcileCompany, productBaseMap));
        }
        //合并处理线下对账
        resultList.addAll(mergeOfflineReconcileConfirm(reconcileInfo, reconcilePolicy, reconcileCompany));
        return resultList;
    }

    /**
     * 根据险种编码获取主险信息
     *
     * @param policyNos
     * @return
     */
    public static Map<String, MainProductInfoVo> findMainProductInfoListByPolicyNos(List<String> policyNos) {
        return reconcileBaseHelper.policyCenterBaseClient.findMainProductInfoListByPolicyNos(policyNos).stream()
                .collect(Collectors.toMap(MainProductInfoVo::getPolicyNo, v -> v, (v1, v2) -> {
                    // 这里处理自己的逻辑
                    return v2;
                }));
    }

    private static void completionReconcileOffline(SettlementReconcileInfoEntity reconcileInfo,
                                                   List<SettlementReconcilePolicyFileEntity> reconcileOffline) {
        List<String> policyNoList = reconcileOffline.stream().filter(f -> StrUtil.isBlank(f.getProductCode()))
                .map(SettlementReconcilePolicyFileEntity::getPolicyNo).collect(Collectors.toList());
        Map<String, MainProductInfoVo> policyMap = new HashMap<>(1);
        CollUtil.split(policyNoList, 500).forEach(policyNos -> {
            if (!policyNos.isEmpty()) {
                //根据保单号获取主险编码集合信息
                policyMap.putAll(findMainProductInfoListByPolicyNos(policyNos));
            }
        });
        reconcileOffline.forEach(action -> {
            // 获取到了保单信息,那么就开始补全险种信息
            if (policyMap.containsKey(action.getPolicyNo())) {
                action.setProductCode(policyMap.get(action.getPolicyNo()).getProductCode());
                action.setProductName(policyMap.get(action.getPolicyNo()).getProductName());
            }
        });
        List<String> productCodes = reconcileOffline.stream().filter(f -> StrUtil.isNotBlank(f.getProductCode()))
                .map(SettlementReconcilePolicyFileEntity::getProductCode).distinct().collect(Collectors.toList());
        if (!productCodes.isEmpty()) {
            Map<String, ProtocolInsuranceProductInfoOut> insuranceProductInfoMap =
                    ProtocolBaseHelper.queryProtocolInsuranceProductList(productCodes, reconcileInfo.getReconcileType())
                            .stream()
                            .collect(Collectors.toMap(ProtocolInsuranceProductInfoOut::getProductCode, v -> v, (v1, v2) -> {
                                // 这里处理自己的逻辑
                                return v2;
                            }));
            reconcileOffline.forEach(action -> {
                // 获取到了保单信息,那么就开始补全产品信息
                if (insuranceProductInfoMap.containsKey(action.getProductCode())) {
                    ProtocolInsuranceProductInfoOut protocolInsuranceProductInfo =
                            insuranceProductInfoMap.get(action.getProductCode());
                    action.setInsuranceProductName(protocolInsuranceProductInfo.getInsuranceProductName());
                    action.setInsuranceProductCode(protocolInsuranceProductInfo.getInsuranceProductCode());
                }
            });
        }
    }

    /**
     * 合并线下确认单
     *
     * @param reconcileInfo    对账单基本信息
     * @param reconcilePolicy  我司线上对账单
     * @param reconcileCompany 保司线上对账单
     * @return
     */
    private static List<SettlementReconcileConfirmEntity> mergeOfflineReconcileConfirm(
            SettlementReconcileInfoEntity reconcileInfo, List<SettlementReconcilePolicyEntity> reconcilePolicy,
            List<SettlementReconcileCompanyEntity> reconcileCompany) {
        List<SettlementReconcilePolicyFileEntity> reconcileOffline =
                reconcileBaseHelper.settlementReconcilePolicyFileService.lambdaQuery()
                        .eq(SettlementReconcilePolicyFileEntity::getFileType, 0)
                        .eq(SettlementReconcilePolicyFileEntity::getReconcileCode, reconcileInfo.getReconcileCode())
                        .notIn(SettlementReconcilePolicyFileEntity::getReconcileSubjectCode, RECONCILE_SUBJECT_LIST).list();
        if (CollUtil.isEmpty(reconcileOffline)) {
            return Collections.emptyList();
        }
        // 优先处理一下线下单,不存在险种编码的数据,获取保单号 去查询保单信息,然后进行更新数据的处理
        if (CollUtil.isNotEmpty(reconcileOffline)) {
            // 补全险种和产品信息
            completionReconcileOffline(reconcileInfo, reconcileOffline);
        }
        // 线下对账单险种集合
        List<String> offlineProductCode =
                reconcileOffline.stream().map(SettlementReconcilePolicyFileEntity::getProductCode)
                        .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        // 合并险种信息(没有保司的对账单险种信息 因为线上的险种信息都以我司险种信息为准)
        Map<String, ProductBase> productBaseMap =
                reconcileBaseHelper.productBaseService.getProductBaseList(offlineProductCode).stream()
                        .collect(Collectors.toMap(ProductBase::getProductCode, v -> v));
        if (offlineProductCode.size() != productBaseMap.size()) {
            log.info("缺失险种信信息原险种编码={},获取到险种编码={}", offlineProductCode, productBaseMap.keySet());
        }
        // 我司线下对账单
        List<SettlementReconcilePolicyEntity> reconcilePolicyList = buildSettlementReconcilePolicy(reconcileInfo, reconcileOffline, productBaseMap);
        // 保司线下对账单
        List<SettlementReconcileCompanyEntity> reconcileCompanyList = buildSettlementReconcileCompany(reconcileInfo, reconcileOffline);

        Map<String, SettlementReconcilePolicyFileEntity> settlementReconcileConfirmMap = reconcileOffline.stream()
                .collect(Collectors.toMap(
                        k -> getKey(k.getPolicyNo(), k.getReconcileSubjectCode(), k.getInsuranceProductCode(),
                                k.getProductCode()), v -> v, (v1, v2) -> {
                            SettlementReconcilePolicyFileEntity settlementReconcilePolicyFile =
                                    BeanUtil.copyProperties(v1, SettlementReconcilePolicyFileEntity.class);
                            settlementReconcilePolicyFile.setRealityPremium(v1.getRealityPremium().add(v2.getRealityPremium()));
                            settlementReconcilePolicyFile.setCompanyAmount(v1.getCompanyAmount().add(v2.getCompanyAmount()));
                            if (StrUtil.isNotBlank(v1.getBatchCode()) && StrUtil.isNotBlank(v2.getBatchCode())) {
                                settlementReconcilePolicyFile.setBatchCode(v1.getBatchCode() + "," + v2.getBatchCode());
                            } else if (StrUtil.isNotBlank(v2.getBatchCode())) {
                                settlementReconcilePolicyFile.setBatchCode(v2.getBatchCode());
                            }
                            return settlementReconcilePolicyFile;
                        }));

        List<String> reconcilePolicyKeyList = reconcilePolicyList.stream().map(
                        k -> getKey(k.getPolicyNo(), k.getReconcileSubjectCode(), k.getProtocolProductCode(),
                                k.getProductCode()))
                .distinct().collect(Collectors.toList());


        List<String> xiaoWhaleMissed = settlementReconcileConfirmMap.keySet().stream()
                .filter(x -> !reconcilePolicyKeyList.contains(x))
                .map(x -> settlementReconcileConfirmMap.get(x).getPolicyNo())
                .collect(Collectors.toList());
        List<String> timeMissedPolicyKeyList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(xiaoWhaleMissed)) {
            List<SettlementPolicyInfoEntity> settlementPolicyList =
                    reconcileBaseHelper.settlementPolicyInfoService.lambdaQuery().eq(SettlementPolicyInfoEntity::getReconcileStatus, 0)
                            .in(SettlementPolicyInfoEntity::getReconcileExecuteStatus, CollUtil.newArrayList(1, 3))
                            .eq(SettlementPolicyInfoEntity::getReconcileType, reconcileInfo.getReconcileType())
                            .eq(SettlementPolicyInfoEntity::getRectificationMark, StatusEnum.INVALID.getCode())
                            .and(
                                    a -> a.in(SettlementPolicyInfoEntity::getPolicyNo, xiaoWhaleMissed).or()
                                            .in(SettlementPolicyInfoEntity::getThirdPolicyNo, xiaoWhaleMissed))
                            .and(
                                    a -> a.gt(SettlementPolicyInfoEntity::getPostponedMonth, reconcileInfo.getReconcileMonth()).or()
                                            .isNull(SettlementPolicyInfoEntity::getPostponedMonth))
                            .list();

            timeMissedPolicyKeyList = settlementPolicyList.stream().map(
                            k -> getKey(k.getPolicyNo(), k.getSettlementSubjectCode(), k.getProtocolProductCode(),
                                        k.getProductCode()))
                    .distinct().collect(Collectors.toList());


        }


        List<SettlementReconcileConfirmEntity> resultList = new ArrayList<>();
        Map<String, String> billCodeMap = new HashMap<>(1);
        List<String> finalTimeMissedPolicyKeyList = timeMissedPolicyKeyList;
        settlementReconcileConfirmMap.forEach((key, settlementReconcileConfirm) -> {
            // 生成policyBillCode 编号 + 系统生成标记
            SettlementReconcileConfirmEntity bean =
                    initSettlementReconcileConfirm(reconcileInfo, ReconcileModelEnum.OFFLINE);
            billCodeMap.put(bean.getReconcileModel() + key, bean.getBillCode());
            bean.setBillKey(key);
            bean.setPolicyNo(settlementReconcileConfirm.getPolicyNo());
            // 科目信息
            bean.setReconcileSubjectCode(settlementReconcileConfirm.getReconcileSubjectCode());
            bean.setReconcileSubjectName(settlementReconcileConfirm.getReconcileSubjectName());
            //险种基本信息
            bean.setProductCode(settlementReconcileConfirm.getProductCode());
            bean.setProductName(settlementReconcileConfirm.getProductName());
            bean.setProtocolProductCode(settlementReconcileConfirm.getInsuranceProductCode());
            bean.setProtocolProductName(settlementReconcileConfirm.getInsuranceProductName());
            //我司手续费信息
            bean.setXiaowhaleAmount(settlementReconcileConfirm.getCompanyAmount());
            bean.setXiaowhaleSettlementRate(settlementReconcileConfirm.getSettlementRate().toPlainString());
            bean.setPremium(settlementReconcileConfirm.getRealityPremium());
            bean.setProductPremiumTotal(settlementReconcileConfirm.getRealityPremium());
            //保司手续费信息
            bean.setCompanyPremium(settlementReconcileConfirm.getRealityPremium());
            bean.setCompanySettlementRate(settlementReconcileConfirm.getSettlementRate().toPlainString());
            bean.setCompanyAmount(settlementReconcileConfirm.getCompanyAmount());
            //结算单手续费信息
            bean.setSettlementAmount(settlementReconcileConfirm.getCompanyAmount());
            bean.setSettlementRate(settlementReconcileConfirm.getSettlementRate().toPlainString());
            bean.setSettlementPremium(settlementReconcileConfirm.getRealityPremium());
            // 差额为0
            bean.setDiffAmount(BigDecimal.ZERO);
            bean.setDiffPremium(BigDecimal.ZERO);
            // 设置批改单集合
            bean.setEndorsementNoList(settlementReconcileConfirm.getBatchCode());
            if (StringUtils.isNotBlank(
                    settlementReconcileConfirm.getBatchCode()) && settlementReconcileConfirm.getBatchCode()
                    .split(",").length > 1) {
                bean.setEndorsementNo(settlementReconcileConfirm.getBatchCode().split(",")[0]);
            }
            // 续期年期 + 续期期次
            bean.setRenewalYear(settlementReconcileConfirm.getRenewalYear());
            bean.setRenewalPeriod(settlementReconcileConfirm.getRenewalPeriod());


            // 判断是否小鲸缺失
            if (!reconcilePolicyKeyList.contains(key)) {
                bean.setDiffFlag(1);
                if (!finalTimeMissedPolicyKeyList.contains(key)) {
                    bean.setDiffType(ReconcileDiffTypeEnum.XIAOWHALE_MISSED.getCode());
                } else {
                    bean.setDiffType(ReconcileDiffTypeEnum.XIAOWHALE_MISSED_TIME_DIFF.getCode());
                }
                // 小鲸缺失，差额保费以保司为准
                bean.setDiffPremium(settlementReconcileConfirm.getRealityPremium());
                bean.setDiffAmount(BigDecimal.ZERO.subtract(settlementReconcileConfirm.getCompanyAmount()));
                //我司手续费信息
                bean.setXiaowhaleAmount(BigDecimal.ZERO);
                bean.setXiaowhaleSettlementRate(BigDecimal.ZERO.toPlainString());
                bean.setPremium(BigDecimal.ZERO);
                bean.setProductPremiumTotal(BigDecimal.ZERO);
            }
            // 如果没有小鲸险种编码，判断为无法进行业财处理
            if (StringUtils.isNotBlank(bean.getProductCode())) {
                if (!productBaseMap.containsKey(bean.getProductCode())) {
                    throw new GlobalException(
                            BasicCodeMsg.SERVER_ERROR.setMsg("险种信息不存在，险种编码=" + bean.getProductCode()));
                }
                ProductBase productBase = productBaseMap.get(bean.getProductCode());
                bean.setProductGroup(productBase.getProductGroup());
                bean.setLevel2Code(productBase.getLevel2Code());
                bean.setLevel3Code(productBase.getLevel3Code());
                bean.setPolicyProductType(productBase.getProductGroup());
                if (StrUtil.isBlank(productBase.getLevel2Code())) {
                    throw new GlobalException(
                            BasicCodeMsg.SERVER_ERROR.setMsg("险种编码=" + bean.getProductCode() + "不存在二级分类"));
                }
            }
            resultList.add(bean);
        });
        reconcilePolicyList.forEach(action -> {
            String key = getKey(action.getPolicyNo(), action.getReconcileSubjectCode(), action.getProtocolProductCode(),
                    action.getProductCode());
            String billCode = billCodeMap.get(ReconcileModelEnum.OFFLINE.getCode() + key);
            if (StrUtil.isBlank(billCode)) {
                log.info("Key={},没有获取小鲸对账信息={}", key, JSONUtil.toJsonStr(action));
                throw new GlobalException(
                        BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("Key={},没有获取小鲸对账信息", key)));
            }
            action.setBillKey(key);
            action.setBillCode(billCode);
        });
        reconcileCompanyList.forEach(action -> {
            String key =
                    getKey(action.getPolicyNo(), action.getSettlementSubjectCode(), action.getProtocolProductCode(),
                            action.getProductCode());
            String billCode = billCodeMap.get(ReconcileModelEnum.OFFLINE.getCode() + key);
            if (StrUtil.isBlank(billCode)) {
                log.info("Key={},没有获取小鲸对账信息={}", key, JSONUtil.toJsonStr(action));
                throw new GlobalException(
                        BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("Key={},没有获取保司对账信息", key)));
            }
            action.setBillKey(key);
            action.setBillCode(billCode);
        });
        reconcilePolicy.addAll(reconcilePolicyList);
        reconcileCompany.addAll(reconcileCompanyList);
        return resultList;
    }

    /**
     * 合并线上确认单
     *
     * @param reconcileInfo    对账单基本信息
     * @param reconcilePolicy  我司线上对账单
     * @param reconcileCompany 保司线上对账单
     * @param productBaseMap   险种集合
     * @return
     */
    private static List<SettlementReconcileConfirmEntity> mergeOnlineReconcileConfirm(
            SettlementReconcileInfoEntity reconcileInfo, List<SettlementReconcilePolicyEntity> reconcilePolicy,
            List<SettlementReconcileCompanyEntity> reconcileCompany, Map<String, ProductBase> productBaseMap) {
        List<SettlementReconcileConfirmEntity> result = new ArrayList<>();
        // 开始构建线上对账单 合并批单数据....
        Map<String, SettlementReconcilePolicyEntity> reconcilePolicyMap = reconcilePolicy.stream()
                .filter(f -> f.getReconcileGenerateType() == SettlementGenerateTypeEnum.BUSINESS_EVENTS.getCode()).collect(
                        Collectors.toMap(k -> getKey(k.getPolicyNo(), k.getReconcileSubjectCode(), k.getProtocolProductCode(),
                                k.getProductCode()), v -> v, (v1, v2) -> {
                            SettlementReconcilePolicyEntity settlementReconcilePolicy =
                                    BeanUtil.copyProperties(v1, SettlementReconcilePolicyEntity.class);
                            settlementReconcilePolicy.setPremium(v1.getPremium().add(v2.getPremium()));
                            settlementReconcilePolicy.setSettlementAmount(
                                    v1.getSettlementAmount().add(v2.getSettlementAmount()));
                            settlementReconcilePolicy.setProductPremiumTotal(
                                    v1.getProductPremiumTotal().add(v2.getProductPremiumTotal()));
                            if (StrUtil.isNotBlank(v1.getEndorsementNo()) && StrUtil.isNotBlank(v2.getEndorsementNo())) {
                                settlementReconcilePolicy.setEndorsementNo(v1.getEndorsementNo() + "," + v2.getEndorsementNo());
                            } else if (StrUtil.isNotBlank(v1.getEndorsementNo())) {
                                settlementReconcilePolicy.setEndorsementNo(v1.getEndorsementNo());
                            } else if (StrUtil.isNotBlank(v2.getEndorsementNo())) {
                                settlementReconcilePolicy.setEndorsementNo(v2.getEndorsementNo());
                            }
                            return settlementReconcilePolicy;
                        }));
        Map<String, SettlementReconcileCompanyEntity> reconcileCompanyMap = reconcileCompany.stream().collect(
                Collectors.toMap(k -> getKey(k.getPolicyNo(), k.getSettlementSubjectCode(), k.getProtocolProductCode(),
                        k.getProductCode()), v -> v, (v1, v2) -> {
                    SettlementReconcileCompanyEntity settlementReconcileCompany =
                            BeanUtil.copyProperties(v1, SettlementReconcileCompanyEntity.class);
                    settlementReconcileCompany.setRealityPremium(v1.getRealityPremium().add(v2.getRealityPremium()));
                    settlementReconcileCompany.setCompanyAmount(v1.getCompanyAmount().add(v2.getCompanyAmount()));
                    if (StrUtil.isNotBlank(v1.getEndorsementNo()) && StrUtil.isNotBlank(v2.getEndorsementNo())) {
                        settlementReconcileCompany.setEndorsementNo(v1.getEndorsementNo() + "," + v2.getEndorsementNo());
                    } else if (StrUtil.isNotBlank(v1.getEndorsementNo())) {
                        settlementReconcileCompany.setEndorsementNo(v1.getEndorsementNo());
                    } else if (StrUtil.isNotBlank(v2.getEndorsementNo())) {
                        settlementReconcileCompany.setEndorsementNo(v2.getEndorsementNo());
                    }
                    return settlementReconcileCompany;
                }));
        Set<String> keySet = new HashSet<>(reconcilePolicyMap.keySet());
        keySet.addAll(reconcileCompanyMap.keySet());

        List<String> xiaoWhaleMissed = reconcileCompanyMap.keySet().stream()
                .filter(x -> !reconcilePolicyMap.containsKey(x))
                .collect(Collectors.toList());
        List<String> timeMissedPolicyKeyList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(xiaoWhaleMissed)) {
            List<String> missedPolicyList = xiaoWhaleMissed.stream().map(x -> reconcileCompanyMap.get(x).getPolicyNo()).collect(
                    Collectors.toList());
            List<SettlementPolicyInfoEntity> settlementPolicyList =
                    reconcileBaseHelper.settlementPolicyInfoService.lambdaQuery().eq(SettlementPolicyInfoEntity::getReconcileStatus, 0)
                            .in(SettlementPolicyInfoEntity::getReconcileExecuteStatus, CollUtil.newArrayList(1, 3))
                            .eq(SettlementPolicyInfoEntity::getReconcileType, reconcileInfo.getReconcileType())
                            .eq(SettlementPolicyInfoEntity::getRectificationMark, StatusEnum.INVALID.getCode())
                            .and(
                                    a -> a.in(SettlementPolicyInfoEntity::getPolicyNo, missedPolicyList).or()
                                            .in(SettlementPolicyInfoEntity::getThirdPolicyNo, missedPolicyList)
                            )
                            .and(
                                    a -> a.gt(SettlementPolicyInfoEntity::getPostponedMonth, reconcileInfo.getReconcileMonth()).or()
                                            .isNull(SettlementPolicyInfoEntity::getPostponedMonth))
                            .list();

            timeMissedPolicyKeyList = settlementPolicyList.stream().map(
                            k -> getKey(k.getPolicyNo(), k.getSettlementSubjectCode(), k.getProtocolProductCode(),
                                        k.getProductCode()))
                    .distinct().collect(Collectors.toList());

        }

        Map<String, String> billCodeMap = new HashMap<>(1);
        log.info("结算编码=[{}]处理线上确认单数量={}", reconcileInfo.getReconcileCode(), keySet.size());
        List<String> finalTimeMissedPolicyKeyList = timeMissedPolicyKeyList;
        keySet.forEach(key -> {
            // 初始化一个Bean
            SettlementReconcileConfirmEntity bean =
                    initSettlementReconcileConfirm(reconcileInfo, ReconcileModelEnum.ONLINE);
            billCodeMap.put(bean.getReconcileModel() + key, bean.getBillCode());
            bean.setBillKey(key);
            if (reconcilePolicyMap.containsKey(key) && reconcileCompanyMap.containsKey(key)) {
                // 两变都有数据 基础数据以小鲸为准,因为对账单的数据很少
                SettlementReconcilePolicyEntity settlementReconcilePolicy = reconcilePolicyMap.get(key);
                SettlementReconcileCompanyEntity settlementReconcileCompany = reconcileCompanyMap.get(key);
                settlementReconcilePolicy.setReconcileSubjectCode(settlementReconcileCompany.getSettlementSubjectCode());
                settlementReconcilePolicy.setReconcileSubjectName(settlementReconcileCompany.getSettlementSubjectName());
                settlementReconcilePolicy.setBillKey(key);
                settlementReconcileCompany.setBillKey(key);
                buildReconcilePolicyToReconcileConfirm(bean, settlementReconcilePolicy);

                // 补充保司相关数据
                bean.setCompanySettlementRate(settlementReconcileCompany.getSettlementRate());
                bean.setCompanyAmount(settlementReconcileCompany.getCompanyAmount());
                bean.setCompanyPremium(settlementReconcileCompany.getRealityPremium());
                // 判断保司手续费和小鲸手续费是否一致.
                if (settlementReconcilePolicy.getSettlementAmount()
                        .compareTo(settlementReconcileCompany.getCompanyAmount()) == 0) {
                    // 无差异,就以小鲸的结算保费为作为结算
                    bean.setSettlementAmount(settlementReconcilePolicy.getSettlementAmount());
                    bean.setSettlementRate(settlementReconcilePolicy.getSettlementRate());
                    bean.setSettlementPremium(settlementReconcilePolicy.getPremium());
                    // 差额为0
                    bean.setDiffAmount(BigDecimal.ZERO);
                    bean.setDiffPremium(BigDecimal.ZERO);
                } else if (settlementReconcilePolicy.getPremium().setScale(4, RoundingMode.HALF_UP)
                        .compareTo(settlementReconcileCompany.getRealityPremium().setScale(4, RoundingMode.HALF_UP)) != 0) {
                    // 保费不一致
                    bean.setDiffFlag(1);
                    bean.setDiffType(ReconcileDiffTypeEnum.PREMIUM_DIFF.getCode());
                    // 差额
                    bean.setDiffAmount(settlementReconcilePolicy.getSettlementAmount()
                            .subtract(settlementReconcileCompany.getCompanyAmount()));
                    bean.setDiffPremium(settlementReconcilePolicy.getPremium()
                            .subtract(settlementReconcileCompany.getRealityPremium()));

                } else if (new BigDecimal(settlementReconcilePolicy.getSettlementRate()).setScale(4,
                        RoundingMode.HALF_UP).compareTo(
                        new BigDecimal(settlementReconcileCompany.getSettlementRate()).setScale(4,
                                RoundingMode.HALF_UP)) != 0) {
                    // 费率不一致
                    bean.setDiffFlag(1);
                    bean.setDiffType(ReconcileDiffTypeEnum.RATE_DIFF.getCode());
                    // 差额
                    bean.setDiffAmount(settlementReconcilePolicy.getSettlementAmount()
                            .subtract(settlementReconcileCompany.getCompanyAmount()));
                    bean.setDiffPremium(settlementReconcilePolicy.getPremium()
                            .subtract(settlementReconcileCompany.getRealityPremium()));

                } else if (settlementReconcilePolicy.getSettlementAmount()
                        .subtract(settlementReconcileCompany.getCompanyAmount()).abs()
                        .compareTo(new BigDecimal("0.1")) < 0) {
                    // 精度差异
                    bean.setDiffFlag(1);
                    bean.setDiffType(ReconcileDiffTypeEnum.AMOUNT_ACCURACY.getCode());
                    // 差额
                    bean.setDiffAmount(settlementReconcilePolicy.getSettlementAmount()
                            .subtract(settlementReconcileCompany.getCompanyAmount()));
                    bean.setDiffPremium(settlementReconcilePolicy.getPremium()
                            .subtract(settlementReconcileCompany.getRealityPremium()));
                } else {
                    // 不一致
                    bean.setDiffFlag(1);
                    bean.setDiffType(ReconcileDiffTypeEnum.AMOUNT_DIFF.getCode());
                    // 差额
                    bean.setDiffAmount(settlementReconcilePolicy.getSettlementAmount()
                            .subtract(settlementReconcileCompany.getCompanyAmount()));
                    bean.setDiffPremium(settlementReconcilePolicy.getPremium()
                            .subtract(settlementReconcileCompany.getRealityPremium()));
                }
            } else if (reconcilePolicyMap.containsKey(key)) {
                SettlementReconcilePolicyEntity settlementReconcilePolicy = reconcilePolicyMap.get(key);
                settlementReconcilePolicy.setBillKey(key);
                buildReconcilePolicyToReconcileConfirm(bean, settlementReconcilePolicy);
                // 如果保费不是0.收入为0，并且可执行对账 则为仅报送保费
                // 可执行对账状态为不可对账
                if (settlementReconcilePolicy.getPremium().compareTo(BigDecimal.ZERO) != 0
                    &&settlementReconcilePolicy.getSettlementAmount().compareTo(BigDecimal.ZERO) == 0
                    &&StatusEnum.NORMAL.getCode().equals(settlementReconcilePolicy.getReconcileExecuteStatus())){
                    // 仅报送保费
                    bean.setDiffFlag(1);
                    bean.setDiffType(ReconcileDiffTypeEnum.ONLY_SUBMIT_PREMIUM.getCode());
                    // 保司缺失，差异金额取小鲸的
                    bean.setDiffPremium(settlementReconcilePolicy.getPremium());
                    bean.setDiffAmount(settlementReconcilePolicy.getSettlementAmount());
                }else {
                    // 保司缺失
                    bean.setDiffFlag(1);
                    bean.setDiffType(ReconcileDiffTypeEnum.COMPANY_MISSED.getCode());
                    // 保司缺失，差异金额取小鲸的
                    bean.setDiffPremium(settlementReconcilePolicy.getPremium());
                    bean.setDiffAmount(settlementReconcilePolicy.getSettlementAmount());
                }

            } else if (reconcileCompanyMap.containsKey(key)) {
                SettlementReconcileCompanyEntity settlementReconcileCompany = reconcileCompanyMap.get(key);
                settlementReconcileCompany.setBillKey(key);
                // 小鲸缺失
                bean.setDiffFlag(1);
                if (!finalTimeMissedPolicyKeyList.contains(key)) {
                    bean.setDiffType(ReconcileDiffTypeEnum.XIAOWHALE_MISSED.getCode());
                } else {
                    bean.setDiffType(ReconcileDiffTypeEnum.XIAOWHALE_MISSED_TIME_DIFF.getCode());
                }

                bean.setPolicyNo(settlementReconcileCompany.getPolicyNo());
                bean.setReconcileSubjectCode(settlementReconcileCompany.getSettlementSubjectCode());
                bean.setReconcileSubjectName(settlementReconcileCompany.getSettlementSubjectName());
                bean.setCompanyPremium(settlementReconcileCompany.getRealityPremium());
                bean.setCompanySettlementRate(settlementReconcileCompany.getSettlementRate());
                bean.setCompanyAmount(settlementReconcileCompany.getCompanyAmount());
                bean.setProductCode(settlementReconcileCompany.getProductCode());
                bean.setProductName(settlementReconcileCompany.getProductName());
                // 小鲸缺失，差额保费以保司为准
                bean.setDiffPremium(settlementReconcileCompany.getRealityPremium());
                bean.setDiffAmount(BigDecimal.ZERO.subtract(settlementReconcileCompany.getCompanyAmount()));
                // 设置批改单集合
                bean.setEndorsementNoList(settlementReconcileCompany.getEndorsementNo());
                if (StringUtils.isNotBlank(
                        settlementReconcileCompany.getEndorsementNo()) && settlementReconcileCompany.getEndorsementNo()
                        .split(",").length > 1) {
                    bean.setEndorsementNo(settlementReconcileCompany.getEndorsementNo().split(",")[0]);
                }
                // 续期年期 + 续期期次
                bean.setRenewalYear(settlementReconcileCompany.getRenewalYear());
                bean.setRenewalPeriod(settlementReconcileCompany.getRenewalPeriod());
            } else {
                return;
            }
            // 没有小鲸险种编码也可以进行业财处理 其实保司险种处理
            if (StringUtils.isNotBlank(bean.getProductCode())) {
                if (!productBaseMap.containsKey(bean.getProductCode())) {
                    throw new GlobalException(
                            BasicCodeMsg.SERVER_ERROR.setMsg("险种信息不存在，险种编码=" + bean.getProductCode()));
                }
                ProductBase productBase = productBaseMap.get(bean.getProductCode());
                bean.setProductGroup(productBase.getProductGroup());
                bean.setLevel2Code(productBase.getLevel2Code());
                bean.setLevel3Code(productBase.getLevel3Code());
                if (StrUtil.isBlank(productBase.getLevel2Code())) {
                    throw new GlobalException(
                            BasicCodeMsg.SERVER_ERROR.setMsg("险种编码=" + bean.getProductCode() + "不存在二级分类"));
                }
            }
            result.add(bean);
        });
        reconcilePolicy.forEach(action -> {
            String key = getKey(action.getPolicyNo(), action.getReconcileSubjectCode(), action.getProtocolProductCode(),
                    action.getProductCode());
            String billCode = billCodeMap.get(ReconcileModelEnum.ONLINE.getCode() + key);
            if (StrUtil.isBlank(billCode)) {
                log.info("Key={},没有获取小鲸对账信息={}", key, JSONUtil.toJsonStr(action));
                throw new GlobalException(
                        BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("Key={},没有获取小鲸对账信息", key)));
            }
            action.setBillCode(billCode);
        });
        reconcileCompany.forEach(action -> {
            String key =
                    getKey(action.getPolicyNo(), action.getSettlementSubjectCode(), action.getProtocolProductCode(),
                            action.getProductCode());
            String billCode = billCodeMap.get(ReconcileModelEnum.ONLINE.getCode() + key);
            if (StrUtil.isBlank(billCode)) {
                log.info("Key={},没有获取小鲸对账信息={}", key, JSONUtil.toJsonStr(action));
                throw new GlobalException(
                        BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("Key={},没有获取保司对账信息", key)));
            }
            action.setBillCode(billCode);
        });
        log.info("结算编码=[{}]处理线上确认单数量={}条", reconcileInfo.getReconcileCode(), result.size());
        return result;
    }

    /**
     * 结算结佣保费
     *
     * @param bean    结算保单信息
     * @param premium 保费信息
     * @return void
     * <AUTHOR>
     * @since 2023/8/14
     */
    private static boolean builderSettlementAmount(SettlementPolicyInfoEntity bean, BigDecimal premium) {
        try {
            BigDecimal settlementRate = new BigDecimal(bean.getSettlementRate()); //费率
            // 判断是税前还是税后
            if (bean.getSettlementRateMethod() == 0) {
                // 100×0.25=25
                bean.setSettlementAmount(BigDecimalUtils.mul(premium.doubleValue(), settlementRate.doubleValue())
                        .setScale(2, RoundingMode.HALF_UP));
                return true;
            } else if (bean.getSettlementRateMethod() == 1) {
                // 修正一下如果税率为空的时候 税率为1
                if (bean.getSettlementRateTax() == null) {
                    bean.setSettlementRateTax(new BigDecimal("1"));
                }
                // 100÷1.06×0.25=23.58
                bean.setSettlementAmount(BigDecimalUtils.mul(
                        premium.divide(bean.getSettlementRateTax(), 4, RoundingMode.HALF_UP).doubleValue(),
                        settlementRate.doubleValue()).setScale(2, RoundingMode.HALF_UP));
                return true;
            } else {
                bean.setReconcileExecuteDesc(StrUtil.format(
                        "获取到协议费率表结算方式或者结算费率不识别，settlementRateMethod={} " + "settlementRateTax={}",
                        bean.getSettlementMonth(), bean.getSettlementRateTax()));
            }
        } catch (Exception e) {
            bean.setReconcileExecuteDesc(
                    StrUtil.format("计算费率异常，settlementRateMethod={} settlementRateTax={}", bean.getSettlementMonth(),
                            bean.getSettlementRateTax()));
        }
        return false;
    }

    /**
     * 获取保单明细结算费率信息
     *
     * @param settlementPolicyInfo 结算明细
     * @return
     */
    public static BuildSettlementAmountResult buildSettlementAmount(SettlementPolicyInfoEntity settlementPolicyInfo) {
        String policyNo = settlementPolicyInfo.getPolicyNo();
        String productCode = settlementPolicyInfo.getProductCode();
        BuildSettlementAmountResult result = new BuildSettlementAmountResult();
        result.setReconcileExecuteStatus(0);
        result.setYearRate(BigDecimal.ZERO);
        result.setSettlementAmount(BigDecimal.ZERO);
        try {
            PolicyProductPremInput premInfoInput = builderSettlementPolicyInfoToPrem(settlementPolicyInfo);
            // 判断是小鲸还是非小鲸
            PremEventHandler premEventHandler = PremEventFactory.getInvoke(
                    settlementPolicyInfo.getReconcileType() == 1 ? PremEventEnum.CONTRACT_PREM.getCode()
                            : PremEventEnum.PROTOCOL_PREM.getCode());
            List<PolicyProductPremResult> resultList = premEventHandler.queryPolicyProductPrem(premInfoInput);
            if (CollUtil.isEmpty(resultList)) {
                result.setReconcileExecuteDesc(
                        StrUtil.format("没有获取到结算科目信息，保单号={} 险种编码={}", policyNo, productCode));
                return result;
            }
            if (resultList.size() > 1) {
                result.setReconcileExecuteDesc(
                        StrUtil.format("获取到多个结算科目信息，保单号={} 险种编码={}", policyNo, productCode));
                return result;
            }

            PolicyProductPremResult productPremInfo = resultList.get(0);

            //结算方式为空的时候 默认税前
            if (productPremInfo.getSettlementMethod() == null) {
                productPremInfo.setSettlementMethod(0);
            }
            // 税率为空的时候 默认为1
            if (productPremInfo.getTaxRate() == null) {
                productPremInfo.setTaxRate(new BigDecimal("1"));
            }
            // 复制其他信息
            BeanUtils.copyProperties(productPremInfo, result);
            // 保费
            BigDecimal premium = settlementPolicyInfo.getPremium();


            PolicyPremResult settlementPolicyPrem = null;
            if (productPremInfo.getIsCustomYearRate() == 1) {
                // 判断是否为一单一议的佣金费率
                settlementPolicyPrem = premEventHandler.queryPolicyPrem(
                        PolicyPremInput.builder()
                                .policyNo(policyNo).productCode(productCode)
                                .batchCode(settlementPolicyInfo.getEndorsementNo())
                                .year(settlementPolicyInfo.getRenewalYear())
                                .period(settlementPolicyInfo.getRenewalPeriod())
                                .productCode(settlementPolicyInfo.getProductCode())
                                .insuranceProductCode(settlementPolicyInfo.getProtocolProductCode())
                                .build());
            }

            // 判断是否为一单一议的佣金费率
            PolicyPremInput build = PolicyPremInput.builder()
                    .policyNo(policyNo).productCode(productCode)
                    .batchCode(settlementPolicyInfo.getEndorsementNo())
                    .year(settlementPolicyInfo.getRenewalYear())
                    .period(settlementPolicyInfo.getRenewalPeriod())
                    .productCode(settlementPolicyInfo.getProductCode())
                    .insuranceProductCode(settlementPolicyInfo.getProtocolProductCode())
                    .build();
            PolicyTaxPremResult settlementPolicyTaxPrem = null;
            if (productPremInfo.getIsCustomTaxRate() == 1) {
                settlementPolicyTaxPrem = premEventHandler.queryPolicyTaxPrem(build);
            }

            if (productPremInfo.getIsCustomYearRate() == 0 && productPremInfo.getIsCustomTaxRate() == 0) {
                result.setReconcileExecuteStatus(1);
                result.setReconcileExecuteDesc("费率表标准配置-可执行对账");
            } else if (productPremInfo.getIsCustomYearRate() == 0 && productPremInfo.getIsCustomTaxRate() == 1) {
                if (Objects.isNull(settlementPolicyTaxPrem)) {
                    result.setReconcileExecuteStatus(3);
                    result.setReconcileExecuteDesc("一单一议保单税率获取失败");
                } else {
                    result.setTaxRate(settlementPolicyTaxPrem.getTaxRate());
                    result.setReconcileExecuteStatus(1);
                    result.setReconcileExecuteDesc("一单一议保单税率-可执行对账");

                }
            } else if (productPremInfo.getIsCustomYearRate() == 1 && productPremInfo.getIsCustomTaxRate() == 0) {
                if (Objects.isNull(settlementPolicyPrem)) {
                    result.setReconcileExecuteStatus(3);
                    result.setReconcileExecuteDesc("一单一议保单费率获取失败");
                } else {
                    if (StrUtil.isNotEmpty(settlementPolicyPrem.getSettlementMethod())) {
                        result.setSettlementMethod(Integer.parseInt(settlementPolicyPrem.getSettlementMethod()));
                    }
                    result.setYearRate(settlementPolicyPrem.getYearRate());
                    result.setReconcileExecuteStatus(1);
                    result.setReconcileExecuteDesc("一单一议保单费率-可执行对账");
                }
            } else {
                if (Objects.nonNull(settlementPolicyPrem) && Objects.nonNull(settlementPolicyTaxPrem)) {
                    result.setReconcileExecuteStatus(1);
                    result.setReconcileExecuteDesc("一单一议保单费率、税率-可执行对账");
                    if (StrUtil.isNotEmpty(settlementPolicyPrem.getSettlementMethod())) {
                        result.setSettlementMethod(Integer.parseInt(settlementPolicyPrem.getSettlementMethod()));
                    }
                    result.setTaxRate(settlementPolicyTaxPrem.getTaxRate());
                    result.setYearRate(settlementPolicyPrem.getYearRate());
                } else {
                    result.setReconcileExecuteStatus(3);
                    if (Objects.isNull(settlementPolicyPrem)) {
                        result.setReconcileExecuteDesc("一单一议保单费率获取失败");
                    } else if (Objects.isNull(settlementPolicyTaxPrem)) {
                        result.setReconcileExecuteDesc("一单一议保单税率获取失败");
                    } else {
                        result.setReconcileExecuteDesc("一单一议保单费率、税率获取失败");
                    }
                }
            }

            // 当前可执行对账的 可以计算一下费率信息
            if (StatusEnum.NORMAL.getCode().equals(result.getReconcileExecuteStatus())) {
                // 判断是税前还是税后
                if (StatusEnum.NORMAL.getCode().equals(result.getSettlementMethod())) {
                    // 税后 --- 修正一下如果税率为空的时候 税率为1
                    // 100÷1.06×0.25=23.58
                    result.setSettlementAmount(
                            BigDecimalUtils.mul(premium.divide(result.getTaxRate(), 4, RoundingMode.HALF_UP).doubleValue(),
                                    result.getYearRate().doubleValue()).setScale(2, RoundingMode.HALF_UP));
                } else {
                    //税前
                    result.setSettlementAmount(
                            BigDecimalUtils.mul(premium.doubleValue(), result.getYearRate().doubleValue())
                                    .setScale(2, RoundingMode.HALF_UP));
                }
            }
        } catch (GlobalException g) {
            log.warn("获取产品险种费率表信息出现自定义异常，异常信息={}", g.getMessage(), g);
            result.setReconcileExecuteStatus(0);
            result.setReconcileExecuteDesc(g.getMsg());
        } catch (Exception e) {
            log.warn("获取产品险种费率表信息异常，异常信息={}", e.getMessage(), e);
            result.setReconcileExecuteStatus(0);
            result.setReconcileExecuteDesc(
                    StrUtil.format("险种未获取到协议费率表配置信息，保单号={} 险种编码={}", policyNo, productCode));
        }
        return result;
    }

    public static boolean buildSettlementCommission(SettlementPolicyInfoEntity bean) {
        PolicyProductPremInput premInfoInput = builderSettlementPolicyInfoToPrem(bean);
        // 判断是小鲸还是非小鲸
        PremEventHandler premEventHandler = PremEventFactory.getInvoke(
                bean.getReconcileType() == 1 ? PremEventEnum.CONTRACT_PREM.getCode()
                        : PremEventEnum.PROTOCOL_PREM.getCode());
        List<PolicyProductPremResult> resultList = premEventHandler.queryPolicyProductPrem(premInfoInput);
        if (CollUtil.isEmpty(resultList)) {
            bean.setReconcileExecuteDesc(
                    StrUtil.format("没有获取到结算科目信息，保单号={} 险种编码={}", bean.getPolicyNo(),
                            bean.getProductCode()));
            return false;
        }
        if (resultList.size() > 1) {
            bean.setReconcileExecuteDesc(
                    StrUtil.format("获取到多个结算科目信息，保单号={} 险种编码={}", bean.getPolicyNo(),
                            bean.getProductCode()));
            return false;
        }
        String policyNo = bean.getPolicyNo();
        String productCode = bean.getProductCode();
        BigDecimal premium = bean.getPremium();
        try {
            PolicyProductPremResult productPremInfo = resultList.get(0);
            //设置结算科目
            bean.setPremCode(productPremInfo.getPremCode());
            bean.setSettlementSubjectName(productPremInfo.getSettlementSubjectName());
            bean.setSettlementSubjectCode(productPremInfo.getSettlementSubjectCode());
            bean.setProtocolProductCode(productPremInfo.getInsuranceProductCode());
            bean.setProtocolProductName(productPremInfo.getInsuranceProductName());
            // 协议编码
            bean.setProtocolCode(productPremInfo.getBusinessCode());
            if (productPremInfo.getSettlementMethod() == null || productPremInfo.getTaxRate() == null) {
                bean.setReconcileExecuteDesc(
                        StrUtil.format("险种获取到协议费率表结算方式或者结算费率错误，保单号={} 险种编码={}", policyNo,
                                productCode));
                return false;
            }
            // 复制其他信息
            BeanUtils.copyProperties(productPremInfo, bean);
            // 设置结算信息
            bean.setSettlementRateMethod(productPremInfo.getSettlementMethod());
            bean.setSettlementRateTax(productPremInfo.getTaxRate());
            bean.setIsCustomRate(productPremInfo.getIsCustomYearRate());

            PolicyPremResult settlementPolicyPrem = null;
            if (productPremInfo.getIsCustomYearRate() == 1) {
                // 判断是否为一单一议的佣金费率
                settlementPolicyPrem = premEventHandler.queryPolicyPrem(
                        PolicyPremInput.builder().policyNo(policyNo)
                                .productCode(productCode)
                                .batchCode(bean.getEndorsementNo())
                                .year(bean.getRenewalYear())
                                .period(bean.getRenewalPeriod())
                                .insuranceProductCode(bean.getProtocolProductCode())
                                .build());
                if (Objects.nonNull(settlementPolicyPrem)) {
                    bean.setSettlementRateMethod(Integer.parseInt(settlementPolicyPrem.getSettlementMethod()));
                }
            }

            // 判断是否为一单一议的佣金费率
            PolicyPremInput build = PolicyPremInput.builder()
                    .policyNo(policyNo)
                    .productCode(productCode)
                    .batchCode(bean.getEndorsementNo())
                    .year(bean.getRenewalYear())
                    .period(bean.getRenewalPeriod())
                    .insuranceProductCode(bean.getProtocolProductCode())
                    .build();
            PolicyTaxPremResult settlementPolicyTaxPrem = null;
            if (productPremInfo.getIsCustomTaxRate() == 1) {
                settlementPolicyTaxPrem = premEventHandler.queryPolicyTaxPrem(build);
            }

            if (productPremInfo.getIsCustomYearRate() == 0 && productPremInfo.getIsCustomTaxRate() == 0) {
                bean.setReconcileExecuteStatus(1);
                bean.setReconcileExecuteTime(new Date());
                bean.setReconcileExecuteDesc("费率表标准配置-可执行对账");
                bean.setProtocolCode(productPremInfo.getBusinessCode());
                // 计算费率及小鲸结算金额
                bean.setSettlementRate(String.valueOf(productPremInfo.getYearRate()));
                return builderSettlementAmount(bean, premium);
            } else if (productPremInfo.getIsCustomYearRate() == 0 && productPremInfo.getIsCustomTaxRate() == 1) {
                if (Objects.isNull(settlementPolicyTaxPrem)) {
                    bean.setReconcileExecuteStatus(3);
                    bean.setReconcileExecuteDesc("一单一议保单税率获取失败");
                } else {
                    bean.setReconcileExecuteStatus(1);
                    bean.setSettlementRateTax(settlementPolicyTaxPrem.getTaxRate());
                    bean.setTaxPremCode(settlementPolicyTaxPrem.getPremCode());
                    bean.setReconcileExecuteDesc("一单一议保单税率-可执行对账");
                    bean.setReconcileExecuteTime(new Date());
                    bean.setSettlementRate(String.valueOf(productPremInfo.getYearRate()));
//                    bean.setSettlementRateMethod(Integer.parseInt(settlementPolicyTaxPrem.getSettlementMethod()));
                    return builderSettlementAmount(bean, premium);
                }
            } else if (productPremInfo.getIsCustomYearRate() == 1 && productPremInfo.getIsCustomTaxRate() == 0) {
                if (Objects.isNull(settlementPolicyPrem)) {
                    bean.setReconcileExecuteStatus(3);
                    bean.setReconcileExecuteDesc("一单一议保单费率获取失败");
                } else {
                    bean.setReconcileExecuteStatus(1);
                    bean.setPremCode(settlementPolicyPrem.getPremCode());
                    bean.setReconcileExecuteTime(new Date());
                    bean.setReconcileExecuteDesc("一单一议保单费率-可执行对账");
                    // 计算费率及小鲸结算金额
                    bean.setSettlementRateMethod(Integer.valueOf(settlementPolicyPrem.getSettlementMethod()));
                    bean.setSettlementRateTax(settlementPolicyPrem.getTaxRate());
                    bean.setSettlementRate(String.valueOf(settlementPolicyPrem.getYearRate()));
                    return builderSettlementAmount(bean, premium);
                }
            } else {
                if (Objects.nonNull(settlementPolicyPrem) && Objects.nonNull(settlementPolicyTaxPrem)) {
                    bean.setReconcileExecuteStatus(1);
                    bean.setPremCode(settlementPolicyPrem.getPremCode());
                    bean.setTaxPremCode(settlementPolicyTaxPrem.getPremCode());
                    bean.setReconcileExecuteTime(new Date());
                    // 计算费率及小鲸结算金额
                    bean.setSettlementRateMethod(Integer.parseInt(settlementPolicyPrem.getSettlementMethod()));
                    bean.setSettlementRateTax(settlementPolicyTaxPrem.getTaxRate());
                    bean.setSettlementRate(String.valueOf(settlementPolicyPrem.getYearRate()));
                    bean.setReconcileExecuteStatus(1);
                    bean.setReconcileExecuteDesc("一单一议保单费率、税率-可执行对账");
                    return builderSettlementAmount(bean, premium);
                } else {
                    bean.setReconcileExecuteStatus(3);
                    if (Objects.isNull(settlementPolicyPrem)) {
                        bean.setReconcileExecuteDesc("一单一议保单费率获取失败");
                    } else if (Objects.isNull(settlementPolicyTaxPrem)) {
                        bean.setReconcileExecuteDesc("一单一议保单税率获取失败");
                    } else {
                        bean.setReconcileExecuteDesc("一单一议保单费率、税率获取失败");
                    }
                }
            }

        } catch (GlobalException g) {
            log.warn("获取产品险种费率表信息出现自定义异常，异常信息={}", g.getMessage(), g);
            bean.setReconcileExecuteDesc(
                    StrUtil.format("获取产品险种费率表信息出现自定义异常，保单号={} 险种编码={} 异常信息={}", policyNo,
                            productCode, g.getMessage()));
        } catch (Exception e) {
            log.warn("获取产品险种费率表信息异常，异常信息={}", e.getMessage(), e);
            bean.setReconcileExecuteDesc(
                    StrUtil.format("险种未获取到协议费率表配置信息，保单号={} 险种编码={}", policyNo, productCode));
        }
        return false;
    }

    /**
     * 根据保单明细纪录构建调用协议获取产品信息
     *
     * @param product 保单明细纪录
     * @return com.mpolicy.settlement.core.modules.protocol.dto.InsurancePolicyPremInfoInput
     * <AUTHOR>
     * @since 2023/6/12
     */
    private static PolicyProductPremInput builderSettlementPolicyInfoToPrem(SettlementPolicyInfoEntity product) {
        PolicyProductPremInput input = new PolicyProductPremInput();
        input.setPolicyNo(product.getPolicyNo());
        input.setPlantCode(product.getPlanCode());
        //0:新单 1:续期
        input.setInsuranceType(0);
        if (StringUtils.equals(product.getSettlementEventCode(), SettlementEventTypeEnum.RENEWAL_TERM_POLICY.getEventCode())) {
            input.setInsuranceType(1);
        }
        // 是否自保件
        input.setSelfPreservation(product.getSelfPreservation());
        // 代理人机构编码
        input.setOrgCode(product.getOrgCode());
        // 险种信息
        input.setApprovedTime(product.getApprovedTime());
        input.setInsuredPeriodType(product.getInsuredPeriodType());
        input.setInsuredPeriod(product.getInsuredPeriod());
        input.setPaymentPeriodType(product.getPaymentPeriodType());
        input.setPaymentPeriod(product.getPaymentPeriod());
        input.setPeriodType(product.getPeriodType());
        input.setRenewalYear(product.getRenewalYear());
        input.setRenewalPeriod(product.getRenewalPeriod());
        input.setProductCode(product.getProductCode());
        input.setSalesType(product.getSalesType());
        // 投保人信息 + 被保人信息
        input.setApplicantGender(product.getApplicantGender());
        input.setApplicantBirthday(product.getApplicantBirthday());
        input.setInsuredGender(product.getInsuredGender());
        input.setInsuredBirthday(product.getInsuredBirthday());
        input.setInsuredPolicyAge(product.getInsuredPolicyAge());
        return input;
    }

    /**
     * <p>
     * 长险：趸交产品折标系数为0.1；三年期产品折标系数为0.3；五年期产品折标系数为0.5；十年期及以上的产品折标系数为1；
     * （趸交=0.1；≥10年期，折标系数=1；非趸交，＜10年期，几年期产品折标系数就是零点几，比如8年期的，就算0.8；）
     * <p>
     * 短险（非车险）：默认折标系数为1； 短险（车险）：默认折标系数为0.1；
     * </p>
     *
     * @param policyNo          保单号
     * @param productCode       险种编码
     * @param insuredPolicyAge  被保人保单生效时年龄
     * @param PolicyProductType 保单类型
     * @param longShortFlag     长短线
     * @param premium           保费
     * @param periodType        缴费方式
     * @param paymentPeriodType 缴费期间类型
     * @param paymentPeriod     缴费时长
     * @param bean              结算保单明细
     * <AUTHOR>
     * @since 2023/6/27
     */
    public static void buildDiscountPremium(String policyNo, String productCode, Integer insuredPolicyAge,
                                            PolicyProductTypeEnum PolicyProductType, Integer longShortFlag, BigDecimal premium, String periodType,
                                            String paymentPeriodType, Integer paymentPeriod, SettlementPolicyInfoEntity bean) {
        BigDecimal discountPremium =
                buildDiscountPremium(policyNo, productCode, insuredPolicyAge, PolicyProductType, longShortFlag, premium,
                        periodType, paymentPeriodType, paymentPeriod);
        bean.setDiscountPremium(discountPremium);
    }

    public static BigDecimal buildDiscountPremium(String policyNo, String productCode, Integer insuredPolicyAge,
                                                  PolicyProductTypeEnum PolicyProductType, Integer longShortFlag, BigDecimal premium, String periodType,
                                                  String paymentPeriodType, Integer paymentPeriod) {
        // 赋值折算保费
        BigDecimal discountPremium =
                calcDiscountPremium(policyNo, productCode, insuredPolicyAge, PolicyProductType, longShortFlag, premium,
                        periodType, paymentPeriodType, paymentPeriod);
        log.info(
                "计算【折标保费】完成 保单号={} , 险种编码 = {} 保单类型 = {} 长短线 = {} 保费 = {} 缴费方式={} 缴费期间类型 = {} 缴费时长={} 【折标保费】= {}",
                policyNo, productCode, PolicyProductType.getPolicyProductType(), longShortFlag, premium, periodType,
                paymentPeriodType, paymentPeriod, discountPremium);
        return discountPremium;
    }

    /**
     * <p>
     * 长险：趸交产品折标系数为0.1；三年期产品折标系数为0.3；五年期产品折标系数为0.5；十年期及以上的产品折标系数为1；
     * （趸交=0.1；≥10年期，折标系数=1；非趸交，＜10年期，几年期产品折标系数就是零点几，比如8年期的，就算0.8；）
     * <p>
     * 短险（非车险）：默认折标系数为1； 短险（车险）：默认折标系数为0.1；
     * </p>
     *
     * @param policyNo          保单号
     * @param productCode       险种编码
     * @param insuredPolicyAge  被保人保单生效时年龄
     * @param PolicyProductType 保单类型
     * @param longShortFlag     长短线
     * @param premium           保费
     * @param periodType        缴费方式
     * @param paymentPeriodType 缴费期间类型
     * @param paymentPeriod     缴费时长
     * <AUTHOR>
     * @since 2023/6/27
     */
    public static BigDecimal calcDiscountPremium(String policyNo, String productCode, Integer insuredPolicyAge,
                                                 PolicyProductTypeEnum PolicyProductType, Integer longShortFlag, BigDecimal premium, String periodType,
                                                 String paymentPeriodType, Integer paymentPeriod) {
        // 校验保费
        if (premium == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                    StrUtil.format("保单号={} 险种编码={} " + "保费信息为空，无法运算折算保费", policyNo, productCode)));
        }
        // 如果长短线传入为空，降级根据险种编码获取
        if (longShortFlag == null) {
            longShortFlag = reconcileBaseHelper.productBaseService.getProductInfo(productCode).getLongShortFlag();
        }
        log.info(
                "开始计算【折标保费】 保单号={} , 险种编码 = {} 保单类型 = {} 长短线 = {} 保费 = {} 缴费方式={} 缴费期间类型 = {} 缴费时长={}",
                policyNo, productCode, PolicyProductType.getPolicyProductType(), longShortFlag, premium, periodType,
                paymentPeriodType, paymentPeriod);
        BigDecimal discountPremium = null;
        // 长线
        if (longShortFlag == 1) {
            // 缴费方式 YCJQ("PERIOD_TYPE:0", "一次缴清", "一次交清"),NJ("PERIOD_TYPE:1", "年缴", "年交"),YJ("PERIOD_TYPE:2", "月缴",
            // "月交"),JDJ("PERIOD_TYPE:3", "季度缴", "季交"),DJ("PERIOD_TYPE:4", "趸缴", "趸交"),BNJ("PERIOD_TYPE:5", "半年缴",
            // "半年交"),BDQJ("PERIOD_TYPE:6", "不定期缴", "不定期交"),DXYCX("PERIOD_TYPE:7", "短险一次缴清", "短险一次交清"),
            PolicyPaymentTypeEnum policyPaymentType = PolicyPaymentTypeEnum.getPaymentTypeEnumByCode(periodType);
            // 缴费期间类型 ONE("PAYMENT_PERIOD_TYPE:0", "一次缴清", "一次缴清", "一次交清"),YEAR("PAYMENT_PERIOD_TYPE:1", "按年缴", "年",
            // "年"),MONTH("PAYMENT_PERIOD_TYPE:2", "按月缴", "个月", "月"),IRREGULAR("PAYMENT_PERIOD_TYPE:3", "不定期缴",
            // "不定期缴", "一次交清"),AGE("PAYMENT_PERIOD_TYPE:4", "缴至确定年龄", "岁", "岁"),ALL("PAYMENT_PERIOD_TYPE:5", "缴至终身",
            // "缴至终身", "缴至终身"),
            PolicyPaymentPeriodTypeEnum policyPaymentPeriodType =
                    PolicyPaymentPeriodTypeEnum.getPaymentPeriodTypeEnumByCode(paymentPeriodType);
            switch (policyPaymentType) {
                case YCJQ:
                case BDQJ:
                case DJ:
                case DXYCX: {
                    // 趸交/一次性交情/短险一次缴清/不定期缴  产品折标系数为0.1
                    discountPremium = BigDecimalUtils.mul(premium.doubleValue(), new BigDecimal("0.1").doubleValue());
                    break;
                }
                case NJ:
                case YJ:
                case JDJ:
                case BNJ: {
                    // 年缴/月缴/季度缴/半年缴  产品折标系需要运算
                    switch (policyPaymentPeriodType) {
                        case ONE:
                        case IRREGULAR: {
                            // 一次缴清/不定期缴  产品折标系数为0.1
                            discountPremium =
                                    BigDecimalUtils.mul(premium.doubleValue(), new BigDecimal("0.1").doubleValue());
                            break;
                        }
                        case YEAR: {
                            // 按年缴纳，需要提取paymentPeriod除10
                            if (paymentPeriod == null) {
                                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                                        StrUtil.format("保单号={} " + "险种编码={} 【年缴】缴费时长为空，无法运算折算保费",
                                                policyNo, productCode)));
                            }
                            if (paymentPeriod >= 10) {
                                discountPremium =
                                        BigDecimalUtils.mul(premium.doubleValue(), new BigDecimal("0.1").doubleValue());
                            } else {
                                discountPremium = BigDecimalUtils.mul(premium.doubleValue(),
                                        BigDecimalUtils.div(String.valueOf(paymentPeriod), "10").doubleValue());
                            }
                            break;
                        }
                        case MONTH: {
                            // 按月缴纳，需要提取paymentPeriod除12获取年限, 转换为年后再除10
                            if (paymentPeriod == null) {
                                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                                        StrUtil.format("保单号={} " + "险种编码={} 【月缴】缴费时长为空，无法运算折算保费",
                                                policyNo, productCode)));
                            }
                            Integer monthYear = paymentPeriod / 12;
                            if (monthYear >= 10) {
                                discountPremium =
                                        BigDecimalUtils.mul(premium.doubleValue(), new BigDecimal("0.1").doubleValue());
                            } else {
                                discountPremium = BigDecimalUtils.mul(premium.doubleValue(),
                                        BigDecimalUtils.div(String.valueOf(monthYear), "10").doubleValue());
                            }
                            break;
                        }
                        case AGE: {
                            if (insuredPolicyAge == null || insuredPolicyAge < 0) {
                                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format(
                                        "保单号={} " + "险种编码={} 【缴至x岁】没有被保人保单生效时年龄，无法运算折算保费",
                                        policyNo, productCode)));
                            }
                            if (paymentPeriod == null) {
                                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                                        StrUtil.format("保单号={} " + "险种编码={} 【缴至x岁】缴费时长为空，无法运算折算保费",
                                                policyNo, productCode)));
                            }
                            // paymentPeriod - insuredPolicyAge / 10
                            Integer ageYear = paymentPeriod - insuredPolicyAge;
                            if (ageYear >= 10) {
                                discountPremium =
                                        BigDecimalUtils.mul(premium.doubleValue(), new BigDecimal("0.1").doubleValue());
                            } else {
                                discountPremium = BigDecimalUtils.mul(premium.doubleValue(),
                                        BigDecimalUtils.div(String.valueOf(ageYear), "10").doubleValue());
                            }
                            break;
                        }
                        case ALL: {
                            if (insuredPolicyAge == null || insuredPolicyAge < 0) {
                                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format(
                                        "保单号={} " + "险种编码={} 【缴至终身】没有被保人保单生效时年龄，无法运算折算保费",
                                        policyNo, productCode)));
                            }
                            if (paymentPeriod == null) {
                                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                                        StrUtil.format("保单号={} " + "险种编码={} 【缴至终身】缴费时长为空，无法运算折算保费",
                                                policyNo, productCode)));
                            }
                            // paymentPeriod - insuredPolicyAge / 10
                            Integer ageYear = 105 - insuredPolicyAge;
                            if (ageYear >= 10) {
                                discountPremium =
                                        BigDecimalUtils.mul(premium.doubleValue(), new BigDecimal("0.1").doubleValue());
                            } else {
                                discountPremium = BigDecimalUtils.mul(premium.doubleValue(),
                                        BigDecimalUtils.div(String.valueOf(ageYear), "10").doubleValue());
                            }
                            break;
                        }
                        default: {
                            log.info("未知缴费期间类型，暂不处理");
                        }
                    }
                }
                default: {
                    log.info("未知缴费期间类型，暂不处理");
                }
            }
        } else {
            if (PolicyProductType == PolicyProductTypeEnum.VEHICLE) {
                // 短险（车险）
                discountPremium = BigDecimalUtils.mul(premium.doubleValue(), new BigDecimal("0.1").doubleValue());
            } else {
                // 短险（非车险）
                discountPremium = BigDecimalUtils.mul(premium.doubleValue(), new BigDecimal("1").doubleValue());
            }
        }
        // 判断折算保费 是否正常运算获取到结果
        if (discountPremium == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                    StrUtil.format("保单号={} 险种编码={} 折算保费未能正确命中，请核查", policyNo, productCode)));
        }
        // 赋值折算保费
        return discountPremium;

    }

    /**
     * 从农保获取一单一议的支出佣金配置比例
     *
     * @param policyNo 保单号
     * <AUTHOR>
     * @since 2023/8/22 10:34
     */
    public static BigDecimal buildPolicyCostCommission(String policyNo) {
        // 获取农保一单一议手续费
        List<CsVehicleCommissionDetailEntity> vehicleCommissionList =
                reconcileBaseHelper.csVehicleCommissionDetailService.lambdaQuery()
                        .eq(CsVehicleCommissionDetailEntity::getPolicyNo, policyNo)
                        .eq(CsVehicleCommissionDetailEntity::getEnabledFlag, 0)
                        .eq(CsVehicleCommissionDetailEntity::getBusinessType, "common").list();
        log.info("降级从农保获取一单一议的结算佣金配置，保单号={}，命中结果={}", policyNo,
                JSON.toJSONString(vehicleCommissionList));
        if (!CollectionUtils.isEmpty(vehicleCommissionList)) {
            return vehicleCommissionList.stream().filter(o -> Objects.equals(o.getPolicyStatus(), "1")).findFirst()
                    .get().getPaymentRate();
        }
        return null;
    }

    /**
     * 从农保获取历史的支出佣金
     *
     * @param policyNo  保单号
     * @param idNumbers 证件号
     * <AUTHOR>
     * @since 2023/8/22 10:34
     */
    public static List<CsVehicleCommissionDetailEntity> listCsCommissionDetail(String policyNo,
                                                                               List<String> idNumbers) {
        // 获取农保一单一议手续费
        List<CsVehicleCommissionDetailEntity> vehicleCommissionList =
                reconcileBaseHelper.csVehicleCommissionDetailService.lambdaQuery()
                        .eq(CsVehicleCommissionDetailEntity::getPolicyNo, policyNo)
                        .eq(CsVehicleCommissionDetailEntity::getEnabledFlag, 0)
                        .in(CollectionUtils.isNotEmpty(idNumbers), CsVehicleCommissionDetailEntity::getInsuredIdNumber,
                                idNumbers).orderByDesc(CsVehicleCommissionDetailEntity::getAccountTime).list();
        log.info("从农保获取历史的支出佣金，保单号={}，证件号={},命中结果={}", policyNo, idNumbers,
                JSON.toJSONString(vehicleCommissionList));

        return vehicleCommissionList;
    }

    /**
     * businessType = common时termNum传1，businessType=renewal时，termNum=实际期数
     *
     * @param csCommissionDetails
     * @param policyNo
     * @param idNumber
     * @param businessType
     * @param termNum
     * @return
     */
    public static CsCommissionDto getByCsCommissionInfo(List<CsVehicleCommissionDetailEntity> csCommissionDetails,
                                                        String policyNo, String idNumber, String businessType, Integer termNum) {
        if (CollectionUtils.isEmpty(csCommissionDetails)) {
            return null;
        }

        Optional<CsVehicleCommissionDetailEntity> opt = csCommissionDetails.stream().filter(
                o -> Objects.equals(policyNo, o.getPolicyNo()) && Objects.equals(idNumber,
                        o.getInsuredIdNumber()) && Objects.equals(o.getPolicyStatus(), "1") && Objects.equals(termNum,
                        o.getTermNum()) && Objects.equals(businessType, o.getBusinessType())).findFirst();
        //vehicleCommissionList.stream().filter(o->Objects.equals(o.getPolicyStatus(),"1")).findFirst();
        if (opt.isPresent()) {
            CsCommissionDto dto = new CsCommissionDto();
            CsVehicleCommissionDetailEntity entity = opt.get();
            dto.setPolicyNo(policyNo);
            dto.setCommissionUserId(entity.getCommissionUserId());
            if (entity.getPaymentRate() != null) {
                dto.setRate(entity.getPaymentRate().multiply(new BigDecimal("0.01")));
            } else {
                //支付比例为空的情况下，需要通过支付佣金、保费计算比例
                if (entity.getAmount() != null) {
                    dto.setRate(
                            entity.getPaymentAmount().divide(entity.getAmount()).setScale(2, BigDecimal.ROUND_HALF_UP));
                } else {
                    return null;
                }
            }
            return dto;
        }
        return null;
    }

    public static String getSellProductName(String sellProductCode) {
        log.info("保单商品编码:{}", sellProductCode);
        if (StringUtils.isBlank(sellProductCode)) {
            return null;
        }
        // 初始化缓存
        initSellProductInfoCache();
        // 从redis 获取
        String cacheData =
                reconcileBaseHelper.redisService.get(SettlementCoreCenterKeys.SELL_PRODUCT_INFO, "LIST_DATA", String.class);
        List<SellProductCache> list = JSONArray.parseArray(cacheData, SellProductCache.class);

        if (list.isEmpty()) {
            return null;
        }
        SellProductCache sellProductCache =
                list.stream().filter(x -> x.getProductCode().equals(sellProductCode)).findFirst().orElse(null);
        return !Objects.isNull(sellProductCache) ? sellProductCache.getProductName() : null;
    }

    public static void initSellProductInfoCache() {
        if (!reconcileBaseHelper.redisService.exists(SettlementCoreCenterKeys.SELL_PRODUCT_INFO, "LIST_DATA")) {
            List<SellProductCache> cacheData = new ArrayList<>();

            List<AllSellProductListOut> orgList = reconcileBaseHelper.agentBaseService.findAllSellProductList(true);
            if (orgList.isEmpty()) {
                return;
            }
            orgList.forEach(x -> {
                SellProductCache bean = new SellProductCache();
                BeanUtils.copyProperties(x, bean);
                cacheData.add(bean);
            });

            reconcileBaseHelper.redisService.set(SettlementCoreCenterKeys.SELL_PRODUCT_INFO, "LIST_DATA",
                    JSONArray.toJSON(cacheData));
        }
    }

    /**
     * 获取保单结算状态
     *
     * @param input 请求参数 个团新单 续投/续期
     */
    public static List<SettlementStatusOut> getPolicySettlementStatus(List<SettlementStatusInput> input) {
        log.info("获取保单结算状态请求数据={}", JSONUtil.toJsonStr(input));
        List<String> policyNos =
                input.stream().map(SettlementStatusInput::getPolicyNo).distinct().collect(Collectors.toList());
        List<String> mainProductCodes =
                input.stream().map(SettlementStatusInput::getMainProductCode).distinct().collect(Collectors.toList());
        List<Integer> renewalPeriods =
                input.stream().map(SettlementStatusInput::getRenewalPeriod).distinct().collect(Collectors.toList());
        String key = "保单号={}期次={}主险编码={}";
        List<SettlementPolicyInfoEntity> settlementPolicyInfoList =
                reconcileBaseHelper.settlementPolicyInfoService.lambdaQuery()
                        .in(SettlementPolicyInfoEntity::getSettlementEventCode, "settlement.global.policy.group_new_policy",
                                "settlement.global.policy.personal_new_policy", "settlement.global.policy.renewal",
                                "settlement.global.policy.renewal_term").in(SettlementPolicyInfoEntity::getPolicyNo, policyNos)
                        .in(SettlementPolicyInfoEntity::getRenewalPeriod, renewalPeriods)
                        .in(SettlementPolicyInfoEntity::getProductCode, mainProductCodes)
                        .eq(SettlementPolicyInfoEntity::getRectificationMark, 0) // 非冲正数据
                        .eq(SettlementPolicyInfoEntity::getReconcileStatus, 3) // 结算完成
                        .list();
        log.info("获取保单结算状态数据库结果数据={}", JSONUtil.toJsonStr(settlementPolicyInfoList));
        Map<String, List<SettlementPolicyInfoEntity>> settlementPolicyInfoMap = settlementPolicyInfoList.stream()
                .collect(Collectors.groupingBy(m -> MD5.create()
                        .digestHex16(StrUtil.format(key, m.getPolicyNo(), m.getRenewalPeriod(), m.getProductCode()))));
        List<SettlementStatusOut> resultList = new ArrayList<>();
        input.forEach(action -> {
            SettlementStatusOut result = BeanUtil.copyProperties(action, SettlementStatusOut.class);
            String mapKey = MD5.create().digestHex16(
                    StrUtil.format(key, action.getPolicyNo(), action.getRenewalPeriod(), action.getMainProductCode()));
            result.setSettlementStatus(settlementPolicyInfoMap.containsKey(mapKey) ? 1 : 0);
            resultList.add(result);
        });
        return resultList;
    }
}
