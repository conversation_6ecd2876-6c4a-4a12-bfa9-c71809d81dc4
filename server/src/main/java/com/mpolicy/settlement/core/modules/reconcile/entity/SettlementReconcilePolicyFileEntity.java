package com.mpolicy.settlement.core.modules.reconcile.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@TableName("settlement_reconcile_policy_file")
public class SettlementReconcilePolicyFileEntity implements Serializable {

    private static final long serialVersionUID = -5440556661601450529L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private String reconcileCode;
    /**
     * 保单号
     */
    private String policyNo;

    private String fileCode;
    private Integer fileType;
    /**
     * 批次号
     */
    private String batchCode;
    /**
     * 小鲸险种名称(可能为空)
     */
    private String productName;
    /**
     * 小鲸险种编码(可能为空)
     */
    private String productCode;
    /**
     * 协议保司产品编码
     */
    private String insuranceProductCode;
    /**
     * 协议保司产品名称
     */
    private String insuranceProductName;
    /**
     * 保单状态
     */
    private String policyStatus;
    /**
     * 记账科目编码
     */
    private String reconcileSubjectCode;
    /**
     * 记账科目名称
     */
    private String reconcileSubjectName;
    /**
     * 新单/续期
     */
    private String insuranceType;
    /**
     * 0:免税 1:应税
     */
    private String dutyType;
    /**
     * 续期年期
     */
    private Integer renewalYear;
    /**
     * 续期期次
     */
    private Integer renewalPeriod;
    /**
     * 实收保费
     */
    private BigDecimal realityPremium;
    /**
     * 手续费占比
     */
    private BigDecimal settlementRate;
    /**
     * 手续费金额
     */
    private BigDecimal companyAmount;
}
