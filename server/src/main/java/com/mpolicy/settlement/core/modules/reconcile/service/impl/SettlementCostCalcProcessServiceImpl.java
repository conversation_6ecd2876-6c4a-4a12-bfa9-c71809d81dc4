package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.other.BigDecimalUtils;
import com.mpolicy.policy.common.enums.EpPolicyDetailsReqOperationTypeEnum;
import com.mpolicy.policy.common.ep.policy.*;
import com.mpolicy.policy.common.ep.policy.qry.details.GroupPreservationInsuredVo;
import com.mpolicy.product.common.base.ProductBase;
import com.mpolicy.settlement.core.common.Constant;
import com.mpolicy.settlement.core.common.reconcile.enums.PremEventEnum;
import com.mpolicy.settlement.core.modules.protocol.dto.ProtocolInsuranceProductInfoOut;
import com.mpolicy.settlement.core.modules.protocol.helper.ProtocolBaseHelper;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.*;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.BasicCostEventDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.CsVehicleCommissionDetailEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.*;
import com.mpolicy.settlement.core.modules.reconcile.event.factory.PremEventFactory;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.PremEventHandler;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyPremInput;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyPremResult;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyProductPremInput;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyProductPremResult;
import com.mpolicy.settlement.core.modules.reconcile.helper.ReconcileBaseHelper;
import com.mpolicy.settlement.core.modules.reconcile.service.*;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import com.mpolicy.settlement.core.service.common.PolicyCenterBaseClient;
import com.mpolicy.settlement.core.service.common.ProductBaseService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mpolicy.settlement.core.common.Constant.SYSTEM_CORRECTION_USER;
import static com.mpolicy.settlement.core.common.Constant.ZHNX_CHANNEL_CODE;
import static com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum.PERSONAL_NEW_POLICY;
import static com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum.STANDARD_SURRENDER;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/16 2:49 下午
 * @Version 1.0
 */
@Slf4j
@Service("settlementCostCalcProcessService")
public class SettlementCostCalcProcessServiceImpl implements SettlementCostCalcProcessService {

    @Autowired
    protected PolicyCenterBaseClient policyCenterBaseClient;

    @Autowired
    private ProductBaseService productBaseService;

    @Autowired
    protected SettlementCostInfoService settlementCostInfoService;

    @Autowired
    protected SettlementCostPolicyInfoService settlementCostPolicyInfoService;

    @Autowired
    protected SettlementCostOwnerService settlementCostOwnerService;

    private static List<String> LIST_RENEWAL_INSURANCE = Arrays.asList(SettlementEventTypeEnum.RENEWAL_TERM_POLICY.getEventCode(), SettlementEventTypeEnum.RENEWAL_POLICY.getEventCode());

    /**
     * 城市业务保费为0的事件类型编码
     */
    protected static List<String> LIST_CITY_ZERO_PREMIUM_EVENT = Arrays.asList(SettlementEventTypeEnum.STANDARD_SURRENDER.getEventCode(), SettlementEventTypeEnum.TERMINATION_PRODUCT.getEventCode());

    /**
     * 支出端-获取险种配置信息
     *
     * @param productInfoVos
     * @return
     */
    @Override
    public Map<String, ProductBase> getProductBaseMap(List<EpProductInfoVo> productInfoVos) {
        if (CollectionUtils.isEmpty(productInfoVos)) {
            return Collections.EMPTY_MAP;
        }
        List<String> productCodes = productInfoVos.stream().map(EpProductInfoVo::getProductCode).distinct().collect(Collectors.toList());
        List<ProductBase> productList = productBaseService.getProductBaseList(productCodes);
        if (CollectionUtils.isEmpty(productList)) {
            return Collections.EMPTY_MAP;
        }
        return productList.stream().collect(Collectors.toMap(ProductBase::getProductCode, Function.identity(), (x, y) -> y));
    }

    /**
     * 支出端-佣金计算入参验证
     *
     * @param eventJob
     * @param policyInfo
     */
    @Override
    public void validParam(SettlementEventJobEntity eventJob, EpContractInfoVo policyInfo, Runnable validCustomize) {
        String mainEpProductCode = policyInfo.getContractBaseInfo().getMainProductCode();
        if (mainEpProductCode == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-入参验证-保单号={}, 缺少主险信息", eventJob.getEventBusinessCode())));
        }

        if (validCustomize != null) {
            validCustomize.run();
        }
    }

    public Boolean isWhaleLongPolicy(EpContractInfoVo policyInfo, Map<String, ProductBase> productMap) {
        if (!Objects.equals(policyInfo.getChannelInfo().getChannelCode(), Constant.ZHNX_CHANNEL_CODE)) {
            ProductBase mainProductBase = productMap.get(policyInfo.getContractBaseInfo().getMainProductCode());
            if (mainProductBase.getLongShortFlag() != null && mainProductBase.getLongShortFlag() == 1) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }


    /**
     * 支出端-创建保单基础信息
     *
     * @param policyInfo
     * @return
     */
    @Override
    public SettlementCostPolicyInfoEntity buildSettlementCostPolicyInfo(EpContractInfoVo policyInfo) {
        SettlementCostPolicyInfoEntity entity = new SettlementCostPolicyInfoEntity();
        EpContractBaseInfoVo contractBaseInfo = policyInfo.getContractBaseInfo();
        EpContractExtendInfoVo contractExtendInfo = policyInfo.getContractExtendInfo();
        entity.setContractCode(policyInfo.getContractCode());
        BeanUtils.copyProperties(contractBaseInfo, entity);
        BeanUtils.copyProperties(contractExtendInfo, entity);


        //设置商品名称
        entity.setCommodityName(ReconcileBaseHelper.getSellProductName(entity.getCommodityCode()));
        //回执/回访日志
        entity.setReceiptStatus(contractExtendInfo.getIsNeedReceipt());
        entity.setReceiptTime(contractExtendInfo.getReceiptSignTime());
        entity.setRevisitStatus(contractExtendInfo.getIsNeedRevisit());
        entity.setRevisitTime(contractExtendInfo.getRevisitTime());
        entity.setPolicyProductType(policyInfo.getPolicyProductType());
        entity.setPolicySource(policyInfo.getPolicySource());
        entity.setSerialNumber(policyInfo.getSerialNumber());
        entity.setLongShortFlag(contractBaseInfo.getLongShortFlag());


        //todo 后续小鲸保单号与保司保单号分开以后再修改回来,默认与小鲸保单号一致
        if(StringUtils.isNotBlank(contractBaseInfo.getThirdPolicyNo())) {
            entity.setCompanyPolicyNo(contractBaseInfo.getThirdPolicyNo());
        }else{
            entity.setCompanyPolicyNo(contractBaseInfo.getPolicyNo());
        }
        //投保人信息
        entity.setApplicantName(policyInfo.getApplicantInfo().getApplicantName());
        entity.setApplicantMobile(policyInfo.getApplicantInfo().getApplicantMobile());
        entity.setApplicantIdCard(policyInfo.getApplicantInfo().getApplicantIdCard());
        entity.setApplicantGender(policyInfo.getApplicantInfo().getApplicantGender());
        entity.setApplicantBirthday(policyInfo.getApplicantInfo().getApplicantBirthday());
        entity.setApplicantAge(policyInfo.getApplicantInfo().getApplicantAge());
        // 代理人机构编码
        if (policyInfo.getAgentInfoList() != null && policyInfo.getAgentInfoList().size() > 0) {
            policyInfo.getAgentInfoList().stream().filter(a -> a.getMainFlag() == 1).findFirst().ifPresent(epAgentInfoVo -> entity.setOrgCode(epAgentInfoVo.getOrgCode()));
        }
        //保单活动信息：整村推荐、四级分销单
        builderPolicyActivityInfos(policyInfo,entity);
        //车险评分等级处理
        if(Objects.equals(policyInfo.getPolicyProductType(),PolicyProductTypeEnum.VEHICLE.getCode())){
            if(policyInfo.getVehicleInfo()!=null){
                entity.setVehicleBusinessScore(policyInfo.getVehicleInfo().getVehicleBusinessScore());
            }
        }
        return entity;
    }

    /**
     * 组装退保更新保单基础信息
     * @param policyInfo
     * @param entity
     */
    @Override
    public void builderUpdateSettlementCostPolicyInfo(EpContractInfoVo policyInfo,SettlementCostPolicyInfoEntity entity){
        EpContractBaseInfoVo contractBaseInfo = policyInfo.getContractBaseInfo();
        EpContractExtendInfoVo contractExtendInfo = policyInfo.getContractExtendInfo();

        entity.setReceiptStatus(contractExtendInfo.getIsNeedReceipt());
        entity.setReceiptTime(contractExtendInfo.getReceiptSignTime());
        entity.setRevisitStatus(contractExtendInfo.getIsNeedRevisit());
        entity.setRevisitTime(contractExtendInfo.getRevisitTime());
        entity.setPolicyStatus(contractBaseInfo.getPolicyStatus());
    }

    /**
     * 1.四级分销单:表中ep_policy_activity存在记录，且type为200
     * 2.活动保单保单 表中ep_policy_activity存在记录，且type为100，且activity_code有值。
     * 3.整村推进表中ep_policy_activity存在记录，且type为100，且activity_code有值，且product_activity_code有值。(整村推荐保单属于活动保单中的一中)
     * @param policyInfo
     * @param entity
     */
    private void builderPolicyActivityInfos(EpContractInfoVo policyInfo,SettlementCostPolicyInfoEntity entity){
        entity.setDistributionFlag(0);
        entity.setRuralProxyFlag(0);
        if(CollectionUtils.isNotEmpty(policyInfo.getEpPolicyActivitys())){
            for(EpPolicyActivityVo activityVo : policyInfo.getEpPolicyActivitys()){

                //四级分销
                if(Objects.equals(EpPolicyActivityTypeEnum.FOURTH_LEVEL_DISTRIBUTION.getCode(),activityVo.getType())){
                    entity.setDistributionFlag(1);
                }
                //整村推荐
                if(Objects.equals(EpPolicyActivityTypeEnum.WHOLE_VILLAGE_PROMOTION.getCode(),activityVo.getType())
                        && StringUtils.isNotBlank(activityVo.getActivityCode())
                        && StringUtils.isNotBlank(activityVo.getProductActivityCode())){
                    entity.setRuralProxyFlag(1);
                    //活动编码
                    entity.setProductActivityCode(activityVo.getProductActivityCode());
                }

            }
        }
    }

    /**
     * 支出端-初始化支出8大基础佣金记录
     *

     * @return
     */
    public SettlementCostInfoEntity initSettlementCostInfo(BasicCostEventDto dto, String contractCode, Integer insuranceType) {
        SettlementCostInfoEntity cost = new SettlementCostInfoEntity();
        cost.setCostCode(PolicySettlementUtils.createCodeLastNumber("CC"));
        //事件编号
        cost.setEventSourceCode(dto.getPushEventCode());
        //事件信息
        if (Objects.nonNull(dto.getEventType())) {
            cost.setSettlementEventCode(dto.getEventType().getEventCode());
            //八大基础佣金事件编码时与SettlementEventCode一直
            cost.setInitialEventCode(dto.getEventType().getEventCode());
            cost.setSettlementEventDesc(dto.getEventType().getEventDesc());
        }
        //科目信息
        cost.setSettlementSubjectCode(dto.getSubjectEnum().getCode());
        cost.setSettlementSubjectName(dto.getSubjectEnum().getName());
        cost.setSettlementGenerateType(1);
        cost.setContractCode(contractCode);
        cost.setInsuranceType(insuranceType);
        //设置佣金类型
        setCommissionTypeByEventType(dto.getEventType(), cost);

        return cost;
    }

    protected void setCommissionTypeByEventType(SettlementEventTypeEnum eventType, SettlementCostInfoEntity cost) {
        switch (eventType) {
            case PERSONAL_NEW_POLICY:
            case RENEWAL_POLICY:
            case GROUP_NEW_POLICY:
                cost.setCommissionType(CommissionTypeEnum.COMMON.getCode());
                break;
            case RENEWAL_TERM_POLICY:
                cost.setCommissionType(CommissionTypeEnum.RENEWAL.getCode());
                break;
            case GROUP_ADD_OR_SUBTRACT:
                cost.setCommissionType(CommissionTypeEnum.CORRECT_INC_OR_DEC.getCode());
                break;
            case STANDARD_SURRENDER:
            case HESITATE_SURRENDER:
            case TERMINATION_PRODUCT:
            case PROTOCOL_TERMINATION:
                cost.setCommissionType(CommissionTypeEnum.CORRECT_SURRENDER.getCode());
                break;
            default:
                log.warn("该佣金类型未知！！！");
        }
    }

    /**
     * 支出端-组装获取基础佣金配置的入参(被保人参数+续期参数除外)
     *
     * @param policyInfo
     * @return
     */
    public PolicyProductPremInput buildBasicCommissionConfigParam(EpContractInfoVo policyInfo,
                                                                        Integer insuranceType,
                                                                        EpProductInfoVo product,
                                                                        Integer renewalYear,
                                                                        Integer renewalPeriod
    ) {
        PolicyProductPremInput input = new PolicyProductPremInput();
        input.setChannelCode(policyInfo.getChannelInfo().getChannelCode());
        input.setPolicyNo(policyInfo.getContractBaseInfo().getPolicyNo());
        input.setMainProductCode(policyInfo.getContractBaseInfo().getMainProductCode());
        input.setProductCode(product.getProductCode());
        input.setPlantCode(product.getPlanCode());
        input.setInsuredPeriodType(product.getInsuredPeriodType());
        input.setInsuredPeriod(product.getInsuredPeriod());

        input.setPeriodType(product.getPeriodType());
        input.setPaymentPeriodType(product.getPaymentPeriodType());
        input.setPaymentPeriod(product.getPaymentPeriod());
        //0:新单 1:续投
        input.setInsuranceType(insuranceType);
        input.setApprovedTime(policyInfo.getContractExtendInfo().getApprovedTime());
        input.setRenewalYear(renewalYear);
        input.setRenewalPeriod(renewalPeriod);
        // 投保人信息
        if (policyInfo.getApplicantInfo() != null) {
            input.setApplicantGender(policyInfo.getApplicantInfo().getApplicantGender());
            input.setApplicantBirthday(policyInfo.getApplicantInfo().getApplicantBirthday());
        }
        input.setSalesType(policyInfo.getContractBaseInfo().getSalesType());
        // 是否自保件
        input.setSelfPreservation(policyInfo.getContractBaseInfo().getSelfPreservation());

        // 代理人机构编码
        if (policyInfo.getAgentInfoList() != null && policyInfo.getAgentInfoList().size() > 0) {
            policyInfo.getAgentInfoList().stream().filter(a -> a.getMainFlag() == 1).findFirst().ifPresent(epAgentInfoVo -> input.setOrgCode(epAgentInfoVo.getOrgCode()));
        }
        return input;

    }

    private JSONObject getRenewalProductFromEventJob(BasicCostEventDto costEventDto, String insuredIdCard, String productCode) {
        JSONObject eventData = JSONObject.parseObject(costEventDto.getEventRequest());
        JSONArray insuredInfoList = eventData.getJSONArray("insuredInfoList");
        for (int i = 0; i < insuredInfoList.size(); i++) {
            JSONObject insured = (JSONObject) insuredInfoList.get(i);
            String idCard = insured.get("insuredIdCard").toString();
            if (insuredIdCard.equals(idCard)) {
                JSONArray productInfoList = insured.getJSONArray("productInfoList");
                for (int j = 0; j < productInfoList.size(); j++) {
                    JSONObject productInfo = (JSONObject) productInfoList.get(j);
                    if (productCode.equals(productInfo.getString("productCode"))) {
                        return productInfo;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 支出端-获取基础佣金配置
     */
    public List<PolicyProductPremResult> getCostBasicCommissionConfig(String policyNo, PolicyProductPremInput input) {
        log.info("获取佣金配置比例入参：{}", input);
        PremEventHandler premEventHandler = PremEventFactory.getInvoke(PremEventEnum.COMMISSION_PREM.getCode());
        List<PolicyProductPremResult> basicConfigList = premEventHandler.queryPolicyProductPrem(input);
        if (CollectionUtils.isEmpty(basicConfigList)) {
            log.warn("支出端-获取基础佣金配置-保单号={}, 基础佣金配置信息不存在，入参：{}", policyNo, input);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-获取基础佣金配置-保单号={}, 基础佣金配置信息不存在", policyNo)));
        }
        //验证配置
        if (basicConfigList.size() > 1) {
            Set<String> set = Sets.newHashSet();
            for (PolicyProductPremResult cof : basicConfigList) {
                if (set.contains(cof.getSettlementCompanyCode())) {
                    log.warn("支出端-获取基础佣金配置-保单号={}, 结算机构编码{}存在重复配置，入参：{}", policyNo, cof.getSettlementCompanyCode(), input);
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-获取基础佣金配置-保单号={}, 结算机构编码存在重复配置", policyNo)));
                }
                String str = validBasicCommissionConfig(cof);
                if (StringUtil.isNotBlank(str)) {
                    log.warn("支出端-获取基础佣金配置-保单号={},{}，入参：{}", policyNo, str, input);
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-获取基础佣金配置-保单号={}, {}", policyNo, str)));
                }
                set.add(cof.getSettlementCompanyCode());
            }
        } else {
            String str = validBasicCommissionConfig(basicConfigList.get(0));
            if (StringUtil.isNotBlank(str)) {
                log.warn("支出端-获取基础佣金配置-保单号={},{}，入参：{}", policyNo, str, input);
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-获取基础佣金配置-保单号={}, {}", policyNo, str)));
            }
        }
        log.info("获取到的佣金配置列表：{}", basicConfigList);
        return basicConfigList;
    }

    /**
     * 支出端-验证基础佣金配置
     *
     * @param basicConfig
     * @return
     */
    private String validBasicCommissionConfig(PolicyProductPremResult basicConfig) {
        if (StringUtil.isBlank(basicConfig.getSettlementCompanyCode())) {
            return "结算机构编码不存在";
        }
        boolean valid1 = basicConfig.getYearRate() == null && basicConfig.getIsCustomYearRate() != 0;
        boolean valid2 = basicConfig.getYearRate() != null &&
                (basicConfig.getYearRate().compareTo(BigDecimal.ZERO) < 0 || basicConfig.getYearRate().compareTo(new BigDecimal("100")) > 0);
        if (valid1 || valid2) {
            return "基础佣金配置的利率" + basicConfig.getYearRate() + "不在[0,100]范围内";
        }
        return "";
    }

    /**
     * @param policyNo
     * @param product
     * @param config
     * @param bean
     */
    protected void calcBasicCommission(String policyNo, String endorsementNo, EpProductInfoVo product, PolicyProductPremResult config, SettlementCostInfoEntity bean) {
        bean.setSingleProposeFlag(config.getIsCustomYearRate());

        if (config.getIsCustomYearRate() == 1) {
            // 判断是否为一单一议的佣金费率
            PremEventHandler premEventHandler = PremEventFactory.getInvoke(PremEventEnum.COMMISSION_PREM.getCode());
            PolicyPremResult settlementPolicyPrem = premEventHandler.queryPolicyPrem(PolicyPremInput.builder()
                    .policyNo(policyNo)
                    .settlementCompanyCode(config.getSettlementCompanyCode())
                    .batchCode(endorsementNo)
                    .year(bean.getRenewalYear())
                    .period(bean.getRenewalPeriod())
                    .build());
            log.info("配置中返回一单一议保单{}/{}佣金费率信息：{}", policyNo, endorsementNo, settlementPolicyPrem);
            if (Objects.nonNull(settlementPolicyPrem)) {
                builderBasicCommission(config.getSettlementCompanyCode(),config.getSettlementCompanyName(), bean.getPremium(), settlementPolicyPrem.getYearRate(), config.getPremCode(), CostTypeEnum.BASIC, bean);
            } else {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-计算基础佣金-保单号={},一单一议保单佣金费率信息未找到", policyNo)));
                //builderCsPolicyCommision(policyNo, product, bean);
            }
        } else {
            builderBasicCommission(config.getSettlementCompanyCode(),config.getSettlementCompanyName(), bean.getPremium(), config.getYearRate(), config.getPremCode(), CostTypeEnum.BASIC, bean);
        }

        if (bean.getCostAmount() == null) {
            log.warn("支出端-计算基础佣金-保单号={},计算出的支出佣金为空,佣金配置未找到", policyNo);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-计算基础佣金-保单号={},计算出的支出佣金为空,佣金配置未找到", policyNo)));
        }
    }

    //计算【农保一单一议】佣金配置
    private void builderCsPolicyCommision(String policyNo, EpProductInfoVo product, SettlementCostInfoEntity bean) {
        //获取【农保一单一议】佣金配置
        log.info("支出端--计算基础佣金(农保一单一议)-保单号={}", policyNo);
        BigDecimal csRate = ReconcileBaseHelper.buildPolicyCostCommission(policyNo);
        if (csRate != null) {
            //农保的佣金比例为百分比，转成小鲸比例需要*0.01
            csRate = csRate.multiply(new BigDecimal("0.01"));
            builderBasicCommission("","", bean.getPremium(), csRate, null, CostTypeEnum.BASIC, bean);
        }
    }

//    public void getPolicyCommissionConfig(String policyNo, String endorsementNo, PolicyProductPremResult config) {
//        PremEventHandler premEventHandler = PremEventFactory.getInvoke(PremEventEnum.COMMISSION_PREM.getCode());
//        PolicyPremResult settlementPolicyPrem = premEventHandler.queryPolicyPrem(PolicyPremInput.builder()
//                .policyNo(policyNo)
//                .settlementCompanyCode(config.getSettlementCompanyCode())
//                .batchCode(endorsementNo)
//                .year(bean.getRenewalYear())
//                .period(bean.getRenewalPeriod())
//                .build());
//        log.info("配置中返回一单一议保单{}/{}佣金费率信息：{}", policyNo, endorsementNo, settlementPolicyPrem);
//        if (Objects.nonNull(settlementPolicyPrem)) {
//            config.setYearRate(settlementPolicyPrem.getYearRate());
//        } else {
//            log.info("支出端--计算基础佣金(农保一单一议)-保单号={}", policyNo);
//            BigDecimal csRate = ReconcileBaseHelper.buildPolicyCostCommission(policyNo);
//            config.setYearRate(csRate.multiply(new BigDecimal("0.01")));
//        }
//    }

    /**
     * 基础佣金
     *
     * @param settlementInstitution
     * @param premium
     * @param rate
     * @param costType
     * @param bean
     */
    private void builderBasicCommission(String settlementInstitution,String settlementInstitutionName,  BigDecimal premium, BigDecimal rate, String configKey, CostTypeEnum costType, SettlementCostInfoEntity bean) {

        bean.setSettlementInstitution(settlementInstitution);
        bean.setSettlementInstitutionName(settlementInstitutionName);
        bean.setCostType(costType.getCode());
        //todo 活动编码保单中心暂时未返回
        //bean.setCostActivityCode();
        bean.setCostRate(rate);
        //默认不是联合展业,联合展业的时候，后面在设置佣金归属人时候再设置具体分佣比例
        bean.setCostDivideRate(BigDecimal.ONE);
        bean.setCostActualRate(rate);
        BigDecimal commissionAmount = BigDecimalUtils.mul(premium.doubleValue(), rate.doubleValue());
        bean.setCostAmount(commissionAmount);
        //8大事件基础佣金默认100%发放
        bean.setGrantRate(new BigDecimal("100"));
        bean.setGrantAmount(commissionAmount);
        //配置key
        bean.setCostConfigKey(configKey);
    }

    private void builderCostProductInfo(EpContractInfoVo policyInfo, Map<String, ProductBase> productMap, EpProductInfoVo product, SettlementCostInfoEntity bean, Integer renewalYear, Integer renewalPeriod) {
        // 协议险种信息 + 是否可对账 todo 是否关系是否为非小鲸险种....
        ProtocolInsuranceProductInfoOut insuranceProductInfo = ProtocolBaseHelper.queryProtocolInsuranceProductInfoByProductCode(product.getProductCode(),0);
        if (insuranceProductInfo != null) {
            // 协议产品信息
            bean.setProtocolProductCode(insuranceProductInfo.getInsuranceProductCode());
            bean.setProtocolProductName(insuranceProductInfo.getInsuranceProductName());
        }
        //险种分类信息
        if (productMap.containsKey(product.getProductCode())) {
            ProductBase productBase = productMap.get(product.getProductCode());
            bean.setProductGroup(productBase.getProductGroup());
            bean.setLevel2Code(productBase.getLevel2Code());
            bean.setLevel3Code(productBase.getLevel3Code());
            bean.setLongShortFlag(productBase.getLongShortFlag());
            bean.setProductType(productBase.getProductType());
            bean.setProductChannel(productBase.getProductChannel());
        } else {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + product.getProductCode()));
        }
        bean.setProductCode(product.getProductCode());
        bean.setProductName(product.getProductName());
        bean.setProductStatus(product.getProductStatus());
        //对于个险、团险、续保的保单不管状态，产品状态强转为生效状态，（因为没有快照，状态是实时状态）
        if(Objects.equals(bean.getInitialEventCode(),SettlementEventTypeEnum.RENEWAL_POLICY.getEventCode())
            || Objects.equals(bean.getInitialEventCode(),SettlementEventTypeEnum.PERSONAL_NEW_POLICY.getEventCode())
            || Objects.equals(bean.getInitialEventCode(),SettlementEventTypeEnum.GROUP_NEW_POLICY.getEventCode())){
            bean.setProductStatus(ProductStatusEnum.ACTIVE.getCode());
        }
        bean.setPlanCode(product.getPlanCode());
        bean.setPlanName(product.getPlanName());
        bean.setMainInsurance(product.getMainInsurance());
        bean.setAdditionalRisksType(product.getAdditionalRisksType());
        //险种的生效事件必须去ep_policy_product_insured_map表或者ep_policy_product_info表中生效时间
        //bean.setEffectiveDate(policyInfo.getContractExtendInfo().getEnforceTime());
        bean.setEffectiveDate(product.getEffectiveDate());

        bean.setEndDate(PolicySettlementUtils.getInsuredEndDate(bean.getEffectiveDate(), product.getInsuredPeriod(), product.getInsuredPeriodType()));
        bean.setRenewalYear(renewalYear);
        bean.setRenewalPeriod(renewalPeriod);
        bean.setCoverage(product.getCoverage());
        bean.setCoverageUnit(product.getCoverageUnit());
        bean.setCoverageUnitName(product.getCoverageUnitName());
        bean.setInsuredPeriodType(product.getInsuredPeriodType());
        bean.setInsuredPeriod(product.getInsuredPeriod());
        bean.setPeriodType(product.getPeriodType());
        bean.setPaymentPeriodType(product.getPaymentPeriodType());
        bean.setPaymentPeriod(product.getPaymentPeriod());
        bean.setDrawAge(product.getAnnDrawAge());

    }

    public Date getBasicSettlementTime(SettlementEventTypeEnum eventType, EpContractInfoVo policyInfo) {
        return new Date();
//        if(eventType == null){
//            return new Date();
//        }
//        switch (eventType){
//            case PERSONAL_NEW_POLICY:
//                GROUP_NEW_POLICY:
//                RENEWAL_POLICY:
//                return Objects.equals(policyInfo.getChannelInfo().getChannelCode(), Constant.ZHNX_CHANNEL_CODE)?policyInfo.getContractExtendInfo().getApprovedTime():policyInfo.getContractExtendInfo().getOrderTime();
//            case RENEWAL_TERM_POLICY:
//                //todo 做续期时需要根据续期缴费时间来处理
//                    return new Date();
//            case STANDARD_SURRENDER:
//                HESITATE_SURRENDER:
//                PROTOCOL_TERMINATION:
//                GROUP_ADD_OR_SUBTRACT:
//                    return new Date();
//            case TERMINATION_PRODUCT:
//                    return new Date();
//            default:return new Date();
//        }
    }


    /**
     * 个险新契约/续保 根据被保人明细记录生成支出端佣金明细
     *
     * @param productMap
     * @param policyInfo
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> builderSettlementCostInfoByInsuredList(BasicCostEventDto eventDto,
                                                                                 Map<String, ProductBase> productMap,
                                                                                 EpContractInfoVo policyInfo, List<EpInsuredInfoVo> insuredInfoVos) {

        if (CollectionUtils.isEmpty(insuredInfoVos)) {
            return Collections.EMPTY_LIST;
        }
        Integer insuranceType = getInsuranceType(eventDto.getEventType(), policyInfo.getSourcePolicyNo());
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        //
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();
        insuredInfoVos.stream().forEach(insured -> {
            List<EpPersonalProductInfoVo> personalProductLst = insured.getProductInfoList();
            if (CollectionUtils.isEmpty(personalProductLst)) {
                log.warn("支出端-根据被保人信息计算支出端佣金明细-保单号={},被保人没有险种明细：{}", eventDto.getEventBusinessCode(), insured.getInsuredName());
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-根据被保人信息计算支出端佣金明细-保单号={},被保人没有险种明细：{}", eventDto.getEventBusinessCode(), insured.getInsuredName())));
            }
            for (EpPersonalProductInfoVo product : personalProductLst) {
                //查配置
                PolicyProductPremInput input = buildBasicCommissionConfigParam(policyInfo, insuranceType, product, 1, 1);
                List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(policyNo, input);
                //遍历佣金配置
                for (PolicyProductPremResult config : configList) {
                    //初始化，生成科目信息
                    SettlementCostInfoEntity bean = initSettlementCostInfo(eventDto, policyInfo.getContractCode(), insuranceType);

                    //记账时间
                    bean.setSettlementTime(getBasicSettlementTime(eventDto.getEventType(), policyInfo));
                    bean.setSettlementDate(bean.getSettlementTime());
                    //设置保费
                    bean.setProductPremium(product.getPremium());
                    bean.setPremium(product.getPremium());
                    bean.setBusinessPremium(product.getPremium());

                    //佣金信息
                    calcBasicCommission(policyNo, null, product, config, bean);
                    //确认信息
                    bean.setConfirmStatus(0);
                    //被保人信息
                    setInsuredInfo(insured, bean);
                    //险种信息
                    builderCostProductInfo(policyInfo, productMap, product, bean, 1, 1);
                    //折算保费
                    builderDiscountPremium(policyNo,policyInfo.getPolicyProductType(),bean);


                    costInfoEntities.add(bean);
                }

            }

        });
        //设置佣金归属人信息并返回
        return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);
    }

    private void setInsuredInfo(EpInsuredInfoVo insured, SettlementCostInfoEntity bean) {
        bean.setInsuredCode(insured.getInsuredCode());
        bean.setInsuredBirthday(insured.getInsuredBirthday());
        bean.setInsuredGender(insured.getInsuredGender());
        bean.setInsuredIdCard(insured.getInsuredIdCard());
        bean.setInsuredMobile(insured.getInsuredMobile());
        bean.setInsuredName(insured.getInsuredName());
    }

    @Override
    public List<SettlementCostInfoEntity> builderRenewalSettlementCostInfoByInsuredList(BasicCostEventDto eventDto, Map<String, ProductBase> productMap, EpContractInfoVo policyInfo, List<EpInsuredInfoVo> insuredInfoVos) {
        if (CollectionUtils.isEmpty(insuredInfoVos)) {
            return Collections.EMPTY_LIST;
        }
        Integer insuranceType = getInsuranceType(eventDto.getEventType(), policyInfo.getSourcePolicyNo());
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        //
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();
        insuredInfoVos.stream().forEach(insured -> {
            List<EpPersonalProductInfoVo> personalProductLst = insured.getProductInfoList();
            if (CollectionUtils.isEmpty(personalProductLst)) {
                log.error("支出端-根据被保人信息计算支出端佣金明细-保单号={},被保人没有险种明细：{}", eventDto.getEventBusinessCode(), insured.getInsuredName());
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-根据被保人信息计算支出端佣金明细-保单号={},被保人没有险种明细：{}", eventDto.getEventBusinessCode(), insured.getInsuredName())));
            }
            for (EpPersonalProductInfoVo product : personalProductLst) {
                JSONObject eventProduct = getRenewalProductFromEventJob(eventDto, insured.getInsuredIdCard(), product.getProductCode());
                Integer policyPeriodYear = eventProduct.getInteger("policyPeriodYear");
                Integer period = eventProduct.getInteger("period");
                PolicyProductPremInput input = buildBasicCommissionConfigParam(policyInfo, insuranceType, product, policyPeriodYear, period);
                List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(policyNo, input);
                //遍历佣金配置
                for (PolicyProductPremResult config : configList) {
                    //初始化，生成科目信息
                    SettlementCostInfoEntity bean = initSettlementCostInfo(eventDto, policyInfo.getContractCode(), insuranceType);
                    //记账时间
                    bean.setSettlementTime(getBasicSettlementTime(eventDto.getEventType(), policyInfo));
                    bean.setSettlementDate(bean.getSettlementTime());
                    //设置保费
                    bean.setProductPremium(product.getPremium());
                    bean.setPremium(product.getPremium());

                    //佣金信息
                    calcBasicCommission(policyNo, null, product, config, bean);
                    //确认信息
                    bean.setConfirmStatus(0);
                    //被保人信息
                    bean.setInsuredCode(insured.getInsuredCode());
                    bean.setInsuredBirthday(insured.getInsuredBirthday());
                    bean.setInsuredGender(insured.getInsuredGender());
                    bean.setInsuredIdCard(insured.getInsuredIdCard());
                    bean.setInsuredMobile(insured.getInsuredMobile());
                    bean.setInsuredName(insured.getInsuredName());
                    //险种信息
                    builderCostProductInfo(policyInfo, productMap, product, bean, 1, 1);

                    costInfoEntities.add(bean);
                }

            }

        });
        //设置佣金归属人信息并返回
        return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);
    }


    /**
     * 团险新契约/增减员 根据被保人明细记录生成支出端佣金明细
     *
     * @param eventDto
     * @param productMap
     * @param policyInfo
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> builderGroupSettlementCostInfoByInsuredList(BasicCostEventDto eventDto,
                                                                                      Map<String, ProductBase> productMap,
                                                                                      EpContractInfoVo policyInfo, PolicyPreservationDetailDto preservationDetail, List<GroupPreservationInsuredVo> insuredInfoVos) {

        if (CollectionUtils.isEmpty(insuredInfoVos)) {
            return Collections.EMPTY_LIST;
        }
        Integer insuranceType = getInsuranceType(eventDto.getEventType(), policyInfo.getSourcePolicyNo());
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();
        insuredInfoVos.stream().forEach(insured -> {
            List<EpPersonalProductInfoVo> personalProductLst = insured.getProductInfoList();
            if (CollectionUtils.isEmpty(personalProductLst)) {
                log.warn("支出端-团险根据被保人信息计算支出端佣金明细-保单号={},被保人没有险种明细：{}", eventDto.getEventBusinessCode(), insured.getInsuredName());
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-团险根据被保人信息计算支出端佣金明细-保单号={},被保人没有险种明细：{}", eventDto.getEventBusinessCode(), insured.getInsuredName())));
            }

            for (EpPersonalProductInfoVo product : personalProductLst) {
                /******佣金配置查询*******/
                PolicyProductPremInput input = buildBasicCommissionConfigParam(policyInfo, insuranceType, product, 1, 1);
                //保全，则查询佣金配置时间则是被保人生效时间，减员需要取被减人当时的生效时间
                if (preservationDetail != null) {
                    input.setApprovedTime(insured.getInsuredEffectiveTime());
                }
                List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(policyNo, input);

                /******遍历佣金配置*******/
                for (PolicyProductPremResult config : configList) {
                    //初始化，生成科目信息
                    SettlementCostInfoEntity bean = initSettlementCostInfo(eventDto, policyInfo.getContractCode(), insuranceType);
                    //记账时间
                    bean.setSettlementTime(getBasicSettlementTime(eventDto.getEventType(), policyInfo));
                    bean.setSettlementDate(bean.getSettlementTime());
                    //设置险种保费和实际用于计算的保费
                    bean.setProductPremium(product.getPremium());
                    if (Objects.equals(insured.getOperationType(), EpPolicyDetailsReqOperationTypeEnum.NEW.getCode())
                            || Objects.equals(insured.getOperationType(), EpPolicyDetailsReqOperationTypeEnum.ADD.getCode())) {

                        bean.setPremium(product.getPremium());
                    } else if (Objects.equals(insured.getOperationType(), EpPolicyDetailsReqOperationTypeEnum.BATCH.getCode())) {
                        bean.setPremium(product.getSurrenderAmount().negate());
                    } else {
                        log.warn("支出端-根据被保人信息计算支出端佣金明细-保单号={},不支持此被保人明细操作类型：{}", eventDto.getEventBusinessCode(), insured.getOperationType());
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-根据被保人信息计算支出端佣金明细-保单号={},不支持此被保人明细操作类型：{}", eventDto.getEventBusinessCode(), insured.getOperationType())));
                    }
                    //设置保费,增员设置增员费用，减员设置退保金额
//                    if(Objects.equals(insured.getOperationType(),EpPolicyDetailsReqOperationTypeEnum.ADD.getCode())
//                    || Objects.equals(insured.getOperationType(),EpPolicyDetailsReqOperationTypeEnum.NEW.getCode())) {
//                        bean.setPremium(product.getPremium());
//                    }else if(Objects.equals(insured.getOperationType(),EpPolicyDetailsReqOperationTypeEnum.BATCH.getCode())){
//                        bean.setPremium(product.getSurrenderAmount().negate());
//                    }else{
//                        log.warn("支出端-根据被保人信息计算支出端佣金明细-保单号={},不支持此被保人明细操作类型：{}",eventJob.getEventBusinessCode(),insured.getOperationType());
//                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-根据被保人信息计算支出端佣金明细-保单号={},不支持此被保人明细操作类型：{}",eventJob.getEventBusinessCode(),insured.getOperationType())));
//                    }
                    //佣金归属人信息

//                    if(Objects.equals(insured.getOperationType(),EpPolicyDetailsReqOperationTypeEnum.ADD.getCode())
//                            || Objects.equals(insured.getOperationType(),EpPolicyDetailsReqOperationTypeEnum.NEW.getCode())) {

                    settlementCostOwnerService.builderGroupPolicyAddCostOwnerInfoByInsured(insured.getChannelCode(), insured, bean);
//                    }else{
//
//                    }
                    //佣金信息
                    calcBasicCommission(policyNo, insured.getAddEndorsementNo(), product, config, bean);
                    //确认信息
                    bean.setConfirmStatus(0);
                    //被保人信息
                    bean.setInsuredCode(insured.getInsuredCode());
                    bean.setInsuredBirthday(insured.getInsuredBirthday());
                    bean.setInsuredGender(insured.getInsuredGender());
                    bean.setInsuredIdCard(insured.getInsuredIdCard());
                    bean.setInsuredMobile(insured.getInsuredMobile());
                    bean.setInsuredName(insured.getInsuredName());
                    //险种信息
                    builderCostProductInfo(policyInfo, productMap, product, bean, 1, 1);
                    //保全信息
                    if (Objects.nonNull(preservationDetail)) {
                        builderPreservationInfo(preservationDetail, null, bean);
                    }

                    costInfoEntities.add(bean);
                }

            }

        });
//        //按增员、减员进行分组
//        Map<String,List<EpInsuredInfoVo>> insuredMap = LambdaUtils.groupBy(insuredInfoVos, EpInsuredInfoVo::getOperationType);
//        //增员佣金计算
//        List<EpInsuredInfoVo> addInsuredLst = insuredMap.get(EpPolicyDetailsReqOperationTypeEnum.ADD.getCode());
//        if(CollectionUtils.isNotEmpty(addInsuredLst)){
//            costInfoEntities.addAll(builderSettlementCostInfoByInsuredList(eventJob,subjectEnum,eventType,productMap,policyInfo,preservationDetail,addInsuredLst));
//        }
//        //减员佣金计算
//        List<EpInsuredInfoVo> subInsuredLst = insuredMap.get(EpPolicyDetailsReqOperationTypeEnum.BATCH.getCode());
//        if(CollectionUtils.isNotEmpty(subInsuredLst)){
//
//        }


        return costInfoEntities;
    }


    /**
     * 个险新契约/续保 根据险种列表生成佣金记录(车险)
     *
     * @param eventDto
     * @param productMap
     * @param policyInfo
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> builderSettlementCostInfoByProductList(BasicCostEventDto eventDto,
                                                                                 Map<String, ProductBase> productMap,
                                                                                 EpContractInfoVo policyInfo
    ) {
        List<EpProductInfoVo> productInfoVos = policyInfo.getProductInfoList();
        if (CollectionUtils.isEmpty(productInfoVos)) {
            return Collections.EMPTY_LIST;
        }
        Integer insuranceType = getInsuranceType(eventDto.getEventType(), policyInfo.getSourcePolicyNo());
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();
        productInfoVos.stream().forEach(product -> {
            //查配置
            PolicyProductPremInput input = buildBasicCommissionConfigParam(policyInfo, insuranceType, product, 1, 1);
            List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(policyNo, input);
            //遍历佣金配置
            for (PolicyProductPremResult config : configList) {
                //初始化，生成科目信息
                SettlementCostInfoEntity bean = initSettlementCostInfo(eventDto, policyInfo.getContractCode(), insuranceType);
                //记账时间处理
                bean.setSettlementTime(getBasicSettlementTime(eventDto.getEventType(), policyInfo));
                bean.setSettlementDate(bean.getSettlementTime());
                //设置险种保费和实际用于计算的保费
                bean.setProductPremium(product.getPremium());
                bean.setPremium(product.getPremium());
                //佣金信息
                calcBasicCommission(policyNo, null, product, config, bean);
                //初始化确认信息
                bean.setConfirmStatus(0);
                //险种信息
                builderCostProductInfo(policyInfo, productMap, product, bean, 1, 1);

                costInfoEntities.add(bean);
            }

        });
        //设置佣金归属人信息并返回
        return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);
    }

    /**
     * 获取保单类型：1续投、0新保
     *
     * @param eventType
     * @return
     */
    public Integer getInsuranceType(SettlementEventTypeEnum eventType, String sourcePolicyNo) {
        if (LIST_RENEWAL_INSURANCE.contains(eventType.getEventCode())) {
            return 1;
        }
        if (StringUtils.isNotBlank(sourcePolicyNo)) {
            return 1;
        }
        return 0;
    }

    private void builderPreservationInfo(PolicyPreservationDetailDto preservationDetail, BigDecimal surrenderPremium, SettlementCostInfoEntity bean) {
        bean.setPreservationCode(preservationDetail.getPreservationCode());
        bean.setEndorsementNo(preservationDetail.getEndorsementNo());

        bean.setPreservationType(preservationDetail.getPreservationType());
        bean.setPreservationProject(preservationDetail.getPreservationProject());
        bean.setPreservationPeriod(preservationDetail.getRenewalTermPeriod());
        bean.setPreservationEffectTime(preservationDetail.getPreservationEffectTime());
        if (PreservationProjectEnum.isSurrender(preservationDetail.getPreservationProject())) {
            bean.setSurrenderTime(preservationDetail.getPreservationEffectTime());
            bean.setSurrenderAmount(surrenderPremium);
            bean.setHesitateSurrender(Objects.equals(preservationDetail.getPreservationProject(), PreservationProjectEnum.HESITATION_SURRENDER.getCode()) ? 1 : 0);
        }
    }


    /**
     * 团险新契约、增减员 根据险种明细生成支出明细（没有被保人明细的团险）
     *
     * @param eventDto
     * @param productMap
     * @param policyInfo
     * @param preservationDetail
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> builderGroupSettlementCostInfoByProductList(BasicCostEventDto eventDto,
                                                                                      Map<String, ProductBase> productMap,
                                                                                      EpContractInfoVo policyInfo,
                                                                                      PolicyPreservationDetailDto preservationDetail) {
        List<EpProductInfoVo> productInfoVos = policyInfo.getProductInfoList();
        if (CollectionUtils.isEmpty(productInfoVos)) {
            return Collections.EMPTY_LIST;
        }
        Integer insuranceType = getInsuranceType(eventDto.getEventType(), policyInfo.getSourcePolicyNo());
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();
        BigDecimal dynamicPremium = BigDecimal.ZERO;
        BigDecimal totalPremium = productInfoVos.stream().map(EpProductInfoVo::getPremium).reduce(BigDecimal.ZERO, BigDecimal::add);


        //按先主险，后附加险的顺序排序，然后进行佣金计算
        productInfoVos = productInfoVos.stream().sorted(Comparator.comparing(EpProductInfoVo::getMainInsurance).reversed()).collect(Collectors.toList());
        int size = productInfoVos.size();
        for (int i = 0; i < size; i++) {

            EpProductInfoVo product = productInfoVos.get(i);
            //按险种维度分摊团险保费(保全新单保费/增减员保费)
            BigDecimal premium;
            //保全为空则为新保单

            if (preservationDetail != null) {
                if (preservationDetail.getSurrenderCash().compareTo(dynamicPremium) == 0) {
                    break;
                }
                premium = preservationDetail.getSurrenderCash().multiply(product.getPremium()).divide(totalPremium, 2, BigDecimal.ROUND_HALF_UP);
                //preservationDetail.getSurrenderCash()存在为负数，比较大小用绝对值比较
                if (dynamicPremium.abs().add(premium.abs()).compareTo(preservationDetail.getSurrenderCash().abs()) > 0 || i == size - 1) {
                    premium = preservationDetail.getSurrenderCash().subtract(dynamicPremium);
                }
                dynamicPremium = dynamicPremium.add(premium);
            } else {
                premium = product.getPremium();
            }


            //查配置输入参数
            PolicyProductPremInput input = buildBasicCommissionConfigParam(policyInfo, insuranceType, product, 1, 1);
            input.setApprovedTime(product.getEffectiveDate());
            List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(policyNo, input);

            //遍历佣金配置
            for (PolicyProductPremResult config : configList) {
                //初始化，生成科目信息
                SettlementCostInfoEntity bean = initSettlementCostInfo(eventDto, policyInfo.getContractCode(), insuranceType);
                //记账时间处理
                bean.setSettlementTime(getBasicSettlementTime(eventDto.getEventType(), policyInfo));
                bean.setSettlementDate(bean.getSettlementTime());
                //设置险种保费和实际用于计算的保费
                bean.setProductPremium(product.getPremium());
                bean.setPremium(premium);
                //佣金信息
                calcBasicCommission(policyNo, preservationDetail != null ? preservationDetail.getEndorsementNo() : null, product, config, bean);
                //初始化确认信息
                bean.setConfirmStatus(0);
                //险种信息
                builderCostProductInfo(policyInfo, productMap, product, bean, 1, 1);
                //保全信息
                if (preservationDetail != null) {
                    builderPreservationInfo(preservationDetail, null, bean);
                }
                costInfoEntities.add(bean);
            }

        }

        //设置佣金归属人信息并返回
        if (preservationDetail != null) {
            return settlementCostOwnerService.builderPreservationCostOwnerInfo(preservationDetail, costInfoEntities);
        } else {
            return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);
        }
    }

    /**
     * 附加险解约
     *
     * @param eventDto
     * @param productMap
     * @param policyInfo
     * @param preservationDetail
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> builderPolicyTerminationProductCostInfo(BasicCostEventDto eventDto,
                                                                                  Map<String, ProductBase> productMap,
                                                                                  EpContractInfoVo policyInfo,
                                                                                  PolicyPreservationDetailDto preservationDetail) {
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();
        List<ProductTerminationInfoDto> productTerminationList = preservationDetail.getProductTerminationList();
        PolicyProductTypeEnum policyProductTypeEnum = PolicyProductTypeEnum.getProdTypeEnum(policyInfo.getPolicyProductType());
        //List<String> productCodes = productTerminationList.stream().map(ProductTerminationInfoDto::getProductCode).collect(Collectors.toList());
        //List<SettlementCostInfoEntity> existsCostInfos = settlementCostInfoService.listUnCorrectionPolicyCostInfoByProductCodes(policyInfo.getContractCode(),productCodes);
        Integer insuranceType = getInsuranceType(eventDto.getEventType(), policyInfo.getSourcePolicyNo());
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        Boolean isWhaleLongPolicy = isWhaleLongPolicy(policyInfo, productMap);
        switch (policyProductTypeEnum) {
            case PERSONAL:
            case PROPERTY: {
                for (int i = 0; i < productTerminationList.size(); i++) {
                    ProductTerminationInfoDto dto = productTerminationList.get(i);
                    List<EpInsuredInfoVo> insureds = policyInfo.getInsuredInfoList();
                    List<EpInsuredInfoVo> list = Lists.newArrayList();
                    BigDecimal totalPremium = BigDecimal.ZERO;
                    BigDecimal dynamicPremium = BigDecimal.ZERO;
                    //获取含有附加险的被保人信息和保费之和
                    for (EpInsuredInfoVo insured : insureds) {
                        Optional<EpPersonalProductInfoVo> opt = insured.getProductInfoList().stream().filter(p -> Objects.equals(p.getProductCode(), dto.getProductCode())).findFirst();
                        if (opt.isPresent()) {
                            list.add(insured);
                            totalPremium = totalPremium.add(opt.get().getPremium());
                        }
                    }
                    int size = list.size();
                    for (int j = 0; j < size; j++) {
                        EpInsuredInfoVo insured = list.get(j);
                        EpPersonalProductInfoVo product = list.get(j).getProductInfoList().stream().filter(p -> Objects.equals(p.getProductCode(), dto.getProductCode())).findFirst().get();
                        //查配置输入参数
                        PolicyProductPremInput input = buildBasicCommissionConfigParam(policyInfo, insuranceType, product, preservationDetail.getRenewalTermPeriod(), 1);
                        List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(policyNo, input);

                        //按险种维度分摊团险保费(保全新单保费/增减员保费)
                        BigDecimal surrenderPremium = dto.getSurrenderCash().multiply(product.getPremium()).divide(totalPremium, 2, BigDecimal.ROUND_HALF_UP);
                        if (dynamicPremium.abs().add(surrenderPremium.abs()).compareTo(dto.getSurrenderCash().abs()) > 0 || j == size - 1) {
                            surrenderPremium = dto.getSurrenderCash().subtract(dynamicPremium);
                        }
                        dynamicPremium = dynamicPremium.add(surrenderPremium);

                        //遍历佣金配置
                        for (PolicyProductPremResult config : configList) {
                            //初始化，生成科目信息
                            SettlementCostInfoEntity bean = initSettlementCostInfo(eventDto, policyInfo.getContractCode(), insuranceType);
                            //记账时间处理
                            bean.setSettlementTime(getBasicSettlementTime(eventDto.getEventType(), policyInfo));
                            bean.setSettlementDate(bean.getSettlementTime());
                            //设置险种保费和实际用于计算的保费
                            bean.setProductPremium(product.getPremium());
                            //非中和农信长险附加险解约按保费0来计算
                            if (isWhaleLongPolicy) {
                                bean.setPremium(BigDecimal.ZERO);
                            } else {
                                bean.setPremium(surrenderPremium.abs().negate());
                            }
                            //佣金信息
                            calcBasicCommission(policyNo, null, product, config, bean);
                            //初始化确认信息
                            bean.setConfirmStatus(0);
                            //被保人信息
                            setInsuredInfo(insured, bean);
                            //险种信息
                            builderCostProductInfo(policyInfo, productMap, product, bean, preservationDetail.getRenewalTermPeriod(), 1);
                            //保全信息
                            builderPreservationInfo(preservationDetail, surrenderPremium, bean);
                            costInfoEntities.add(bean);
                        }
                    }
                }
                break;
            }
            case VEHICLE: {
                productTerminationList.stream().forEach(p -> {
                    Optional<EpProductInfoVo> opt = policyInfo.getProductInfoList().stream().filter(o -> Objects.equals(o.getProductCode(), p.getProductCode())).findFirst();
                    EpProductInfoVo product = opt.get();
                    //查配置输入参数
                    PolicyProductPremInput input = buildBasicCommissionConfigParam(policyInfo, insuranceType, product, preservationDetail.getRenewalTermPeriod(), 1);
                    List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(policyNo, input);
                    //遍历佣金配置
                    for (PolicyProductPremResult config : configList) {
                        //初始化，生成科目信息
                        SettlementCostInfoEntity bean = initSettlementCostInfo(eventDto, policyInfo.getContractCode(), insuranceType);
                        //记账时间处理
                        bean.setSettlementTime(getBasicSettlementTime(eventDto.getEventType(), policyInfo));
                        bean.setSettlementDate(bean.getSettlementTime());
                        //设置险种保费和实际用于计算的保费
                        bean.setProductPremium(product.getPremium());
                        bean.setPremium(p.getSurrenderCash().abs().negate());
                        //佣金信息
                        calcBasicCommission(policyNo, null, product, config, bean);
                        //初始化确认信息
                        bean.setConfirmStatus(0);
                        //险种信息
                        builderCostProductInfo(policyInfo, productMap, product, bean, preservationDetail.getRenewalTermPeriod(), 1);
                        //保全信息
                        builderPreservationInfo(preservationDetail, p.getSurrenderCash(), bean);
                        costInfoEntities.add(bean);
                    }
                });
                break;
            }
            case GROUP: {
                log.info("团险保单不存在协议解约，不处理");
                break;
            }
            default: {
                log.warn("保单类型暂不处理.");
            }
        }
        //设置佣金归属人信息并返回
        return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);
    }

    /**
     * 犹豫期退保
     *
     * @param eventDto
     * @param productMap
     * @param policyInfo
     * @param preservationDetail
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> builderPolicyHesitateSurrenderCostInfo(BasicCostEventDto eventDto,
                                                                                 Map<String, ProductBase> productMap,
                                                                                 EpContractInfoVo policyInfo,
                                                                                 PolicyPreservationDetailDto preservationDetail) {
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();
        EpPreserveSurrenderDetailDto detail = preservationDetail.getSurrenderDetail();
        PolicyProductTypeEnum policyProductTypeEnum = PolicyProductTypeEnum.getProdTypeEnum(policyInfo.getPolicyProductType());
        Integer insuranceType = getInsuranceType(eventDto.getEventType(), policyInfo.getSourcePolicyNo());
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        switch (policyProductTypeEnum) {
            case PERSONAL:
            case PROPERTY: {
                //BigDecimal totalPremium = surrenderDetails.stream().map(EpPreserveSurrenderDetailVo::getPremium).reduce(BigDecimal.ZERO, BigDecimal::add);
                //boolean haveSurrenderPremium = surrenderDetails.get(0).getSurrenderPremium()==null?true:false;
                //BigDecimal dynamicPremium = BigDecimal.ZERO;
                List<EpPreserveSurrenderInsuredDto> surrenderDetails = detail.getInsuredList();
                for (int i = 0; i < surrenderDetails.size(); i++) {
                    EpPreserveSurrenderInsuredDto dto = surrenderDetails.get(i);
                    Optional<EpInsuredInfoVo> opt = policyInfo.getInsuredInfoList().stream().filter(o -> Objects.equals(o.getInsuredCode(), dto.getInsuredCode())).findFirst();
                    if (!opt.isPresent()) {
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-犹豫期退保人员明细明细在保单详情明细中不存在-保单号={},被保人：{}", eventDto.getEventBusinessCode(), dto.getInsuredCode())));
                    }
                    List<EpPreserveProductDto> surrenderProductDtos = dto.getInsuredProductList();
                    for (int j = 0; j < surrenderProductDtos.size(); j++) {
                        EpPreserveProductDto productDto = surrenderProductDtos.get(j);
                        Optional<EpPersonalProductInfoVo> productOpt = opt.get().getProductInfoList().stream().filter(p -> Objects.equals(p.getProductCode(), productDto.getProductCode())).findFirst();
                        EpProductInfoVo product = productOpt.get();
                        EpInsuredInfoVo insured = opt.get();
                        PolicyProductPremInput input = buildBasicCommissionConfigParam(policyInfo, insuranceType, product, preservationDetail.getRenewalTermPeriod(), 1);
                        List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(policyNo, input);
                        for (PolicyProductPremResult config : configList) {
                            //初始化，生成科目信息
                            SettlementCostInfoEntity bean = initSettlementCostInfo(eventDto, policyInfo.getContractCode(), insuranceType);
                            //记账时间处理
                            bean.setSettlementTime(getBasicSettlementTime(eventDto.getEventType(), policyInfo));
                            bean.setSettlementDate(bean.getSettlementTime());
                            //设置险种保费和实际用于计算的保费
                            bean.setProductPremium(product.getPremium());
                            bean.setPremium(product.getPremium().negate());
                            //佣金信息
                            calcBasicCommission(policyNo, null, product, config, bean);
                            //初始化确认信息
                            bean.setConfirmStatus(0);
                            //被保人信息
                            setInsuredInfo(insured, bean);

                            //险种信息
                            builderCostProductInfo(policyInfo, productMap, product, bean, preservationDetail.getRenewalTermPeriod(), 1);
                            //保全信息
                            builderPreservationInfo(preservationDetail, product.getPremium().negate(), bean);
                            costInfoEntities.add(bean);
                        }
                    }
                }
                break;
            }
            case VEHICLE: {
                List<EpPreserveSurrenderProductDto> surrenderDetails = detail.getProductList();
                for (int i = 0; i < surrenderDetails.size(); i++) {
                    EpPreserveSurrenderProductDto dto = surrenderDetails.get(i);
                    Optional<EpProductInfoVo> opt = policyInfo.getProductInfoList().stream().filter(o -> Objects.equals(o.getProductCode(), dto.getProductCode())).findFirst();
                    if (!opt.isPresent()) {
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-犹豫期退保险种明细在保单详情明细中不存在-保单号={},险种编码：{}", eventDto.getEventBusinessCode(), dto.getProductCode())));
                    }

                    EpProductInfoVo product = opt.get();
                    PolicyProductPremInput input = buildBasicCommissionConfigParam(policyInfo, insuranceType, product, preservationDetail.getRenewalTermPeriod(), 1);
                    List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(policyNo, input);

                    for (PolicyProductPremResult config : configList) {
                        //初始化，生成科目信息
                        SettlementCostInfoEntity bean = initSettlementCostInfo(eventDto, policyInfo.getContractCode(), insuranceType);
                        //记账时间处理
                        bean.setSettlementTime(getBasicSettlementTime(eventDto.getEventType(), policyInfo));
                        bean.setSettlementDate(bean.getSettlementTime());
                        //设置险种保费和实际用于计算的保费
                        bean.setProductPremium(product.getPremium());
                        bean.setPremium(product.getPremium().negate());
                        //佣金信息
                        calcBasicCommission(policyNo, null, product, config, bean);
                        //初始化确认信息
                        bean.setConfirmStatus(0);
                        //险种信息
                        builderCostProductInfo(policyInfo, productMap, product, bean, preservationDetail.getRenewalTermPeriod(), 1);
                        //保全信息
                        builderPreservationInfo(preservationDetail, product.getPremium().negate(), bean);
                        costInfoEntities.add(bean);
                    }
                }
            }
            case GROUP: {
                log.warn("团险没有犹豫期退保.");
                break;
            }
            default: {
                log.warn("保单类型暂不处理.");
            }
        }
        //设置佣金归属人信息并返回
        return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);
    }


    /**
     * 标准退保
     *
     * @param eventDto
     * @param productMap
     * @param policyInfo
     * @param preservationDetail
     * @return
     */
    @Override
    public List<SettlementCostInfoEntity> builderStandardSurrenderCostInfo(BasicCostEventDto eventDto,
                                                                           Map<String, ProductBase> productMap,
                                                                           EpContractInfoVo policyInfo,
                                                                           PolicyPreservationDetailDto preservationDetail) {
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();
        //List<EpPreserveSurrenderDetailVo> surrenderDetails = preservationDetail.getSurrenderDetail();
        PolicyProductTypeEnum policyProductTypeEnum = PolicyProductTypeEnum.getProdTypeEnum(policyInfo.getPolicyProductType());
        Integer insuranceType = getInsuranceType(eventDto.getEventType(), policyInfo.getSourcePolicyNo());
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();

        //BigDecimal totalPremium = preservationDetail.getSurrenderDetail().stream().map(EpPreserveSurrenderDetailVo::getPremium).reduce(BigDecimal.ZERO, BigDecimal::add);
        //boolean haveDetailSurrenderPremium = preservationDetail.getSurrenderDetail().get(0).getSurrenderPremium()==null?false:true;
        //BigDecimal dynamicPremium = BigDecimal.ZERO;
        //是否长险
        Integer longShortFlag = policyInfo.getContractBaseInfo().getLongShortFlag();
        BaseSurrenderDto param = BaseSurrenderDto.builder()
                .eventDto(eventDto)
                .productMap(productMap)
                .policyInfo(policyInfo)
                .preservationDetail(preservationDetail)

                .insuranceType(insuranceType)
                .policyNo(policyNo)
                //.totalPremium(totalPremium)
                //.haveDetailSurrenderPremium(haveDetailSurrenderPremium)
                //.dynamicPremium(dynamicPremium)
                .longShortFlag(longShortFlag)
                .build();


        switch (policyProductTypeEnum) {
            case PERSONAL:
            case PROPERTY: {

                if (longShortFlag == 1) {

                    builderLongPolicySurrenderCostInfo(eventDto,productMap,policyInfo,preservationDetail,costInfoEntities);
                    return costInfoEntities;
                }else{
                    builderStandardSurrenderByInsuredInfo(param, costInfoEntities);
                    return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);
                }

            }
            case VEHICLE: {
                builderStandardSurrenderByProductInfo(param,costInfoEntities);
                return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);
            }
            case GROUP: {
                if(Objects.equals(param.getPolicyInfo().getChannelInfo().getChannelCode(),ZHNX_CHANNEL_CODE)){
                    builderGroupStandardSurrenderByInsuredInfo(param,costInfoEntities);
                    return costInfoEntities;
                }else{
                    builderStandardSurrenderByProductInfo(param,costInfoEntities);
                    return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);
                }

            }
            default: {
                log.warn("保单类型暂不处理.");
            }
        }
        return costInfoEntities;
    }

    private void builderGroupStandardSurrenderByInsuredInfo(BaseSurrenderDto param, List<SettlementCostInfoEntity> costInfoEntities) {
        List<EpPreserveSurrenderInsuredDto> surrenderDetails = param.getPreservationDetail().getSurrenderDetail().getInsuredList();
        boolean haveSurrenderPremium = surrenderDetails.get(0).getInsuredSurrenderPremium() == null ? false : true;
        //中和农信渠道，如果没有被保人退保保费则抛出异常
        if (Objects.equals(param.getPolicyInfo().getChannelInfo().getChannelCode(), ZHNX_CHANNEL_CODE) && !haveSurrenderPremium) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-团险整单退保被保人退保明细金额不存在-保单号={}",param.getEventDto().getEventBusinessCode())));
        }

        for (int i = 0; i < surrenderDetails.size(); i++) {
            EpPreserveSurrenderInsuredDto dto = surrenderDetails.get(i);
            List<EpPreserveProductDto> surrenderProductDtos = dto.getInsuredProductList();

            BigDecimal dynamicPremium = BigDecimal.ZERO;
            BigDecimal totalPremium = surrenderProductDtos.stream().map(EpPreserveProductDto::getPremium).reduce(BigDecimal.ZERO, BigDecimal::add);

            for (int j = 0; j < surrenderProductDtos.size(); j++) {
                EpPreserveProductDto productDto = surrenderProductDtos.get(j);


                EpProductInfoVo product = new EpProductInfoVo();
                BeanUtils.copyProperties(productDto, product);
                PolicyProductPremInput input = buildBasicCommissionConfigParam(param.getPolicyInfo(), param.getInsuranceType(), product, param.getPreservationDetail().getRenewalTermPeriod(), 1);
                List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(param.getPolicyNo(), input);

                //计算退保保费
                BigDecimal premium = dto.getInsuredSurrenderPremium().multiply(productDto.getPremium()).divide(totalPremium, 2, BigDecimal.ROUND_HALF_UP);
                if (dynamicPremium.abs().add(premium.abs()).compareTo(dto.getInsuredSurrenderPremium().abs()) > 0 || j == surrenderProductDtos.size() - 1) {
                    premium = dto.getInsuredSurrenderPremium().subtract(dynamicPremium);
                }
                dynamicPremium = dynamicPremium.add(premium);


                for (PolicyProductPremResult config : configList) {
                    //初始化，生成科目信息
                    SettlementCostInfoEntity bean = initSettlementCostInfo(param.getEventDto(),param.getPolicyInfo().getContractCode(), param.getInsuranceType());
                    //记账时间处理
                    bean.setSettlementTime(getBasicSettlementTime(param.getEventType(), param.getPolicyInfo()));
                    bean.setSettlementDate(bean.getSettlementTime());
                    //设置保费,退保保费为在结算表里为负数、和实际用于计算的保费
                    bean.setProductPremium(product.getPremium());
                    bean.setPremium(premium.abs().negate());
                    //佣金信息
                    calcBasicCommission(param.getPolicyNo(), dto.getAddEndorsementNo(), product, config, bean);
                    //初始化确认信息
                    bean.setConfirmStatus(0);
                    //被保人信息
                    bean.setInsuredCode(dto.getInsuredCode());
                    bean.setInsuredBirthday(dto.getInsuredBirthday());
                    bean.setInsuredGender(dto.getInsuredGender());
                    bean.setInsuredIdCard(dto.getInsuredIdCard());
                    bean.setInsuredMobile(dto.getInsuredMobile());
                    bean.setInsuredName(dto.getInsuredName());
                    //险种信息
                    builderCostProductInfo(param.getPolicyInfo(), param.getProductMap(), product, bean, param.getPreservationDetail().getRenewalTermPeriod(), 1);
                    //保全信息
                    builderPreservationInfo(param.getPreservationDetail(), premium.abs().negate(), bean);

                    settlementCostOwnerService.builderGroupPolicySurrenderOwnerInfoByInsured(dto.getChannelCode(),dto,bean);

                    costInfoEntities.add(bean);
                }
            }
        }

    }


    private void builderStandardSurrenderByInsuredInfo(BaseSurrenderDto param, List<SettlementCostInfoEntity> costInfoEntities) {
        List<EpPreserveSurrenderInsuredDto> surrenderDetails = param.getPreservationDetail().getSurrenderDetail().getInsuredList();


        //是否有被保人退保明细金额
        boolean haveSurrenderPremium = surrenderDetails.get(0).getInsuredSurrenderPremium() == null ? false : true;
        BigDecimal totalPremium = BigDecimal.ZERO;
        BigDecimal dynamicPremium = BigDecimal.ZERO;
        if (!haveSurrenderPremium) {
            for (EpPreserveSurrenderInsuredDto dto : surrenderDetails) {
                totalPremium = totalPremium.add(dto.getInsuredProductList().stream().map(EpPreserveProductDto::getPremium).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
        }

        for (int i = 0; i < surrenderDetails.size(); i++) {
            EpPreserveSurrenderInsuredDto dto = surrenderDetails.get(i);
            Optional<EpInsuredInfoVo> opt = param.getPolicyInfo().getInsuredInfoList().stream().filter(o -> Objects.equals(o.getInsuredCode(), dto.getInsuredCode())).findFirst();
            if (!opt.isPresent()) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-标准退保人员明细明细在保单详情明细中不存在-保单号={},被保人：{}", param.getEventDto().getEventBusinessCode(), dto.getInsuredCode())));
            }
            EpInsuredInfoVo insured = opt.get();

            List<EpPreserveProductDto> surrenderProductDtos = dto.getInsuredProductList();
            if (haveSurrenderPremium) {
                dynamicPremium = BigDecimal.ZERO;
                totalPremium = surrenderProductDtos.stream().map(EpPreserveProductDto::getPremium).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            for (int j = 0; j < surrenderProductDtos.size(); j++) {
                EpPreserveProductDto productDto = surrenderProductDtos.get(j);

                Optional<EpPersonalProductInfoVo> productOpt = insured.getProductInfoList().stream().filter(p -> Objects.equals(p.getProductCode(), productDto.getProductCode())).findFirst();
                EpProductInfoVo product = productOpt.get();
                PolicyProductPremInput input = buildBasicCommissionConfigParam(param.getPolicyInfo(), param.getInsuranceType(), product, param.getPreservationDetail().getRenewalTermPeriod(), 1);
                List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(param.getPolicyNo(), input);

                //计算退保保费
                BigDecimal premium;
                if (haveSurrenderPremium) {
                    premium = dto.getInsuredSurrenderPremium().multiply(productDto.getPremium()).divide(totalPremium, 2, BigDecimal.ROUND_HALF_UP);
                    if (dynamicPremium.abs().add(premium.abs()).compareTo(dto.getInsuredSurrenderPremium().abs()) > 0 || j == surrenderProductDtos.size() - 1) {
                        premium = dto.getInsuredSurrenderPremium().subtract(dynamicPremium);
                    }
                    dynamicPremium = dynamicPremium.add(premium);
                } else {
                    premium = param.getPreservationDetail().getSurrenderCash().multiply(productDto.getPremium()).divide(totalPremium, 2, BigDecimal.ROUND_HALF_UP);
                    if (dynamicPremium.abs().add(premium.abs()).compareTo(param.getPreservationDetail().getSurrenderCash().abs()) > 0 || (i == surrenderDetails.size() - 1 && j == surrenderProductDtos.size() - 1)) {
                        premium = param.getPreservationDetail().getSurrenderCash().subtract(dynamicPremium);
                    }
                    dynamicPremium = dynamicPremium.add(premium);
                }

                for (PolicyProductPremResult config : configList) {
                    //初始化，生成科目信息
                    SettlementCostInfoEntity bean = initSettlementCostInfo(param.getEventDto(), param.getPolicyInfo().getContractCode(), param.getInsuranceType());
                    //记账时间处理
                    bean.setSettlementTime(getBasicSettlementTime(param.getEventType(), param.getPolicyInfo()));
                    bean.setSettlementDate(bean.getSettlementTime());
                    //设置保费,退保保费为在结算表里为负数、和实际用于计算的保费
                    bean.setProductPremium(product.getPremium());
                    bean.setPremium(premium.abs().negate());
                    //佣金信息
                    calcBasicCommission(param.getPolicyNo(), null, product, config, bean);
                    //初始化确认信息
                    bean.setConfirmStatus(0);
                    //被保人信息
                    setInsuredInfo(insured, bean);
                    //险种信息
                    builderCostProductInfo(param.getPolicyInfo(), param.getProductMap(), product, bean, param.getPreservationDetail().getRenewalTermPeriod(), 1);
                    //保全信息
                    builderPreservationInfo(param.getPreservationDetail(), premium.abs().negate(), bean);
                    costInfoEntities.add(bean);
                }
            }
//            Optional<EpInsuredInfoVo> opt = param.getPolicyInfo().getInsuredInfoList().stream().filter(o->Objects.equals(o.getInsuredCode(),dto.getInsuredCode())).findFirst();
//            if(!opt.isPresent()){
//                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-标准退保人员明细明细在保单详情明细中不存在-保单号={},被保人：{}",param.getEventJob().getEventBusinessCode(),dto.getInsuredCode())));
//            }
//            EpInsuredInfoVo insured = opt.get();
//            Optional<EpPersonalProductInfoVo> productOpt = insured.getProductInfoList().stream().filter(p->Objects.equals(p.getProductCode(),dto.getProductCode())).findFirst();
//            EpProductInfoVo product = productOpt.get();
//            CommissionBasicPremInfoInput input = buildBasicCommissionConfigParam(param.getPolicyInfo(),param.getInsuranceType(),product,param.getPreservationDetail().getRenewalTermPeriod() ,1);
//            List<CommissionBasicPremInfo> configList = getCostBasicCommissionConfig(param.getPolicyNo(),input);
//
//            //长险全额退,即退保保费为源保费
//            BigDecimal premium;
//            if(param.getLongShortFlag() == 1){
//                premium = dto.getPremium();
//            }else {
//                premium = dto.getSurrenderPremium();
//                if (!param.getHaveDetailSurrenderPremium()) {
//                    premium = param.getPreservationDetail().getSurrenderCash().multiply(dto.getPremium()).divide(param.getTotalPremium(), 2, BigDecimal.ROUND_HALF_UP);
//                    if (param.getDynamicPremium().abs().add(premium.abs()).compareTo(param.getPreservationDetail().getSurrenderCash().abs()) > 0 || i == surrenderDetails.size() - 1) {
//                        premium = param.getPreservationDetail().getSurrenderCash().subtract(param.getDynamicPremium());
//                    }
//                    param.setDynamicPremium(param.getDynamicPremium().add(premium));
//                }
//            }
//            for(CommissionBasicPremInfo config : configList){
//                //初始化，生成科目信息
//                SettlementCostInfoEntity bean = initSettlementCostInfo(param.getEventJob(),param.getSubjectEnum(),param.getEventType(),param.getPolicyInfo().getContractCode(),param.getInsuranceType());
//                //记账时间处理
//                bean.setSettlementTime(getBasicSettlementTime(param.getEventType(),param.getPolicyInfo()));
//                bean.setSettlementDate(bean.getSettlementTime());
//                //设置保费,退保保费为在结算表里为负数、和实际用于计算的保费
//                bean.setProductPremium(product.getPremium());
//                bean.setPremium(premium.abs().negate());
//                //佣金信息
//                calcBasicCommission(param.getPolicyNo(),null,product,config,bean);
//                //初始化确认信息
//                bean.setConfirmStatus(0);
//                //被保人信息
//                setInsuredInfo(insured,bean);
//                //险种信息
//                builderCostProductInfo(param.getPolicyInfo(),param.getProductMap(),product,bean,param.getPreservationDetail().getRenewalTermPeriod(),1);
//                //保全信息
//                builderPreservationInfo(param.getPreservationDetail(),product.getPremium().negate(),bean);
//                costInfoEntities.add(bean);
//            }
        }
    }

    private void builderStandardSurrenderByProductInfo(BaseSurrenderDto param, List<SettlementCostInfoEntity> costInfoEntities) {
        List<EpPreserveSurrenderProductDto> surrenderDetails = param.getPreservationDetail().getSurrenderDetail().getProductList();
        boolean haveSurrenderPremium = surrenderDetails.get(0).getSurrenderPremium() == null ? false : true;
        BigDecimal totalPremium = surrenderDetails.stream().map(EpPreserveSurrenderProductDto::getPremium).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal dynamicPremium = BigDecimal.ZERO;
        Boolean isWhaleLong = isWhaleLongPolicy(param.getPolicyInfo(), param.getProductMap());
        for (int i = 0; i < surrenderDetails.size(); i++) {
            EpPreserveSurrenderProductDto dto = surrenderDetails.get(i);
            Optional<EpProductInfoVo> opt = param.getPolicyInfo().getProductInfoList().stream().filter(o -> Objects.equals(o.getProductCode(), dto.getProductCode())).findFirst();
            if (!opt.isPresent()) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-标准退保险种明细在保单详情明细中不存在-保单号={},险种编码：{}", param.getEventDto().getEventBusinessCode(), dto.getProductCode())));
            }
            EpProductInfoVo product = opt.get();
            PolicyProductPremInput input = buildBasicCommissionConfigParam(param.getPolicyInfo(), param.getInsuranceType(), product, param.getPreservationDetail().getRenewalTermPeriod(), 1);
            List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(param.getPolicyNo(), input);

            //长险全额退,即退保保费为源保费
            BigDecimal surrenderPremium;
            if (param.getLongShortFlag() == 1) {
                surrenderPremium = dto.getPremium();
            } else {
                surrenderPremium = dto.getSurrenderPremium();
                if (!haveSurrenderPremium) {
                    surrenderPremium = param.getPreservationDetail().getSurrenderCash().multiply(dto.getPremium()).divide(totalPremium, 2, BigDecimal.ROUND_HALF_UP);
                    if (dynamicPremium.abs().add(surrenderPremium.abs()).compareTo(param.getPreservationDetail().getSurrenderCash().abs()) > 0 || i == surrenderDetails.size() - 1) {
                        surrenderPremium = param.getPreservationDetail().getSurrenderCash().subtract(dynamicPremium);
                    }
                    dynamicPremium = dynamicPremium.add(surrenderPremium);
                }
            }

            for (PolicyProductPremResult config : configList) {
                //初始化，生成科目信息
                SettlementCostInfoEntity bean = initSettlementCostInfo(param.getEventDto(),  param.getPolicyInfo().getContractCode(), param.getInsuranceType());

                //记账时间处理
                bean.setSettlementTime(getBasicSettlementTime(param.getEventType(), param.getPolicyInfo()));
                bean.setSettlementDate(bean.getSettlementTime());
                //设置保费,退保保费为在结算表里为负数、和实际用于计算的保费
                bean.setProductPremium(product.getPremium());
                if (isWhaleLong) {
                    bean.setPremium(BigDecimal.ZERO);
                } else {
                    bean.setPremium(surrenderPremium.abs().negate());
                }
                //佣金信息
                calcBasicCommission(param.getPolicyNo(), null, product, config, bean);
                //初始化确认信息
                bean.setConfirmStatus(0);
                //险种信息
                builderCostProductInfo(param.getPolicyInfo(), param.getProductMap(), product, bean, param.getPreservationDetail().getRenewalTermPeriod(), 1);
                //保全信息
                builderPreservationInfo(param.getPreservationDetail(), surrenderPremium, bean);
                costInfoEntities.add(bean);
            }
        }
    }


    /**
     * 协议解约
     *
     * @param eventDto
     * @param productMap
     * @param policyInfo
     * @param preservationDetail
     * @return
     */
    @Override
    @Deprecated
    public List<SettlementCostInfoEntity> builderProtocolTerminationCostInfo(BasicCostEventDto eventDto,
                                                                             Map<String, ProductBase> productMap,
                                                                             EpContractInfoVo policyInfo,
                                                                             PolicyPreservationDetailDto preservationDetail) {
        List<SettlementCostInfoEntity> costInfoEntities = Lists.newArrayList();
        EpPreserveSurrenderDetailDto detail = preservationDetail.getSurrenderDetail();
        PolicyProductTypeEnum policyProductTypeEnum = PolicyProductTypeEnum.getProdTypeEnum(policyInfo.getPolicyProductType());
        Integer insuranceType = getInsuranceType(eventDto.getEventType(), policyInfo.getSourcePolicyNo());
        String policyNo = policyInfo.getContractBaseInfo().getPolicyNo();
        //是否长险
        Integer longShortFlag = policyInfo.getContractBaseInfo().getLongShortFlag();
        switch (policyProductTypeEnum) {
            case PERSONAL:
            case PROPERTY: {
                if (longShortFlag == 1) {
                    builderLongPolicySurrenderCostInfo(eventDto, productMap, policyInfo, preservationDetail, costInfoEntities);
                    return costInfoEntities;
                } else {
                    List<EpPreserveSurrenderInsuredDto> surrenderDetails = detail.getInsuredList();
                    for (int i = 0; i < surrenderDetails.size(); i++) {
                        EpPreserveSurrenderInsuredDto dto = surrenderDetails.get(i);
                        Optional<EpInsuredInfoVo> opt = policyInfo.getInsuredInfoList().stream().filter(o -> Objects.equals(o.getInsuredCode(), dto.getInsuredCode())).findFirst();
                        if (!opt.isPresent()) {
                            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-协议解约人员明细明细在保单详情明细中不存在-保单号={},被保人：{}", eventDto.getEventBusinessCode(), dto.getInsuredCode())));
                        }
                        EpInsuredInfoVo insured = opt.get();
                        List<EpPreserveProductDto> surrenderProductDtos = dto.getInsuredProductList();
                        for (int j = 0; j < surrenderProductDtos.size(); j++) {
                            EpPreserveProductDto productDto = surrenderProductDtos.get(j);

                            Optional<EpPersonalProductInfoVo> productOpt = insured.getProductInfoList().stream().filter(p -> Objects.equals(p.getProductCode(), productDto.getProductCode())).findFirst();
                            EpProductInfoVo product = productOpt.get();
                            PolicyProductPremInput input = buildBasicCommissionConfigParam(policyInfo, insuranceType, product, preservationDetail.getRenewalTermPeriod(), 1);
                            List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(policyNo, input);
                            for (PolicyProductPremResult config : configList) {
                                //初始化，生成科目信息
                                SettlementCostInfoEntity bean = initSettlementCostInfo(eventDto, policyInfo.getContractCode(), insuranceType);

                                //记账时间处理
                                bean.setSettlementTime(getBasicSettlementTime(eventDto.getEventType(), policyInfo));
                                bean.setSettlementDate(bean.getSettlementTime());
                                //设置险种保费和实际用于计算的保费
                                bean.setProductPremium(product.getPremium());
                                bean.setPremium(productDto.getPremium().abs().negate());
                                //佣金信息
                                calcBasicCommission(policyNo, null, product, config, bean);
                                //初始化确认信息
                                bean.setConfirmStatus(0);
                                //被保人信息
                                setInsuredInfo(insured, bean);
                                //险种信息
                                builderCostProductInfo(policyInfo, productMap, product, bean, preservationDetail.getRenewalTermPeriod(), 1);
                                //保全信息
                                builderPreservationInfo(preservationDetail, productDto.getPremium(), bean);
                                costInfoEntities.add(bean);
                            }
                        }


                    }
                    return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);

                }
            }
            case VEHICLE: {
                List<EpPreserveSurrenderProductDto> surrenderDetails = detail.getProductList();
                for (int i = 0; i < surrenderDetails.size(); i++) {
                    EpPreserveSurrenderProductDto dto = surrenderDetails.get(i);
                    Optional<EpProductInfoVo> opt = policyInfo.getProductInfoList().stream().filter(o -> Objects.equals(o.getProductCode(), dto.getProductCode())).findFirst();
                    if (!opt.isPresent()) {
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-协议解约险种明细在保单详情明细中不存在-保单号={},险种编码：{}", eventDto.getEventBusinessCode(), dto.getProductCode())));
                    }
                    EpProductInfoVo product = opt.get();
                    PolicyProductPremInput input = buildBasicCommissionConfigParam(policyInfo, insuranceType, product, preservationDetail.getRenewalTermPeriod(), 1);
                    List<PolicyProductPremResult> configList = getCostBasicCommissionConfig(policyNo, input);
                    //长险全额退,即退保保费为源保费
                    BigDecimal premium = dto.getPremium();
                    for (PolicyProductPremResult config : configList) {
                        //初始化，生成科目信息
                        SettlementCostInfoEntity bean = initSettlementCostInfo(eventDto, policyInfo.getContractCode(), insuranceType);
                        //记账时间处理
                        bean.setSettlementTime(getBasicSettlementTime(eventDto.getEventType(), policyInfo));
                        bean.setSettlementDate(bean.getSettlementTime());
                        //设置险种保费和实际用于计算的保费
                        bean.setProductPremium(product.getPremium());
                        bean.setPremium(premium.abs().negate());
                        //佣金信息
                        calcBasicCommission(policyNo, null, product, config, bean);
                        //初始化确认信息
                        bean.setConfirmStatus(0);
                        //险种信息
                        builderCostProductInfo(policyInfo, productMap, product, bean, preservationDetail.getRenewalTermPeriod(), 1);
                        //保全信息
                        builderPreservationInfo(preservationDetail, premium, bean);
                        costInfoEntities.add(bean);
                    }
                }
                return settlementCostOwnerService.builderBasePolicyCostOwnerInfo(policyInfo, costInfoEntities);
            }

            case GROUP: {
                break;
            }
            default: {
                log.warn("保单类型暂不处理.");
            }
        }


        return costInfoEntities;
    }

    private String keyLongPolicySurrender(SettlementCostInfoEntity old) {
        return old.getContractCode() + old.getProductCode() + old.getInsuredCode() + old.getRenewalPeriod();
    }

    private void builderLongPolicySurrenderCostInfo(BasicCostEventDto eventDto,
                                                    Map<String, ProductBase> productMap,
                                                    EpContractInfoVo policyInfo,
                                                    PolicyPreservationDetailDto preservationDetail,
                                                    List<SettlementCostInfoEntity> costInfoEntities) {
        List<SettlementCostInfoEntity> oldList = settlementCostInfoService.listUnCorrectionPolicyCostInfoByContractCode(policyInfo.getContractCode());
        if (CollectionUtils.isEmpty(oldList)) {
            //csLongPolicyCommissionToSurrender();
        }
        List<SettlementCostInfoEntity> terminationProdList = oldList.stream().filter(m -> ProductStatusEnum.isSurrenderStatus(m.getProductStatus())).collect(Collectors.toList());
        //过滤所有险种编码、被保人编码、期数等于退保记录的险种编码、被保人编码、期数（增加期数是可能存在某期退了附加险，后面又新增了附加险）
        if (CollectionUtils.isNotEmpty(terminationProdList)) {
            List<String> keys = terminationProdList.stream().map((t) -> {
                return keyLongPolicySurrender(t);
            }).collect(Collectors.toList());
            oldList = oldList.stream().filter(o -> !keys.contains(keyLongPolicySurrender(o))).collect(Collectors.toList());
        }
        for (SettlementCostInfoEntity old : oldList) {
            costInfoEntities.add(builderLongSurrenderCostInfoByOldCost(eventDto, eventDto.getEventType(), policyInfo, old, preservationDetail));
        }
        //判断是否有首期新契约佣金，没有则需要去农保佣金记录表中查询
        Optional<SettlementCostInfoEntity> first = oldList.stream().filter(o -> Objects.equals(o.getSettlementEventCode(), PERSONAL_NEW_POLICY.getEventCode())).findFirst();
        if (!first.isPresent()) {
            //List<String> idNumbers = preservationDetail.getSurrenderDetail().stream().map(EpPreserveSurrenderDetailVo::getInsuredIdCard).distinct().collect(Collectors.toList());

        }

    }

    private void csLongPolicyCommissionToSurrender(EpContractInfoVo policyInfo, List<String> insuredIdNumbers) {
        List<CsVehicleCommissionDetailEntity> csList = ReconcileBaseHelper.listCsCommissionDetail(policyInfo.getContractBaseInfo().getPolicyNo(), insuredIdNumbers);

    }



    /**
     * 支出端-佣金信息保存(不适合修改保单号事件变更)
     *
     * @param eventJob
     * @param policy
     * @param costInfoList
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveCostCommissionRecord(SettlementEventJobEntity eventJob, SettlementCostPolicyInfoEntity policy, List<SettlementCostInfoEntity> costInfoList) {
        if (CollectionUtils.isEmpty(costInfoList)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-佣金信息保存-保单号={}, 佣金记录为空", eventJob.getEventBusinessCode())));
        }
        if (policy != null) {
            if (Objects.isNull(policy.getId())) {
                settlementCostPolicyInfoService.save(policy);
            }else{
                settlementCostPolicyInfoService.updateById(policy);
            }
            costInfoList.stream().forEach(c -> {
                c.setCostPolicyId(policy.getId());
            });
        }
        settlementCostInfoService.saveBatch(costInfoList);
    }

    /**
     * 生成源记录的对冲记录(冲正、长险退保用于生成对冲记录)
     *
     * @param eventJob
     * @param eventType
     * @param old
     * @param correctionUser
     * @param correctionRemark
     * @return
     */
    protected SettlementCostInfoEntity builderOffsetCostInfo(SettlementEventJobEntity eventJob,
                                                             SettlementEventTypeEnum eventType,
                                                             SettlementCostInfoEntity old,
                                                             String correctionUser,
                                                             String correctionRemark, Boolean isCorrection) {
        SettlementCostInfoEntity bean = new SettlementCostInfoEntity();
        BeanUtils.copyProperties(old, bean);
        bean.setId(null);
        bean.setCostCode(PolicySettlementUtils.createCodeLastNumber("CC"));
        //记账时间处理
        bean.setSettlementTime(new Date());
        bean.setSettlementDate(bean.getSettlementTime());
        //事件编号
        bean.setEventSourceCode(eventJob.getPushEventCode());
        //事件信息
        if (Objects.nonNull(eventType)) {
            bean.setSettlementEventCode(eventType.getEventCode());
            bean.setSettlementEventDesc(eventType.getEventDesc());
            setCommissionTypeByEventType(eventType, bean);
        }
        //科目信息与被冲正记录保持一直
        //bean.setSettlementSubjectCode(subjectEnum.getCode());
        //bean.setSettlementSubjectName(subjectEnum.getName());
        bean.setSettlementGenerateType(1);
        //是否冲正
        if (isCorrection) {
            bean.setSourceCostCode(old.getCostCode());
            bean.setCorrectionFlag(1);
        }
        if (StringUtil.isNotBlank(correctionUser)) {
            bean.setCorrectionUser(correctionUser);
        }

        if (Objects.equals(SYSTEM_CORRECTION_USER, correctionUser)) {
            bean.setCorrectionOpType(0);
        } else {
            bean.setCorrectionOpType(1);
        }
        if (StringUtil.isNotBlank(correctionRemark)) {
            bean.setCorrectionRemark(correctionRemark);
        }
        //清除确认信息
        bean.setConfirmStatus(0);
        bean.setConfirmUser(null);
        bean.setConfirmTime(null);
        bean.setConfirmGrantTime(null);
        bean.setCostSettlementCycle(null);
        bean.setDocumentCode(null);
        bean.setAutoCostCode(null);
        //冲正金额字段
        bean.setPremium(old.getPremium().negate());
        bean.setCostAmount(old.getCostAmount().negate());
        bean.setGrantAmount(old.getGrantAmount().negate());
        return bean;
    }

    protected SettlementCostInfoEntity builderLongSurrenderCostInfoByOldCost(BasicCostEventDto eventDto,
                                                                             SettlementEventTypeEnum eventType, EpContractInfoVo policyInfo,
                                                                             SettlementCostInfoEntity old, PolicyPreservationDetailDto preservationDetail) {
        SettlementCostInfoEntity bean = new SettlementCostInfoEntity();
        BeanUtils.copyProperties(old, bean);
        bean.setId(null);
        bean.setCostCode(PolicySettlementUtils.createCodeLastNumber("CC"));
        //记账时间处理
        bean.setSettlementTime(new Date());
        bean.setSettlementDate(bean.getSettlementTime());
        //事件编号
        bean.setEventSourceCode(eventDto.getPushEventCode());
        //事件信息
        if (Objects.nonNull(eventType)) {
            bean.setSettlementEventCode(eventType.getEventCode());
            bean.setSettlementEventDesc(eventType.getEventDesc());
            bean.setInitialEventCode(eventType.getEventCode());
            setCommissionTypeByEventType(eventType, bean);
        }

        bean.setSettlementGenerateType(1);

        //清除确认信息
        bean.setConfirmStatus(0);
        bean.setConfirmUser(null);
        bean.setConfirmTime(null);
        bean.setConfirmGrantTime(null);
        bean.setDocumentCode(null);
        bean.setCostSettlementCycle(null);
        bean.setAutoCostCode(null);
        //退保保费
        if (Objects.equals(preservationDetail.getSellChannelCode(), ZHNX_CHANNEL_CODE)) {
            //标准退保、当前期数大于1，回访成功的情况下就不扣钱
            if (Objects.equals(eventType.getEventCode(), STANDARD_SURRENDER.getEventCode())
                    && preservationDetail.getRenewalTermPeriod() > 1
                    && Objects.equals(policyInfo.getContractExtendInfo().getRevisitResult(), 1)) {
                bean.setPremium(BigDecimal.ZERO);
                bean.setCostAmount(BigDecimal.ZERO);
                bean.setGrantAmount(BigDecimal.ZERO);
            } else {
                bean.setPremium(old.getPremium().negate());
                bean.setCostAmount(old.getCostAmount().negate());
                bean.setGrantAmount(old.getGrantAmount().negate());
            }
        } else {
            if (LIST_CITY_ZERO_PREMIUM_EVENT.contains(eventType.getEventCode())) {
                bean.setPremium(BigDecimal.ZERO);
                bean.setCostAmount(BigDecimal.ZERO);
                bean.setGrantAmount(BigDecimal.ZERO);
            } else {
                bean.setPremium(old.getPremium().negate());
                bean.setCostAmount(old.getCostAmount().negate());
                bean.setGrantAmount(old.getGrantAmount().negate());
            }
        }

        return bean;
    }

    /**
     * 创建折标保费
     * @param policyNo
     * @param policyProductType
     * @param cost
     */
    public void builderDiscountPremium(String policyNo,String policyProductType,SettlementCostInfoEntity cost){
        PolicyProductTypeEnum policyProductTypeEnum = PolicyProductTypeEnum.getProdTypeEnum(policyProductType);

        BigDecimal discountPremium = ReconcileBaseHelper.calcDiscountPremium(policyNo, cost.getProductCode(), cost.getInsuredPolicyAge(),
                policyProductTypeEnum, cost.getLongShortFlag(), cost.getBusinessPremium(), cost.getPeriodType(), cost.getPaymentPeriodType(),
                cost.getPaymentPeriod());

        cost.setDiscountPremium(discountPremium);
    }



}
