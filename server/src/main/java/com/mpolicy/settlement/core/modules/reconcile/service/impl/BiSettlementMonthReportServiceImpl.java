package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.modules.reconcile.dao.BiSettlementMonthReportDao;
import com.mpolicy.settlement.core.modules.reconcile.entity.BiSettlementMonthReportEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.BiSettlementMonthReportService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BiSettlementMonthReportServiceImpl extends ServiceImpl<BiSettlementMonthReportDao, BiSettlementMonthReportEntity> implements BiSettlementMonthReportService{
    @Override
    public void saveList(List<BiSettlementMonthReportEntity> saveList) {
        baseMapper.insertBatchSomeColumn(saveList);
    }
}
