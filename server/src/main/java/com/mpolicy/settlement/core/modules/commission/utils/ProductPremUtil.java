package com.mpolicy.settlement.core.modules.commission.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.mpolicy.settlement.core.modules.commission.entity.CommissionBasicPolicyPremEntity;
import lombok.extern.slf4j.Slf4j;

import java.math.RoundingMode;

/**
 * <AUTHOR>
 */
@Slf4j
public class ProductPremUtil {



    /**
     * 基础佣金一单一议获取Key
     *
     * @param productPrem
     * @return
     */
    public static String getCommissionPolicyPremCode(CommissionBasicPolicyPremEntity productPrem) {
        String template = "保单号[{}]_批单号[{}]_险种编码[{}]_结算机构[{}]_保单年期[{}]_缴费期次[{}]_费率[{}]_车船税税率[{}]_车船税费率[{}]";
        String key = StrUtil.format(template, StrUtil.nullToDefault(productPrem.getPolicyNo(), ""),
            StrUtil.nullToDefault(productPrem.getBatchCode(), ""),
            StrUtil.nullToDefault(productPrem.getProductCode(), ""),
            StrUtil.nullToDefault(productPrem.getSettlementCompanyCode(), ""),
            productPrem.getYear() == null ? "" : productPrem.getYear(),
            productPrem.getPeriod() == null ? "" : productPrem.getPeriod(),
            productPrem.getCommissionRate() != null ? productPrem.getCommissionRate().setScale(4, RoundingMode.HALF_UP) : "",
            productPrem.getVehicleVesselTax() != null ? productPrem.getVehicleVesselTax().setScale(4, RoundingMode.HALF_UP) : "",
            productPrem.getVehicleVesselTaxRate() != null ? productPrem.getVehicleVesselTaxRate().setScale(4, RoundingMode.HALF_UP) : "");
        return DigestUtil.md5Hex(key);
    }

    /**
     * 税率和费率信息等,需要计算的值
     *
     * @param productPrem
     * @return
     */
    public static String getCommissionPolicyRateCode(CommissionBasicPolicyPremEntity productPrem) {
        String template = "费率[{}]_车船税税率[{}]_车船税费率[{}]";
        String key = StrUtil.format(template,
            productPrem.getCommissionRate() != null ? productPrem.getCommissionRate().setScale(4, RoundingMode.HALF_UP) : "",
            productPrem.getVehicleVesselTax() != null ? productPrem.getVehicleVesselTax().setScale(4, RoundingMode.HALF_UP) : "",
            productPrem.getVehicleVesselTaxRate() != null ? productPrem.getVehicleVesselTaxRate().setScale(4, RoundingMode.HALF_UP) : "");
        return DigestUtil.md5Hex(key);
    }

    /**
     * 获取基础佣金一单一匹配因子唯一值
     *
     * @param productPrem
     * @return
     */
    public static String getCommissionPolicyFactor(CommissionBasicPolicyPremEntity productPrem) {
        String template = "保单号[{}]_批单号[{}]_险种编码[{}]_结算机构[{}]_保单年期[{}]_缴费期次[{}]";
        String key = StrUtil.format(template, StrUtil.nullToDefault(productPrem.getPolicyNo(), ""),
            StrUtil.nullToDefault(productPrem.getBatchCode(), ""),
            StrUtil.nullToDefault(productPrem.getProductCode(), ""),
            StrUtil.nullToDefault(productPrem.getSettlementCompanyCode(), ""),
            productPrem.getYear() == null ? "" : productPrem.getYear(),
            productPrem.getPeriod() == null ? "" : productPrem.getPeriod());
        return DigestUtil.md5Hex(key);
    } 




}
