package com.mpolicy.settlement.core.modules.reconcile.service;

import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.policy.common.ep.policy.qry.details.GroupPreservationInsuredVo;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.*;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostPolicyInfoEntity;

import java.util.List;
/**
 * <AUTHOR>
 * @description
 * @date 2023/9/17 10:34 上午
 * @Version 1.0
 */
public interface SettlementCostOwnerService {
    /**
     *
     * @param preservationDetail
     * @return
     */
    String getPreservationOwnerCodeByPreservationDetail(PolicyPreservationDetailDto preservationDetail);

    /**
     * 分单推荐人变更归属人处理
     * @param dto
     * @return
     */
    String getSplitPolicyOwnerCodeByPreservationDetail(PreserveChannelReferrerChangeDto dto);
    /**
     *
     * @param policyInfo
     * @param costInfoEntities
     * @return
     */
    List<SettlementCostInfoEntity> builderBasePolicyCostOwnerInfo(EpContractInfoVo policyInfo, List<SettlementCostInfoEntity> costInfoEntities);

    /**
     *
     * @param policyInfo
     * @param renewalTermDto
     * @param costInfoEntities
     * @return
     */
    List<SettlementCostInfoEntity> builderRenewalPolicyCostOwnerInfo(EpContractInfoVo policyInfo, PolicyRenewalTermDto renewalTermDto, List<SettlementCostInfoEntity> costInfoEntities);
    /**
     * 非退保的保单维度保全佣金归属人处理
     * @param preservationDetail
     * @param costInfoEntities
     * @return
     */
    List<SettlementCostInfoEntity> builderPreservationCostOwnerInfo(PolicyPreservationDetailDto preservationDetail, List<SettlementCostInfoEntity> costInfoEntities);

    /**
     * 保全佣金归属人处理
     * @param channelCode
     * @param preservationDetail
     * @param costInfoEntity
     */
    void builderSinglePreservationCostOwnerInfo(String channelCode,PolicyPreservationDetailDto preservationDetail,SettlementCostInfoEntity costInfoEntity);
    /**
     * 团单退保明细佣金归属人
     * @param channelCode
     * @param insured
     * @param costInfoEntity
     */
    void builderGroupPolicySurrenderOwnerInfoByInsured(String channelCode, EpPreserveSurrenderInsuredDto insured, SettlementCostInfoEntity costInfoEntity);
    /**
     *
     * @param channelCode
     * @param insured
     * @param costInfoEntity
     */
    void builderGroupPolicyAddCostOwnerInfoByInsured(String channelCode,GroupPreservationInsuredVo insured, SettlementCostInfoEntity costInfoEntity);

    /**
     *
     * @param channelCode
     * @param insured
     * @param costInfoEntity
     */
    void builderGroupPolicyNewAddCostOwnerInfoByInsured(String channelCode,EpPreserveAddSubtractDetailDto insured, SettlementCostInfoEntity costInfoEntity);
    /**
     * 管护经理变更佣金归属人处理
     * @param afterCustomerManager
     * @param bean
     */
    void builderOwnerInfoByCustomerManageChange(PreservationCustomerChangeDto afterCustomerManager, SettlementCostInfoEntity bean);

    /**
     * 根据历史佣金信息设置佣金归属人
     * @param oldCostInfo
     * @param bean
     */
    void builderOwnerInfoByOldCostInfo(SettlementCostInfoEntity oldCostInfo ,SettlementCostInfoEntity bean);

    /**
     * 处理佣金归属人状态信息
     * @param costPolicy
     * @param costInfoEntities
     */
    void handlerOwnerThirdStatus(SettlementCostPolicyInfoEntity costPolicy, List<SettlementCostInfoEntity> costInfoEntities);
}
