package com.mpolicy.settlement.core.modules.reconcile.job;

import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementInnerCompanyLicenseInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementInnerCompanyLicenseInfoService;
import com.mpolicy.settlement.core.thirdpart.dto.omsbiz.LicenseInfoDto;
import com.mpolicy.settlement.core.thirdpart.dto.omsbiz.LicenseSyncPageData;
import com.mpolicy.settlement.core.thirdpart.dto.omsbiz.LicenseSyncRequest;
import com.mpolicy.settlement.core.thirdpart.dto.omsbiz.LicenseSyncResponse;
import com.mpolicy.settlement.core.thirdpart.service.OmsBizClient;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 工商信息同步定时任务
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Component
@Slf4j
public class SettlementInnerCompanyLicenseInfoSyncJob {

    @Autowired
    private OmsBizClient omsBizClient;

    @Autowired
    private SettlementInnerCompanyLicenseInfoService settlementInnerCompanyLicenseInfoService;

    private static final int PAGE_SIZE = 100;
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

    /**
     * 工商信息同步定时任务
     * 每小时从oms-biz服务同步工商信息数据
     */
    @XxlJob("syncLicenseInfoJob")
    public void syncLicenseInfoJob() {
        XxlJobHelper.log("开始执行工商信息同步任务");
        
        try {
            // 获取同步时间范围，默认同步从2018-01-01到当前时间的数据
            String startTime = "2018-01-01";
            String endTime = DATE_FORMAT.format(new Date());
            
            XxlJobHelper.log("同步时间范围：{} 到 {}", startTime, endTime);
            
            int totalSyncCount = 0;
            int pageIndex = 1;
            boolean hasMoreData = true;
            
            while (hasMoreData) {
                XxlJobHelper.log("开始同步第{}页数据", pageIndex);
                
                // 构建请求参数
                LicenseSyncRequest request = LicenseSyncRequest.builder()
                        .startTime(startTime)
                        .endTime(endTime)
                        .pageIndex(pageIndex)
                        .pageSize(PAGE_SIZE)
                        .build();
                
                // 调用oms-biz服务获取数据
                LicenseSyncResponse response = omsBizClient.syncLicenseInfo(request);
                LicenseSyncPageData pageData = response.getData();
                
                if (pageData == null || CollectionUtils.isEmpty(pageData.getList())) {
                    XxlJobHelper.log("第{}页无数据，结束同步", pageIndex);
                    break;
                }
                
                // 转换并保存数据
                List<SettlementInnerCompanyLicenseInfoEntity> entityList = convertToEntityList(pageData.getList());
                if (!CollectionUtils.isEmpty(entityList)) {
                    settlementInnerCompanyLicenseInfoService.batchInsertOrUpdate(entityList);
                    totalSyncCount += entityList.size();
                    XxlJobHelper.log("第{}页同步完成，本页数据量：{}", pageIndex, entityList.size());
                }
                
                // 判断是否还有下一页
                hasMoreData = Boolean.TRUE.equals(pageData.getHasNextPage());
                pageIndex++;
                
                // 防止无限循环，设置最大页数限制
                if (pageIndex > 1000) {
                    XxlJobHelper.log("达到最大页数限制，结束同步");
                    break;
                }
            }

            // 同步完成后刷新缓存
            if (totalSyncCount > 0) {
                XxlJobHelper.log("开始刷新工商信息缓存");
                settlementInnerCompanyLicenseInfoService.refreshCache();
                XxlJobHelper.log("工商信息缓存刷新完成");
            }

            XxlJobHelper.log("工商信息同步任务完成，总共同步数据：{}条", totalSyncCount);
            
        } catch (Exception e) {
            log.error("工商信息同步任务执行异常", e);
            XxlJobHelper.log("工商信息同步任务执行异常：{}", e.getMessage());
            throw e;
        }
    }

    /**
     * 将DTO列表转换为实体列表
     * 
     * @param dtoList DTO列表
     * @return 实体列表
     */
    private List<SettlementInnerCompanyLicenseInfoEntity> convertToEntityList(List<LicenseInfoDto> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        
        List<SettlementInnerCompanyLicenseInfoEntity> entityList = new ArrayList<>();
        
        for (LicenseInfoDto dto : dtoList) {
            SettlementInnerCompanyLicenseInfoEntity entity = SettlementInnerCompanyLicenseInfoEntity.builder()
                    .businessLicenseStatus(dto.getBusinessLicenseStatus())
                    .id(dto.getId())
                    .businessNature(dto.getBusinessNature())
                    .licenseName(dto.getLicenseName())
                    .licenseType(dto.getLicenseType())
                    .managementType(dto.getManagementType())
                    .modifyDate(dto.getModifyDate())
                    .socialCreditCode(dto.getSocialCreditCode())
                    .subjectType(dto.getSubjectType())
                    .deleted(0)
                    .createUser("system")
                    .updateUser("system")
                    .revision(1)
                    .build();
            
            entityList.add(entity);
        }
        
        return entityList;
    }
}
