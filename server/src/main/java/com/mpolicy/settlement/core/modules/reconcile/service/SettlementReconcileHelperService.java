package com.mpolicy.settlement.core.modules.reconcile.service;

import org.springframework.scheduling.annotation.Async;

/**
 * 保司结算对账单帮助服务
 *
 * <AUTHOR>
 * @since 2023-06-27 22:28:39
 */
public interface SettlementReconcileHelperService {

    /**
     * 根据对账单唯一编号获取保单信息，生成文件上传oss, 纪录到对账明细里面
     *
     * @param reconcileCode 对账单唯一编号
     * @return java.lang.String
     * <AUTHOR>
     * @since 2023/6/27
     */
    @Async
    void createPolicySummary(String reconcileCode,String uuid);
}

