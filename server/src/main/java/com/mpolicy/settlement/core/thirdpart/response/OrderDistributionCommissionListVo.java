package com.mpolicy.settlement.core.thirdpart.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class OrderDistributionCommissionListVo implements Serializable {
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty("佣金信息")
    private List<OrderDistributionCommissionVo> commissionVos;
}
