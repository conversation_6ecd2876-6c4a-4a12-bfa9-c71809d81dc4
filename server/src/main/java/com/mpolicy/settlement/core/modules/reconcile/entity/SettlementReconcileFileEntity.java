package com.mpolicy.settlement.core.modules.reconcile.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 保司结算对账单关联文件
 * 
 * <AUTHOR>
 * @date 2023-05-21 22:28:34
 */
@TableName("settlement_reconcile_file")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementReconcileFileEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 对账唯一单号
	 */
	private String reconcileCode;
	/**
	 * 对账文件编码
	 */
	private String reconcileFileCode;
	/**
	 * 对账文件类型;3+1文件编码
	 */
	private String reconcileFileType;
	/**
	 * 文件名称
	 */
	private String fileName;
	/**
	 * 文件下载地址
	 */
	private String fileUrl;
	/**
	 * 解析缓存数据
	 */
	private String cacheData;
	/**
	 * 是否删除;0有效-1删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private Integer revision;
}
