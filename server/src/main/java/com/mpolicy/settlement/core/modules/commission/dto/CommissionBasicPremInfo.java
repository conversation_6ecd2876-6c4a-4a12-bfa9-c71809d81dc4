package com.mpolicy.settlement.core.modules.commission.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class CommissionBasicPremInfo implements Serializable {
    /**
     * 费率
     */
    private BigDecimal yearRate;

    /**
     * 自定义年费率 (0不是  1:是)
     */
    private Integer isCustomYearRate;
    private String premCode;
    private String channelCode;
    private String channelName;
    /**
     * 结算保司编码
     */
    private String settlementCompanyCode;
    /**
     * 结算保司名称
     */
    private String settlementCompanyName;
}
