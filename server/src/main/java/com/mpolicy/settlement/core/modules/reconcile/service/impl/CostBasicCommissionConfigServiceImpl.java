package com.mpolicy.settlement.core.modules.reconcile.service.impl;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.policy.common.ep.policy.EpProductInfoVo;
import com.mpolicy.settlement.core.common.reconcile.enums.PremEventEnum;
import com.mpolicy.settlement.core.enums.SettlementExceptionEnum;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.EpPreserveProductDto;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.CostBasicCommissionConfigDto;
import com.mpolicy.settlement.core.modules.reconcile.enums.PolicyProductTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.factory.PremEventFactory;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.PremEventHandler;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyPremInput;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyPremResult;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyProductPremInput;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.PolicyProductPremResult;
import com.mpolicy.settlement.core.modules.reconcile.service.CostBasicCommissionConfigService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static com.mpolicy.settlement.core.common.Constant.POLICY_RENEWAL_FIRST_APPROVE_TIME_PRODUCTS;
import static com.mpolicy.settlement.core.modules.reconcile.service.impl.SettlementCostProcessServiceImpl.findFirstPolicy;

/**
 * <AUTHOR>
 * @description 支出端基础佣金费率配置服务
 * @date 2024/1/6 2:43 下午
 * @Version 1.0
 */
@Slf4j
@Service
public class CostBasicCommissionConfigServiceImpl implements CostBasicCommissionConfigService {


    /**
     * 支出端-组装获取基础佣金配置的入参(被保人参数+续期参数除外)
     *
     * @param policyInfo
     * @return
     */
    @Override
    public PolicyProductPremInput buildBasicCommissionConfigParam(EpContractInfoVo policyInfo,
                                                                  Integer insuranceType,
                                                                  EpProductInfoVo product,
                                                                  Integer insuredPolicyAge,
                                                                  Integer renewalYear,
                                                                  Integer renewalPeriod) {
        PolicyProductPremInput input = new PolicyProductPremInput();
        input.setChannelCode(policyInfo.getChannelInfo().getChannelCode());
        input.setPolicyNo(policyInfo.getContractBaseInfo().getPolicyNo());
        input.setMainProductCode(policyInfo.getContractBaseInfo().getMainProductCode());
        input.setProductCode(product.getProductCode());
        input.setPlantCode(product.getPlanCode());
        input.setInsuredPeriodType(product.getInsuredPeriodType());
        input.setInsuredPeriod(product.getInsuredPeriod());
        input.setInsuredPolicyAge(insuredPolicyAge);
        input.setPeriodType(product.getPeriodType());
        input.setPaymentPeriodType(product.getPaymentPeriodType());
        input.setPaymentPeriod(product.getPaymentPeriod());
        //0:新单 1:续投
        input.setInsuranceType(insuranceType);
        input.setApprovedTime(policyInfo.getContractExtendInfo().getApprovedTime());
        if (POLICY_RENEWAL_FIRST_APPROVE_TIME_PRODUCTS.contains(product.getProductCode()) && StrUtil.isNotEmpty(policyInfo.getSourcePolicyNo())) {
            EpContractInfoVo firstContractInfoVo = findFirstPolicy(policyInfo.getSourcePolicyNo() , 0);
            if (Objects.isNull(firstContractInfoVo) || Objects.isNull(firstContractInfoVo.getContractExtendInfo())) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("首单信息不存在-{}", policyInfo.getSourcePolicyNo())));
            }
            input.setApprovedTime(firstContractInfoVo.getContractExtendInfo().getApprovedTime());
        }

        input.setRenewalYear(renewalYear);
        input.setRenewalPeriod(renewalPeriod);
        // 投保人信息
        if (policyInfo.getApplicantInfo() != null) {
            input.setApplicantGender(policyInfo.getApplicantInfo().getApplicantGender());
            input.setApplicantBirthday(policyInfo.getApplicantInfo().getApplicantBirthday());
        }
        input.setSalesType(policyInfo.getContractBaseInfo().getSalesType());
        // 是否自保件
        input.setSelfPreservation(policyInfo.getContractBaseInfo().getSelfPreservation());

        // 代理人机构编码
        if (policyInfo.getAgentInfoList() != null && policyInfo.getAgentInfoList().size() > 0) {
            policyInfo.getAgentInfoList().stream().filter(a -> a.getMainFlag() == 1).findFirst().ifPresent(epAgentInfoVo -> input.setOrgCode(epAgentInfoVo.getOrgCode()));
        }
        return input;

    }
    @Override
    public PolicyProductPremInput buildBasicCommissionConfigParam(EpContractInfoVo policyInfo,
                                                           Integer insuranceType,
                                                           EpPreserveProductDto product,
                                                           Integer insuredPolicyAge,
                                                           Integer renewalYear,
                                                           Integer renewalPeriod){
        PolicyProductPremInput input = new PolicyProductPremInput();
        input.setChannelCode(policyInfo.getChannelInfo().getChannelCode());
        input.setPolicyNo(policyInfo.getContractBaseInfo().getPolicyNo());
        input.setMainProductCode(policyInfo.getContractBaseInfo().getMainProductCode());
        input.setProductCode(product.getProductCode());
        input.setPlantCode(product.getPlanCode());
        input.setInsuredPeriodType(product.getInsuredPeriodType());
        input.setInsuredPeriod(product.getInsuredPeriod());
        input.setInsuredPolicyAge(insuredPolicyAge);
        input.setPeriodType(product.getPeriodType());
        input.setPaymentPeriodType(product.getPaymentPeriodType());
        input.setPaymentPeriod(product.getPaymentPeriod());
        //0:新单 1:续投
        input.setInsuranceType(insuranceType);
        input.setApprovedTime(policyInfo.getContractExtendInfo().getApprovedTime());
        if (POLICY_RENEWAL_FIRST_APPROVE_TIME_PRODUCTS.contains(product.getProductCode()) && StrUtil.isNotEmpty(policyInfo.getSourcePolicyNo())) {
            EpContractInfoVo firstContractInfoVo = findFirstPolicy(policyInfo.getSourcePolicyNo() , 0);
            if (Objects.isNull(firstContractInfoVo) || Objects.isNull(firstContractInfoVo.getContractExtendInfo())) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("首单信息不存在-{}", policyInfo.getSourcePolicyNo())));
            }
            input.setApprovedTime(firstContractInfoVo.getContractExtendInfo().getApprovedTime());
        }
        input.setRenewalYear(renewalYear);
        input.setRenewalPeriod(renewalPeriod);
        // 投保人信息
        if (policyInfo.getApplicantInfo() != null) {
            input.setApplicantGender(policyInfo.getApplicantInfo().getApplicantGender());
            input.setApplicantBirthday(policyInfo.getApplicantInfo().getApplicantBirthday());
        }
        input.setSalesType(policyInfo.getContractBaseInfo().getSalesType());
        // 是否自保件
        input.setSelfPreservation(policyInfo.getContractBaseInfo().getSelfPreservation());

        // 代理人机构编码
        if (policyInfo.getAgentInfoList() != null && policyInfo.getAgentInfoList().size() > 0) {
            policyInfo.getAgentInfoList().stream().filter(a -> a.getMainFlag() == 1).findFirst().ifPresent(epAgentInfoVo -> input.setOrgCode(epAgentInfoVo.getOrgCode()));
        }
        return input;
    }

    @Override
    public List<CostBasicCommissionConfigDto> getCostBasicCommissionConfig(String policyNo, String policyProductType, PolicyProductPremInput input){

        return getCostBasicCommissionConfig(policyNo,null,policyProductType,input);
    }


    @Override
    public List<CostBasicCommissionConfigDto> getCostBasicCommissionConfig(String policyNo,String endorsementNo, String policyProductType,PolicyProductPremInput input){
        PolicyProductTypeEnum policyProductTypeEnum = PolicyProductTypeEnum.getProdTypeEnum(policyProductType);
        List<CostBasicCommissionConfigDto> results = listCostBasicCommissionConfig(policyNo,endorsementNo,input);
        //验证是否有一单一议的保单
        for(CostBasicCommissionConfigDto dto : results){
            dto.setPolicyProductTypeEnum(policyProductTypeEnum);
            dto.setPolicyNo(policyNo);
            dto.setEndorsementNo(endorsementNo);
            if(dto.getIsCustomYearRate() == 1) {
                PolicyPremResult policyPremResult = getPolicySettlementPrem(policyNo,endorsementNo,
                        input.getProductCode(),
                        dto.getSettlementCompanyCode(),
                        input.getRenewalYear(),
                        input.getRenewalPeriod());
                //必须设置
                dto.setYearRate(policyPremResult.getYearRate());
                //必须设置
                dto.setPremCode(policyPremResult.getPremCode());
                dto.setTaxRate(policyPremResult.getTaxRate());
                dto.setVehicleVesselTax(policyPremResult.getVehicleVesselTax());
                dto.setVehicleVesselTaxRate(policyPremResult.getVehicleVesselTaxRate());
                //验证一单一议
                validPolicySettlementPrem(dto);
            }
        }
        return results;
    }



    /**
     * 基础佣金配置获取
     * @param policyNo
     * @param endorsementNo
     * @param input
     * @return
     */
    public List<CostBasicCommissionConfigDto> listCostBasicCommissionConfig(String policyNo,String endorsementNo, PolicyProductPremInput input) {
        log.info("获取佣金配置比例入参：{}", input);
        PremEventHandler premEventHandler = PremEventFactory.getInvoke(PremEventEnum.COMMISSION_PREM.getCode());
        List<PolicyProductPremResult> basicConfigList = premEventHandler.queryPolicyProductPrem(input);
        if (CollectionUtils.isEmpty(basicConfigList)) {
            log.warn("支出端-获取基础佣金配置-保单号={}, 基础佣金配置信息不存在，入参：{}", policyNo, input);
            throw new GlobalException(SettlementExceptionEnum.CONFIG_COST_CONFIG_INFO_NOT_EXIST.getException(StrUtil.format("支出端-获取基础佣金配置, 基础佣金配置信息不存在,保单号={},入参：{}", policyNo,input)));
        }
        //验证配置
        if (basicConfigList.size() > 1) {
            Set<String> set = Sets.newHashSet();
            for (PolicyProductPremResult cof : basicConfigList) {
                if (set.contains(cof.getSettlementCompanyCode())) {
                    log.warn("支出端-获取基础佣金配置-保单号={}, 结算机构编码{}存在重复配置，入参：{}", policyNo, cof.getSettlementCompanyCode(), input);
                    throw new GlobalException(SettlementExceptionEnum.CONFIG_SETTLEMENT_COMPANY_EXIST_REPEAT.getException(StrUtil.format("支出端-获取基础佣金配置, 结算机构编码存在重复配置,保单号={},入参：{}", policyNo,input)));
                }
                String str = validBasicCommissionConfig(cof);
                if (StringUtil.isNotBlank(str)) {
                    log.warn("支出端-获取基础佣金配置-保单号={},{}，入参：{}", policyNo, str, input);
                    throw new GlobalException(SettlementExceptionEnum.CONFIG_INFO_EXCEPTION.getException(StrUtil.format("支出端-获取基础佣金配置,保单号={}, {}", policyNo, str)));
                }
                set.add(cof.getSettlementCompanyCode());


            }
        } else {
            String str = validBasicCommissionConfig(basicConfigList.get(0));
            if (StringUtil.isNotBlank(str)) {
                log.warn("支出端-获取基础佣金配置-保单号={},{}，入参：{}", policyNo, str, input);
                throw new GlobalException(SettlementExceptionEnum.CONFIG_INFO_EXCEPTION.getException(StrUtil.format("支出端-获取基础佣金配置,保单号={}, {}", policyNo, str)));
            }
        }
        log.info("获取到的佣金配置列表：{}", basicConfigList);
        List<CostBasicCommissionConfigDto> list = Lists.newArrayListWithCapacity(basicConfigList.size());
        if(CollectionUtils.isNotEmpty(basicConfigList)){
            basicConfigList.stream().forEach(config->{
                if(StringUtils.isBlank(config.getSettlementCompanyCode())){
                    throw new GlobalException(SettlementExceptionEnum.CONFIG_SETTLEMENT_COMPANY_NOT_EXIST.getException(StrUtil.format("支出端-计算基础佣金,佣金配置的结算机构为空,保单号={}", policyNo)));
                }
                CostBasicCommissionConfigDto dto = new CostBasicCommissionConfigDto();
                BeanUtils.copyProperties(config,dto);
                list.add(dto);
            });
        }
        return list;
    }

    /**
     * 获取一单一议的接口
     * @param policyNo
     * @param endorsementNo
     * @param settlementCompanyCode
     * @return
     */
    private PolicyPremResult getPolicySettlementPrem(String policyNo,
                                                     String endorsementNo,
                                                     String productCode,
                                                     String settlementCompanyCode,
                                                     Integer year,
                                                     Integer period){
        PremEventHandler premEventHandler = PremEventFactory.getInvoke(PremEventEnum.COMMISSION_PREM.getCode());
        PolicyPremResult settlementPolicyPrem = premEventHandler.queryPolicyPrem(PolicyPremInput.builder()
                .policyNo(policyNo)
                .settlementCompanyCode(settlementCompanyCode)
                .batchCode(endorsementNo)
                .productCode(productCode)
                .year(year)
                .period(period)
                .build());
        log.info("配置中返回一单一议保单{}/{}佣金费率信息：{}", policyNo, endorsementNo, settlementPolicyPrem);
        if (Objects.isNull(settlementPolicyPrem)) {
            throw new GlobalException(SettlementExceptionEnum.CONFIG_A_SINGLE_PROPOSAL_NOT_EXIST.getException(StrUtil.format("支出端-计算基础佣金,一单一议保单佣金费率信息未找到,保单号={}", policyNo)));
        }

        return settlementPolicyPrem;
    }

    /**
     * 验证一单一议
     * @param dto
     */
    private void validPolicySettlementPrem(CostBasicCommissionConfigDto dto){
        //一单一议费率验证
        boolean valid2 = dto.getYearRate() != null &&
                (dto.getYearRate().compareTo(BigDecimal.ZERO) < 0 || dto.getYearRate().compareTo(BigDecimal.ONE) > 0);
        if (valid2) {
            throw new GlobalException(SettlementExceptionEnum.CONFIG_A_SINGLE_PROPOSAL_EXCEPTION.getException(StrUtil.format("支出端-计算基础佣金,一单一议费率不在[0,1]范围内,保单号={}", dto.getPolicyNo())));
        }
        //是否车险
        if(Objects.equals(dto.getPolicyProductTypeEnum(),PolicyProductTypeEnum.VEHICLE)){
            //车船税金额验证
            if(dto.getVehicleVesselTax()!=null && BigDecimal.ZERO.compareTo(dto.getVehicleVesselTax())>0){
                throw new GlobalException(SettlementExceptionEnum.CONFIG_VEHICLE_VESSEL_EXCEPTION.getException(StrUtil.format("支出端-计算基础佣金,车船税金额异常,保单号={},车船税金额={}", dto.getPolicyNo(),dto.getVehicleVesselTax())));
            }else if(dto.getVehicleVesselTax()!=null && BigDecimal.ZERO.compareTo(dto.getVehicleVesselTax())<0){
                dto.setExistVehicleVesselTax(Boolean.TRUE);
            }
            //车窗税费率验证
            valid2 = dto.getVehicleVesselTaxRate() != null &&
                    (dto.getVehicleVesselTaxRate().compareTo(BigDecimal.ZERO) < 0 || dto.getVehicleVesselTaxRate().compareTo(BigDecimal.ONE) > 0);
            if (valid2) {
                throw new GlobalException(SettlementExceptionEnum.CONFIG_VEHICLE_VESSEL_EXCEPTION.getException(StrUtil.format("支出端-计算基础佣金,车船税率不在[0,1]范围内,保单号={},车船税费率={}", dto.getPolicyNo(),dto.getVehicleVesselTaxRate())));
            }

        }
    }

    /**
     * 支出端-验证基础佣金配置
     *
     * @param basicConfig
     * @return
     */
    private String validBasicCommissionConfig(PolicyProductPremResult basicConfig) {
        if (StringUtil.isBlank(basicConfig.getSettlementCompanyCode())) {
            return "结算机构编码不存在";
        }
        boolean valid1 = basicConfig.getYearRate() == null && basicConfig.getIsCustomYearRate() != 0;
        boolean valid2 = basicConfig.getYearRate() != null &&
                (basicConfig.getYearRate().compareTo(BigDecimal.ZERO) < 0 || basicConfig.getYearRate().compareTo(BigDecimal.ONE) > 0);
        if (valid1 || valid2) {
            return "基础佣金配置的利率" + basicConfig.getYearRate() + "不在[0,1]范围内";
        }
        return "";
    }

}
