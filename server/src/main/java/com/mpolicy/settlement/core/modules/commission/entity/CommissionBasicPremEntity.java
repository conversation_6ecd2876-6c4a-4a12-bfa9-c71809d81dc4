package com.mpolicy.settlement.core.modules.commission.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 基础佣金费率
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-31 18:39:39
 */
@TableName("commission_basic_prem")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommissionBasicPremEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @TableId
    private Integer id;
    /**
     * 佣金编码
     */
    private String commissionCode;
    /**
     * 费率编码
     */
    private String premCode;
    /**
     * 小鲸险种编码
     */
    private String productCode;
    /**
     * 小鲸险种名称
     */
    private String productName;
    private String mainProductCode;
    /**
     * 适用分公司编码
     */
    private String orgCode;
    /**
     * 适用分公司名称
     */
    private String orgName;
    /**
     * 小鲸险种保司编码
     */
    private String companyCode;
    /**
     * 小鲸险种保司名称
     */
    private String companyName;
    /**
     * 渠道编码
     */
    private String channelCode;
    /**
     * 渠道名称
     */
    private String channelName;
    /**
     * 结算结构编码
     */
    private String settlementCompanyCode;
    /**
     * 结算结构名称
     */
    private String settlementCompanyName;
    /**
     * 投保计划
     */
    private String productPlan;
    /**
     * 继续率红线
     */
    private String persistencyRate;
    /**
     * 保障期间
     */
    private String coveragePeriod;
    /**
     * 满期年龄
     */
    private String expireAge;
    /**
     * 缴费方式
     */
    private String paymentType;
    /**
     * 缴费期
     */
    private String paymentPeriod;
    /**
     * 缴费期间类型
     */
    private String paymentPeriodType;
    /**
     * 标保系数
     */
    private String underwritingRate;
    /**
     * 投保人年龄
     */
    private String applicantAge;
    /**
     * 投保人性别:男/女
     */
    private String applicantGender;
    /**
     * 被保人年龄
     */
    private String insuredAge;
    /**
     * 被保人性别:男/女
     */
    private String insuredGender;
    /**
     * 投保类型:新单/续保
     */
    private String insuranceType;
    /**
     * 费用类型 字典选项
     */
    private String costType;
    /**
     * 结算方式
     */
    private Integer settlementMethod;
    private String taxRate;
    /**
     * 首期自动结算: 0 不自动结算, 1 自动结算， null 无结算项
     */
    private Integer autoSettlementFlag;
    /**
     * 首年度费率
     */
    private String firstYearBrokage;
    /**
     * 续期自动结算: 0 不自动结算, 1 自动结算， null 无结算项
     */
    private Integer renewalSettlementFlag;
    /**
     * 续期佣金第二年
     */
    private String renewalBrokage2year;
    /**
     * 续期佣金第三年
     */
    private String renewalBrokage3year;
    /**
     * 续期佣金第四年
     */
    private String renewalBrokage4year;
    /**
     * 续期佣金第五年
     */
    private String renewalBrokage5year;
    /**
     * 续期佣金第六年
     */
    private String renewalBrokage6year;
    /**
     * 有效期起期
     */
    private String effectiveStartDate;
    /**
     * 有效期止期
     */
    private String effectiveEndDate;
    /**
     * 适用分公司
     */
    private String applyBranches;
    /**
     * 结算标准
     */
    private String settlementStandard;
    /**
     * 是否是自保件
     */
    private String selfPreservation;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 费率自动拓展列
     */
    private String renewalAutoExpand;
    /**
     * '佣金费率编码根据首年度费率/续期佣金第二年/续期佣金第三年/续期佣金第四年/续期佣金第五年/续期佣金第六年/扩展年份续期佣金生成'
     */
    private String rateCode;

    /**
     * 监控编码(佣金配置因子编码md5加密串,用于监控配置是否过期)
     */
    private String monitorCode;
    /**
     * 是否删除
     */
    @TableLogic
    private Integer deleted;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 乐观锁
     */
    @Version
    @TableField(fill = FieldFill.INSERT)
    private long revision;
}
