package com.mpolicy.settlement.core.modules.commission.job;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.mpolicy.settlement.core.modules.commission.entity.CommissionBasicPremEntity;
import com.mpolicy.settlement.core.modules.commission.service.CommissionBasicPolicyPremService;
import com.mpolicy.settlement.core.modules.commission.service.CommissionBasicPremService;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigureOrder;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class CommissionBasicPremJob {
    @Autowired
    private CommissionBasicPremService commissionBasicPremService;

    /**
     * 开始定时补偿基础佣金配置表监控编码
     *
     * <AUTHOR>
     * @since 2023/05/21
     */
    @XxlJob("compensateMonitorCodeHandler")
    public void compensateMonitorCodeHandler() {
        XxlJobHelper.log("开始定时补偿基础佣金配置表监控编码");
        String jobParam = XxlJobHelper.getJobParam();

        // 1 获取需要处理的事件 暂时只考虑【小鲸签约】
        List<CommissionBasicPremEntity> list = commissionBasicPremService.lambdaQuery()
                .isNull(StringUtil.isBlank(jobParam) || !Objects.equals("all",jobParam),CommissionBasicPremEntity::getMonitorCode)
                .last("limit 500")
                .list();
        if(CollectionUtils.isEmpty(list)){
            return ;
        }
        XxlJobHelper.log("补偿基础佣金配置表监控编码需要处理的记录条数={},", list.size());
        list.stream().forEach(l->{
            try{
                String monitorCode = getCommissionMonitorCode(l);
                l.setMonitorCode(monitorCode);
            }catch (Exception e){
                log.warn("补偿险种{}/{}基础佣金配置表监控编码异常，记录id={}",l.getProductCode(),l.getProductName(),l.getId(),e);
                XxlJobHelper.log("补偿险种{}/{}基础佣金配置表监控编码异常，记录id={}",l.getProductCode(),l.getProductName(),l.getId());
            }
        });
        commissionBasicPremService.batchUpdateMonitorCode(list);
        XxlJobHelper.log("定时补偿基础佣金配置表监控编码结束");
    }

    /**
     * 基础佣金获取费率唯一值
     *
     * @param productPrem
     * @return
     */
    public static String getCommissionMonitorCode(CommissionBasicPremEntity productPrem) {
        String template =
                "险种编码[{}]_主险险种编码[{}]_继续率红线[{}]_投保计划[{}]_保障期间[{}]_满期年龄[{}]_投保人年龄[{}]_投保人性别[{}]_" + "被保人年龄[{}]_被保人性别[{}]_" +
                        "缴费方式[{}]_缴费期[{}]_标保系数[{}]_费用类型[{}]_首期自动结算[{}]_续期自动结算[{}]_适用分公司组织ID[{}]_" + "自保件[{}]_" +
                        "销售渠道编码[{}]_结算机构编码[{}]";

        // 保司协议险种编码-主险保司协议险种编码-_
        // 继续率红线-投保计划-保障期间-满期年龄-投保人年龄-投保人性别-被保人年龄-被保人性别-缴费方式-缴费期-标保系数-费用类型-首期自动结算-续期自动结算-适用分公司组织ID-自保件-有效期起期-有效期止期
        String key = StrUtil.format(template, StrUtil.nullToDefault(productPrem.getProductCode(), ""),
                StrUtil.nullToDefault(productPrem.getMainProductCode(), ""),
                StrUtil.nullToDefault(productPrem.getPersistencyRate(), ""),
                StrUtil.nullToDefault(productPrem.getProductPlan(), ""),
                StrUtil.nullToDefault(productPrem.getCoveragePeriod(), ""),
                StrUtil.nullToDefault(productPrem.getExpireAge(), ""),
                StrUtil.nullToDefault(productPrem.getApplicantAge(), ""),
                StrUtil.nullToDefault(productPrem.getApplicantGender(), ""),
                StrUtil.nullToDefault(productPrem.getInsuredAge(), ""),
                StrUtil.nullToDefault(productPrem.getInsuredGender(), ""),
                StrUtil.nullToDefault(productPrem.getPaymentType(), ""),
                StrUtil.nullToDefault(productPrem.getPaymentPeriod(), ""),
                StrUtil.nullToDefault(productPrem.getUnderwritingRate(), ""),
                StrUtil.nullToDefault(productPrem.getCostType(), ""),
                StrUtil.nullToDefault(productPrem.getAutoSettlementFlag()+"", ""),
                StrUtil.nullToDefault(productPrem.getRenewalSettlementFlag()+"", ""),
                StrUtil.nullToDefault(productPrem.getOrgCode(), ""),
                StrUtil.nullToDefault(productPrem.getSelfPreservation(), ""),
                StrUtil.nullToDefault(productPrem.getChannelCode(), ""),
                StrUtil.nullToDefault(productPrem.getSettlementCompanyCode(), ""));
        log.info("Key:{}", key);
        return DigestUtil.md5Hex(key);
    }
}
