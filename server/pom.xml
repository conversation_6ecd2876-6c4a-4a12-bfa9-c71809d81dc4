<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>mpolicy-settlement-center-core-server</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>mpolicy-settlement-center-core-server</name>
    <description>mpolicy-settlement-center-core-server</description>
    <parent>
        <groupId>com.mpolicy</groupId>
        <artifactId>mpolicy-settlement-center-core</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <properties>
        <mpolicy.commont.web.version>2.0.11</mpolicy.commont.web.version>
        <mpolicy.commont.service.version>2.0.10</mpolicy.commont.service.version>
        <mpolicy.public.api.client.vsersion>2.0.87-SNAPSHOT</mpolicy.public.api.client.vsersion>
        <mpolicy.policy.client.version>2.5.42</mpolicy.policy.client.version>
        <project.settlement.core.common.version>1.0.47</project.settlement.core.common.version>
        <mpolicy.product.client.version>2.0.33</mpolicy.product.client.version>
        <project.mpolicy.agent.client.version>2.0.18</project.mpolicy.agent.client.version>
        <org.mapstruct.version>1.3.1.Final</org.mapstruct.version>
        <groovy.version>2.4.12</groovy.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dingtalk</artifactId>
            <version>1.3.38</version>
        </dependency>

        <dependency>
            <groupId>com.dingtalk</groupId>
            <artifactId>dingtalk</artifactId>
            <version>20220101</version>
        </dependency>

        <!-- 基础模块 -->
        <dependency>
            <groupId>com.mpolicy</groupId>
            <artifactId>mpolicy-common-web</artifactId>
            <version>${mpolicy.commont.web.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mpolicy</groupId>
            <artifactId>mpolicy-common-service</artifactId>
            <version>${mpolicy.commont.service.version}</version>
        </dependency>
        <!-- settlement center core common -->
        <dependency>
            <groupId>com.mpolicy</groupId>
            <artifactId>mpolicy-settlement-center-core-common</artifactId>
            <version>${project.settlement.core.common.version}</version>
        </dependency>
        <!--public feign -->
        <dependency>
            <groupId>com.mpolicy</groupId>
            <artifactId>mpolicy-public-client</artifactId>
            <version>${mpolicy.public.api.client.vsersion}</version>
        </dependency>
        <!--保单中心-->
        <dependency>
            <groupId>com.mpolicy</groupId>
            <artifactId>mpolicy-policy-client</artifactId>
            <version>${mpolicy.policy.client.version}</version>
        </dependency>
        <!-- 产品中心feign-->
        <dependency>
            <groupId>com.mpolicy</groupId>
            <artifactId>mpolicy-product-client</artifactId>
            <version>${mpolicy.product.client.version}</version>
        </dependency>
        <!--代理人feign服务 -->
        <dependency>
            <groupId>com.mpolicy</groupId>
            <artifactId>mpolicy-agent-client</artifactId>
            <version>${project.mpolicy.agent.client.version}</version>
        </dependency>
        <!-- spring actuator-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!-- spring -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-config-client</artifactId>
        </dependency>
        <!--接入bus config的需要引入
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bus-amqp</artifactId>
        </dependency>
        -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
        </dependency>
        <!--包含sleuth和zipkin-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-zipkin</artifactId>
        </dependency>
        <!--knife4j-->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-aggregation-spring-boot-starter</artifactId>
        </dependency>
        <!--mapstruct-->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${org.mapstruct.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${org.mapstruct.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
        </dependency>
        <!--Groovy-->
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-bsf</artifactId>
            <version>${groovy.version}</version>
        </dependency>

        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <version>${groovy.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <configuration>
                    <!-- deploy 时忽略此model -->
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
