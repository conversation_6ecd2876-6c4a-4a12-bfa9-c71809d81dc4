### 结算系统服务

#### client说明 
- 该代码最终以jar包形式提供其他服务引用进行feign调用，属于服务端编写feign
- feign接口调用支持服务端研发提供，调用方引入该jar包即可以完成服务直接调用
- 如果考虑降级等，外部服务可以自行编写feign


#### common说明 
- client调用及service feign调用需要用到的vo实体类、枚举等，用于序列化

#### server服务目录结构
```
service
   |--doc 数据库脚本及工程其他说明文件
   |--src/main/java
   |      |--com.mpolicy.xxx
   |              |--common      常量及redis key配置
   |              |--config      其他的bean配置文件
   |              |--controller  controller层
   |              |--dao         对象的存储及查询
   |              |--dto         dto对象，承载与外部交互的数据
   |              |--entity      持久对象
   |              |--enums       枚举对象
   |              |--helper      辅助服务类
   |              |--job         xxl-job执行服务
   |              |--mq          mq消息消费者处理
   |                  |--enums        消费者-服务端的OpeType
   |                  |--service      消息处理
   |                  |--xxxx.java    消费者监听类
   |              |--service     服务层
    |                  |--common    内部服务feign调用包装(日志、主动降级等)  
   |              |--utils       工具类
   |              |--modules     业务模块   
   |                  |--company    保险公司 
   |                  |--agent    代理人 
   |              |--thirdpart   访问第三方接口服务
   |                  |--request      请求数据传输对象
   |                  |--response     响应数据传输对象
   |                  |--facade       基于fegin的第三方接口定义
   |                  |--integration  封装第三方接口的调用（记录访问日志，第三方接口调用异常处理）
   |              
   |--src/main/resources
          |--mappers OR mapping文件
          
```
#### 相关使用说明
##### redis使用



* 新建一个业务模块keys存储类继承`BasePrefix`

```java
  public class AccountKeys extends BasePrefix {
    	// 存在过期key 100为秒
      public static AccountKeys accountAddress = new AccountKeys(200, "address");
      // 永久有效key
      public static AccountKeys dictinoary = new AccountKeys("DICT");
  
      private AccountKeys(String prefix) {
          super(prefix);
      }
      private AccountKeys(int expireSeconds, String prefix) {
        super(expireSeconds, prefix);
      }
  }
```

  > 继承`BasePrefix`是为规范redis存储keys及更方便管理keys的生命周期及统计

    * 使用基本操作

```java
      /**
       * redis 操作
       */
      @Autowired
      private IRedisService redisService;
      
      // set
      redisService.set(ReportKeys.reportHtml, "key", "内容");
      // get
      String reportHtml = redisService.get(ReportKeys.reportHtml, "key", String.class);
    // delete
      boolean delKey = redisService.delete(ReportKeys.reportHtml, reportCode);
```
##### JWT支持

默认情况下，jwt拦截器是不起效的，需要单体服务开启

> api 网关会鉴权token，解签后传入到下层服务，下层服务开启jwt拦截器，获取操作对象信息


* 开启jwt拦截器

```java
package com.mpolicy.customer.config;

import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.mpolicy.web.common.jwt.RsaInterceptor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.ArrayList;
import java.util.List;

/**
 * WebMvc配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-01-19 15:30
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    /**
     * 环境
     */
    @Value("${spring.profiles.active:dev}")
    private String profilesActive;


    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        /*放行swagger*/
        registry.addResourceHandler("swagger-ui.html")
                .addResourceLocations("classpath:/META-INF/resources/");

        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 本地环境不加入此拦截器
        if (!StringUtils.equals(profilesActive, "dev")) {
            registry.addInterceptor(rsaInterceptor())
                    .addPathPatterns("/**")
                    .excludePathPatterns("/swagger-resources/**", "/webjars/**", "/v2/**", "/swagger-ui.html/**", "/doc.html");
        }
    }

    @Bean
    public RsaInterceptor rsaInterceptor() {
        return new RsaInterceptor();
    }
}
 ```

* 控制器获取操作对象

  ```java
  // 获取ThreadLocal 中操作用户信息
  SubjectInfo info = UserContext.getUser();
  zuul > 解析token > 验证 > 解析user对象 > userid头信息 > RsaInterceptor解析头信息 >  ThreadLocal
    
  @passtoken(teyGetUser = true)  
  控制器 SubjectInfo info = UserContext.getUser(); // 志高对象 info= null
  info 有可能值
  可以未登录  但是登陆我们要纪录
  ```

> 此对象获取如果没有特殊情况，只推荐在`controller`获取使用，`service`和`interface`都推荐作为参数传输

##### 多数据源

> 多数据库支持模块代码，编写在mpolicy-common-service 包 com.mpolicy.service.common.datasources 采用注解+aop进行动态改变数据源

* 使用

  ```java
  @DataSource(value = "xxxx")
  public void clearReportUrl(String reportCode) {
  
  }
  ```

  > `@DataSource` 注解为类、方法级； 推荐使用在service impl的方法上
  >
  > `slave1` 需要联系配置中心的人员，配置好了后才能使用 
