FROM registry.cn-hangzhou.aliyuncs.com/shinyn/openjdk:8
MAINTAINER zhangqin <<EMAIL>>
ARG ENV_MY_JAR
ARG ENV_MY_SER
ARG ENV_KEY
ARG ENV_PORT
ARG JAVA_AGENT
ARG JAVA_OPTS
ENV ENV_MY_JAR ${ENV_MY_JAR}
ENV ENV_MY_SER ${ENV_MY_SER}
ENV ENV_KEY ${ENV_KEY}
ENV ENV_PORT ${ENV_PORT}
ENV JAVA_AGENT ${JAVA_AGENT}
ENV JAVA_OPTS ${JAVA_OPTS}
RUN mkdir -p ${ENV_MY_SER}/logs \
    && ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone
WORKDIR ${ENV_MY_SER}
ADD target/${ENV_MY_JAR} /${ENV_MY_SER}
RUN chmod 777 ${ENV_MY_JAR}
EXPOSE ${ENV_PORT}
CMD java ${JAVA_AGENT} -jar ${JAVA_OPTS}  ./${ENV_MY_JAR} --spring.profiles.active=${ENV_KEY} --spring.cloud.config.profile=${ENV_KEY} --spring.cloud.config.label=${ENV_KEY}
