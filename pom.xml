<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.mpolicy</groupId>
	<artifactId>mpolicy-settlement-center-core</artifactId>
	<version>1.0.0-SNAPSHOT</version>
	<name>mpolicy-settlement-center-core</name>
	<packaging>pom</packaging>
	<description>mpolicy policy center</description>

    <modules>
        <module>common</module>
        <module>server</module>
        <module>client</module>
    </modules>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.0.2.RELEASE</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>

	<!-- 引用依赖版本定义 -->
	<properties>
		<!--spring-boot版本-->
		<spring-boot.version>2.0.2.RELEASE</spring-boot.version>
		<!--spring-cloud 版本-->
		<spring-cloud.version>Finchley.RELEASE</spring-cloud.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>1.8</java.version>
		<junit.version>4.12</junit.version>
		<commons.lang.version>3.8.1</commons.lang.version>
		<commons.fileupload.version>1.3.1</commons.fileupload.version>
		<commons.io.version>2.5</commons.io.version>
		<commons.codec.version>1.10</commons.codec.version>
		<fastjson.version>1.2.71</fastjson.version>
		<swagger.version>2.9.2</swagger.version>
		<swagger-ui.version>1.9.3</swagger-ui.version>
		<joda.time.version>2.9.9</joda.time.version>
		<knife4j.version>2.0.4</knife4j.version>
		<knife4j.aggregation.version>2.0.8</knife4j.aggregation.version>

		<lombok.version>1.18.16</lombok.version>
	</properties>

	<dependencies>
		<!-- junit -->
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>${junit.version}</version>
			<scope>test</scope>
		</dependency>
		<!-- fastjson -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>${fastjson.version}</version>
		</dependency>
		<!-- lombok -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>${lombok.version}</version>
		</dependency>
		<!-- commons -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>${commons.lang.version}</version>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>${commons.io.version}</version>
		</dependency>
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
			<version>${commons.codec.version}</version>
		</dependency>
		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
			<version>${joda.time.version}</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.google.guava/guava -->
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>27.0.1-jre</version>
		</dependency>
	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>commons-fileupload</groupId>
				<artifactId>commons-fileupload</artifactId>
				<version>${commons.fileupload.version}</version>
			</dependency>
			<!--swagger2-->
			<dependency>
				<groupId>io.springfox</groupId>
				<artifactId>springfox-swagger2</artifactId>
				<version>${swagger.version}</version>
			</dependency>
			<dependency>
				<groupId>io.springfox</groupId>
				<artifactId>springfox-swagger-ui</artifactId>
				<version>${swagger.version}</version>
			</dependency>
			<dependency>
				<groupId>com.github.xiaoymin</groupId>
				<artifactId>knife4j-spring-boot-starter</artifactId>
				<version>${knife4j.version}</version>
			</dependency>
			<dependency>
				<groupId>com.github.xiaoymin</groupId>
				<artifactId>knife4j-aggregation-spring-boot-starter</artifactId>
				<version>${knife4j.aggregation.version}</version>
			</dependency>

			<!--  swagger导出PDF/HTML所需依赖 -->
			<dependency>
				<groupId>io.github.swagger2markup</groupId>
				<artifactId>swagger2markup</artifactId>
				<version>1.3.1</version>
			</dependency>
			<!--引入JWT依赖,由于是基于Java，所以需要的是java-jwt-->
			<dependency>
				<groupId>io.jsonwebtoken</groupId>
				<artifactId>jjwt</artifactId>
				<version>0.9.1</version>
			</dependency>
			<dependency>
				<groupId>com.auth0</groupId>
				<artifactId>java-jwt</artifactId>
				<version>3.4.0</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-source-plugin</artifactId>
					<version>3.0.1</version>
					<configuration>
						<attach>true</attach>
					</configuration>
					<executions>
						<execution>
							<phase>compile</phase>
							<goals>
								<goal>jar</goal>
							</goals>
						</execution>
					</executions>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>

	<!-- 阿里云私有仓库 -->
	<distributionManagement>
		<repository>
			<id>rdc-releases</id>
			<url>https://packages.aliyun.com/maven/repository/2061892-release-ErPEvR/</url>
		</repository>
		<snapshotRepository>
			<id>rdc-snapshots</id>
			<url>https://packages.aliyun.com/maven/repository/2061892-snapshot-AGtpf7/</url>
		</snapshotRepository>
	</distributionManagement>
</project>
